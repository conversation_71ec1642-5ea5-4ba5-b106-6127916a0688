from mcp.server.fastmcp import FastMCP, Context
from typing import Dict, Any, List, Optional
from unity_connection import get_unity_connection

def register_champion_fusion_system_tools(mcp: FastMCP):
    """Register Champion Fusion System tools with the MCP server."""

    @mcp.tool()
    def champion_fusion_system(
        ctx: Context,
        action: str,
        champion1_id: Optional[str] = None,
        champion2_id: Optional[str] = None,
        fusion_recipe_id: Optional[str] = None,
        fusion_name: Optional[str] = None,
        fusion_type: Optional[str] = None,
        compatibility_threshold: Optional[float] = None,
        balance_mode: Optional[str] = None,
        preview_settings: Optional[Dict[str, Any]] = None,
        tree_operation: Optional[str] = None,
        fusion_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Sistema de Fusão de Campeões para MOBA AURACRON.

        Funcionalidades:
        - fuse_champions: Funde dois campeões
        - analyze_fusion: Analisa compatibilidade de fusão
        - create_fusion_recipe: Cria receita de fusão
        - preview_fusion: Visualiza resultado da fusão
        - manage_fusion_tree: Gerencia árvore de fusões
        - balance_fusion: Balanceia estatísticas da fusão
        - get_fusion_info: Obtém informações de fusão

        [MOBA FEATURES]:
        - Fusão de habilidades e estatísticas
        - Sistema de compatibilidade entre campeões
        - Balanceamento automático
        - Árvore de evolução de fusões

        Args:
            action: Operação (fuse_champions, analyze_fusion, create_fusion_recipe, 
                   preview_fusion, manage_fusion_tree, balance_fusion, get_fusion_info)
            champion1_id: ID do primeiro campeão
            champion2_id: ID do segundo campeão
            fusion_recipe_id: ID da receita de fusão
            fusion_name: Nome da fusão resultante
            fusion_type: Tipo de fusão (stat_fusion, ability_fusion, hybrid_fusion)
            compatibility_threshold: Limite de compatibilidade (0.0-1.0)
            balance_mode: Modo de balanceamento (auto, manual, competitive)
            preview_settings: Configurações de preview
            tree_operation: Operação na árvore (add, remove, update, query)
            fusion_data: Dados específicos da fusão

        Returns:
            Dictionary com resultados ('success', 'message', 'data')
        """
        try:
            # Preparar parâmetros, removendo valores None
            params = {
                "action": action,
                "champion1_id": champion1_id,
                "champion2_id": champion2_id,
                "fusion_recipe_id": fusion_recipe_id,
                "fusion_name": fusion_name,
                "fusion_type": fusion_type,
                "compatibility_threshold": compatibility_threshold,
                "balance_mode": balance_mode,
                "preview_settings": preview_settings,
                "tree_operation": tree_operation,
                "fusion_data": fusion_data
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            # Enviar comando para Unity
            response = get_unity_connection().send_command("champion_fusion_system", params)

            # Processar resposta
            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Champion Fusion operation completed successfully."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Unknown error in Champion Fusion operation.")
                }

        except Exception as e:
            return {"success": False, "message": f"Python error in Champion Fusion System: {str(e)}"}
