using System;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// Registry for all MCP command handlers (Refactored Version)
    /// </summary>
    public static class CommandRegistry
    {
        // Maps command names (matching those called from Python via ctx.bridge.unity_editor.HandlerName)
        // to the corresponding static HandleCommand method in the appropriate tool class.
        private static readonly Dictionary<string, Func<JObject, object>> _handlers = new()
        {
            { "HandleManageScript", ManageScript.HandleCommand },
            { "HandleManageScene", ManageScene.HandleCommand },
            { "HandleManageEditor", ManageEditor.HandleCommand },
            { "HandleManageGameObject", ManageGameObject.HandleCommand },
            { "HandleManageAsset", ManageAsset.HandleCommand },
            { "HandleReadConsole", ReadConsole.HandleCommand },
            { "HandleExecuteMenuItem", ExecuteMenuItem.HandleCommand },
            { "HandleAudioSystem", AudioSystem.HandleCommand },
            { "HandleProjectAuditorRuntime", ProjectAuditorRuntime.HandleCommand },
            { "HandleLightingRendering", LightingRendering.HandleCommand },
            { "HandleMultiplayerNetworking", MultiplayerNetworking.HandleCommand },
            { "HandleAIRuntime", AIRuntime.HandleCommand },
            { "HandleAnimationRuntime", AnimationRuntime.HandleCommand },
            { "HandleXRRuntime", XRRuntime.HandleCommand },
            { "HandleAssetProcessing", AssetProcessing.HandleCommand },
            { "HandleProjectCodeOperations", ProjectCodeOperations.HandleCommand },
            { "HandleUnityTesting", UnityTesting.HandleCommand },
            { "HandleUnityTestRunner", UnityTestRunner.HandleCommand },
            { "HandleUnityCloudTesting", UnityCloudTesting.HandleCommand },
            { "HandleMultiplayerSessions", MultiplayerSessions.HandleCommand },
            { "HandleDistributedAuthority", DistributedAuthority.HandleCommand },
            { "HandleSessionMatchmaking", SessionMatchmaking.HandleCommand },
            { "HandleSessionPersistence", SessionPersistence.HandleCommand },
            { "HandleSessionMigration", SessionMigration.HandleCommand },
            { "HandleBuildProfilesRuntime", BuildProfilesRuntime.HandleCommand },
            { "HandleAdvancedAudio", AdvancedAudio.HandleCommand },
            { "HandleAdvancedPhysics", AdvancedPhysics.HandleCommand },
            { "HandleWebGPURuntime", WebGPURuntime.HandleCommand },
            { "HandleWebGPUIndirectRendering", WebGPUIndirectRendering.HandleCommand },
            { "HandleWebGPUGPUSkinning", WebGPUGPUSkinning.HandleCommand },
            { "HandleWebGPUVFXSystem", WebGPUVFXSystem.HandleCommand },
            { "HandleWebGPUCompatibilityDetection", WebGPUCompatibilityDetection.HandleCommand },
            { "HandleAdvancedRendering", AdvancedRendering.HandleCommand },
            { "HandleRaytracingOperations", RaytracingOperations.HandleCommand },
            { "HandleAdvancedAI", AdvancedAI.HandleCommand },
            { "HandleUnityAIAssistant", UnityAIAssistant.HandleCommand },
            { "HandleTwoDToolkitRuntime", TwoDToolkitRuntime.HandleCommand },
            { "HandleSetupVrsCustomPasses", VariableRateShading.HandleSetupVrsCustomPasses },
            { "HandleConfigureShadingRateImage", VariableRateShading.HandleConfigureShadingRateImage },
            { "HandleSetupVrsPerformanceScaling", VariableRateShading.HandleSetupVrsPerformanceScaling },
            { "HandleCreateVrsQualityRegions", VariableRateShading.HandleCreateVrsQualityRegions },
            { "HandleSetupVrsApiIntegration", VariableRateShading.HandleSetupVrsApiIntegration },
            { "HandleConfigureVrsDebugVisualization", VariableRateShading.HandleConfigureVrsDebugVisualization },
            
            // Project Auditor Enhanced handlers
            { "HandleSetupProjectAuditorIntegration", ProjectAuditorEnhanced.HandleSetupProjectAuditorIntegration },
            { "HandleConfigureAssetUsageAnalysis", ProjectAuditorEnhanced.HandleConfigureAssetUsageAnalysis },
            { "HandleSetupShaderVariantReporting", ProjectAuditorEnhanced.HandleSetupShaderVariantReporting },
            { "HandleCreateMemoryProfilingReport", ProjectAuditorEnhanced.HandleCreateMemoryProfilingReport },
            { "HandleSetupDomainReloadMonitoring", ProjectAuditorEnhanced.HandleSetupDomainReloadMonitoring },
            { "HandleConfigureBuildSizeAnalysis", ProjectAuditorEnhanced.HandleConfigureBuildSizeAnalysis },
            { "HandleSetupCodeAnalysisRuntime", ProjectAuditorEnhanced.HandleSetupCodeAnalysisRuntime },
            { "HandleCreatePerformanceRecommendations", ProjectAuditorEnhanced.HandleCreatePerformanceRecommendations },
            
            // Inference Engine Neural Network Runtime handlers
            { "HandleSetupInferenceFrameSlicing", InferenceNeuralNetwork.HandleCommand },
            { "HandleCreateInferenceQuantizationSystem", InferenceNeuralNetwork.HandleCommand },
            { "HandleSetupInferenceBackendSelection", InferenceNeuralNetwork.HandleCommand },
            { "HandleConfigureInferenceTensorOperations", InferenceNeuralNetwork.HandleCommand },
            { "HandleSetupInferenceMemoryOptimization", InferenceNeuralNetwork.HandleCommand },
            { "HandleCreateInferenceModelVisualization", InferenceNeuralNetwork.HandleCommand },
            { "HandleSetupInferenceAsyncInference", InferenceNeuralNetwork.HandleCommand },
            { "HandleConfigureInferenceOnnxImport", InferenceNeuralNetwork.HandleCommand },
            
            // VFX & Shader Graph Runtime Control handlers
            { "HandleSetupVfxGraphShaderBinding", VfxShaderGraphControl.HandleCommand },
            { "HandleConfigureVfxGpuInstancing", VfxShaderGraphControl.HandleCommand },
            { "HandleSetupVfxRuntimeRecompilation", VfxShaderGraphControl.HandleCommand },
            { "HandleCreateVfxParameterController", VfxShaderGraphControl.HandleCommand },
            { "HandleSetupVfxParallelizationTuning", VfxShaderGraphControl.HandleCommand },
            { "HandleConfigureShaderGraphVfxSupport", VfxShaderGraphControl.HandleCommand },
            { "HandleSetupVfxParticleDataLayout", VfxShaderGraphControl.HandleCommand },
            { "HandleCreateVfxGarbageCollectionMonitor", VfxShaderGraphControl.HandleCommand },
            
            // Editor Build Tools handlers
            { "HandleEditorBuildTools", EditorBuildTools.HandleCommand },
            
            // Generative 2D World Building handlers
            { "HandleGenerative2DWorld", Generative2DWorld.HandleCommand },
            
            // Advanced Procedural Generation handlers
            { "HandleGenerateProceduralMesh", AdvancedProceduralGeneration.HandleGenerateProceduralMesh },
            { "HandleCreateProceduralTerrainSystem", AdvancedProceduralGeneration.HandleCreateProceduralTerrainSystem },
            { "HandleSetupProceduralVegetation", AdvancedProceduralGeneration.HandleSetupProceduralVegetation },
            { "HandleGenerateMeshFromHeightmap", AdvancedProceduralGeneration.HandleGenerateMeshFromHeightmap },
            
            // Procedural Character Assembly handlers
            { "HandleProceduralCharacterAssembly", ProceduralCharacterAssembly.HandleCommand },
            
            // NEW Unity 6.2 Procedural Generation handlers
            { "HandleProceduralBodyPartGeneration", ProceduralBodyPartGeneration.HandleCommand },
            { "HandleProceduralTextureGeneration", ProceduralTextureGeneration.HandleCommand },
            { "HandleProceduralSkeletonGeneration", ProceduralSkeletonGeneration.HandleCommand },
            { "HandleProceduralAnimationGeneration", ProceduralAnimationGeneration.HandleCommand },
            { "HandleCharacterGameplaySystem", CharacterGameplaySystem.HandleCommand },
            { "HandleOneClickCharacterCreator", OneClickCharacterCreator.HandleCommand },
            
            // Dynamic Game Systems handlers
            { "HandleGenerativeStateMachine", DynamicGameSystems.HandleGenerativeStateMachine },
            { "HandleAttachBehaviourScript", DynamicGameSystems.HandleBehaviourScriptToState },
            { "HandleProceduralEventSystem", DynamicGameSystems.HandleProceduralEventSystem },
            { "HandleGameplayRulesEngine", DynamicGameSystems.HandleGameplayRulesEngine },
            { "HandleDynamicSkillTree", DynamicGameSystems.HandleDynamicSkillTree },
            { "HandleBuffDebuffSystem", DynamicGameSystems.HandleBuffDebuffSystem },
            { "HandleAIBehaviourTree", DynamicGameSystems.HandleAIBehaviourTree },
            { "HandleGameStateManager", DynamicGameSystems.HandleGameStateManager },
            { "HandleObjectiveTrackerSystem", DynamicGameSystems.HandleObjectiveTrackerSystem },
            
            // Procedural Narrative & Quest Generation handlers
            { "HandleProceduralNarrative", ProceduralNarrative.HandleCommand },
            
            // Generative Game Economy & Balancing handlers
            { "HandleAnalyzeGameplayDataForBalancing", GenerativeGameEconomy.HandleAnalyzeGameplayDataForBalancing },
            { "HandleGenerateItemDatabaseProcedural", GenerativeGameEconomy.HandleGenerateItemDatabaseProcedural },
            
            // Automated Cinematics handlers
            { "HandleAutomatedCinematics", AutomatedCinematics.HandleCommand },
            { "HandleCreateDynamicShopInventory", GenerativeGameEconomy.HandleCreateDynamicShopInventory },
            { "HandleSetupProceduralLootTables", GenerativeGameEconomy.HandleSetupProceduralLootTables },
            { "HandleSimulateSupplyAndDemand", GenerativeGameEconomy.HandleSimulateSupplyAndDemand },
            { "HandleBalanceSkillCostsAndEffects", GenerativeGameEconomy.HandleBalanceSkillCostsAndEffects },
            { "HandleGenerateEnemyStatCurves", GenerativeGameEconomy.HandleGenerateEnemyStatCurves },
            { "HandleCreateEconomyEventSimulator", GenerativeGameEconomy.HandleCreateEconomyEventSimulator },
            
            // World Simulation Systems
            { "HandleWorldSimulationSystems", WorldSimulationSystems.HandleCommand },
            
            // Behavior AI Generation handlers
            { "HandleBehaviorAIGeneration", BehaviorAIGeneration.HandleCommand },
            
            // Unity Cloud AI Integration handlers
            { "HandleUnityCloudAI", UnityCloudAI.HandleCommand },

            // MOBA AURACRON Specific Systems
            { "HandleDynamicRealmSystem", DynamicRealmSystem.HandleCommand },
            { "HandleChampionFusionSystem", ChampionFusionSystem.HandleCommand },
            { "HandleAIAdaptiveJungle", AIAdaptiveJungle.HandleCommand },
            { "HandleProceduralObjectives", ProceduralObjectives.HandleCommand },

            // HANDLERS FALTANDO - Adicionados para completar o registro
            { "HandleAIAssetGeneration", AIAssetGeneration.HandleCommand },
            { "HandleTerrainEnvironment", TerrainEnvironment.HandleCommand },
            { "HandlePerformanceProfiling", PerformanceProfiling.HandleCommand },
            { "HandlePhysicsSystem", PhysicsSystem.HandleCommand },
            { "HandleVfxParticles", VfxParticles.HandleCommand },
            { "HandleWaterSystemRuntime", WaterSystemRuntime.HandleCommand },
            { "HandleUIToolkitRuntime", UIToolkitRuntime.HandleCommand },
            { "HandleNavigationEnhanced", NavigationEnhanced.HandleCommand },
            { "HandleLightmapEdgeSmoothing", LightmapEdgeSmoothing.HandleCommand },
            { "HandleLightmapPerformanceAnalysis", LightmapPerformanceAnalysis.HandleCommand },
            { "HandleLightmapQualitySettings", LightmapQualitySettings.HandleCommand },
            { "HandleLightmapVisualizationTools", LightmapVisualizationTools.HandleCommand },
            { "HandleGraphicsStateCollection", GraphicsStateCollection.HandleCommand },
            { "HandleBicubicLightmapSampling", BicubicLightmapSampling.HandleCommand },
            { "HandleDeferredPlusRendering", DeferredPlusRendering.HandleCommand },
            { "HandleGenerativeGameEconomy", GenerativeGameEconomy.HandleCommand },
            { "HandleInferenceNeuralNetwork", InferenceNeuralNetwork.HandleCommand },
            { "HandleInputSystemRuntime", InputSystemRuntime.HandleCommand },
            { "HandleVariableRateShading", VariableRateShading.HandleCommand }
        };

        /// <summary>
        /// Gets a command handler by name.
        /// </summary>
        /// <param name="commandName">Name of the command handler (e.g., "HandleManageAsset").</param>
        /// <returns>The command handler function if found, null otherwise.</returns>
        public static Func<JObject, object> GetHandler(string commandName)
        {
            // Use case-insensitive comparison for flexibility, although Python side should be consistent
            return _handlers.TryGetValue(commandName, out var handler) ? handler : null;
            // Consider adding logging here if a handler is not found
            /*
            if (_handlers.TryGetValue(commandName, out var handler)) {
                return handler;
            } else {
                UnityEngine.Debug.LogError($\"[CommandRegistry] No handler found for command: {commandName}\");
                return null;
            }
            */
        }
    }
}

