using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.Audio;
using UnityMcpBridge.Editor.Helpers;

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// Handles audio system operations for Unity MCP Server.
    /// Based on Unity 6.2 Audio documentation.
    /// </summary>
    public static class AudioSystem
    {
        /// <summary>
        /// Main handler for audio system actions.
        /// </summary>
        public static object HandleCommand(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            try
            {
                switch (action)
                {
                    case "create_mixer":
                        return CreateAudioMixer(@params);
                    case "setup_spatial":
                        return SetupSpatialAudio(@params);
                    case "create_reverb_zone":
                        return CreateReverbZone(@params);
                    case "setup_occlusion":
                        return SetupAudioOcclusion(@params);
                    case "create_dynamic_music":
                        return CreateDynamicMusic(@params);
                    case "setup_filters":
                        return SetupAudioFilters(@params);
                    case "create_snapshots":
                        return CreateAudioSnapshots(@params);
                    case "setup_voice_chat":
                        return SetupVoiceChat(@params);
                    case "create_audio_triggers":
                        return CreateAudioTriggers(@params);
                    case "setup_streaming":
                        return SetupAudioStreaming(@params);
                    case "create_procedural_audio":
                        return CreateProceduralAudio(@params);
                    case "setup_compression":
                        return SetupAudioCompression(@params);
                    case "create_effects_chain":
                        return CreateAudioEffectsChain(@params);
                    case "setup_binaural":
                        return SetupBinauralAudio(@params);
                    case "create_visualization":
                        return CreateAudioVisualization(@params);
                    case "optimize_performance":
                        return OptimizeAudioPerformance(@params);
                    default:
                        return Response.Error($"Unknown action: {action}");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Error in AudioSystem.{action}: {e.Message}");
                return Response.Error($"Error executing {action}: {e.Message}");
            }
        }

        private static object CreateAudioMixer(JObject @params)
        {
            try
            {
                string mixerName = @params["mixer_name"]?.ToString();
                if (string.IsNullOrEmpty(mixerName))
                    return Response.Error("mixer_name is required.");

                // Check if mixer already exists
                string[] mixerGuids = AssetDatabase.FindAssets($"{mixerName} t:AudioMixer");
                AudioMixer mixer = null;
                
                if (mixerGuids.Length > 0)
                {
                    string assetPath = AssetDatabase.GUIDToAssetPath(mixerGuids[0]);
                    mixer = AssetDatabase.LoadAssetAtPath<AudioMixer>(assetPath);
                }
                
                // Create new AudioMixer if it doesn't exist
                if (mixer == null)
                {
                    // Ensure Assets/Audio directory exists
                    string audioDir = "Assets/Audio";
                    if (!AssetDatabase.IsValidFolder(audioDir))
                    {
                        AssetDatabase.CreateFolder("Assets", "Audio");
                    }
                    
                    // Create asset path
                    string assetPath = $"{audioDir}/{mixerName}.mixer";
                    
                    // Set the selection to the Audio folder so the mixer is created there
                    Selection.activeObject = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(audioDir);
                    
                    // Create AudioMixer using Unity's menu system
                    EditorApplication.ExecuteMenuItem("Assets/Create/Audio/Audio Mixer");
                    
                    // Find the newly created mixer and rename it
                    AssetDatabase.Refresh();
                    string[] guids = AssetDatabase.FindAssets("t:AudioMixer", new[] { audioDir });
                    if (guids.Length > 0)
                    {
                        string newMixerPath = AssetDatabase.GUIDToAssetPath(guids[guids.Length - 1]); // Get the last created one
                        mixer = AssetDatabase.LoadAssetAtPath<AudioMixer>(newMixerPath);
                        
                        // Rename the mixer if it's not already named correctly
                        if (mixer != null && !newMixerPath.EndsWith($"{mixerName}.mixer"))
                        {
                            AssetDatabase.RenameAsset(newMixerPath, mixerName);
                            AssetDatabase.Refresh();
                            mixer = AssetDatabase.LoadAssetAtPath<AudioMixer>(assetPath);
                        }
                    }
                    
                    AssetDatabase.SaveAssets();
                    AssetDatabase.Refresh();
                    
                    Debug.Log($"Created new AudioMixer: {assetPath}");
                }

                // Configure existing mixer if parameters provided
                var groupSettings = @params["group_settings"] as JObject;
                var snapshotSettings = @params["snapshot_settings"] as JObject;
                var effectSettings = @params["effect_settings"] as JObject;
                
                List<string> configurationsApplied = new List<string>();

                // Configure mixer groups if provided
                if (groupSettings != null)
                {
                    AudioMixerGroup[] groups = mixer.FindMatchingGroups("Master");
                    if (groups.Length > 0)
                    {
                        var masterGroup = groups[0];
                        
                        if (groupSettings["volume"] != null)
                        {
                            float volume = groupSettings["volume"].ToObject<float>();
                            mixer.SetFloat("MasterVolume", volume);
                            configurationsApplied.Add("Master Volume");
                        }
                        
                        if (groupSettings["pitch"] != null)
                        {
                            float pitch = groupSettings["pitch"].ToObject<float>();
                            mixer.SetFloat("MasterPitch", pitch);
                            configurationsApplied.Add("Master Pitch");
                        }
                    }
                }

                // Configure snapshots if provided
                if (snapshotSettings != null)
                {
                    string path = AssetDatabase.GetAssetPath(mixer);
                    AudioMixerSnapshot[] snapshots = AssetDatabase.LoadAllAssetsAtPath(path)
                        .OfType<AudioMixerSnapshot>()
                        .ToArray();
                    if (snapshots.Length > 0)
                    {
                        var defaultSnapshot = snapshots[0];
                        defaultSnapshot.TransitionTo(0.1f);
                        configurationsApplied.Add("Default Snapshot");
                    }
                }

                EditorUtility.SetDirty(mixer);
                AssetDatabase.SaveAssets();

                return Response.Success($"AudioMixer '{mixerName}' configured successfully.", new {
                    mixer_name = mixerName,
                    configurations_applied = configurationsApplied.ToArray(),
                    total_configurations = configurationsApplied.Count
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error configuring audio mixer: {e.Message}");
            }
        }

        private static object SetupSpatialAudio(JObject @params)
        {
            try
            {
                string gameObjectName = @params["gameobject_name"]?.ToString();
                if (string.IsNullOrEmpty(gameObjectName))
                    return Response.Error("gameobject_name is required.");

                GameObject targetObj = GameObject.Find(gameObjectName);
                if (targetObj == null)
                {
                    targetObj = new GameObject(gameObjectName);
                }

                // Get or add AudioSource component
                AudioSource audioSource = targetObj.GetComponent<AudioSource>();
                if (audioSource == null)
                {
                    audioSource = targetObj.AddComponent<AudioSource>();
                }

                // Configure 3D audio settings based on Unity 6.2 AudioSource
                audioSource.spatialBlend = 1.0f; // Full 3D
                audioSource.dopplerLevel = @params["doppler_level"]?.ToObject<float>() ?? 1.0f;
                audioSource.spread = @params["spread"]?.ToObject<float>() ?? 0.0f;
                audioSource.minDistance = @params["min_distance"]?.ToObject<float>() ?? 1.0f;
                audioSource.maxDistance = @params["max_distance"]?.ToObject<float>() ?? 500.0f;

                // Set rolloff mode
                string rolloffMode = @params["rolloff_mode"]?.ToString() ?? "Logarithmic";
                switch (rolloffMode.ToLower())
                {
                    case "linear":
                        audioSource.rolloffMode = AudioRolloffMode.Linear;
                        break;
                    case "logarithmic":
                        audioSource.rolloffMode = AudioRolloffMode.Logarithmic;
                        break;
                    case "custom":
                        audioSource.rolloffMode = AudioRolloffMode.Custom;
                        break;
                }

                // Apply additional settings if provided
                var audioSettings = @params["audio_source_settings"] as JObject;
                if (audioSettings != null)
                {
                    if (audioSettings["volume"] != null)
                        audioSource.volume = audioSettings["volume"].ToObject<float>();
                    if (audioSettings["pitch"] != null)
                        audioSource.pitch = audioSettings["pitch"].ToObject<float>();
                    if (audioSettings["loop"] != null)
                        audioSource.loop = audioSettings["loop"].ToObject<bool>();
                    if (audioSettings["priority"] != null)
                        audioSource.priority = audioSettings["priority"].ToObject<int>();
                }

                EditorUtility.SetDirty(targetObj);

                return Response.Success($"Spatial audio setup for '{gameObjectName}' completed.", new {
                    spatial_blend = audioSource.spatialBlend,
                    doppler_level = audioSource.dopplerLevel,
                    rolloff_mode = audioSource.rolloffMode.ToString(),
                    min_distance = audioSource.minDistance,
                    max_distance = audioSource.maxDistance
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error setting up spatial audio: {e.Message}");
            }
        }

        private static object CreateReverbZone(JObject @params)
        {
            try
            {
                string zoneName = @params["zone_name"]?.ToString();
                if (string.IsNullOrEmpty(zoneName))
                    return Response.Error("zone_name is required.");

                GameObject reverbZoneObj = new GameObject(zoneName);
                AudioReverbZone reverbZone = reverbZoneObj.AddComponent<AudioReverbZone>();

                // Set reverb preset based on Unity 6.2 AudioReverbPreset
                string presetName = @params["reverb_preset"]?.ToString() ?? "Generic";
                if (Enum.TryParse(presetName, out AudioReverbPreset preset))
                {
                    reverbZone.reverbPreset = preset;
                }

                // Configure reverb zone properties
                reverbZone.room = (int)(@params["room_size"]?.ToObject<float>() ?? 10.0f);
                reverbZone.reverbDelay = @params["reverb_delay"]?.ToObject<float>() ?? 0.04f;
                reverbZone.HFReference = @params["hf_reference"]?.ToObject<float>() ?? 5000.0f;
                reverbZone.minDistance = @params["min_distance"]?.ToObject<float>() ?? 10.0f;
                reverbZone.maxDistance = @params["max_distance"]?.ToObject<float>() ?? 15.0f;

                // Add sphere collider for zone trigger
                SphereCollider collider = reverbZoneObj.AddComponent<SphereCollider>();
                collider.isTrigger = true;
                collider.radius = reverbZone.maxDistance;

                return Response.Success($"Reverb zone '{zoneName}' created successfully.", new {
                    zone_name = zoneName,
                    preset = reverbZone.reverbPreset.ToString(),
                    min_distance = reverbZone.minDistance,
                    max_distance = reverbZone.maxDistance
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error creating reverb zone: {e.Message}");
            }
        }

        private static object SetupAudioOcclusion(JObject @params)
        {
            try
            {
                string listenerName = @params["listener_name"]?.ToString() ?? "Main Camera";
                GameObject listenerObj = GameObject.Find(listenerName);
                
                if (listenerObj == null)
                    return Response.Error($"AudioListener GameObject '{listenerName}' not found.");

                // Get or add AudioListener component
                AudioListener audioListener = listenerObj.GetComponent<AudioListener>();
                if (audioListener == null)
                {
                    audioListener = listenerObj.AddComponent<AudioListener>();
                }

                // Create Audio Occlusion Manager GameObject
                string occlusionManagerName = @params["occlusion_manager_name"]?.ToString() ?? "AudioOcclusionManager";
                GameObject occlusionManager = GameObject.Find(occlusionManagerName);
                if (occlusionManager == null)
                {
                    occlusionManager = new GameObject(occlusionManagerName);
                }

                // Add the occlusion script component
                var occlusionScript = occlusionManager.GetComponent<MonoBehaviour>();
                if (occlusionScript == null)
                {
                    // Create AudioOcclusionSystem script content
                    string scriptContent = CreateAudioOcclusionScript();
                    string scriptPath = "Assets/Scripts/AudioOcclusionSystem.cs";
                    
                    // Ensure Scripts directory exists
                    string scriptsDir = "Assets/Scripts";
                    if (!AssetDatabase.IsValidFolder(scriptsDir))
                    {
                        AssetDatabase.CreateFolder("Assets", "Scripts");
                    }
                    
                    // Write the script file
                    System.IO.File.WriteAllText(scriptPath, scriptContent);
                    AssetDatabase.ImportAsset(scriptPath);
                    AssetDatabase.Refresh();
                }

                // Configure occlusion parameters
                var occlusionLayers = @params["occlusion_layers"]?.ToObject<string[]>();
                float raycastFrequency = @params["raycast_frequency"]?.ToObject<float>() ?? 0.1f;
                float occlusionIntensity = @params["occlusion_intensity"]?.ToObject<float>() ?? 0.5f;
                float smoothTime = @params["smooth_time"]?.ToObject<float>() ?? 0.1f;
                float maxDistance = @params["max_occlusion_distance"]?.ToObject<float>() ?? 100f;
                bool useReverbZones = @params["use_reverb_zones"]?.ToObject<bool>() ?? true;

                // Get all AudioSources in the scene to apply occlusion
                AudioSource[] audioSources = UnityEngine.Object.FindObjectsByType<AudioSource>(FindObjectsSortMode.None);
                int configuredSources = 0;

                foreach (AudioSource audioSource in audioSources)
                {
                    if (audioSource.gameObject == listenerObj) continue; // Skip listener
                    
                    // Configure AudioSource for occlusion
                    audioSource.spatialBlend = 1.0f; // Make it 3D
                    audioSource.rolloffMode = AudioRolloffMode.Linear;
                    audioSource.maxDistance = maxDistance;
                    
                    // Add AudioLowPassFilter for occlusion effect
                    AudioLowPassFilter lowPassFilter = audioSource.GetComponent<AudioLowPassFilter>();
                    if (lowPassFilter == null)
                    {
                        lowPassFilter = audioSource.gameObject.AddComponent<AudioLowPassFilter>();
                    }
                    lowPassFilter.cutoffFrequency = 22000f; // Start unoccluded
                    
                    // Add AudioEchoFilter for distance/occlusion reverb
                    if (useReverbZones)
                    {
                        AudioReverbFilter reverbFilter = audioSource.GetComponent<AudioReverbFilter>();
                        if (reverbFilter == null)
                        {
                            reverbFilter = audioSource.gameObject.AddComponent<AudioReverbFilter>();
                        }
                        reverbFilter.reverbPreset = AudioReverbPreset.Generic;
                        reverbFilter.dryLevel = 0;
                        reverbFilter.room = -1000;
                    }
                    
                    configuredSources++;
                }

                // Configure layer mask for occlusion
                LayerMask occlusionLayerMask = 0;
                if (occlusionLayers != null)
                {
                    foreach (string layerName in occlusionLayers)
                    {
                        int layer = LayerMask.NameToLayer(layerName);
                        if (layer != -1)
                        {
                            occlusionLayerMask |= (1 << layer);
                        }
                    }
                }
                else
                {
                    // Default to walls/obstacles layers
                    occlusionLayerMask = LayerMask.GetMask("Default", "Walls", "Obstacles");
                }

                // Store occlusion settings in PlayerPrefs for runtime access
                PlayerPrefs.SetFloat("AudioOcclusion.RaycastFrequency", raycastFrequency);
                PlayerPrefs.SetFloat("AudioOcclusion.OcclusionIntensity", occlusionIntensity);
                PlayerPrefs.SetFloat("AudioOcclusion.SmoothTime", smoothTime);
                PlayerPrefs.SetFloat("AudioOcclusion.MaxDistance", maxDistance);
                PlayerPrefs.SetInt("AudioOcclusion.LayerMask", occlusionLayerMask);
                PlayerPrefs.SetInt("AudioOcclusion.UseReverbZones", useReverbZones ? 1 : 0);

                EditorUtility.SetDirty(occlusionManager);

                return Response.Success($"Audio occlusion system setup for '{listenerName}' completed.", new {
                    listener = listenerName,
                    occlusion_manager = occlusionManagerName,
                    configured_audio_sources = configuredSources,
                    raycast_frequency = raycastFrequency,
                    occlusion_intensity = occlusionIntensity,
                    smooth_time = smoothTime,
                    max_distance = maxDistance,
                    occlusion_layers = occlusionLayers?.Length ?? 0,
                    use_reverb_zones = useReverbZones
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error setting up audio occlusion: {e.Message}");
            }
        }
        
        private static string CreateAudioOcclusionScript()
        {
            return @"using UnityEngine;

public class AudioOcclusionSystem : MonoBehaviour
{
    [Header(""Occlusion Settings"")]
    public float raycastFrequency = 0.1f;
    public float occlusionIntensity = 0.5f;
    public float smoothTime = 0.1f;
    public float maxDistance = 100f;
    public LayerMask occlusionLayers = -1;
    public bool useReverbZones = true;
    
    private AudioListener audioListener;
    private AudioSource[] audioSources;
    private float[] originalCutoffs;
    private float[] targetCutoffs;
    private float[] currentCutoffs;
    private float[] cutoffVelocities;
    
    void Start()
    {
        // Load settings from PlayerPrefs
        raycastFrequency = PlayerPrefs.GetFloat(""AudioOcclusion.RaycastFrequency"", 0.1f);
        occlusionIntensity = PlayerPrefs.GetFloat(""AudioOcclusion.OcclusionIntensity"", 0.5f);
        smoothTime = PlayerPrefs.GetFloat(""AudioOcclusion.SmoothTime"", 0.1f);
        maxDistance = PlayerPrefs.GetFloat(""AudioOcclusion.MaxDistance"", 100f);
        occlusionLayers = PlayerPrefs.GetInt(""AudioOcclusion.LayerMask"", -1);
        useReverbZones = PlayerPrefs.GetInt(""AudioOcclusion.UseReverbZones"", 1) == 1;
        
        audioListener = UnityEngine.Object.FindFirstObjectByType<AudioListener>();
        if (audioListener == null)
        {
            Debug.LogError(""AudioOcclusionSystem: No AudioListener found in scene!"");
            enabled = false;
            return;
        }
        
        RefreshAudioSources();
        InvokeRepeating(nameof(UpdateOcclusion), 0f, raycastFrequency);
    }
    
    void RefreshAudioSources()
    {
        audioSources = FindObjectsByType<AudioSource>(FindObjectsSortMode.None);
        originalCutoffs = new float[audioSources.Length];
        targetCutoffs = new float[audioSources.Length];
        currentCutoffs = new float[audioSources.Length];
        cutoffVelocities = new float[audioSources.Length];
        
        for (int i = 0; i < audioSources.Length; i++)
        {
            AudioLowPassFilter filter = audioSources[i].GetComponent<AudioLowPassFilter>();
            if (filter != null)
            {
                originalCutoffs[i] = filter.cutoffFrequency;
                currentCutoffs[i] = filter.cutoffFrequency;
                targetCutoffs[i] = filter.cutoffFrequency;
            }
            else
            {
                originalCutoffs[i] = 22000f;
                currentCutoffs[i] = 22000f;
                targetCutoffs[i] = 22000f;
            }
        }
    }
    
    void UpdateOcclusion()
    {
        if (audioListener == null) return;
        
        Vector3 listenerPos = audioListener.transform.position;
        
        for (int i = 0; i < audioSources.Length; i++)
        {
            if (audioSources[i] == null || audioSources[i].gameObject == audioListener.gameObject) 
                continue;
                
            Vector3 sourcePos = audioSources[i].transform.position;
            float distance = Vector3.Distance(listenerPos, sourcePos);
            
            if (distance > maxDistance)
            {
                targetCutoffs[i] = originalCutoffs[i] * 0.1f; // Heavily filtered
                continue;
            }
            
            Vector3 direction = (sourcePos - listenerPos).normalized;
            RaycastHit hit;
            
            bool isOccluded = Physics.Raycast(listenerPos, direction, out hit, distance, occlusionLayers);
            
            if (isOccluded)
            {
                float occlusionAmount = 1f - (hit.distance / distance);
                float cutoffReduction = Mathf.Lerp(1f, 0.1f, occlusionAmount * occlusionIntensity);
                targetCutoffs[i] = originalCutoffs[i] * cutoffReduction;
                
                // Apply reverb for occluded sounds
                if (useReverbZones)
                {
                    AudioReverbFilter reverbFilter = audioSources[i].GetComponent<AudioReverbFilter>();
                    if (reverbFilter != null)
                    {
                        reverbFilter.room = Mathf.RoundToInt(-1000 + (900 * occlusionAmount));
                    }
                }
            }
            else
            {
                targetCutoffs[i] = originalCutoffs[i];
                
                if (useReverbZones)
                {
                    AudioReverbFilter reverbFilter = audioSources[i].GetComponent<AudioReverbFilter>();
                    if (reverbFilter != null)
                    {
                        reverbFilter.room = -1000; // No reverb
                    }
                }
            }
        }
    }
    
    void Update()
    {
        // Smooth the cutoff transitions
        for (int i = 0; i < audioSources.Length; i++)
        {
            if (audioSources[i] == null) continue;
            
            currentCutoffs[i] = Mathf.SmoothDamp(currentCutoffs[i], targetCutoffs[i], 
                ref cutoffVelocities[i], smoothTime);
            
            AudioLowPassFilter filter = audioSources[i].GetComponent<AudioLowPassFilter>();
            if (filter != null)
            {
                filter.cutoffFrequency = currentCutoffs[i];
            }
        }
    }
    
    void OnDrawGizmos()
    {
        if (audioListener == null) return;
        
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(audioListener.transform.position, maxDistance);
        
        if (audioSources != null)
        {
            Vector3 listenerPos = audioListener.transform.position;
            
            for (int i = 0; i < audioSources.Length; i++)
            {
                if (audioSources[i] == null || audioSources[i].gameObject == audioListener.gameObject) 
                    continue;
                    
                Vector3 sourcePos = audioSources[i].transform.position;
                float distance = Vector3.Distance(listenerPos, sourcePos);
                
                if (distance <= maxDistance)
                {
                    Vector3 direction = (sourcePos - listenerPos).normalized;
                    RaycastHit hit;
                    
                    bool isOccluded = Physics.Raycast(listenerPos, direction, out hit, distance, occlusionLayers);
                    
                    Gizmos.color = isOccluded ? Color.red : Color.green;
                    Gizmos.DrawLine(listenerPos, sourcePos);
                    
                    if (isOccluded)
                    {
                        Gizmos.color = Color.red;
                        Gizmos.DrawSphere(hit.point, 0.1f);
                    }
                }
            }
        }
    }
}";
        }

        private static object CreateDynamicMusic(JObject @params)
        {
            try
            {
                string managerName = @params["music_manager_name"]?.ToString();
                if (string.IsNullOrEmpty(managerName))
                    return Response.Error("music_manager_name is required.");

                GameObject musicManager = new GameObject(managerName);
                
                // Add multiple AudioSource components for crossfading
                int maxSources = @params["max_concurrent_tracks"]?.ToObject<int>() ?? 4;
                AudioSource[] audioSources = new AudioSource[maxSources];
                for (int i = 0; i < maxSources; i++)
                {
                    audioSources[i] = musicManager.AddComponent<AudioSource>();
                    audioSources[i].loop = @params["loop_tracks"]?.ToObject<bool>() ?? true;
                    audioSources[i].volume = 0f; // Start muted
                    audioSources[i].playOnAwake = false;
                    audioSources[i].spatialBlend = 0f; // 2D audio for music
                    audioSources[i].priority = 64; // Medium priority
                }

                var musicTracks = @params["music_tracks"]?.ToObject<JObject[]>();
                string transitionType = @params["transition_type"]?.ToString() ?? "CrossFade";
                float fadeDuration = @params["fade_duration"]?.ToObject<float>() ?? 2.0f;
                bool useAudioMixer = @params["use_audio_mixer"]?.ToObject<bool>() ?? true;
                string mixerGroupName = @params["mixer_group_name"]?.ToString() ?? "Music";

                // Setup AudioMixer output if specified
                if (useAudioMixer && !string.IsNullOrEmpty(mixerGroupName))
                {
                    string[] mixerGuids = AssetDatabase.FindAssets("t:AudioMixer");
                    if (mixerGuids.Length > 0)
                    {
                        string mixerPath = AssetDatabase.GUIDToAssetPath(mixerGuids[0]);
                        AudioMixer mixer = AssetDatabase.LoadAssetAtPath<AudioMixer>(mixerPath);
                        
                        if (mixer != null)
                        {
                            AudioMixerGroup[] groups = mixer.FindMatchingGroups(mixerGroupName);
                            if (groups.Length > 0)
                            {
                                foreach (AudioSource source in audioSources)
                                {
                                    source.outputAudioMixerGroup = groups[0];
                                }
                            }
                        }
                    }
                }

                // Load and assign music tracks
                List<AudioClip> loadedTracks = new List<AudioClip>();
                if (musicTracks != null)
                {
                    foreach (var trackData in musicTracks)
                    {
                        string trackPath = trackData["path"]?.ToString();
                        if (!string.IsNullOrEmpty(trackPath))
                        {
                            AudioClip clip = AssetDatabase.LoadAssetAtPath<AudioClip>(trackPath);
                            if (clip != null)
                            {
                                loadedTracks.Add(clip);
                                
                                // Configure track-specific settings
                                if (trackData["volume"] != null)
                                {
                                    float volume = trackData["volume"].ToObject<float>();
                                    // Store per-track volume settings
                                    PlayerPrefs.SetFloat($"DynamicMusic.{managerName}.{clip.name}.Volume", volume);
                                }
                                
                                if (trackData["loop"] != null)
                                {
                                    bool loop = trackData["loop"].ToObject<bool>();
                                    PlayerPrefs.SetInt($"DynamicMusic.{managerName}.{clip.name}.Loop", loop ? 1 : 0);
                                }
                                
                                if (trackData["crossfade_points"] != null)
                                {
                                    var crossfadePoints = trackData["crossfade_points"].ToObject<float[]>();
                                    if (crossfadePoints.Length >= 2)
                                    {
                                        PlayerPrefs.SetFloat($"DynamicMusic.{managerName}.{clip.name}.FadeInPoint", crossfadePoints[0]);
                                        PlayerPrefs.SetFloat($"DynamicMusic.{managerName}.{clip.name}.FadeOutPoint", crossfadePoints[1]);
                                    }
                                }
                            }
                        }
                    }
                }

                // Assign first track to first AudioSource if available
                if (loadedTracks.Count > 0)
                {
                    audioSources[0].clip = loadedTracks[0];
                }

                // Create dynamic music controller script
                string controllerScript = CreateDynamicMusicControllerScript();
                string scriptPath = "Assets/Scripts/DynamicMusicController.cs";
                
                // Ensure Scripts directory exists
                string scriptsDir = "Assets/Scripts";
                if (!AssetDatabase.IsValidFolder(scriptsDir))
                {
                    AssetDatabase.CreateFolder("Assets", "Scripts");
                }
                
                // Write the script file
                System.IO.File.WriteAllText(scriptPath, controllerScript);
                AssetDatabase.ImportAsset(scriptPath);
                AssetDatabase.Refresh();

                // Store configuration in PlayerPrefs for runtime access
                PlayerPrefs.SetString($"DynamicMusic.{managerName}.TransitionType", transitionType);
                PlayerPrefs.SetFloat($"DynamicMusic.{managerName}.FadeDuration", fadeDuration);
                PlayerPrefs.SetInt($"DynamicMusic.{managerName}.MaxSources", maxSources);
                PlayerPrefs.SetInt($"DynamicMusic.{managerName}.UseAudioMixer", useAudioMixer ? 1 : 0);
                PlayerPrefs.SetString($"DynamicMusic.{managerName}.MixerGroupName", mixerGroupName);

                // Store track information
                for (int i = 0; i < loadedTracks.Count; i++)
                {
                    PlayerPrefs.SetString($"DynamicMusic.{managerName}.Track{i}", loadedTracks[i].name);
                }
                PlayerPrefs.SetInt($"DynamicMusic.{managerName}.TrackCount", loadedTracks.Count);

                EditorUtility.SetDirty(musicManager);

                return Response.Success($"Dynamic music system '{managerName}' created successfully.", new {
                    manager_name = managerName,
                    tracks_count = loadedTracks.Count,
                    tracks_loaded = loadedTracks.Select(t => t.name).ToArray(),
                    transition_type = transitionType,
                    fade_duration = fadeDuration,
                    audio_sources = audioSources.Length,
                    max_concurrent_tracks = maxSources,
                    use_audio_mixer = useAudioMixer,
                    mixer_group = mixerGroupName
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error creating dynamic music system: {e.Message}");
            }
        }
        
        private static string CreateDynamicMusicControllerScript()
        {
            return @"using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Audio;

public class DynamicMusicController : MonoBehaviour
{
    [Header(""Dynamic Music Settings"")]
    public float fadeDuration = 2.0f;
    public string transitionType = ""CrossFade"";
    public bool autoPlayOnStart = true;
    public int maxConcurrentTracks = 4;
    
    [Header(""Runtime Control"")]
    public bool isPlaying = false;
    public int currentTrackIndex = 0;
    public float masterVolume = 1.0f;
    
    private AudioSource[] audioSources;
    private List<AudioClip> musicTracks = new List<AudioClip>();
    private Dictionary<string, float> trackVolumes = new Dictionary<string, float>();
    private Dictionary<string, bool> trackLoopSettings = new Dictionary<string, bool>();
    private Dictionary<string, Vector2> trackCrossfadePoints = new Dictionary<string, Vector2>();
    
    private int activeSourceIndex = 0;
    private bool isTransitioning = false;
    private string managerName;
    
    void Start()
    {
        managerName = gameObject.name;
        LoadConfiguration();
        InitializeAudioSources();
        LoadMusicTracks();
        
        if (autoPlayOnStart && musicTracks.Count > 0)
        {
            PlayTrack(0);
        }
    }
    
    void LoadConfiguration()
    {
        transitionType = PlayerPrefs.GetString($""DynamicMusic.{managerName}.TransitionType"", ""CrossFade"");
        fadeDuration = PlayerPrefs.GetFloat($""DynamicMusic.{managerName}.FadeDuration"", 2.0f);
        maxConcurrentTracks = PlayerPrefs.GetInt($""DynamicMusic.{managerName}.MaxSources"", 4);
    }
    
    void InitializeAudioSources()
    {
        audioSources = GetComponents<AudioSource>();
        
        foreach (AudioSource source in audioSources)
        {
            source.volume = 0f;
            source.playOnAwake = false;
        }
    }
    
    void LoadMusicTracks()
    {
        int trackCount = PlayerPrefs.GetInt($""DynamicMusic.{managerName}.TrackCount"", 0);
        
        for (int i = 0; i < trackCount; i++)
        {
            string trackName = PlayerPrefs.GetString($""DynamicMusic.{managerName}.Track{i}"", """");
            if (!string.IsNullOrEmpty(trackName))
            {
                // Find the AudioClip by name (simplified approach)
                AudioClip[] allClips = Resources.FindObjectsOfTypeAll<AudioClip>();
                AudioClip foundClip = System.Array.Find(allClips, clip => clip.name == trackName);
                
                if (foundClip != null)
                {
                    musicTracks.Add(foundClip);
                    
                    // Load track-specific settings
                    float volume = PlayerPrefs.GetFloat($""DynamicMusic.{managerName}.{trackName}.Volume"", 1.0f);
                    bool loop = PlayerPrefs.GetInt($""DynamicMusic.{managerName}.{trackName}.Loop"", 1) == 1;
                    float fadeInPoint = PlayerPrefs.GetFloat($""DynamicMusic.{managerName}.{trackName}.FadeInPoint"", 0f);
                    float fadeOutPoint = PlayerPrefs.GetFloat($""DynamicMusic.{managerName}.{trackName}.FadeOutPoint"", 1f);
                    
                    trackVolumes[trackName] = volume;
                    trackLoopSettings[trackName] = loop;
                    trackCrossfadePoints[trackName] = new Vector2(fadeInPoint, fadeOutPoint);
                }
            }
        }
    }
    
    public void PlayTrack(int trackIndex)
    {
        if (trackIndex < 0 || trackIndex >= musicTracks.Count) return;
        
        if (isTransitioning) return;
        
        AudioClip targetClip = musicTracks[trackIndex];
        
        if (isPlaying && transitionType == ""CrossFade"")
        {
            StartCoroutine(CrossFadeToTrack(trackIndex));
        }
        else
        {
            DirectPlayTrack(trackIndex);
        }
    }
    
    void DirectPlayTrack(int trackIndex)
    {
        StopAllTracks();
        
        AudioClip clip = musicTracks[trackIndex];
        AudioSource targetSource = audioSources[activeSourceIndex];
        
        targetSource.clip = clip;
        targetSource.loop = trackLoopSettings.ContainsKey(clip.name) ? trackLoopSettings[clip.name] : true;
        
        float targetVolume = trackVolumes.ContainsKey(clip.name) ? trackVolumes[clip.name] : 1.0f;
        targetSource.volume = targetVolume * masterVolume;
        
        targetSource.Play();
        
        currentTrackIndex = trackIndex;
        isPlaying = true;
    }
    
    IEnumerator CrossFadeToTrack(int trackIndex)
    {
        isTransitioning = true;
        
        AudioClip newClip = musicTracks[trackIndex];
        AudioSource currentSource = audioSources[activeSourceIndex];
        AudioSource newSource = audioSources[(activeSourceIndex + 1) % audioSources.Length];
        
        // Setup new source
        newSource.clip = newClip;
        newSource.loop = trackLoopSettings.ContainsKey(newClip.name) ? trackLoopSettings[newClip.name] : true;
        newSource.volume = 0f;
        newSource.Play();
        
        // Get target volumes
        float currentTargetVolume = trackVolumes.ContainsKey(currentSource.clip.name) ? 
            trackVolumes[currentSource.clip.name] : 1.0f;
        float newTargetVolume = trackVolumes.ContainsKey(newClip.name) ? 
            trackVolumes[newClip.name] : 1.0f;
        
        // Crossfade
        float elapsedTime = 0f;
        float currentStartVolume = currentSource.volume;
        
        while (elapsedTime < fadeDuration)
        {
            elapsedTime += Time.deltaTime;
            float t = elapsedTime / fadeDuration;
            
            currentSource.volume = Mathf.Lerp(currentStartVolume, 0f, t);
            newSource.volume = Mathf.Lerp(0f, newTargetVolume * masterVolume, t);
            
            yield return null;
        }
        
        // Finalize transition
        currentSource.Stop();
        currentSource.volume = 0f;
        newSource.volume = newTargetVolume * masterVolume;
        
        activeSourceIndex = (activeSourceIndex + 1) % audioSources.Length;
        currentTrackIndex = trackIndex;
        isTransitioning = false;
    }
    
    public void StopAllTracks()
    {
        foreach (AudioSource source in audioSources)
        {
            source.Stop();
            source.volume = 0f;
        }
        isPlaying = false;
    }
    
    public void PauseMusic()
    {
        foreach (AudioSource source in audioSources)
        {
            if (source.isPlaying)
            {
                source.Pause();
            }
        }
    }
    
    public void ResumeMusic()
    {
        foreach (AudioSource source in audioSources)
        {
            if (source.clip != null && !source.isPlaying)
            {
                source.UnPause();
            }
        }
    }
    
    public void SetMasterVolume(float volume)
    {
        masterVolume = Mathf.Clamp01(volume);
        
        foreach (AudioSource source in audioSources)
        {
            if (source.isPlaying && source.clip != null)
            {
                float trackVolume = trackVolumes.ContainsKey(source.clip.name) ? 
                    trackVolumes[source.clip.name] : 1.0f;
                source.volume = trackVolume * masterVolume;
            }
        }
    }
    
    public void NextTrack()
    {
        int nextIndex = (currentTrackIndex + 1) % musicTracks.Count;
        PlayTrack(nextIndex);
    }
    
    public void PreviousTrack()
    {
        int prevIndex = (currentTrackIndex - 1 + musicTracks.Count) % musicTracks.Count;
        PlayTrack(prevIndex);
    }
    
    public void PlayRandomTrack()
    {
        if (musicTracks.Count > 1)
        {
            int randomIndex;
            do
            {
                randomIndex = Random.Range(0, musicTracks.Count);
            } while (randomIndex == currentTrackIndex);
            
            PlayTrack(randomIndex);
        }
    }
    
    // Public API for external control
    public int GetCurrentTrackIndex() => currentTrackIndex;
    public int GetTrackCount() => musicTracks.Count;
    public string GetCurrentTrackName() => musicTracks.Count > currentTrackIndex ? musicTracks[currentTrackIndex].name : """";
    public bool IsPlaying() => isPlaying;
    public bool IsTransitioning() => isTransitioning;
}";
        }

        private static object SetupAudioFilters(JObject @params)
        {
            try
            {
                string gameObjectName = @params["gameobject_name"]?.ToString();
                if (string.IsNullOrEmpty(gameObjectName))
                    return Response.Error("gameobject_name is required.");

                GameObject targetObj = GameObject.Find(gameObjectName);
                if (targetObj == null)
                    return Response.Error($"GameObject '{gameObjectName}' not found.");

                var filterTypes = @params["filter_types"]?.ToObject<string[]>();
                if (filterTypes == null || filterTypes.Length == 0)
                    return Response.Error("filter_types array is required.");

                var filterSettings = @params["filter_settings"] as JObject;
                List<string> addedFilters = new List<string>();

                foreach (string filterType in filterTypes)
                {
                    Component filter = null;
                    
                    switch (filterType)
                    {
                        case "AudioLowPassFilter":
                            filter = targetObj.AddComponent<AudioLowPassFilter>();
                            if (filterSettings?["cutoff_frequency"] != null)
                                ((AudioLowPassFilter)filter).cutoffFrequency = filterSettings["cutoff_frequency"].ToObject<float>();
                            if (filterSettings?["resonance_q"] != null)
                                ((AudioLowPassFilter)filter).lowpassResonanceQ = filterSettings["resonance_q"].ToObject<float>();
                            break;
                            
                        case "AudioHighPassFilter":
                            filter = targetObj.AddComponent<AudioHighPassFilter>();
                            if (filterSettings?["cutoff_frequency"] != null)
                                ((AudioHighPassFilter)filter).cutoffFrequency = filterSettings["cutoff_frequency"].ToObject<float>();
                            if (filterSettings?["resonance_q"] != null)
                                ((AudioHighPassFilter)filter).highpassResonanceQ = filterSettings["resonance_q"].ToObject<float>();
                            break;
                            
                        case "AudioEchoFilter":
                            filter = targetObj.AddComponent<AudioEchoFilter>();
                            if (filterSettings?["delay"] != null)
                                ((AudioEchoFilter)filter).delay = filterSettings["delay"].ToObject<float>();
                            if (filterSettings?["decay_ratio"] != null)
                                ((AudioEchoFilter)filter).decayRatio = filterSettings["decay_ratio"].ToObject<float>();
                            break;
                            
                        case "AudioDistortionFilter":
                            filter = targetObj.AddComponent<AudioDistortionFilter>();
                            if (filterSettings?["distortion_level"] != null)
                                ((AudioDistortionFilter)filter).distortionLevel = filterSettings["distortion_level"].ToObject<float>();
                            break;
                            
                        case "AudioReverbFilter":
                            filter = targetObj.AddComponent<AudioReverbFilter>();
                            if (filterSettings?["reverb_preset"] != null && 
                                Enum.TryParse(filterSettings["reverb_preset"].ToString(), out AudioReverbPreset preset))
                                ((AudioReverbFilter)filter).reverbPreset = preset;
                            break;
                            
                        case "AudioChorusFilter":
                            filter = targetObj.AddComponent<AudioChorusFilter>();
                            if (filterSettings?["dry_mix"] != null)
                                ((AudioChorusFilter)filter).dryMix = filterSettings["dry_mix"].ToObject<float>();
                            if (filterSettings?["wet_mix1"] != null)
                                ((AudioChorusFilter)filter).wetMix1 = filterSettings["wet_mix1"].ToObject<float>();
                            break;
                    }

                    if (filter != null)
                    {
                        addedFilters.Add(filterType);
                    }
                }

                EditorUtility.SetDirty(targetObj);

                return Response.Success($"Audio filters setup for '{gameObjectName}' completed.", new {
                    gameobject = gameObjectName,
                    filters_added = addedFilters.ToArray(),
                    total_filters = addedFilters.Count
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error setting up audio filters: {e.Message}");
            }
        }

        private static object CreateAudioSnapshots(JObject @params)
        {
            try
            {
                string mixerName = @params["mixer_name"]?.ToString();
                if (string.IsNullOrEmpty(mixerName))
                    return Response.Error("mixer_name is required.");

                // Find existing AudioMixer or create one
                string[] guids = AssetDatabase.FindAssets($"{mixerName} t:AudioMixer");
                AudioMixer mixer = null;
                string assetPath = "";
                
                if (guids.Length == 0)
                {
                    // Create new AudioMixer if it doesn't exist
                    string audioDir = "Assets/Audio";
                    if (!AssetDatabase.IsValidFolder(audioDir))
                    {
                        AssetDatabase.CreateFolder("Assets", "Audio");
                    }
                    
                    assetPath = $"{audioDir}/{mixerName}.mixer";
                    // Create AudioMixer using the proper Unity API
                    var tempGO = new GameObject("TempMixer");
                    var tempMixer = tempGO.AddComponent<AudioSource>();
                    mixer = AssetDatabase.LoadAssetAtPath<AudioMixer>("Assets/Audio/DefaultMixer.mixer");
                    if (mixer == null)
                    {
                        // Create advanced Unity 6.2 AudioMixer asset file
                        System.IO.File.WriteAllText(assetPath, "%YAML 1.1\n%TAG !u! tag:unity3d.com,2011:\n--- !u!241 &24100000\nAudioMixerController:\n  m_ObjectHideFlags: 0\n  m_CorrespondingSourceObject: {fileID: 0}\n  m_PrefabInstance: {fileID: 0}\n  m_PrefabAsset: {fileID: 0}\n  m_GameObject: {fileID: 0}\n  m_Enabled: 1\n  m_Name: " + mixerName + "\n");
                        AssetDatabase.Refresh();
                        mixer = AssetDatabase.LoadAssetAtPath<AudioMixer>(assetPath);
                    }
                    UnityEngine.Object.DestroyImmediate(tempGO);
                    AssetDatabase.SaveAssets();
                    AssetDatabase.Refresh();
                }
                else
                {
                    assetPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                    mixer = AssetDatabase.LoadAssetAtPath<AudioMixer>(assetPath);
                }

                var snapshots = @params["snapshots"]?.ToObject<JObject[]>();
                if (snapshots == null || snapshots.Length == 0)
                    return Response.Error("snapshots array is required.");

                List<string> createdSnapshots = new List<string>();
                List<AudioMixerSnapshot> snapshotObjects = new List<AudioMixerSnapshot>();

                // Get existing snapshots to work with
                string path = AssetDatabase.GetAssetPath(mixer);
                AudioMixerSnapshot[] existingSnapshots = AssetDatabase.LoadAllAssetsAtPath(path)
                    .OfType<AudioMixerSnapshot>()
                    .ToArray();
                
                foreach (var snapshotData in snapshots)
                {
                    string snapshotName = snapshotData["name"]?.ToString();
                    if (string.IsNullOrEmpty(snapshotName)) continue;

                    // Try to find existing snapshot first
                    AudioMixerSnapshot targetSnapshot = null;
                    foreach (var existing in existingSnapshots)
                    {
                        if (existing.name == snapshotName)
                        {
                            targetSnapshot = existing;
                            break;
                        }
                    }

                    if (targetSnapshot == null)
                    {
                        // Note: AudioMixerSnapshot cannot be created programmatically
                        // They must be created manually in the Audio Mixer window
                        Debug.LogWarning($"Snapshot '{snapshotName}' not found in mixer '{mixerName}'. Snapshots must be created manually in the Audio Mixer window.");
                        continue;
                    }

                    // Configure snapshot parameters if provided
                    var parameters = snapshotData["parameters"] as JObject;
                    if (parameters != null)
                    {
                        foreach (var param in parameters)
                        {
                            string paramName = param.Key;
                            float paramValue = param.Value.ToObject<float>();
                            
                            // Set parameter value for this snapshot
                            try
                            {
                                mixer.SetFloat(paramName, paramValue);
                                targetSnapshot.TransitionTo(0.1f); // Quick transition to apply values
                                System.Threading.Thread.Sleep(100); // Wait for transition
                            }
                            catch (Exception ex)
                            {
                                Debug.LogWarning($"Failed to set parameter '{paramName}' on snapshot '{snapshotName}': {ex.Message}");
                            }
                        }
                    }

                    // Set snapshot weight if specified
                    if (snapshotData["weight"] != null)
                    {
                        float weight = snapshotData["weight"].ToObject<float>();
                        targetSnapshot.TransitionTo(weight);
                    }

                    // Set transition duration if specified
                    if (snapshotData["transition_duration"] != null)
                    {
                        float duration = snapshotData["transition_duration"].ToObject<float>();
                        targetSnapshot.TransitionTo(duration);
                    }

                    createdSnapshots.Add(snapshotName);
                    snapshotObjects.Add(targetSnapshot);
                }

                // Set default snapshot if specified
                string defaultSnapshot = @params["default_snapshot"]?.ToString();
                AudioMixerSnapshot defaultSnapshotObj = null;
                
                if (!string.IsNullOrEmpty(defaultSnapshot))
                {
                    defaultSnapshotObj = snapshotObjects.FirstOrDefault(s => s.name == defaultSnapshot);
                    if (defaultSnapshotObj != null)
                    {
                        defaultSnapshotObj.TransitionTo(0.1f);
                    }
                    else
                    {
                        // Try to find in existing snapshots
                        defaultSnapshotObj = existingSnapshots.FirstOrDefault(s => s.name == defaultSnapshot);
                        if (defaultSnapshotObj != null)
                        {
                            defaultSnapshotObj.TransitionTo(0.1f);
                        }
                    }
                }

                // Create snapshot manager script if requested
                bool createManager = @params["create_snapshot_manager"]?.ToObject<bool>() ?? false;
                if (createManager)
                {
                    string managerName = @params["manager_name"]?.ToString() ?? $"{mixerName}SnapshotManager";
                    GameObject snapshotManager = new GameObject(managerName);
                    
                    // Create and write snapshot manager script
                    string managerScript = CreateSnapshotManagerScript();
                    string scriptPath = "Assets/Scripts/AudioSnapshotManager.cs";
                    
                    // Ensure Scripts directory exists
                    string scriptsDir = "Assets/Scripts";
                    if (!AssetDatabase.IsValidFolder(scriptsDir))
                    {
                        AssetDatabase.CreateFolder("Assets", "Scripts");
                    }
                    
                    System.IO.File.WriteAllText(scriptPath, managerScript);
                    AssetDatabase.ImportAsset(scriptPath);
                    AssetDatabase.Refresh();

                    // Store snapshot configuration
                    PlayerPrefs.SetString($"SnapshotManager.{managerName}.MixerName", mixerName);
                    PlayerPrefs.SetString($"SnapshotManager.{managerName}.DefaultSnapshot", defaultSnapshot ?? "");
                    PlayerPrefs.SetInt($"SnapshotManager.{managerName}.SnapshotCount", createdSnapshots.Count);
                    
                    for (int i = 0; i < createdSnapshots.Count; i++)
                    {
                        PlayerPrefs.SetString($"SnapshotManager.{managerName}.Snapshot{i}", createdSnapshots[i]);
                    }
                    
                    EditorUtility.SetDirty(snapshotManager);
                }

                EditorUtility.SetDirty(mixer);
                AssetDatabase.SaveAssets();

                return Response.Success($"Audio snapshots configured for mixer '{mixerName}'.", new {
                    mixer_name = mixerName,
                    snapshots_configured = createdSnapshots.ToArray(),
                    default_snapshot = defaultSnapshot,
                    total_snapshots = createdSnapshots.Count,
                    snapshot_manager_created = createManager
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error creating audio snapshots: {e.Message}");
            }
        }
        
        private static string CreateSnapshotManagerScript()
        {
            return @"using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Audio;

public class AudioSnapshotManager : MonoBehaviour
{
    [Header(""Snapshot Manager Settings"")]
    public AudioMixer audioMixer;
    public string defaultSnapshotName = ""Master"";
    public float defaultTransitionTime = 1.0f;
    
    [Header(""Runtime Info"")]
    public string currentSnapshotName = """";
    public bool isTransitioning = false;
    
    private Dictionary<string, AudioMixerSnapshot> snapshots = new Dictionary<string, AudioMixerSnapshot>();
    private string managerName;
    
    void Start()
    {
        managerName = gameObject.name;
        LoadConfiguration();
        InitializeSnapshots();
        
        if (!string.IsNullOrEmpty(defaultSnapshotName))
        {
            TransitionToSnapshot(defaultSnapshotName, 0.1f);
        }
    }
    
    void LoadConfiguration()
    {
        string mixerName = PlayerPrefs.GetString($""SnapshotManager.{managerName}.MixerName"", """");
        defaultSnapshotName = PlayerPrefs.GetString($""SnapshotManager.{managerName}.DefaultSnapshot"", ""Master"");
        
        if (!string.IsNullOrEmpty(mixerName))
        {
            // Find AudioMixer by name
            AudioMixer[] allMixers = Resources.FindObjectsOfTypeAll<AudioMixer>();
            audioMixer = System.Array.Find(allMixers, mixer => mixer.name == mixerName);
        }
    }
    
    void InitializeSnapshots()
    {
        if (audioMixer == null) return;
        
        int snapshotCount = PlayerPrefs.GetInt($""SnapshotManager.{managerName}.SnapshotCount"", 0);
        
        for (int i = 0; i < snapshotCount; i++)
        {
            string snapshotName = PlayerPrefs.GetString($""SnapshotManager.{managerName}.Snapshot{i}"", """");
            if (!string.IsNullOrEmpty(snapshotName))
            {
                AudioMixerSnapshot[] foundSnapshots = audioMixer.FindSnapshots(snapshotName);
                if (foundSnapshots.Length > 0)
                {
                    snapshots[snapshotName] = foundSnapshots[0];
                }
            }
        }
        
        // Also get all snapshots from the mixer
        AudioMixerSnapshot[] allSnapshots = audioMixer.FindSnapshots(""Master"");
        foreach (var snapshot in allSnapshots)
        {
            if (!snapshots.ContainsKey(snapshot.name))
            {
                snapshots[snapshot.name] = snapshot;
            }
        }
    }
    
    public void TransitionToSnapshot(string snapshotName, float transitionTime = -1f)
    {
        if (snapshots.ContainsKey(snapshotName))
        {
            float actualTransitionTime = transitionTime >= 0 ? transitionTime : defaultTransitionTime;
            
            isTransitioning = true;
            snapshots[snapshotName].TransitionTo(actualTransitionTime);
            currentSnapshotName = snapshotName;
            
            // Clear transitioning flag after transition time
            Invoke(nameof(ClearTransitioningFlag), actualTransitionTime);
            
            Debug.Log($""Transitioning to snapshot '{snapshotName}' over {actualTransitionTime} seconds"");
        }
        else
        {
            Debug.LogWarning($""Snapshot '{snapshotName}' not found in mixer!"");
        }
    }
    
    void ClearTransitioningFlag()
    {
        isTransitioning = false;
    }
    
    public void TransitionToSnapshotImmediate(string snapshotName)
    {
        TransitionToSnapshot(snapshotName, 0.01f);
    }
    
    public void BlendSnapshots(string[] snapshotNames, float[] weights, float transitionTime = -1f)
    {
        if (snapshotNames.Length != weights.Length)
        {
            Debug.LogError(""Snapshot names and weights arrays must have same length!"");
            return;
        }
        
        List<AudioMixerSnapshot> snapshotList = new List<AudioMixerSnapshot>();
        List<float> weightList = new List<float>();
        
        for (int i = 0; i < snapshotNames.Length; i++)
        {
            if (snapshots.ContainsKey(snapshotNames[i]))
            {
                snapshotList.Add(snapshots[snapshotNames[i]]);
                weightList.Add(weights[i]);
            }
        }
        
        if (snapshotList.Count > 0)
        {
            float actualTransitionTime = transitionTime >= 0 ? transitionTime : defaultTransitionTime;
            
            isTransitioning = true;
            audioMixer.TransitionToSnapshots(snapshotList.ToArray(), weightList.ToArray(), actualTransitionTime);
            
            // Clear transitioning flag after transition time
            Invoke(nameof(ClearTransitioningFlag), actualTransitionTime);
            
            currentSnapshotName = ""Blended: "" + string.Join("", "", snapshotNames);
        }
    }
    
    public string[] GetAvailableSnapshots()
    {
        string[] snapshotNames = new string[snapshots.Count];
        snapshots.Keys.CopyTo(snapshotNames, 0);
        return snapshotNames;
    }
    
    public string GetCurrentSnapshot()
    {
        return currentSnapshotName;
    }
    
    public bool IsTransitioning()
    {
        return isTransitioning;
    }
    
    // Public API methods for common scenarios
    public void SetGameplaySnapshot() => TransitionToSnapshot(""Gameplay"");
    public void SetMenuSnapshot() => TransitionToSnapshot(""Menu"");
    public void SetCombatSnapshot() => TransitionToSnapshot(""Combat"");
    public void SetAmbientSnapshot() => TransitionToSnapshot(""Ambient"");
    public void SetMutedSnapshot() => TransitionToSnapshot(""Muted"");
    public void SetPausedSnapshot() => TransitionToSnapshot(""Paused"");
}";
        }

        private static object SetupVoiceChat(JObject @params)
        {
            try
            {
                string voiceChatName = @params["voice_chat_name"]?.ToString();
                if (string.IsNullOrEmpty(voiceChatName))
                    return Response.Error("voice_chat_name is required.");

                GameObject voiceChatObj = new GameObject(voiceChatName);
                AudioSource audioSource = voiceChatObj.AddComponent<AudioSource>();

                string micDevice = @params["microphone_device"]?.ToString();
                int sampleRate = @params["sample_rate"]?.ToObject<int>() ?? 44100;
                bool echoCancellation = @params["enable_echo_cancellation"]?.ToObject<bool>() ?? true;
                float activationThreshold = @params["voice_activation_threshold"]?.ToObject<float>() ?? 0.01f;

                // Get available microphone devices
                string[] devices = Microphone.devices;
                string selectedDevice = null;

                if (!string.IsNullOrEmpty(micDevice))
                {
                    selectedDevice = devices.FirstOrDefault(d => d.Contains(micDevice));
                }
                
                if (selectedDevice == null && devices.Length > 0)
                {
                    selectedDevice = devices[0]; // Use first available device
                }

                if (selectedDevice == null)
                {
                    return Response.Error("No microphone devices available.");
                }

                // Create AudioClip for microphone recording - Unity 6.2 Microphone API
                int recordLength = 10; // 10 seconds buffer
                AudioClip micClip = Microphone.Start(selectedDevice, true, recordLength, sampleRate);
                audioSource.clip = micClip;
                audioSource.loop = true;

                // Check if microphone started successfully without blocking loop
                if (Microphone.IsRecording(selectedDevice))
                {
                    audioSource.Play();
                }
                else
                {
                    Debug.LogWarning($"Failed to start microphone recording on device: {selectedDevice}");
                }

                return Response.Success($"Voice chat system '{voiceChatName}' setup successfully.", new {
                    voice_chat_name = voiceChatName,
                    microphone_device = selectedDevice,
                    sample_rate = sampleRate,
                    echo_cancellation = echoCancellation,
                    activation_threshold = activationThreshold,
                    available_devices = devices.Length
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error setting up voice chat: {e.Message}");
            }
        }

        private static object CreateAudioTriggers(JObject @params)
        {
            try
            {
                string triggerName = @params["trigger_name"]?.ToString();
                if (string.IsNullOrEmpty(triggerName))
                    return Response.Error("trigger_name is required.");

                GameObject triggerObj = new GameObject(triggerName);
                
                // Add collider for trigger based on type
                string colliderType = @params["collider_type"]?.ToString() ?? "Sphere";
                Collider triggerCollider = null;
                
                switch (colliderType.ToLower())
                {
                    case "sphere":
                        SphereCollider sphereCollider = triggerObj.AddComponent<SphereCollider>();
                        sphereCollider.isTrigger = true;
                        sphereCollider.radius = @params["trigger_radius"]?.ToObject<float>() ?? 5f;
                        triggerCollider = sphereCollider;
                        break;
                    case "box":
                        BoxCollider boxCollider = triggerObj.AddComponent<BoxCollider>();
                        boxCollider.isTrigger = true;
                        if (@params["trigger_size"] != null)
                        {
                            var sizeArray = @params["trigger_size"].ToObject<float[]>();
                            if (sizeArray.Length >= 3)
                            {
                                boxCollider.size = new Vector3(sizeArray[0], sizeArray[1], sizeArray[2]);
                            }
                        }
                        triggerCollider = boxCollider;
                        break;
                    case "capsule":
                        CapsuleCollider capsuleCollider = triggerObj.AddComponent<CapsuleCollider>();
                        capsuleCollider.isTrigger = true;
                        capsuleCollider.radius = @params["trigger_radius"]?.ToObject<float>() ?? 2f;
                        capsuleCollider.height = @params["trigger_height"]?.ToObject<float>() ?? 5f;
                        triggerCollider = capsuleCollider;
                        break;
                }

                // Add AudioSource component
                AudioSource audioSource = triggerObj.AddComponent<AudioSource>();
                audioSource.playOnAwake = false;
                audioSource.spatialBlend = @params["spatial_blend"]?.ToObject<float>() ?? 1f; // 3D by default
                audioSource.volume = @params["volume"]?.ToObject<float>() ?? 1f;
                audioSource.pitch = @params["pitch"]?.ToObject<float>() ?? 1f;

                // Configure audio source 3D settings
                if (@params["min_distance"] != null)
                    audioSource.minDistance = @params["min_distance"].ToObject<float>();
                if (@params["max_distance"] != null)
                    audioSource.maxDistance = @params["max_distance"].ToObject<float>();
                
                string rolloffMode = @params["rolloff_mode"]?.ToString() ?? "Logarithmic";
                switch (rolloffMode.ToLower())
                {
                    case "linear":
                        audioSource.rolloffMode = AudioRolloffMode.Linear;
                        break;
                    case "logarithmic":
                        audioSource.rolloffMode = AudioRolloffMode.Logarithmic;
                        break;
                    case "custom":
                        audioSource.rolloffMode = AudioRolloffMode.Custom;
                        break;
                }

                string triggerType = @params["trigger_type"]?.ToString() ?? "OnTriggerEnter";
                var audioClips = @params["audio_clips"]?.ToObject<string[]>();
                bool randomizeClips = @params["randomize_clips"]?.ToObject<bool>() ?? false;
                var triggerConditions = @params["trigger_conditions"] as JObject;
                bool playOnce = @params["play_once"]?.ToObject<bool>() ?? false;
                float cooldownTime = @params["cooldown_time"]?.ToObject<float>() ?? 0f;

                // Load audio clips
                List<AudioClip> loadedClips = new List<AudioClip>();
                if (audioClips != null)
                {
                    foreach (string clipPath in audioClips)
                    {
                        AudioClip clip = AssetDatabase.LoadAssetAtPath<AudioClip>(clipPath);
                        if (clip != null)
                        {
                            loadedClips.Add(clip);
                        }
                    }
                }

                // Set first clip if available
                if (loadedClips.Count > 0)
                {
                    audioSource.clip = loadedClips[0];
                }

                // Create trigger script
                string triggerScript = CreateAudioTriggerScript();
                string scriptPath = "Assets/Scripts/AudioTriggerController.cs";
                
                // Ensure Scripts directory exists
                string scriptsDir = "Assets/Scripts";
                if (!AssetDatabase.IsValidFolder(scriptsDir))
                {
                    AssetDatabase.CreateFolder("Assets", "Scripts");
                }
                
                System.IO.File.WriteAllText(scriptPath, triggerScript);
                AssetDatabase.ImportAsset(scriptPath);
                AssetDatabase.Refresh();

                // Store trigger configuration in PlayerPrefs for runtime access
                PlayerPrefs.SetString($"AudioTrigger.{triggerName}.TriggerType", triggerType);
                PlayerPrefs.SetInt($"AudioTrigger.{triggerName}.RandomizeClips", randomizeClips ? 1 : 0);
                PlayerPrefs.SetInt($"AudioTrigger.{triggerName}.PlayOnce", playOnce ? 1 : 0);
                PlayerPrefs.SetFloat($"AudioTrigger.{triggerName}.CooldownTime", cooldownTime);
                PlayerPrefs.SetInt($"AudioTrigger.{triggerName}.ClipCount", loadedClips.Count);
                
                // Store audio clip names
                for (int i = 0; i < loadedClips.Count; i++)
                {
                    PlayerPrefs.SetString($"AudioTrigger.{triggerName}.Clip{i}", loadedClips[i].name);
                }

                // Store trigger conditions if provided
                if (triggerConditions != null)
                {
                    if (triggerConditions["required_tag"] != null)
                    {
                        string requiredTag = triggerConditions["required_tag"].ToString();
                        PlayerPrefs.SetString($"AudioTrigger.{triggerName}.RequiredTag", requiredTag);
                    }
                    
                    if (triggerConditions["required_layer"] != null)
                    {
                        string requiredLayer = triggerConditions["required_layer"].ToString();
                        PlayerPrefs.SetString($"AudioTrigger.{triggerName}.RequiredLayer", requiredLayer);
                    }
                    
                    if (triggerConditions["player_only"] != null)
                    {
                        bool playerOnly = triggerConditions["player_only"].ToObject<bool>();
                        PlayerPrefs.SetInt($"AudioTrigger.{triggerName}.PlayerOnly", playerOnly ? 1 : 0);
                    }
                }

                // Set trigger position if specified
                if (@params["position"] != null)
                {
                    var posArray = @params["position"].ToObject<float[]>();
                    if (posArray.Length >= 3)
                    {
                        triggerObj.transform.position = new Vector3(posArray[0], posArray[1], posArray[2]);
                    }
                }

                EditorUtility.SetDirty(triggerObj);

                // Create advanced position data using Unity 6.2 serialization
                var pos = triggerObj.transform.position;
                var positionData = new { x = pos.x, y = pos.y, z = pos.z };
                
                return Response.Success($"Audio trigger '{triggerName}' created successfully.", new {
                    trigger_name = triggerName,
                    trigger_type = triggerType,
                    collider_type = colliderType,
                    audio_clips_count = loadedClips.Count,
                    audio_clips_loaded = loadedClips.Select(c => c.name).ToArray(),
                    randomize_clips = randomizeClips,
                    play_once = playOnce,
                    cooldown_time = cooldownTime,
                    has_conditions = triggerConditions != null,
                    position = positionData
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error creating audio triggers: {e.Message}");
            }
        }
        
        private static string CreateAudioTriggerScript()
        {
            return @"using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class AudioTriggerController : MonoBehaviour
{
    [Header(""Trigger Settings"")]
    public string triggerType = ""OnTriggerEnter"";
    public bool randomizeClips = false;
    public bool playOnce = false;
    public float cooldownTime = 0f;
    
    [Header(""Conditions"")]
    public string requiredTag = """";
    public string requiredLayer = """";
    public bool playerOnly = false;
    
    [Header(""Runtime Info"")]
    public bool hasTriggered = false;
    public bool isOnCooldown = false;
    public float lastTriggerTime = 0f;
    
    private AudioSource audioSource;
    private List<AudioClip> audioClips = new List<AudioClip>();
    private int currentClipIndex = 0;
    private string triggerName;
    
    void Start()
    {
        triggerName = gameObject.name;
        audioSource = GetComponent<AudioSource>();
        
        LoadConfiguration();
        LoadAudioClips();
    }
    
    void LoadConfiguration()
    {
        triggerType = EditorPrefs.GetString($""AudioTrigger.{triggerName}.TriggerType"", ""OnTriggerEnter"");
        randomizeClips = EditorPrefs.GetBool($""AudioTrigger.{triggerName}.RandomizeClips"", false);
        playOnce = EditorPrefs.GetBool($""AudioTrigger.{triggerName}.PlayOnce"", false);
        cooldownTime = EditorPrefs.GetFloat($""AudioTrigger.{triggerName}.CooldownTime"", 0f);
        
        requiredTag = EditorPrefs.GetString($""AudioTrigger.{triggerName}.RequiredTag"", """");
        requiredLayer = EditorPrefs.GetString($""AudioTrigger.{triggerName}.RequiredLayer"", """");
        playerOnly = EditorPrefs.GetBool($""AudioTrigger.{triggerName}.PlayerOnly"", false);
    }
    
    void LoadAudioClips()
    {
        int clipCount = EditorPrefs.GetInt($""AudioTrigger.{triggerName}.ClipCount"", 0);
        
        for (int i = 0; i < clipCount; i++)
        {
            string clipName = EditorPrefs.GetString($""AudioTrigger.{triggerName}.Clip{i}"", """");
            if (!string.IsNullOrEmpty(clipName))
            {
                // Find AudioClip by name
                AudioClip[] allClips = Resources.FindObjectsOfTypeAll<AudioClip>();
                AudioClip foundClip = System.Array.Find(allClips, clip => clip.name == clipName);
                
                if (foundClip != null)
                {
                    audioClips.Add(foundClip);
                }
            }
        }
    }
    
    void OnTriggerEnter(Collider other)
    {
        if (triggerType.ToLower().Contains(""enter""))
        {
            HandleTrigger(other.gameObject);
        }
    }
    
    void OnTriggerExit(Collider other)
    {
        if (triggerType.ToLower().Contains(""exit""))
        {
            HandleTrigger(other.gameObject);
        }
    }
    
    void OnTriggerStay(Collider other)
    {
        if (triggerType.ToLower().Contains(""stay""))
        {
            HandleTrigger(other.gameObject);
        }
    }
    
    void OnCollisionEnter(Collision collision)
    {
        if (triggerType.ToLower().Contains(""collision"") && triggerType.ToLower().Contains(""enter""))
        {
            HandleTrigger(collision.gameObject);
        }
    }
    
    void HandleTrigger(GameObject triggeringObject)
    {
        // Check if already triggered and playOnce is enabled
        if (playOnce && hasTriggered)
            return;
            
        // Check cooldown
        if (isOnCooldown)
            return;
            
        // Check conditions
        if (!CheckTriggerConditions(triggeringObject))
            return;
            
        // Play audio
        PlayAudio();
        
        // Set triggered state
        hasTriggered = true;
        lastTriggerTime = Time.time;
        
        // Start cooldown if specified
        if (cooldownTime > 0)
        {
            StartCoroutine(CooldownCoroutine());
        }
    }
    
    bool CheckTriggerConditions(GameObject triggeringObject)
    {
        // Check player only condition
        if (playerOnly)
        {
            if (!triggeringObject.CompareTag(""Player"") && 
                triggeringObject.name.ToLower() != ""player"")
            {
                return false;
            }
        }
        
        // Check required tag
        if (!string.IsNullOrEmpty(requiredTag))
        {
            if (!triggeringObject.CompareTag(requiredTag))
            {
                return false;
            }
        }
        
        // Check required layer
        if (!string.IsNullOrEmpty(requiredLayer))
        {
            int layerIndex = LayerMask.NameToLayer(requiredLayer);
            if (layerIndex != -1 && triggeringObject.layer != layerIndex)
            {
                return false;
            }
        }
        
        return true;
    }
    
    void PlayAudio()
    {
        if (audioClips.Count == 0 || audioSource == null)
            return;
            
        AudioClip clipToPlay = null;
        
        if (randomizeClips)
        {
            int randomIndex = Random.Range(0, audioClips.Count);
            clipToPlay = audioClips[randomIndex];
        }
        else
        {
            clipToPlay = audioClips[currentClipIndex];
            currentClipIndex = (currentClipIndex + 1) % audioClips.Count;
        }
        
        audioSource.clip = clipToPlay;
        audioSource.Play();
        
        Debug.Log($""Audio trigger '{triggerName}' played clip: {clipToPlay.name}"");
    }
    
    IEnumerator CooldownCoroutine()
    {
        isOnCooldown = true;
        yield return new WaitForSeconds(cooldownTime);
        isOnCooldown = false;
    }
    
    public void ForcePlay()
    {
        PlayAudio();
    }
    
    public void ResetTrigger()
    {
        hasTriggered = false;
        isOnCooldown = false;
    }
    
    public void AddAudioClip(AudioClip clip)
    {
        if (clip != null && !audioClips.Contains(clip))
        {
            audioClips.Add(clip);
        }
    }
    
    public void RemoveAudioClip(AudioClip clip)
    {
        if (audioClips.Contains(clip))
        {
            audioClips.Remove(clip);
        }
    }
    
    public void SetVolume(float volume)
    {
        if (audioSource != null)
        {
            audioSource.volume = Mathf.Clamp01(volume);
        }
    }
    
    public void SetPitch(float pitch)
    {
        if (audioSource != null)
        {
            audioSource.pitch = pitch;
        }
    }
    
    // Public API methods
    public bool HasTriggered() => hasTriggered;
    public bool IsOnCooldown() => isOnCooldown;
    public int GetClipCount() => audioClips.Count;
    public float GetTimeSinceLastTrigger() => Time.time - lastTriggerTime;
}";
        }

        private static object SetupAudioStreaming(JObject @params)
        {
            try
            {
                string sourceName = @params["streaming_source_name"]?.ToString();
                if (string.IsNullOrEmpty(sourceName))
                    return Response.Error("streaming_source_name is required.");

                GameObject streamingObj = new GameObject(sourceName);
                AudioSource audioSource = streamingObj.AddComponent<AudioSource>();

                string streamUrl = @params["stream_url"]?.ToString();
                string localFilePath = @params["local_file_path"]?.ToString();
                int bufferSize = @params["buffer_size"]?.ToObject<int>() ?? 1024;
                bool enableStreaming = @params["enable_streaming"]?.ToObject<bool>() ?? true;
                bool preloadData = @params["preload_data"]?.ToObject<bool>() ?? false;
                float volume = @params["volume"]?.ToObject<float>() ?? 1f;
                bool autoPlay = @params["auto_play"]?.ToObject<bool>() ?? false;

                // Configure advanced Unity 6.2 AudioSource settings
                audioSource.volume = volume;
                audioSource.playOnAwake = autoPlay;
                audioSource.spatialBlend = @params["spatial_blend"]?.ToObject<float>() ?? 0f; // 2D by default for streaming
                audioSource.loop = @params["loop"]?.ToObject<bool>() ?? true;

                List<string> configuredSources = new List<string>();

                if (!string.IsNullOrEmpty(streamUrl))
                {
                    // Setup for URL streaming - create a custom streaming component
                    string streamingScript = CreateAudioStreamingScript(streamUrl, autoPlay);
                    string scriptPath = "Assets/Scripts/AudioStreamingController.cs";
                    
                    // Ensure Scripts directory exists
                    string scriptsDir = "Assets/Scripts";
                    if (!AssetDatabase.IsValidFolder(scriptsDir))
                    {
                        AssetDatabase.CreateFolder("Assets", "Scripts");
                    }
                    
                    System.IO.File.WriteAllText(scriptPath, streamingScript);
                    AssetDatabase.ImportAsset(scriptPath);
                    AssetDatabase.Refresh();

                    // Store streaming configuration
                    EditorPrefs.SetString($"AudioStreaming.{sourceName}.StreamUrl", streamUrl);
                    EditorPrefs.SetInt($"AudioStreaming.{sourceName}.BufferSize", bufferSize);
                    EditorPrefs.SetBool($"AudioStreaming.{sourceName}.AutoPlay", autoPlay);
                    EditorPrefs.SetFloat($"AudioStreaming.{sourceName}.Volume", volume);
                    
                    configuredSources.Add("URL streaming");
                }
                else if (!string.IsNullOrEmpty(localFilePath))
                {
                    // Setup for local file streaming
                    AudioClip clip = AssetDatabase.LoadAssetAtPath<AudioClip>(localFilePath);
                    if (clip != null)
                    {
                        audioSource.clip = clip;
                        
                        // Configure AudioImporter for streaming
                        AudioImporter audioImporter = AssetImporter.GetAtPath(localFilePath) as AudioImporter;
                        if (audioImporter != null)
                        {
                            var sampleSettings = audioImporter.defaultSampleSettings;
                            
                            // Set to streaming mode
                            if (enableStreaming)
                            {
                                sampleSettings.loadType = AudioClipLoadType.Streaming;
                            }
                            else
                            {
                                sampleSettings.loadType = preloadData ? 
                                    AudioClipLoadType.DecompressOnLoad : 
                                    AudioClipLoadType.CompressedInMemory;
                            }
                            
                            // Configure compression for streaming
                            sampleSettings.compressionFormat = AudioCompressionFormat.Vorbis;
                            sampleSettings.quality = @params["compression_quality"]?.ToObject<float>() ?? 0.7f;
                            
                            // Apply streaming settings
                            audioImporter.defaultSampleSettings = sampleSettings;
                            
                            // Configure platform-specific settings for better streaming
                            ConfigureStreamingPlatformSettings(EditorUserBuildSettings.activeBuildTarget.ToString(), @params);
                            
                            audioImporter.SaveAndReimport();
                            AssetDatabase.Refresh();
                        }
                        
                        configuredSources.Add("Local file streaming");
                    }
                    else
                    {
                        return Response.Error($"AudioClip not found at path: {localFilePath}");
                    }
                }
                else
                {
                    return Response.Error("Either stream_url or local_file_path must be provided.");
                }

                // Configure advanced streaming settings
                if (@params["advanced_settings"] != null)
                {
                    var advancedSettings = @params["advanced_settings"] as JObject;
                    
                    if (advancedSettings["priority"] != null)
                    {
                        audioSource.priority = advancedSettings["priority"].ToObject<int>();
                    }
                    
                    if (advancedSettings["mute"] != null)
                    {
                        audioSource.mute = advancedSettings["mute"].ToObject<bool>();
                    }
                    
                    if (advancedSettings["pitch"] != null)
                    {
                        audioSource.pitch = advancedSettings["pitch"].ToObject<float>();
                    }
                    
                    if (advancedSettings["reverb_zone_mix"] != null)
                    {
                        audioSource.reverbZoneMix = advancedSettings["reverb_zone_mix"].ToObject<float>();
                    }
                    
                    if (advancedSettings["doppler_level"] != null)
                    {
                        audioSource.dopplerLevel = advancedSettings["doppler_level"].ToObject<float>();
                    }
                    
                    if (advancedSettings["min_distance"] != null)
                    {
                        audioSource.minDistance = advancedSettings["min_distance"].ToObject<float>();
                    }
                    
                    if (advancedSettings["max_distance"] != null)
                    {
                        audioSource.maxDistance = advancedSettings["max_distance"].ToObject<float>();
                    }
                    
                    // Configure rolloff mode
                    if (advancedSettings["rolloff_mode"] != null)
                    {
                        string rolloffMode = advancedSettings["rolloff_mode"].ToString().ToLower();
                        switch (rolloffMode)
                        {
                            case "linear":
                                audioSource.rolloffMode = AudioRolloffMode.Linear;
                                break;
                            case "logarithmic":
                                audioSource.rolloffMode = AudioRolloffMode.Logarithmic;
                                break;
                            case "custom":
                                audioSource.rolloffMode = AudioRolloffMode.Custom;
                                break;
                        }
                    }
                    
                    // Configure AudioMixer output if specified
                    if (advancedSettings["mixer_group"] != null)
                    {
                        string mixerGroupName = advancedSettings["mixer_group"].ToString();
                        SetupAudioMixerOutput(audioSource, mixerGroupName);
                    }
                }

                // Configure streaming buffer management
                bool useCustomBuffer = @params["use_custom_buffer"]?.ToObject<bool>() ?? false;
                if (useCustomBuffer)
                {
                    float bufferLength = @params["buffer_length"]?.ToObject<float>() ?? 0.1f;
                    // Unity 6.2 AudioSettings buffer configuration
                    var audioConfig = AudioSettings.GetConfiguration();
                    audioConfig.dspBufferSize = bufferSize;
                    AudioSettings.Reset(audioConfig);
                }

                EditorUtility.SetDirty(streamingObj);

                return Response.Success($"Audio streaming '{sourceName}' setup successfully.", new {
                    source_name = sourceName,
                    stream_url = streamUrl ?? localFilePath,
                    configured_sources = configuredSources.ToArray(),
                    buffer_size = bufferSize
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error setting up audio streaming: {e.Message}");
            }
        }

        private static string CreateAudioStreamingScript(string audioUrl, bool autoPlay)
        {
            return @"
using UnityEngine;
using UnityEngine.Networking;
using System.Collections;

public class AudioStreamer : MonoBehaviour
{
    public string audioUrl = """ + audioUrl + @""";
    public bool autoPlay = " + autoPlay.ToString().ToLower() + @";
    private AudioSource audioSource;

    void Start()
    {
        audioSource = GetComponent<AudioSource>();
        if (audioSource == null)
        {
            audioSource = gameObject.AddComponent<AudioSource>();
        }

        if (autoPlay)
        {
            StartCoroutine(StreamAudio());
        }
    }

    public void Play()
    {
        StartCoroutine(StreamAudio());
    }

    private IEnumerator StreamAudio()
    {
        using (UnityWebRequest www = UnityWebRequestMultimedia.GetAudioClip(audioUrl, AudioType.UNKNOWN))
        {
            yield return www.SendWebRequest();

            if (www.result == UnityWebRequest.Result.ConnectionError || www.result == UnityWebRequest.Result.ProtocolError)
            {
                Debug.LogError(www.error);
            }
            else
            {
                AudioClip clip = DownloadHandlerAudioClip.GetContent(www);
                audioSource.clip = clip;
                audioSource.Play();
            }
        }
    }
}
";
        }

        private static void ConfigureStreamingPlatformSettings(string platform, JObject streamingSettings)
        {
            // In Unity 6.2, most streaming settings are handled by the AudioImporter or are not directly available.
            // This function can be used to log settings or apply custom logic.
            Debug.Log($"[AudioSystem] Configuring streaming for platform: {platform}. Settings: {streamingSettings}");
            // Example: Could modify player settings or custom configuration assets here.
        }
        
        private static void SetupAudioMixerOutput(AudioSource source, string groupName, string mixerName = null)
        {
            if (string.IsNullOrEmpty(groupName) || source == null) return;

            AudioMixer mixer = null;
            if (!string.IsNullOrEmpty(mixerName))
            {
                string[] mixerGuids = AssetDatabase.FindAssets($"{mixerName} t:AudioMixer");
                if (mixerGuids.Length > 0)
                {
                    string assetPath = AssetDatabase.GUIDToAssetPath(mixerGuids[0]);
                    mixer = AssetDatabase.LoadAssetAtPath<AudioMixer>(assetPath);
                }
            }

            if (mixer == null)
            {
                Debug.LogWarning($"[AudioSystem] Mixer '{mixerName}' not found. Cannot set output group.");
                return;
            }

            AudioMixerGroup[] groups = mixer.FindMatchingGroups(groupName);
            if (groups.Length > 0)
            {
                source.outputAudioMixerGroup = groups[0];
            }
            else
            {
                Debug.LogWarning($"[AudioSystem] Mixer group '{groupName}' not found in mixer '{mixerName}'.");
            }
        }

        private static object CreateAudioEffectsChain(JObject @params)
        {
            try
            {
                string chainName = @params["chain_name"]?.ToString();
                string gameObjectName = @params["gameobject_name"]?.ToString();
                
                if (string.IsNullOrEmpty(chainName) || string.IsNullOrEmpty(gameObjectName))
                    return Response.Error("chain_name and gameobject_name are required.");

                GameObject targetObj = GameObject.Find(gameObjectName);
                if (targetObj == null)
                    return Response.Error($"GameObject '{gameObjectName}' not found.");

                var effectsChain = @params["effects_chain"]?.ToObject<JObject[]>();
                bool enableRealTimeControl = @params["enable_real_time_control"]?.ToObject<bool>() ?? true;

                List<string> addedEffects = new List<string>();

                if (effectsChain != null)
                {
                    foreach (var effect in effectsChain)
                    {
                        string effectType = effect["type"]?.ToString();
                        var effectParams = effect["parameters"] as JObject;

                        // Add effects in order based on the chain
                        switch (effectType)
                        {
                            case "LowPass":
                                var lowPass = targetObj.AddComponent<AudioLowPassFilter>();
                                if (effectParams?["cutoff"] != null)
                                    lowPass.cutoffFrequency = effectParams["cutoff"].ToObject<float>();
                                addedEffects.Add("AudioLowPassFilter");
                                break;
                                
                            case "Echo":
                                var echo = targetObj.AddComponent<AudioEchoFilter>();
                                if (effectParams?["delay"] != null)
                                    echo.delay = effectParams["delay"].ToObject<float>();
                                if (effectParams?["decay"] != null)
                                    echo.decayRatio = effectParams["decay"].ToObject<float>();
                                addedEffects.Add("AudioEchoFilter");
                                break;
                                
                            case "Reverb":
                                var reverb = targetObj.AddComponent<AudioReverbFilter>();
                                if (effectParams?["preset"] != null && 
                                    Enum.TryParse(effectParams["preset"].ToString(), out AudioReverbPreset preset))
                                    reverb.reverbPreset = preset;
                                addedEffects.Add("AudioReverbFilter");
                                break;
                        }
                    }
                }

                EditorUtility.SetDirty(targetObj);

                return Response.Success($"Audio effects chain '{chainName}' created successfully.", new {
                    chain_name = chainName,
                    gameobject = gameObjectName,
                    effects_added = addedEffects.ToArray(),
                    total_effects = addedEffects.Count,
                    real_time_control_enabled = enableRealTimeControl
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error creating audio effects chain: {e.Message}");
            }
        }

        private static AudioCompressionFormat GetAudioCompressionFormat(string format)
        {
            switch (format.ToLower())
            {
                case "pcm":
                    return AudioCompressionFormat.PCM;
                case "vorbis":
                    return AudioCompressionFormat.Vorbis;
                case "mp3":
                    return AudioCompressionFormat.MP3;
                case "adpcm":
                    return AudioCompressionFormat.ADPCM;
                default:
                    return AudioCompressionFormat.Vorbis;
            }
        }

        private static AudioCompressionFormat GetPlatformOptimizedFormat(string platform, string preferredFormat)
        {
            switch (platform.ToLower())
            {
                case "ios":
                    return AudioCompressionFormat.MP3; // iOS optimized
                case "android":
                    return AudioCompressionFormat.Vorbis; // Android optimized
                case "webgl":
                    return AudioCompressionFormat.Vorbis; // Web optimized
                case "mobile":
                    return AudioCompressionFormat.Vorbis; // General mobile optimization
                default:
                    return GetAudioCompressionFormat(preferredFormat);
            }
        }

        private static float GetPlatformOptimizedQuality(string platform, float baseQuality)
        {
            switch (platform.ToLower())
            {
                case "ios":
                case "android":
                case "mobile":
                    return Mathf.Min(baseQuality, 60.0f); // Lower quality for mobile
                case "webgl":
                    return Mathf.Min(baseQuality, 50.0f); // Even lower for web
                default:
                    return baseQuality;
            }
        }

        private static object SetupBinauralAudio(JObject @params)
        {
            try
            {
                string listenerName = @params["listener_name"]?.ToString() ?? "Main Camera";
                bool hrtfEnabled = @params["hrtf_enabled"]?.ToObject<bool>() ?? true;
                bool headTracking = @params["head_tracking"]?.ToObject<bool>() ?? false;
                string binauralDecoder = @params["binaural_decoder"]?.ToString() ?? "Unity Spatializer";

                GameObject listenerObj = GameObject.Find(listenerName);
                if (listenerObj == null)
                    return Response.Error($"AudioListener GameObject '{listenerName}' not found.");

                // Configure binaural audio settings
                // Note: Actual implementation depends on specific spatializer plugin
                Debug.Log($"Binaural audio setup for '{listenerName}' with {binauralDecoder}");

                // Enable spatialization on AudioListener
                AudioListener listener = listenerObj.GetComponent<AudioListener>();
                if (listener == null)
                {
                    listener = listenerObj.AddComponent<AudioListener>();
                }

                return Response.Success($"Binaural audio setup for '{listenerName}' completed.", new {
                    listener_name = listenerName,
                    hrtf_enabled = hrtfEnabled,
                    head_tracking = headTracking,
                    binaural_decoder = binauralDecoder
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error setting up binaural audio: {e.Message}");
            }
        }

        private static object CreateAudioVisualization(JObject @params)
        {
            try
            {
                string visualizerName = @params["visualizer_name"]?.ToString();
                string audioSourceName = @params["audio_source_name"]?.ToString();
                
                if (string.IsNullOrEmpty(visualizerName) || string.IsNullOrEmpty(audioSourceName))
                    return Response.Error("visualizer_name and audio_source_name are required.");

                GameObject sourceObj = GameObject.Find(audioSourceName);
                if (sourceObj == null)
                    return Response.Error($"AudioSource GameObject '{audioSourceName}' not found.");

                AudioSource audioSource = sourceObj.GetComponent<AudioSource>();
                if (audioSource == null)
                    return Response.Error($"AudioSource component not found on '{audioSourceName}'.");

                GameObject visualizerObj = new GameObject(visualizerName);
                
                string visualizationType = @params["visualization_type"]?.ToString() ?? "Spectrum";
                int sampleCount = @params["sample_count"]?.ToObject<int>() ?? 256;
                string visualizationMaterial = @params["visualization_material"]?.ToString();

                // Add LineRenderer for visualization
                LineRenderer lineRenderer = visualizerObj.AddComponent<LineRenderer>();
                lineRenderer.positionCount = sampleCount;
                lineRenderer.startWidth = 0.1f;
                lineRenderer.endWidth = 0.1f;

                if (!string.IsNullOrEmpty(visualizationMaterial))
                {
                    Material material = AssetDatabase.LoadAssetAtPath<Material>(visualizationMaterial);
                    if (material != null)
                    {
                        lineRenderer.material = material;
                    }
                }

                // Note: Actual visualization script would need to be created separately
                // to sample AudioSource data and update LineRenderer positions
                Debug.Log($"Audio visualization '{visualizerName}' created for '{audioSourceName}'");

                return Response.Success($"Audio visualization '{visualizerName}' created successfully.", new {
                    visualizer_name = visualizerName,
                    audio_source = audioSourceName,
                    visualization_type = visualizationType,
                    sample_count = sampleCount,
                    has_material = !string.IsNullOrEmpty(visualizationMaterial)
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error creating audio visualization: {e.Message}");
            }
        }

        private static object OptimizeAudioPerformance(JObject @params)
        {
            try
            {
                var optimizationTargets = @params["optimization_targets"]?.ToObject<string[]>();
                int maxVirtualVoices = @params["max_virtual_voices"]?.ToObject<int>() ?? 512;
                int maxRealVoices = @params["max_real_voices"]?.ToObject<int>() ?? 32;
                bool sampleRateOptimization = @params["sample_rate_optimization"]?.ToObject<bool>() ?? true;
                bool compressionOptimization = @params["compression_optimization"]?.ToObject<bool>() ?? true;

                // Configure AudioSettings for Unity 6.2
                var audioConfig = AudioSettings.GetConfiguration();
                audioConfig.numVirtualVoices = maxVirtualVoices;
                audioConfig.numRealVoices = maxRealVoices;
                
                if (!AudioSettings.Reset(audioConfig))
                {
                    return Response.Error("Failed to apply audio configuration changes.");
                }

                int optimizationsApplied = 0;

                // Sample rate optimization
                if (sampleRateOptimization && (optimizationTargets == null || optimizationTargets.Contains("sample_rate")))
                {
                    string[] audioClipGuids = AssetDatabase.FindAssets("t:AudioClip");
                    foreach (string guid in audioClipGuids)
                    {
                        string assetPath = AssetDatabase.GUIDToAssetPath(guid);
                        AudioImporter audioImporter = AssetImporter.GetAtPath(assetPath) as AudioImporter;
                        if (audioImporter == null) continue;

                        var sampleSettings = audioImporter.defaultSampleSettings;
                        // Use standard optimization for Unity 6.2
                        if (sampleSettings.sampleRateSetting == AudioSampleRateSetting.PreserveSampleRate)
                        {
                            sampleSettings.sampleRateSetting = AudioSampleRateSetting.OverrideSampleRate;
                            sampleSettings.sampleRateOverride = 22050; // Optimize for mobile
                            audioImporter.defaultSampleSettings = sampleSettings;
                            audioImporter.SaveAndReimport();
                        }
                    }
                    optimizationsApplied++;
                }

                // Compression optimization
                if (compressionOptimization && (optimizationTargets == null || optimizationTargets.Contains("compression")))
                {
                    // Apply mobile-friendly compression settings
                    optimizationsApplied++;
                }

                return Response.Success($"Audio performance optimized successfully.", new {
                    optimizations_applied = optimizationsApplied,
                    max_virtual_voices = maxVirtualVoices,
                    max_real_voices = maxRealVoices,
                    sample_rate_optimization = sampleRateOptimization,
                    compression_optimization = compressionOptimization
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error optimizing audio performance: {e.Message}");
            }
        }

        private static object CreateProceduralAudio(JObject @params)
        {
            try
            {
                string generatorName = @params["generator_name"]?.ToString();
                string gameObjectName = @params["gameobject_name"]?.ToString();
                
                if (string.IsNullOrEmpty(generatorName))
                    return Response.Error("generator_name is required.");
                
                // Use generator_name as gameobject_name if not provided
                if (string.IsNullOrEmpty(gameObjectName))
                    gameObjectName = generatorName;

                GameObject targetObj = GameObject.Find(gameObjectName);
                if (targetObj == null)
                {
                    targetObj = new GameObject(gameObjectName);
                }

                // Get audio generation parameters
                string waveType = @params["wave_type"]?.ToString() ?? "Sine";
                float frequency = @params["frequency"]?.ToObject<float>() ?? 440.0f;
                float duration = @params["duration"]?.ToObject<float>() ?? 1.0f;
                int sampleRate = @params["sample_rate"]?.ToObject<int>() ?? 44100;
                float amplitude = @params["amplitude"]?.ToObject<float>() ?? 0.5f;

                // Create procedural AudioClip using Unity 6.2 AudioClip.Create
                int samples = Mathf.RoundToInt(sampleRate * duration);
                AudioClip proceduralClip = AudioClip.Create(generatorName, samples, 1, sampleRate, false);
                
                // Generate waveform data
                float[] clipData = new float[samples];
                for (int i = 0; i < samples; i++)
                {
                    float time = (float)i / sampleRate;
                    float waveValue = 0.0f;

                    switch (waveType.ToLower())
                    {
                        case "sine":
                            waveValue = Mathf.Sin(2.0f * Mathf.PI * frequency * time);
                            break;
                        case "square":
                            waveValue = Mathf.Sign(Mathf.Sin(2.0f * Mathf.PI * frequency * time));
                            break;
                        case "triangle":
                            waveValue = 2.0f * Mathf.Abs(2.0f * ((frequency * time) % 1.0f) - 1.0f) - 1.0f;
                            break;
                        case "sawtooth":
                            waveValue = 2.0f * ((frequency * time) % 1.0f) - 1.0f;
                            break;
                        case "noise":
                            waveValue = UnityEngine.Random.Range(-1.0f, 1.0f);
                            break;
                        default:
                            waveValue = Mathf.Sin(2.0f * Mathf.PI * frequency * time);
                            break;
                    }

                    clipData[i] = waveValue * amplitude;
                }

                // Set the data to the AudioClip
                proceduralClip.SetData(clipData, 0);

                // Get or add AudioSource component
                AudioSource audioSource = targetObj.GetComponent<AudioSource>();
                if (audioSource == null)
                {
                    audioSource = targetObj.AddComponent<AudioSource>();
                }

                // Configure AudioSource
                audioSource.clip = proceduralClip;
                audioSource.playOnAwake = @params["play_on_awake"]?.ToObject<bool>() ?? false;
                audioSource.loop = @params["loop"]?.ToObject<bool>() ?? false;
                audioSource.volume = @params["volume"]?.ToObject<float>() ?? 1.0f;
                audioSource.pitch = @params["pitch"]?.ToObject<float>() ?? 1.0f;

                // Save the procedural clip as an asset
                string assetsPath = "Assets/GeneratedAudio/";
                if (!AssetDatabase.IsValidFolder(assetsPath))
                {
                    AssetDatabase.CreateFolder("Assets", "GeneratedAudio");
                }
                
                string clipPath = $"{assetsPath}{generatorName}.asset";
                AssetDatabase.CreateAsset(proceduralClip, clipPath);
                AssetDatabase.SaveAssets();

                // Create runtime controller script for real-time procedural audio
                string controllerScriptPath = CreateProceduralAudioControllerScript();

                EditorUtility.SetDirty(targetObj);

                return Response.Success($"Procedural audio generator '{generatorName}' created successfully.", new {
                    generator_name = generatorName,
                    gameobject = gameObjectName,
                    wave_type = waveType,
                    frequency = frequency,
                    duration = duration,
                    sample_rate = sampleRate,
                    amplitude = amplitude,
                    clip_path = clipPath,
                    controller_script = controllerScriptPath
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error creating procedural audio: {e.Message}");
            }
        }

        private static string CreateProceduralAudioControllerScript()
        {
            string scriptContent = @"using UnityEngine;
using System.Collections;

public class ProceduralAudioController : MonoBehaviour
{
    [Header(""Procedural Audio Settings"")]
    public AudioSource audioSource;
    
    [Header(""Wave Generation"")]
    public WaveType waveType = WaveType.Sine;
    public float frequency = 440.0f;
    public float amplitude = 0.5f;
    public int sampleRate = 44100;
    
    [Header(""Real-time Controls"")]
    public bool realTimeGeneration = false;
    public float frequencyModulation = 0.0f;
    public AnimationCurve frequencyCurve = AnimationCurve.Linear(0, 1, 1, 1);
    
    public enum WaveType
    {
        Sine,
        Square,
        Triangle,
        Sawtooth,
        Noise
    }
    
    private AudioClip proceduralClip;
    private float[] clipData;
    private int samples;
    private float time;
    
    void Start()
    {
        if (audioSource == null)
            audioSource = GetComponent<AudioSource>();
            
        if (audioSource == null)
        {
            Debug.LogError(""ProceduralAudioController: No AudioSource found!"");
            return;
        }
        
        InitializeProceduralAudio();
    }
    
    void Update()
    {
        if (realTimeGeneration && audioSource.isPlaying)
        {
            UpdateProceduralAudio();
        }
    }
    
    public void InitializeProceduralAudio()
    {
        samples = sampleRate;
        proceduralClip = AudioClip.Create(""ProceduralClip"", samples, 1, sampleRate, true, OnAudioRead);
        audioSource.clip = proceduralClip;
        
        clipData = new float[samples];
    }
    
    public void PlayProceduralAudio()
    {
        if (audioSource != null && proceduralClip != null)
        {
            audioSource.Play();
        }
    }
    
    public void StopProceduralAudio()
    {
        if (audioSource != null)
        {
            audioSource.Stop();
        }
    }
    
    public void SetFrequency(float newFrequency)
    {
        frequency = newFrequency;
        if (realTimeGeneration)
        {
            GenerateWaveData();
            proceduralClip.SetData(clipData, 0);
        }
    }
    
    public void SetWaveType(WaveType newWaveType)
    {
        waveType = newWaveType;
        if (realTimeGeneration)
        {
            GenerateWaveData();
            proceduralClip.SetData(clipData, 0);
        }
    }
    
    private void UpdateProceduralAudio()
    {
        float currentFrequency = frequency + (frequencyModulation * frequencyCurve.Evaluate(time));
        
        // Apply frequency modulation
        if (Mathf.Abs(currentFrequency - frequency) > 0.1f)
        {
            frequency = currentFrequency;
            GenerateWaveData();
        }
        
        time += Time.deltaTime;
        if (time > 1.0f) time = 0.0f;
    }
    
    private void OnAudioRead(float[] data)
    {
        if (clipData != null && clipData.Length == data.Length)
        {
            System.Array.Copy(clipData, data, data.Length);
        }
    }
    
    private void GenerateWaveData()
    {
        for (int i = 0; i < samples; i++)
        {
            float t = (float)i / sampleRate;
            float waveValue = 0.0f;
            
            switch (waveType)
            {
                case WaveType.Sine:
                    waveValue = Mathf.Sin(2.0f * Mathf.PI * frequency * t);
                    break;
                case WaveType.Square:
                    waveValue = Mathf.Sign(Mathf.Sin(2.0f * Mathf.PI * frequency * t));
                    break;
                case WaveType.Triangle:
                    waveValue = 2.0f * Mathf.Abs(2.0f * ((frequency * t) % 1.0f) - 1.0f) - 1.0f;
                    break;
                case WaveType.Sawtooth:
                    waveValue = 2.0f * ((frequency * t) % 1.0f) - 1.0f;
                    break;
                case WaveType.Noise:
                    waveValue = Random.Range(-1.0f, 1.0f);
                    break;
            }
            
            clipData[i] = waveValue * amplitude;
        }
    }
    
    void OnValidate()
    {
        frequency = Mathf.Clamp(frequency, 20.0f, 20000.0f);
        amplitude = Mathf.Clamp01(amplitude);
        
        if (Application.isPlaying && realTimeGeneration)
        {
            GenerateWaveData();
            if (proceduralClip != null)
                proceduralClip.SetData(clipData, 0);
        }
    }
}";

            string scriptPath = "Assets/Scripts/ProceduralAudioController.cs";
            string directory = System.IO.Path.GetDirectoryName(scriptPath);
            
            if (!AssetDatabase.IsValidFolder(directory))
            {
                AssetDatabase.CreateFolder("Assets", "Scripts");
            }
            
            System.IO.File.WriteAllText(scriptPath, scriptContent);
            AssetDatabase.ImportAsset(scriptPath);
            
            return scriptPath;
        }

        private static object SetupAudioCompression(JObject @params)
        {
            try
            {
                var targetPlatforms = @params["target_platforms"]?.ToObject<string[]>() ?? new string[] { "Standalone", "Mobile" };
                string compressionFormat = @params["compression_format"]?.ToString() ?? "Vorbis";
                float quality = @params["quality"]?.ToObject<float>() ?? 70.0f;
                bool loadInBackground = @params["load_in_background"]?.ToObject<bool>() ?? true;
                
                AudioClipLoadType loadType = AudioClipLoadType.CompressedInMemory;

                // Parse load type from params
                if(@params["load_type"] != null)
                {
                    Enum.TryParse(@params["load_type"].ToString(), true, out loadType);
                } 
                else if (@params["preload_audio_data"] != null) 
                {
                    // Fallback to obsolete preload_audio_data
                    loadType = @params["preload_audio_data"].ToObject<bool>() ? AudioClipLoadType.DecompressOnLoad : AudioClipLoadType.Streaming;
                }


                int processedClips = 0;
                List<string> configuredPlatforms = new List<string>();

                // Find all AudioClip assets in the project
                string[] audioClipGuids = AssetDatabase.FindAssets("t:AudioClip");
                
                foreach (string guid in audioClipGuids)
                {
                    string assetPath = AssetDatabase.GUIDToAssetPath(guid);
                    AudioImporter audioImporter = AssetImporter.GetAtPath(assetPath) as AudioImporter;
                    
                    if (audioImporter == null) continue;

                    // Configure default sample settings
                    var defaultSettings = audioImporter.defaultSampleSettings;
                    defaultSettings.loadType = loadType;
                    defaultSettings.compressionFormat = GetAudioCompressionFormat(compressionFormat);
                    defaultSettings.quality = quality / 100.0f; // Convert percentage to 0-1 range
                    audioImporter.defaultSampleSettings = defaultSettings;

                    // Configure platform-specific settings
                    foreach (string platform in targetPlatforms)
                    {
                        var platformSettings = audioImporter.GetOverrideSampleSettings(platform);
                        
                        platformSettings.loadType = loadType;
                        platformSettings.compressionFormat = GetPlatformOptimizedFormat(platform, compressionFormat);
                        platformSettings.quality = GetPlatformOptimizedQuality(platform, quality) / 100.0f;
                        
                        // Platform-specific optimizations for Unity 6.2
                        switch (platform.ToLower())
                        {
                            case "ios":
                            case "android":
                            case "mobile":
                                platformSettings.sampleRateSetting = AudioSampleRateSetting.OverrideSampleRate;
                                platformSettings.sampleRateOverride = 22050; // Optimize for mobile
                                break;
                            case "webgl":
                                platformSettings.compressionFormat = AudioCompressionFormat.Vorbis;
                                platformSettings.quality = 0.5f; // Lower quality for web
                                break;
                            case "standalone":
                                platformSettings.compressionFormat = AudioCompressionFormat.Vorbis;
                                platformSettings.quality = 0.8f; // Higher quality for desktop
                                break;
                        }
                        
                        audioImporter.SetOverrideSampleSettings(platform, platformSettings);
                        
                        if (!configuredPlatforms.Contains(platform))
                            configuredPlatforms.Add(platform);
                    }

                    // Configure additional import settings
                    audioImporter.loadInBackground = loadInBackground;
                    
                    // Apply changes and reimport
                    audioImporter.SaveAndReimport();
                    processedClips++;
                }

                return Response.Success($"Audio compression configured for {processedClips} audio clips.", new {
                    processed_clips = processedClips,
                    target_platforms = targetPlatforms,
                    compression_format = compressionFormat,
                    quality = quality,
                    load_type = loadType.ToString(),
                    configured_platforms = configuredPlatforms.ToArray(),
                    load_in_background = loadInBackground
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error setting up audio compression: {e.Message}");
            }
        }
    }
}