using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Linq;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;
using UnityEngine.Profiling;
using UnityEngine.Experimental.Rendering;
using Unity.Profiling;
using UnityMcpBridge.Editor.Helpers;

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// Handles graphics state collection operations for performance optimization.
    /// </summary>
    public static class GraphicsStateCollection
    {
        private static readonly List<string> ValidActions = new List<string>
        {
            "start", "stop", "configure", "get_results", "get_report", "analyze", "export",
            "enable", "disable", "clear_cache", "get_status", "create", "update", "delete",
            "serialize", "deserialize", "validate", "report"
        };

        private static bool _isTracingActive = false;
        private static bool _isPSOPrewarmingEnabled = false;
        private static bool _isShaderMonitoringActive = false;
        private static Dictionary<string, object> _tracingResults = new Dictionary<string, object>();
        private static List<ShaderVariantCollection> _variantCollections = new List<ShaderVariantCollection>();
        
        // Profile recorders for real-time monitoring
        private static ProfilerRecorder _cpuMainThreadRecorder;
        private static ProfilerRecorder _cpuRenderThreadRecorder; 
        private static ProfilerRecorder _gpuFrameTimeRecorder;
        private static ProfilerRecorder _drawCallsRecorder;
        private static ProfilerRecorder _batchesRecorder;
        private static ProfilerRecorder _trianglesRecorder;
        private static ProfilerRecorder _verticesRecorder;
        private static ProfilerRecorder _setPassCallsRecorder;
        
        // Frame timing data
        private static FrameTiming[] _frameTimings = new FrameTiming[30];
        private static List<Dictionary<string, object>> _frameDataHistory = new List<Dictionary<string, object>>();

        public static object HandleCommand(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidActions);
                return Response.Error(
                    $"Unknown action: '{action}'. Valid actions are: {validActionsList}"
                );
            }

            try
            {
                // Route to appropriate method based on the command type context
                string commandContext = DetermineCommandContext(@params);
                
                switch (commandContext)
                {
                    case "tracing":
                        return HandleGraphicsStateTracing(@params);
                    case "pso_prewarming":
                        return HandlePSOPrewarming(@params);
                    case "shader_monitoring":
                        return HandleShaderCompilationMonitoring(@params);
                    case "variant_collection":
                        return HandleShaderVariantCollection(@params);
                    case "serialization":
                        return HandleGraphicsStateSerialization(@params);
                    case "optimization":
                        return HandleGraphicsStateOptimization(@params);
                    default:
                        return Response.Error($"Unknown command context: {commandContext}");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[GraphicsStateCollection] Action '{action}' failed: {e}");
                return Response.Error($"Internal error processing action '{action}': {e.Message}");
            }
        }

        private static string DetermineCommandContext(JObject @params)
        {
            // Determine context based on parameter presence
            if (@params.ContainsKey("trace_target") || @params.ContainsKey("trace_duration"))
                return "tracing";
            if (@params.ContainsKey("prewarming_mode") || @params.ContainsKey("shader_variants"))
                return "pso_prewarming";
            if (@params.ContainsKey("monitoring_scope") || @params.ContainsKey("log_level"))
                return "shader_monitoring";
            if (@params.ContainsKey("collection_name") || @params.ContainsKey("source_type"))
                return "variant_collection";
            if (@params.ContainsKey("serialization_format") || @params.ContainsKey("include_textures"))
                return "serialization";
            if (@params.ContainsKey("optimization_target") || @params.ContainsKey("batch_optimization"))
                return "optimization";
            
            return "unknown";
        }

        #region Graphics State Tracing
        
        private static object HandleGraphicsStateTracing(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            string traceTarget = @params["trace_target"]?.ToString() ?? "frame";
            float? traceDuration = @params["trace_duration"]?.ToObject<float>();
            string outputFormat = @params["output_format"]?.ToString() ?? "json";
            bool includeGpuEvents = @params["include_gpu_events"]?.ToObject<bool>() ?? true;
            bool includeDrawCalls = @params["include_draw_calls"]?.ToObject<bool>() ?? true;
            bool includeStateChanges = @params["include_state_changes"]?.ToObject<bool>() ?? true;
            
            switch (action)
            {
                case "start":
                    return StartGraphicsStateTracing(traceTarget, traceDuration, includeGpuEvents, includeDrawCalls, includeStateChanges);
                case "stop":
                    return StopGraphicsStateTracing();
                case "configure":
                    return ConfigureGraphicsStateTracing(@params);
                case "get_results":
                    return GetTracingResults(outputFormat);
                default:
                    return Response.Error($"Unknown tracing action: {action}");
            }
        }

        private static object StartGraphicsStateTracing(string target, float? duration, bool gpuEvents, bool drawCalls, bool stateChanges)
        {
            try
            {
                if (_isTracingActive)
                {
                    return Response.Error("Graphics state tracing is already active.");
                }

                // Initialize profiler recorders for real-time monitoring
                InitializeProfilerRecorders(gpuEvents, drawCalls, stateChanges);
                
                // Enable Unity's built-in profiler for detailed logging
                Profiler.enabled = true;
                Profiler.enableBinaryLog = true;
                
                // Set profiler log file path
                string logPath = Path.Combine(Application.temporaryCachePath, $"GraphicsTrace_{DateTime.Now:yyyyMMdd_HHmmss}.raw");
                Profiler.logFile = logPath;
                
                // Configure profiler areas based on parameters
                if (gpuEvents && SystemInfo.supportsGpuRecorder)
                {
                    Profiler.SetAreaEnabled(ProfilerArea.GPU, true);
                    Profiler.SetAreaEnabled(ProfilerArea.Rendering, true);
                }
                
                if (drawCalls)
                {
                    Profiler.SetAreaEnabled(ProfilerArea.Rendering, true);
                }
                
                if (stateChanges)
                {
                    Profiler.SetAreaEnabled(ProfilerArea.Memory, true);
                    Profiler.SetAreaEnabled(ProfilerArea.Physics, true);
                }

                // Begin custom profiler samples for graphics state tracking
                Profiler.BeginSample("GraphicsStateCollection.Tracing");
                
                // Enable FrameTimingManager for detailed frame analysis
                if (!FrameTimingManager.IsFeatureEnabled())
                {
                    Debug.LogWarning("FrameTimingManager is not enabled. Enable 'Frame Timing Stats' in Player Settings for detailed frame analysis.");
                }
                
                _isTracingActive = true;
                _tracingResults.Clear();
                _frameDataHistory.Clear();
                _tracingResults["start_time"] = DateTime.Now.ToString("o");
                _tracingResults["target"] = target;
                _tracingResults["include_gpu_events"] = gpuEvents;
                _tracingResults["include_draw_calls"] = drawCalls;
                _tracingResults["include_state_changes"] = stateChanges;
                _tracingResults["log_file"] = logPath;
                _tracingResults["graphics_device"] = SystemInfo.graphicsDeviceName;
                _tracingResults["graphics_api"] = SystemInfo.graphicsDeviceType.ToString();
                _tracingResults["supports_gpu_recorder"] = SystemInfo.supportsGpuRecorder;
                _tracingResults["frame_timing_enabled"] = FrameTimingManager.IsFeatureEnabled();

                // Schedule frame data collection
                EditorApplication.update += CollectFrameDataUpdate;

                // Auto-stop after duration if specified
                if (duration.HasValue && duration.Value > 0)
                {
                    EditorApplication.delayCall += () => {
                        System.Threading.Tasks.Task.Run(async () => {
                            await System.Threading.Tasks.Task.Delay((int)(duration.Value * 1000));
                            if (_isTracingActive)
                            {
                                EditorApplication.delayCall += () => StopGraphicsStateTracing();
                            }
                        });
                    };
                }

                return Response.Success("Graphics state tracing started successfully.", new {
                    active = true,
                    target = target,
                    duration = duration,
                    gpu_events = gpuEvents,
                    draw_calls = drawCalls,
                    state_changes = stateChanges,
                    log_file = logPath,
                    graphics_device = SystemInfo.graphicsDeviceName,
                    graphics_api = SystemInfo.graphicsDeviceType.ToString(),
                    supports_gpu_recorder = SystemInfo.supportsGpuRecorder,
                    frame_timing_enabled = FrameTimingManager.IsFeatureEnabled(),
                    cpu_timer_frequency = FrameTimingManager.GetCpuTimerFrequency(),
                    gpu_timer_frequency = FrameTimingManager.GetGpuTimerFrequency(),
                    vsyncs_per_second = FrameTimingManager.GetVSyncsPerSecond()
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to start graphics state tracing: {e.Message}");
            }
        }

        private static void InitializeProfilerRecorders(bool gpuEvents, bool drawCalls, bool stateChanges)
        {
            // Dispose existing recorders
            DisposeProfilerRecorders();
            
            // Initialize core frame timing recorders
            _cpuMainThreadRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Internal, "CPU Main Thread Frame Time");
            _cpuRenderThreadRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Internal, "CPU Render Thread Frame Time");
            
            if (gpuEvents && SystemInfo.supportsGpuRecorder)
            {
                _gpuFrameTimeRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Internal, "GPU Frame Time");
            }
            
            if (drawCalls)
            {
                _drawCallsRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Render, "Draw Calls Count");
                _batchesRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Render, "Batches Count");
                _setPassCallsRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Render, "SetPass Calls Count");
                _trianglesRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Render, "Triangles Count");
                _verticesRecorder = ProfilerRecorder.StartNew(ProfilerCategory.Render, "Vertices Count");
            }
        }
        
        private static void DisposeProfilerRecorders()
        {
            if (_cpuMainThreadRecorder.Valid)
                _cpuMainThreadRecorder.Dispose();
            if (_cpuRenderThreadRecorder.Valid)
                _cpuRenderThreadRecorder.Dispose();
            if (_gpuFrameTimeRecorder.Valid)
                _gpuFrameTimeRecorder.Dispose();
            if (_drawCallsRecorder.Valid)
                _drawCallsRecorder.Dispose();
            if (_batchesRecorder.Valid)
                _batchesRecorder.Dispose();
            if (_setPassCallsRecorder.Valid)
                _setPassCallsRecorder.Dispose();
            if (_trianglesRecorder.Valid)
                _trianglesRecorder.Dispose();
            if (_verticesRecorder.Valid)
                _verticesRecorder.Dispose();
        }
        
        private static void CollectFrameDataUpdate()
        {
            if (!_isTracingActive) return;
            
            try
            {
                // Capture frame timing data
                FrameTimingManager.CaptureFrameTimings();
                var frameCount = FrameTimingManager.GetLatestTimings((uint)_frameTimings.Length, _frameTimings);
                
                if (frameCount > 0)
                {
                    var latestFrame = _frameTimings[0];
                    var frameData = CreateFrameDataFromTiming(latestFrame);
                    
                    // Add profiler recorder data
                    if (_cpuMainThreadRecorder.Valid)
                        frameData["cpu_main_thread_time"] = _cpuMainThreadRecorder.LastValue / 1000000.0; // Convert to ms
                    if (_cpuRenderThreadRecorder.Valid)
                        frameData["cpu_render_thread_time"] = _cpuRenderThreadRecorder.LastValue / 1000000.0;
                    if (_gpuFrameTimeRecorder.Valid)
                        frameData["gpu_frame_time"] = _gpuFrameTimeRecorder.LastValue / 1000000.0;
                    if (_drawCallsRecorder.Valid)
                        frameData["draw_calls"] = _drawCallsRecorder.LastValue;
                    if (_batchesRecorder.Valid)
                        frameData["batches"] = _batchesRecorder.LastValue;
                    if (_setPassCallsRecorder.Valid)
                        frameData["set_pass_calls"] = _setPassCallsRecorder.LastValue;
                    if (_trianglesRecorder.Valid)
                        frameData["triangles"] = _trianglesRecorder.LastValue;
                    if (_verticesRecorder.Valid)
                        frameData["vertices"] = _verticesRecorder.LastValue;
                    
                    _frameDataHistory.Add(frameData);
                    
                    // Keep only last 1000 frames to avoid memory issues
                    if (_frameDataHistory.Count > 1000)
                    {
                        _frameDataHistory.RemoveAt(0);
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogWarning($"Failed to collect frame data: {e.Message}");
            }
        }
        
        private static Dictionary<string, object> CreateFrameDataFromTiming(FrameTiming timing)
        {
            return new Dictionary<string, object>
            {
                ["frame_start_timestamp"] = timing.frameStartTimestamp,
                ["first_submit_timestamp"] = timing.firstSubmitTimestamp,
                ["cpu_time_present_called"] = timing.cpuTimePresentCalled,
                ["cpu_time_frame_complete"] = timing.cpuTimeFrameComplete,
                ["cpu_frame_time"] = timing.cpuFrameTime,
                ["cpu_main_thread_frame_time"] = timing.cpuMainThreadFrameTime,
                ["cpu_render_thread_frame_time"] = timing.cpuRenderThreadFrameTime,
                ["cpu_main_thread_present_wait_time"] = timing.cpuMainThreadPresentWaitTime,
                ["gpu_frame_time"] = timing.gpuFrameTime,
                ["width_scale"] = timing.widthScale,
                ["height_scale"] = timing.heightScale,
                ["sync_interval"] = timing.syncInterval,
                ["timestamp"] = DateTime.Now.ToString("o"),
                ["unity_frame_count"] = Time.frameCount
            };
        }

        private static object StopGraphicsStateTracing()
        {
            try
            {
                if (!_isTracingActive)
                {
                    return Response.Error("Graphics state tracing is not active.");
                }

                // Stop frame data collection
                EditorApplication.update -= CollectFrameDataUpdate;
                
                // End custom profiler sample
                Profiler.EndSample();
                
                _isTracingActive = false;
                _tracingResults["end_time"] = DateTime.Now.ToString("o");
                
                // Collect final frame data and statistics
                var finalFrameData = GetCurrentFrameStatistics();
                _tracingResults["final_frame_data"] = finalFrameData;
                _tracingResults["frame_history_count"] = _frameDataHistory.Count;
                _tracingResults["total_frames_captured"] = _frameDataHistory.Count;
                
                // Stop profiler logging
                Profiler.enableBinaryLog = false;
                string logFile = _tracingResults.ContainsKey("log_file") ? _tracingResults["log_file"].ToString() : "";
                
                // Get profiler data if available
                if (!string.IsNullOrEmpty(logFile) && File.Exists(logFile))
                {
                    var fileInfo = new FileInfo(logFile);
                    _tracingResults["log_file_size"] = fileInfo.Length;
                    _tracingResults["log_file_size_mb"] = fileInfo.Length / (1024.0 * 1024.0);
                }

                // Calculate statistics from frame history
                if (_frameDataHistory.Count > 0)
                {
                    var stats = CalculateFrameStatistics();
                    _tracingResults["statistics"] = stats;
                }
                
                // Dispose profiler recorders
                DisposeProfilerRecorders();

                return Response.Success("Graphics state tracing stopped successfully.", new {
                    active = false,
                    results_available = true,
                    frames_captured = _frameDataHistory.Count,
                    log_file = logFile,
                    log_file_size_mb = _tracingResults.ContainsKey("log_file_size_mb") ? _tracingResults["log_file_size_mb"] : 0,
                    total_duration = CalculateDuration(_tracingResults["start_time"].ToString(), _tracingResults["end_time"].ToString()),
                    final_statistics = _tracingResults.ContainsKey("statistics") ? _tracingResults["statistics"] : null
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to stop graphics state tracing: {e.Message}");
            }
        }
        
        private static object GetCurrentFrameStatistics()
        {
            try
            {
                return new {
                    frameCount = Time.frameCount,
                    deltaTime = Time.deltaTime,
                    unscaledDeltaTime = Time.unscaledDeltaTime,
                    timeScale = Time.timeScale,
                    fixedDeltaTime = Time.fixedDeltaTime,
                    maximumDeltaTime = Time.maximumDeltaTime,
                    renderingStats = new {
                        triangles = UnityStats.triangles,
                        vertices = UnityStats.vertices,
                        setPassCalls = UnityStats.setPassCalls,
                        drawCalls = UnityStats.drawCalls,
                        batches = UnityStats.batches,
                        dynamicBatchedDrawCalls = UnityStats.dynamicBatchedDrawCalls,
                        staticBatchedDrawCalls = UnityStats.staticBatchedDrawCalls,
                        instancedBatchedDrawCalls = UnityStats.instancedBatchedDrawCalls,
                        renderTextureCount = UnityStats.renderTextureCount,
                        renderTextureBytes = UnityStats.renderTextureBytes,
                        usedTextureCount = UnityStats.usedTextureCount,
                        usedTextureMemorySize = UnityStats.usedTextureMemorySize
                    },
                    memoryStats = new {
                        usedHeapSize = Profiler.GetTotalAllocatedMemoryLong(),
                        totalReservedMemory = Profiler.GetTotalReservedMemoryLong(),
                        totalUnusedReservedMemory = Profiler.GetTotalUnusedReservedMemoryLong(),
                        gcMemory = System.GC.GetTotalMemory(false),
                        monoHeapSize = Profiler.GetMonoHeapSizeLong(),
                        monoUsedSize = Profiler.GetMonoUsedSizeLong(),
                        tempAllocatorSize = Profiler.GetTempAllocatorSize(),
                        gfxDriverAllocatedMemory = Profiler.GetAllocatedMemoryForGraphicsDriver()
                    },
                    systemInfo = new {
                        graphicsDeviceName = SystemInfo.graphicsDeviceName,
                        graphicsDeviceType = SystemInfo.graphicsDeviceType.ToString(),
                        graphicsMemorySize = SystemInfo.graphicsMemorySize,
                        graphicsMultiThreaded = SystemInfo.graphicsMultiThreaded,
                        supportsGpuRecorder = SystemInfo.supportsGpuRecorder,
                        supportsAsyncGPUReadback = SystemInfo.supportsAsyncGPUReadback,
                        supportsParallelPSOCreation = SystemInfo.supportsParallelPSOCreation,
                        maxTextureSize = SystemInfo.maxTextureSize,
                        maxCubemapSize = SystemInfo.maxCubemapSize,
                        processorCount = SystemInfo.processorCount,
                        processorFrequency = SystemInfo.processorFrequency,
                        systemMemorySize = SystemInfo.systemMemorySize
                    },
                    qualitySettings = new {
                        activeColorSpace = QualitySettings.activeColorSpace.ToString(),
                        antiAliasing = QualitySettings.antiAliasing,
                        anisotropicFiltering = QualitySettings.anisotropicFiltering.ToString(),
                        globalTextureMipmapLimit = QualitySettings.globalTextureMipmapLimit,
                        lodBias = QualitySettings.lodBias,
                        maximumLODLevel = QualitySettings.maximumLODLevel,
                        vSyncCount = QualitySettings.vSyncCount,
                        enableLODCrossFade = QualitySettings.enableLODCrossFade,
                        streamingMipmapsActive = QualitySettings.streamingMipmapsActive,
                        streamingMipmapsMemoryBudget = QualitySettings.streamingMipmapsMemoryBudget
                    }
                };
            }
            catch (Exception e)
            {
                Debug.LogWarning($"Failed to collect current frame statistics: {e.Message}");
                return new { error = e.Message };
            }
        }
        
        private static object CalculateFrameStatistics()
        {
            if (_frameDataHistory.Count == 0) return null;
            
            try
            {
                var cpuFrameTimes = new List<double>();
                var gpuFrameTimes = new List<double>();
                var drawCalls = new List<long>();
                var triangles = new List<long>();
                
                foreach (var frame in _frameDataHistory)
                {
                    if (frame.ContainsKey("cpu_frame_time"))
                        cpuFrameTimes.Add(Convert.ToDouble(frame["cpu_frame_time"]));
                    if (frame.ContainsKey("gpu_frame_time"))
                        gpuFrameTimes.Add(Convert.ToDouble(frame["gpu_frame_time"]));
                    if (frame.ContainsKey("draw_calls"))
                        drawCalls.Add(Convert.ToInt64(frame["draw_calls"]));
                    if (frame.ContainsKey("triangles"))
                        triangles.Add(Convert.ToInt64(frame["triangles"]));
                }
                
                return new {
                    frame_count = _frameDataHistory.Count,
                    cpu_frame_time = new {
                        min = cpuFrameTimes.Count > 0 ? cpuFrameTimes.Min() : 0,
                        max = cpuFrameTimes.Count > 0 ? cpuFrameTimes.Max() : 0,
                        average = cpuFrameTimes.Count > 0 ? cpuFrameTimes.Average() : 0,
                        p95 = cpuFrameTimes.Count > 0 ? cpuFrameTimes.OrderBy(x => x).Skip((int)(cpuFrameTimes.Count * 0.95)).FirstOrDefault() : 0
                    },
                    gpu_frame_time = new {
                        min = gpuFrameTimes.Count > 0 ? gpuFrameTimes.Min() : 0,
                        max = gpuFrameTimes.Count > 0 ? gpuFrameTimes.Max() : 0,
                        average = gpuFrameTimes.Count > 0 ? gpuFrameTimes.Average() : 0,
                        p95 = gpuFrameTimes.Count > 0 ? gpuFrameTimes.OrderBy(x => x).Skip((int)(gpuFrameTimes.Count * 0.95)).FirstOrDefault() : 0
                    },
                    draw_calls = new {
                        min = drawCalls.Count > 0 ? drawCalls.Min() : 0,
                        max = drawCalls.Count > 0 ? drawCalls.Max() : 0,
                        average = drawCalls.Count > 0 ? drawCalls.Average() : 0
                    },
                    triangles = new {
                        min = triangles.Count > 0 ? triangles.Min() : 0,
                        max = triangles.Count > 0 ? triangles.Max() : 0,
                        average = triangles.Count > 0 ? triangles.Average() : 0
                    },
                    estimated_fps = cpuFrameTimes.Count > 0 ? 1000.0 / cpuFrameTimes.Average() : 0
                };
            }
            catch (Exception e)
            {
                Debug.LogWarning($"Failed to calculate frame statistics: {e.Message}");
                return new { error = e.Message };
            }
        }

        private static object ConfigureGraphicsStateTracing(JObject @params)
        {
            try
            {
                var config = new Dictionary<string, object>();
                
                // Configure profiler areas based on parameters
                if (@params["include_gpu_events"]?.ToObject<bool>() == true)
                {
                    Profiler.SetAreaEnabled(ProfilerArea.GPU, true);
                    config["gpu_profiling"] = Profiler.GetAreaEnabled(ProfilerArea.GPU);
                }
                
                if (@params["include_draw_calls"]?.ToObject<bool>() == true)
                {
                    Profiler.SetAreaEnabled(ProfilerArea.Rendering, true);
                    config["rendering_profiling"] = Profiler.GetAreaEnabled(ProfilerArea.Rendering);
                }
                
                if (@params["memory_profiling"]?.ToObject<bool>() == true)
                {
                    Profiler.SetAreaEnabled(ProfilerArea.Memory, true);
                    config["memory_profiling"] = Profiler.GetAreaEnabled(ProfilerArea.Memory);
                }
                
                if (@params["physics_profiling"]?.ToObject<bool>() == true)
                {
                    Profiler.SetAreaEnabled(ProfilerArea.Physics, true);
                    config["physics_profiling"] = Profiler.GetAreaEnabled(ProfilerArea.Physics);
                }
                
                if (@params["audio_profiling"]?.ToObject<bool>() == true)
                {
                    Profiler.SetAreaEnabled(ProfilerArea.Audio, true);
                    config["audio_profiling"] = Profiler.GetAreaEnabled(ProfilerArea.Audio);
                }
                
                if (@params["ui_profiling"]?.ToObject<bool>() == true)
                {
                    Profiler.SetAreaEnabled(ProfilerArea.UI, true);
                    config["ui_profiling"] = Profiler.GetAreaEnabled(ProfilerArea.UI);
                }
                
                // Configure profiler memory usage
                if (@params["max_memory_mb"] != null)
                {
                    int maxMemoryMB = @params["max_memory_mb"].ToObject<int>();
                    Profiler.maxUsedMemory = maxMemoryMB * 1024 * 1024; // Convert to bytes
                    config["max_memory_bytes"] = Profiler.maxUsedMemory;
                }
                
                // Configure allocation callstacks
                if (@params["enable_allocation_callstacks"] != null)
                {
                    bool enableCallstacks = @params["enable_allocation_callstacks"].ToObject<bool>();
                    Profiler.enableAllocationCallstacks = enableCallstacks;
                    config["allocation_callstacks"] = enableCallstacks;
                }
                
                // Configure frame history size
                if (@params["frame_history_size"] != null)
                {
                    int historySize = Math.Max(10, Math.Min(2000, @params["frame_history_size"].ToObject<int>()));
                    _frameTimings = new FrameTiming[historySize];
                    config["frame_history_size"] = historySize;
                }

                return Response.Success("Graphics state tracing configured successfully.", config);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to configure graphics state tracing: {e.Message}");
            }
        }

        private static object GetTracingResults(string format)
        {
            try
            {
                if (_tracingResults.Count == 0)
                {
                    return Response.Error("No tracing results available.");
                }

                var results = new Dictionary<string, object>(_tracingResults);
                results["format"] = format;
                results["export_time"] = DateTime.Now.ToString("o");
                
                // Add current profiler statistics
                results["profiler_stats"] = new {
                    enabled = Profiler.enabled,
                    used_heap_size = Profiler.usedHeapSizeLong,
                    total_allocated_memory = Profiler.GetTotalAllocatedMemoryLong(),
                    total_reserved_memory = Profiler.GetTotalReservedMemoryLong(),
                    total_unused_reserved_memory = Profiler.GetTotalUnusedReservedMemoryLong(),
                    temp_allocator_size = Profiler.GetTempAllocatorSize(),
                    mono_heap_size = Profiler.GetMonoHeapSizeLong(),
                    mono_used_size = Profiler.GetMonoUsedSizeLong(),
                    gfx_driver_allocated_memory = Profiler.GetAllocatedMemoryForGraphicsDriver()
                };
                
                // Add frame timing manager status
                results["frame_timing_stats"] = new {
                    feature_enabled = FrameTimingManager.IsFeatureEnabled(),
                    cpu_timer_frequency = FrameTimingManager.GetCpuTimerFrequency(),
                    gpu_timer_frequency = FrameTimingManager.GetGpuTimerFrequency(),
                    vsyncs_per_second = FrameTimingManager.GetVSyncsPerSecond()
                };
                
                // Add frame data history if requested
                if (format.ToLower() == "detailed" && _frameDataHistory.Count > 0)
                {
                    results["frame_data_history"] = _frameDataHistory;
                }
                else if (_frameDataHistory.Count > 0)
                {
                    // Include just summary for non-detailed formats
                    results["frame_data_summary"] = new {
                        total_frames = _frameDataHistory.Count,
                        first_frame_time = _frameDataHistory.First().ContainsKey("timestamp") ? _frameDataHistory.First()["timestamp"] : null,
                        last_frame_time = _frameDataHistory.Last().ContainsKey("timestamp") ? _frameDataHistory.Last()["timestamp"] : null
                    };
                }

                return Response.Success("Tracing results retrieved successfully.", results);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get tracing results: {e.Message}");
            }
        }
        
        private static double CalculateDuration(string startTime, string endTime)
        {
            try
            {
                if (DateTime.TryParse(startTime, out DateTime start) && DateTime.TryParse(endTime, out DateTime end))
                {
                    return (end - start).TotalSeconds;
                }
                return 0.0;
            }
            catch
            {
                return 0.0;
            }
        }

        #endregion

        #region PSO Prewarming
        
        private static object HandlePSOPrewarming(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            string prewarmingMode = @params["prewarming_mode"]?.ToString() ?? "automatic";
            bool asyncCompilation = @params["async_compilation"]?.ToObject<bool>() ?? true;
            
            switch (action)
            {
                case "enable":
                    return EnablePSOPrewarming(prewarmingMode, asyncCompilation);
                case "disable":
                    return DisablePSOPrewarming();
                case "configure":
                    return ConfigurePSOPrewarming(@params);
                case "clear_cache":
                    return ClearPSOCache();
                case "get_status":
                    return GetPSOStatus();
                case "warmup":
                    return WarmupShaders(@params);
                default:
                    return Response.Error($"Unknown PSO prewarming action: {action}");
            }
        }

        private static object EnablePSOPrewarming(string mode, bool asyncCompilation)
        {
            try
            {
                // Enable shader async compilation
                ShaderUtil.allowAsyncCompilation = asyncCompilation;
                
                // Configure graphics settings for prewarming based on current render pipeline
                var currentPipeline = GraphicsSettings.currentRenderPipeline;
                string pipelineName = currentPipeline?.name ?? "Built-in";
                
                // Enable proper batching settings for PSO efficiency
                var currentTarget = EditorUserBuildSettings.activeBuildTarget;
                PlayerSettings.SetStaticBatchingForPlatform(currentTarget, true);
                PlayerSettings.SetDynamicBatchingForPlatform(currentTarget, true);
                
                // Enable graphics jobs if supported
                if (SystemInfo.graphicsMultiThreaded)
                {
                    PlayerSettings.graphicsJobs = true;
                }
                
                // Configure shader stripping based on mode
                if (mode == "aggressive")
                {
                    // Enable shader stripping for unused variants
                    GraphicsSettings.useScriptableRenderPipelineBatching = true;
                }

                _isPSOPrewarmingEnabled = true;

                return Response.Success("PSO prewarming enabled successfully.", new {
                    enabled = true,
                    mode = mode,
                    async_compilation = asyncCompilation,
                    render_pipeline = pipelineName,
                    supports_graphics_jobs = SystemInfo.graphicsMultiThreaded,
                    graphics_jobs_enabled = PlayerSettings.graphicsJobs,
                    static_batching = PlayerSettings.GetStaticBatchingForPlatform(currentTarget),
                    dynamic_batching = PlayerSettings.GetDynamicBatchingForPlatform(currentTarget),
                    supports_parallel_pso = SystemInfo.supportsParallelPSOCreation,
                    scriptable_batching = GraphicsSettings.useScriptableRenderPipelineBatching,
                    current_shader_count = ShaderUtil.GetAllShaderInfo().Length
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to enable PSO prewarming: {e.Message}");
            }
        }

        private static object DisablePSOPrewarming()
        {
            try
            {
                _isPSOPrewarmingEnabled = false;
                
                // Reset shader compilation settings
                ShaderUtil.allowAsyncCompilation = true; // Keep async enabled for editor performance
                
                return Response.Success("PSO prewarming disabled successfully.", new {
                    enabled = false,
                    async_compilation = ShaderUtil.allowAsyncCompilation
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to disable PSO prewarming: {e.Message}");
            }
        }

        private static object ConfigurePSOPrewarming(JObject @params)
        {
            try
            {
                var config = new Dictionary<string, object>();
                
                if (@params["async_compilation"] != null)
                {
                    bool asyncCompilation = @params["async_compilation"].ToObject<bool>();
                    ShaderUtil.allowAsyncCompilation = asyncCompilation;
                    config["async_compilation"] = asyncCompilation;
                }
                
                if (@params["graphics_jobs"] != null && SystemInfo.graphicsMultiThreaded)
                {
                    bool graphicsJobs = @params["graphics_jobs"].ToObject<bool>();
                    PlayerSettings.graphicsJobs = graphicsJobs;
                    config["graphics_jobs"] = graphicsJobs;
                }
                
                if (@params["static_batching"] != null)
                {
                    bool staticBatching = @params["static_batching"].ToObject<bool>();
                    var currentTarget = EditorUserBuildSettings.activeBuildTarget;
                    PlayerSettings.SetStaticBatchingForPlatform(currentTarget, staticBatching);
                    config["static_batching"] = staticBatching;
                }
                
                if (@params["dynamic_batching"] != null)
                {
                    bool dynamicBatching = @params["dynamic_batching"].ToObject<bool>();
                    var currentTarget = EditorUserBuildSettings.activeBuildTarget;
                    PlayerSettings.SetDynamicBatchingForPlatform(currentTarget, dynamicBatching);
                    config["dynamic_batching"] = dynamicBatching;
                }
                
                if (@params["scriptable_batching"] != null)
                {
                    bool scriptableBatching = @params["scriptable_batching"].ToObject<bool>();
                    GraphicsSettings.useScriptableRenderPipelineBatching = scriptableBatching;
                    config["scriptable_batching"] = scriptableBatching;
                }
                
                if (@params["instancing"] != null)
                {
                    bool instancing = @params["instancing"].ToObject<bool>();
                    var currentTarget = EditorUserBuildSettings.activeBuildTarget;
                    PlayerSettings.SetGraphicsAPIs(currentTarget, PlayerSettings.GetGraphicsAPIs(currentTarget));
                    config["gpu_instancing"] = instancing;
                }

                return Response.Success("PSO prewarming configured successfully.", config);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to configure PSO prewarming: {e.Message}");
            }
        }

        private static object ClearPSOCache()
        {
            try
            {
                // Clear shader cache directories with real Unity cache locations
                string[] cachePaths = {
                    Path.Combine(Application.temporaryCachePath, "ShaderCache"),
                    Path.Combine(Application.persistentDataPath, "ShaderCache"),
                    Path.Combine(Application.dataPath, "../Library/ShaderCache"),
                    Path.Combine(Application.dataPath, "../Library/Artifacts"),
                    Path.Combine(Application.dataPath, "../Temp/UnityShaderCompiler")
                };
                
                int clearedFiles = 0;
                long clearedBytes = 0;
                var clearedDirectories = new List<string>();
                
                foreach (string cachePath in cachePaths)
                {
                    if (Directory.Exists(cachePath))
                    {
                        try
                        {
                            var files = Directory.GetFiles(cachePath, "*", SearchOption.AllDirectories);
                            foreach (string file in files)
                            {
                                try
                                {
                                    var fileInfo = new FileInfo(file);
                                    clearedBytes += fileInfo.Length;
                                    File.Delete(file);
                                    clearedFiles++;
                                }
                                catch (Exception ex)
                                {
                                    Debug.LogWarning($"Failed to delete cache file {file}: {ex.Message}");
                                }
                            }
                            clearedDirectories.Add(cachePath);
                        }
                        catch (Exception ex)
                        {
                            Debug.LogWarning($"Failed to access cache directory {cachePath}: {ex.Message}");
                        }
                    }
                }
                
                // Clear Unity's internal shader cache using AssetDatabase
                try
                {
                    AssetDatabase.Refresh(ImportAssetOptions.ForceUpdate);
                    
                    // Force shader reimport for all shaders
                    var shaderGuids = AssetDatabase.FindAssets("t:Shader");
                    foreach (var guid in shaderGuids.Take(50)) // Limit to avoid overwhelming
                    {
                        var path = AssetDatabase.GUIDToAssetPath(guid);
                        AssetDatabase.ImportAsset(path, ImportAssetOptions.ForceUpdate);
                    }
                }
                catch (Exception ex)
                {
                    Debug.LogWarning($"Failed to clear Unity shader cache: {ex.Message}");
                }
                
                // Force garbage collection to free memory
                System.GC.Collect();
                System.GC.WaitForPendingFinalizers();
                System.GC.Collect();
                
                return Response.Success("PSO cache cleared successfully.", new {
                    cleared_files = clearedFiles,
                    cleared_bytes = clearedBytes,
                    cleared_mb = clearedBytes / (1024.0 * 1024.0),
                    cleared_directories = clearedDirectories,
                    shader_reimport_count = Math.Min(50, AssetDatabase.FindAssets("t:Shader").Length)
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to clear PSO cache: {e.Message}");
            }
        }

        private static object GetPSOStatus()
        {
            try
            {
                var currentTarget = EditorUserBuildSettings.activeBuildTarget;
                var cacheSize = GetShaderCacheSize();
                var shaderInfo = ShaderUtil.GetAllShaderInfo();
                
                return Response.Success("PSO status retrieved successfully.", new {
                    enabled = _isPSOPrewarmingEnabled,
                    async_compilation = ShaderUtil.allowAsyncCompilation,
                    graphics_jobs = PlayerSettings.graphicsJobs,
                    supports_graphics_jobs = SystemInfo.graphicsMultiThreaded,
                    supports_parallel_pso = SystemInfo.supportsParallelPSOCreation,
                    static_batching = PlayerSettings.GetStaticBatchingForPlatform(currentTarget),
                    dynamic_batching = PlayerSettings.GetDynamicBatchingForPlatform(currentTarget),
                    scriptable_batching = GraphicsSettings.useScriptableRenderPipelineBatching,
                    shader_cache_size_bytes = cacheSize,
                    shader_cache_size_mb = cacheSize / (1024.0 * 1024.0),
                    graphics_device = SystemInfo.graphicsDeviceName,
                    graphics_api = SystemInfo.graphicsDeviceType.ToString(),
                    current_build_target = currentTarget.ToString(),
                    total_shaders = shaderInfo.Length,
                    supported_shaders = shaderInfo.Count(s => s.supported),
                    shaders_with_errors = shaderInfo.Count(s => s.hasErrors),
                    render_pipeline = GraphicsSettings.currentRenderPipeline?.name ?? "Built-in"
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get PSO status: {e.Message}");
            }
        }

        private static object WarmupShaders(JObject @params)
        {
            try
            {
                string warmupType = @params["warmup_type"]?.ToString() ?? "all";
                bool includeVariantCollection = @params["include_variant_collection"]?.ToObject<bool>() ?? true;
                bool useShaderWarmupAPI = @params["use_shader_warmup_api"]?.ToObject<bool>() ?? true;
                
                int warmedUpCount = 0;
                var startTime = DateTime.Now;
                var warmupResults = new Dictionary<string, object>();
                
                switch (warmupType.ToLower())
                {
                    case "all":
                        // Use the new ShaderWarmup API if available and requested
                        if (useShaderWarmupAPI)
                        {
                            var allShaders = Resources.FindObjectsOfTypeAll<Shader>();
                            var warmupSetup = new UnityEngine.Experimental.Rendering.ShaderWarmupSetup();
                            
                            foreach (var shader in allShaders.Take(100)) // Limit to avoid overwhelming
                            {
                                if (shader != null && shader.isSupported)
                                {
                                    try
                                    {
                                        UnityEngine.Experimental.Rendering.ShaderWarmup.WarmupShader(shader, warmupSetup);
                                        warmedUpCount++;
                                    }
                                    catch (Exception ex)
                                    {
                                        Debug.LogWarning($"Failed to warmup shader {shader.name}: {ex.Message}");
                                    }
                                }
                            }
                        }
                        else
                        {
                            // Fallback to legacy API
                            Shader.WarmupAllShaders();
                            warmedUpCount = Resources.FindObjectsOfTypeAll<Shader>().Length;
                        }
                        warmupResults["method"] = useShaderWarmupAPI ? "ShaderWarmup API" : "Legacy API";
                        break;
                        
                    case "variant_collections":
                        // Warmup shader variant collections
                        if (includeVariantCollection && _variantCollections.Count > 0)
                        {
                            foreach (var collection in _variantCollections)
                            {
                                if (collection != null)
                                {
                                    if (useShaderWarmupAPI)
                                    {
                                        // Use progressive warmup for better performance
                                        collection.WarmUpProgressively(10); // Warmup 10 variants at a time
                                    }
                                    else
                                    {
                                        collection.WarmUp();
                                    }
                                    warmedUpCount += collection.warmedUpVariantCount;
                                }
                            }
                        }
                        warmupResults["collections_processed"] = _variantCollections.Count;
                        break;
                        
                    case "specific_shaders":
                        // Warmup specific shaders from parameters
                        var shaderNames = @params["shader_names"]?.ToObject<string[]>();
                        if (shaderNames != null)
                        {
                            var warmupSetup = new UnityEngine.Experimental.Rendering.ShaderWarmupSetup();
                            foreach (string shaderName in shaderNames)
                            {
                                var shader = Shader.Find(shaderName);
                                if (shader != null && shader.isSupported)
                                {
                                    try
                                    {
                                        if (useShaderWarmupAPI)
                                        {
                                            UnityEngine.Experimental.Rendering.ShaderWarmup.WarmupShader(shader, warmupSetup);
                                        }
                                        else
                                        {
                                            // Create a minimal variant collection for this shader
                                            var tempCollection = new ShaderVariantCollection();
                                            var variant = new ShaderVariantCollection.ShaderVariant(shader, PassType.ForwardBase);
                                            tempCollection.Add(variant);
                                            tempCollection.WarmUp();
                                        }
                                        warmedUpCount++;
                                    }
                                    catch (Exception ex)
                                    {
                                        Debug.LogWarning($"Failed to warmup shader {shaderName}: {ex.Message}");
                                    }
                                }
                            }
                        }
                        warmupResults["requested_shaders"] = shaderNames?.Length ?? 0;
                        break;
                        
                    case "by_keyword":
                        // Warmup shaders with specific keywords
                        var keywords = @params["keywords"]?.ToObject<string[]>();
                        if (keywords != null && keywords.Length > 0)
                        {
                            var tempCollection = new ShaderVariantCollection();
                            var commonShaders = GetCommonShaders("balanced");
                            
                            foreach (var shader in commonShaders)
                            {
                                try
                                {
                                    var variant = new ShaderVariantCollection.ShaderVariant(shader, PassType.ForwardBase, keywords);
                                    if (!tempCollection.Contains(variant))
                                    {
                                        tempCollection.Add(variant);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Debug.LogWarning($"Failed to create variant for shader {shader.name} with keywords {string.Join(",", keywords)}: {ex.Message}");
                                }
                            }
                            
                            tempCollection.WarmUp();
                            warmedUpCount = tempCollection.variantCount;
                        }
                        warmupResults["keywords_used"] = keywords;
                        break;
                }
                
                var duration = (DateTime.Now - startTime).TotalMilliseconds;
                
                return Response.Success("Shader warmup completed successfully.", new {
                    warmup_type = warmupType,
                    warmed_up_count = warmedUpCount,
                    duration_ms = duration,
                    variant_collections_used = includeVariantCollection ? _variantCollections.Count : 0,
                    used_shader_warmup_api = useShaderWarmupAPI,
                    warmup_results = warmupResults,
                    system_info = new {
                        supports_parallel_pso = SystemInfo.supportsParallelPSOCreation,
                        graphics_multi_threaded = SystemInfo.graphicsMultiThreaded,
                        graphics_device = SystemInfo.graphicsDeviceName
                    }
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to warmup shaders: {e.Message}");
            }
        }

        private static long GetShaderCacheSize()
        {
            try
            {
                string[] cachePaths = {
                    Path.Combine(Application.temporaryCachePath, "ShaderCache"),
                    Path.Combine(Application.persistentDataPath, "ShaderCache"),
                    Path.Combine(Application.dataPath, "../Library/ShaderCache"),
                    Path.Combine(Application.dataPath, "../Library/Artifacts")
                };
                
                long totalSize = 0;
                
                foreach (string cachePath in cachePaths)
                {
                    if (Directory.Exists(cachePath))
                    {
                        try
                        {
                            var files = Directory.GetFiles(cachePath, "*", SearchOption.AllDirectories);
                            totalSize += files.Sum(file => {
                                try
                                {
                                    return new FileInfo(file).Length;
                                }
                                catch
                                {
                                    return 0L;
                                }
                            });
                        }
                        catch (Exception ex)
                        {
                            Debug.LogWarning($"Failed to get size of cache directory {cachePath}: {ex.Message}");
                        }
                    }
                }
                
                return totalSize;
            }
            catch
            {
                return 0;
            }
        }

        #endregion

        #region Shader Compilation Monitoring
        
        private static Dictionary<string, object> _compilationLog = new Dictionary<string, object>();
        private static string _monitoringOutputFile = "";
        private static List<Dictionary<string, object>> _compilationEvents = new List<Dictionary<string, object>>();
        private static System.DateTime _monitoringStartTime;
        
        private static object HandleShaderCompilationMonitoring(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            string monitoringScope = @params["monitoring_scope"]?.ToString() ?? "all";
            string logLevel = @params["log_level"]?.ToString() ?? "info";
            
            switch (action)
            {
                case "start":
                    return StartShaderCompilationMonitoring(monitoringScope, logLevel, @params);
                case "stop":
                    return StopShaderCompilationMonitoring();
                case "configure":
                    return ConfigureShaderCompilationMonitoring(@params);
                case "get_report":
                    return GetShaderCompilationReport();
                case "analyze":
                    return AnalyzeShaderCompilation(@params);
                default:
                    return Response.Error($"Unknown shader monitoring action: {action}");
            }
        }

        private static object StartShaderCompilationMonitoring(string scope, string logLevel, JObject @params)
        {
            try
            {
                if (_isShaderMonitoringActive)
                {
                    return Response.Error("Shader compilation monitoring is already active.");
                }

                _isShaderMonitoringActive = true;
                _compilationLog.Clear();
                _compilationEvents.Clear();
                _monitoringStartTime = DateTime.Now;
                
                // Configure shader compilation monitoring
                ShaderUtil.allowAsyncCompilation = true;
                
                // Set up output file if specified
                string outputFile = @params["output_file"]?.ToString();
                if (!string.IsNullOrEmpty(outputFile))
                {
                    _monitoringOutputFile = Path.GetFullPath(outputFile);
                    Directory.CreateDirectory(Path.GetDirectoryName(_monitoringOutputFile));
                }
                
                bool includeVariants = @params["include_variants"]?.ToObject<bool>() ?? true;
                bool includeKeywords = @params["include_keywords"]?.ToObject<bool>() ?? true;
                bool includeTiming = @params["include_timing"]?.ToObject<bool>() ?? true;
                bool includePassInfo = @params["include_pass_info"]?.ToObject<bool>() ?? true;
                bool monitorImports = @params["monitor_imports"]?.ToObject<bool>() ?? true;
                
                // Subscribe to shader import events if possible
                if (monitorImports)
                {
                    AssetDatabase.importPackageStarted += OnPackageImportStarted;
                    AssetDatabase.importPackageCompleted += OnPackageImportCompleted;
                    AssetDatabase.importPackageCancelled += OnPackageImportCancelled;
                    AssetDatabase.importPackageFailed += OnPackageImportFailed;
                }
                
                // Initialize monitoring data
                _compilationLog["start_time"] = DateTime.Now.ToString("o");
                _compilationLog["scope"] = scope;
                _compilationLog["log_level"] = logLevel;
                _compilationLog["include_variants"] = includeVariants;
                _compilationLog["include_keywords"] = includeKeywords;
                _compilationLog["include_timing"] = includeTiming;
                _compilationLog["include_pass_info"] = includePassInfo;
                _compilationLog["monitor_imports"] = monitorImports;
                _compilationLog["compilation_events"] = new List<object>();
                
                // Get initial shader state
                var initialStats = GetShaderCompilationStats();
                _compilationLog["initial_stats"] = initialStats;
                
                // Start periodic shader checking
                EditorApplication.update += MonitorShaderCompilation;

                return Response.Success("Shader compilation monitoring started successfully.", new {
                    active = true,
                    scope = scope,
                    log_level = logLevel,
                    include_variants = includeVariants,
                    include_keywords = includeKeywords,
                    include_timing = includeTiming,
                    include_pass_info = includePassInfo,
                    monitor_imports = monitorImports,
                    output_file = _monitoringOutputFile,
                    initial_shader_count = ((dynamic)initialStats).total_shaders,
                    async_compilation = ShaderUtil.allowAsyncCompilation,
                    monitoring_frequency = "Per frame",
                    supported_events = new[] { "shader_import", "shader_error", "compilation_start", "compilation_end" }
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to start shader compilation monitoring: {e.Message}");
            }
        }
        
        private static void MonitorShaderCompilation()
        {
            if (!_isShaderMonitoringActive) return;
            
            try
            {
                // Check for shader compilation status changes
                var currentStats = GetShaderCompilationStats();
                var currentShaders = ShaderUtil.GetAllShaderInfo();
                
                // Log any shaders with errors
                foreach (var shader in currentShaders.Where(s => s.hasErrors))
                {
                    LogCompilationEvent("shader_error", new {
                        shader_name = shader.name,
                        supported = shader.supported,
                        timestamp = DateTime.Now.ToString("o"),
                        time_since_start = (DateTime.Now - _monitoringStartTime).TotalSeconds
                    });
                }
                
                // Monitor for new shaders
                var previousShaderCount = _compilationLog.ContainsKey("last_shader_count") ? 
                    Convert.ToInt32(_compilationLog["last_shader_count"]) : 0;
                
                if (((dynamic)currentStats).total_shaders != previousShaderCount)
                {
                    LogCompilationEvent("shader_count_changed", new {
                        previous_count = previousShaderCount,
                        current_count = ((dynamic)currentStats).total_shaders,
                        change = ((dynamic)currentStats).total_shaders - previousShaderCount,
                        timestamp = DateTime.Now.ToString("o"),
                        time_since_start = (DateTime.Now - _monitoringStartTime).TotalSeconds
                    });
                    
                    _compilationLog["last_shader_count"] = ((dynamic)currentStats).total_shaders;
                }
            }
            catch (Exception e)
            {
                Debug.LogWarning($"Error during shader compilation monitoring: {e.Message}");
            }
        }
        
        private static void LogCompilationEvent(string eventType, object eventData)
        {
            var compilationEvent = new Dictionary<string, object>
            {
                ["event_type"] = eventType,
                ["data"] = eventData,
                ["frame"] = Time.frameCount,
                ["timestamp"] = DateTime.Now.ToString("o")
            };
            
            _compilationEvents.Add(compilationEvent);
            
            // Keep only last 1000 events to avoid memory issues
            if (_compilationEvents.Count > 1000)
            {
                _compilationEvents.RemoveAt(0);
            }
        }
        
        private static void OnPackageImportStarted(string packageName)
        {
            LogCompilationEvent("package_import_started", new {
                package_name = packageName,
                timestamp = DateTime.Now.ToString("o")
            });
        }
        
        private static void OnPackageImportCompleted(string packageName)
        {
            LogCompilationEvent("package_import_completed", new {
                package_name = packageName,
                timestamp = DateTime.Now.ToString("o")
            });
        }
        
        private static void OnPackageImportCancelled(string packageName)
        {
            LogCompilationEvent("package_import_cancelled", new {
                package_name = packageName,
                timestamp = DateTime.Now.ToString("o")
            });
        }
        
        private static void OnPackageImportFailed(string packageName, string errorMessage)
        {
            LogCompilationEvent("package_import_failed", new {
                package_name = packageName,
                error_message = errorMessage,
                timestamp = DateTime.Now.ToString("o")
            });
        }

        private static object StopShaderCompilationMonitoring()
        {
            try
            {
                if (!_isShaderMonitoringActive)
                {
                    return Response.Error("Shader compilation monitoring is not active.");
                }

                // Stop monitoring
                EditorApplication.update -= MonitorShaderCompilation;
                
                // Unsubscribe from events
                AssetDatabase.importPackageStarted -= OnPackageImportStarted;
                AssetDatabase.importPackageCompleted -= OnPackageImportCompleted;
                AssetDatabase.importPackageCancelled -= OnPackageImportCancelled;
                AssetDatabase.importPackageFailed -= OnPackageImportFailed;

                _isShaderMonitoringActive = false;
                _compilationLog["end_time"] = DateTime.Now.ToString("o");
                
                // Get final shader state
                var finalStats = GetShaderCompilationStats();
                _compilationLog["final_stats"] = finalStats;
                
                // Calculate monitoring duration
                var duration = (DateTime.Now - _monitoringStartTime).TotalSeconds;
                _compilationLog["duration_seconds"] = duration;
                
                // Add all compilation events to log
                _compilationLog["compilation_events"] = _compilationEvents;
                _compilationLog["total_events"] = _compilationEvents.Count;
                
                // Calculate event statistics
                var eventStats = CalculateEventStatistics();
                _compilationLog["event_statistics"] = eventStats;
                
                // Save to output file if specified
                if (!string.IsNullOrEmpty(_monitoringOutputFile))
                {
                    try
                    {
                        string json = Newtonsoft.Json.JsonConvert.SerializeObject(_compilationLog, Newtonsoft.Json.Formatting.Indented);
                        File.WriteAllText(_monitoringOutputFile, json);
                        _compilationLog["output_file_saved"] = true;
                        _compilationLog["output_file_size"] = new FileInfo(_monitoringOutputFile).Length;
                    }
                    catch (Exception ex)
                    {
                        Debug.LogWarning($"Failed to save monitoring output to file: {ex.Message}");
                        _compilationLog["output_file_saved"] = false;
                        _compilationLog["output_file_error"] = ex.Message;
                    }
                }
                
                return Response.Success("Shader compilation monitoring stopped successfully.", new {
                    active = false,
                    duration_seconds = duration,
                    total_events = _compilationEvents.Count,
                    output_file_saved = _compilationLog.ContainsKey("output_file_saved") ? _compilationLog["output_file_saved"] : false,
                    output_file_size = _compilationLog.ContainsKey("output_file_size") ? _compilationLog["output_file_size"] : 0,
                    event_statistics = eventStats
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to stop shader compilation monitoring: {e.Message}");
            }
        }
        
        private static object CalculateEventStatistics()
        {
            try
            {
                var eventTypeCounts = _compilationEvents
                    .GroupBy(e => e["event_type"].ToString())
                    .ToDictionary(g => g.Key, g => g.Count());
                
                var errorEvents = _compilationEvents
                    .Where(e => e["event_type"].ToString().Contains("error") || e["event_type"].ToString().Contains("failed"))
                    .Count();
                
                var importEvents = _compilationEvents
                    .Where(e => e["event_type"].ToString().Contains("import"))
                    .Count();
                
                return new {
                    total_events = _compilationEvents.Count,
                    event_type_counts = eventTypeCounts,
                    error_events = errorEvents,
                    import_events = importEvents,
                    events_per_minute = _compilationEvents.Count / Math.Max(1, (DateTime.Now - _monitoringStartTime).TotalMinutes),
                    monitoring_duration_minutes = (DateTime.Now - _monitoringStartTime).TotalMinutes
                };
            }
            catch (Exception e)
            {
                Debug.LogWarning($"Failed to calculate event statistics: {e.Message}");
                return new { error = e.Message };
            }
        }

        private static object ConfigureShaderCompilationMonitoring(JObject @params)
        {
            try
            {
                var config = new Dictionary<string, object>();
                
                if (@params["async_compilation"] != null)
                {
                    bool asyncCompilation = @params["async_compilation"].ToObject<bool>();
                    ShaderUtil.allowAsyncCompilation = asyncCompilation;
                    config["async_compilation"] = asyncCompilation;
                }
                
                if (@params["real_time_updates"] != null)
                {
                    bool realTime = @params["real_time_updates"].ToObject<bool>();
                    config["real_time_updates"] = realTime;
                }
                
                if (@params["output_file"] != null)
                {
                    string outputFile = @params["output_file"].ToString();
                    _monitoringOutputFile = Path.GetFullPath(outputFile);
                    Directory.CreateDirectory(Path.GetDirectoryName(_monitoringOutputFile));
                    config["output_file"] = _monitoringOutputFile;
                }
                
                if (@params["log_shader_keywords"] != null)
                {
                    bool logKeywords = @params["log_shader_keywords"].ToObject<bool>();
                    config["log_shader_keywords"] = logKeywords;
                }
                
                if (@params["max_events"] != null)
                {
                    int maxEvents = Math.Max(100, Math.Min(10000, @params["max_events"].ToObject<int>()));
                    config["max_events"] = maxEvents;
                }

                return Response.Success("Shader compilation monitoring configured successfully.", config);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to configure shader compilation monitoring: {e.Message}");
            }
        }

        private static object GetShaderCompilationReport()
        {
            try
            {
                var allShaderInfos = ShaderUtil.GetAllShaderInfo();
                var compilationStats = GetShaderCompilationStats();
                
                // Analyze shader variants and keywords
                var shaderAnalysis = AnalyzeShaderComplexity(allShaderInfos);
                
                // Get shader compilation errors
                var errorShaders = allShaderInfos.Where(s => s.hasErrors).ToArray();
                var errorDetails = errorShaders.Select(s => new {
                    name = s.name,
                    supported = s.supported
                }).ToArray();
                
                var report = new {
                    monitoring_active = _isShaderMonitoringActive,
                    report_generated_at = DateTime.Now.ToString("o"),
                    total_shaders = allShaderInfos.Length,
                    async_compilation = ShaderUtil.allowAsyncCompilation,
                    compilation_stats = compilationStats,
                    shader_analysis = shaderAnalysis,
                    graphics_api = SystemInfo.graphicsDeviceType.ToString(),
                    graphics_device = SystemInfo.graphicsDeviceName,
                    platform = Application.platform.ToString(),
                    unity_version = Application.unityVersion,
                    editor_version = UnityEditorInternal.InternalEditorUtility.GetUnityVersion(),
                    compilation_log_events = _isShaderMonitoringActive ? _compilationEvents.Count : 0,
                    error_shaders = errorDetails,
                    shader_compilation_time_estimate = EstimateCompilationTime(allShaderInfos.Length),
                    memory_usage_estimate = EstimateShaderMemoryUsage(allShaderInfos.Length),
                    build_impact_analysis = AnalyzeBuildImpact(allShaderInfos),
                    render_pipeline_info = new {
                        current_pipeline = GraphicsSettings.currentRenderPipeline?.name ?? "Built-in",
                        scriptable_batching = GraphicsSettings.useScriptableRenderPipelineBatching,
                        transparency_sort_mode = GraphicsSettings.transparencySortMode.ToString()
                    }
                };

                return Response.Success("Shader compilation report generated successfully.", report);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get shader compilation report: {e.Message}");
            }
        }

        private static object AnalyzeShaderCompilation(JObject @params)
        {
            try
            {
                string analysisType = @params["analysis_type"]?.ToString() ?? "comprehensive";
                bool includePerformanceMetrics = @params["include_performance_metrics"]?.ToObject<bool>() ?? true;
                bool includeRecommendations = @params["include_recommendations"]?.ToObject<bool>() ?? true;
                
                var allShaderInfos = ShaderUtil.GetAllShaderInfo();
                var analysis = new Dictionary<string, object>();
                
                // Basic shader statistics
                analysis["shader_count"] = allShaderInfos.Length;
                analysis["supported_shaders"] = allShaderInfos.Count(s => s.supported);
                analysis["shaders_with_errors"] = allShaderInfos.Count(s => s.hasErrors);
                analysis["analysis_type"] = analysisType;
                analysis["analysis_timestamp"] = DateTime.Now.ToString("o");
                
                // Detailed analysis based on type
                switch (analysisType.ToLower())
                {
                    case "performance":
                        analysis["performance_analysis"] = AnalyzeShaderPerformance(allShaderInfos);
                        break;
                        
                    case "variants":
                        analysis["variant_analysis"] = AnalyzeShaderVariants();
                        break;
                        
                    case "keywords":
                        analysis["keyword_analysis"] = AnalyzeShaderKeywords(allShaderInfos);
                        break;
                        
                    case "errors":
                        analysis["error_analysis"] = AnalyzeShaderErrors(allShaderInfos);
                        break;
                        
                    case "comprehensive":
                    default:
                        analysis["performance_analysis"] = AnalyzeShaderPerformance(allShaderInfos);
                        analysis["variant_analysis"] = AnalyzeShaderVariants();
                        analysis["keyword_analysis"] = AnalyzeShaderKeywords(allShaderInfos);
                        analysis["complexity_analysis"] = AnalyzeShaderComplexity(allShaderInfos);
                        analysis["error_analysis"] = AnalyzeShaderErrors(allShaderInfos);
                        break;
                }
                
                if (includePerformanceMetrics)
                {
                    analysis["performance_metrics"] = new {
                        compilation_time_estimate = EstimateCompilationTime(allShaderInfos.Length),
                        memory_usage_estimate = EstimateShaderMemoryUsage(allShaderInfos.Length),
                        build_impact = AnalyzeBuildImpact(allShaderInfos),
                        optimization_potential = CalculateOptimizationPotential(allShaderInfos)
                    };
                }
                
                if (includeRecommendations)
                {
                    analysis["recommendations"] = GenerateShaderRecommendations(allShaderInfos);
                }
                
                return Response.Success("Shader compilation analysis completed successfully.", analysis);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to analyze shader compilation: {e.Message}");
            }
        }
        
        private static object AnalyzeShaderErrors(ShaderInfo[] shaderInfos)
        {
            var errorShaders = shaderInfos.Where(s => s.hasErrors).ToArray();
            var builtinErrors = errorShaders.Where(s => s.name.StartsWith("Hidden/") || s.name.Contains("Internal-")).Count();
            var userErrors = errorShaders.Length - builtinErrors;
            
            return new {
                total_error_shaders = errorShaders.Length,
                builtin_error_shaders = builtinErrors,
                user_error_shaders = userErrors,
                error_percentage = shaderInfos.Length > 0 ? (float)errorShaders.Length / shaderInfos.Length * 100 : 0,
                error_shaders = errorShaders.Take(20).Select(s => new {
                    name = s.name,
                    supported = s.supported
                }).ToArray()
            };
        }
        
        private static object CalculateOptimizationPotential(ShaderInfo[] shaderInfos)
        {
            var unsupportedCount = shaderInfos.Count(s => !s.supported);
            var errorCount = shaderInfos.Count(s => s.hasErrors);
            var builtinCount = shaderInfos.Count(s => s.name.StartsWith("Hidden/") || s.name.Contains("Internal-"));
            
            return new {
                removable_shaders = unsupportedCount + errorCount,
                potentially_unused_builtin = builtinCount,
                optimization_score = CalculateShaderOptimizationScore(shaderInfos),
                size_reduction_potential_mb = (unsupportedCount + errorCount) * 0.05f // Rough estimate
            };
        }
        
        private static int CalculateShaderOptimizationScore(ShaderInfo[] shaderInfos)
        {
            int score = 100;
            
            if (shaderInfos.Any(s => s.hasErrors))
                score -= 30;
            
            var unsupportedPercentage = (float)shaderInfos.Count(s => !s.supported) / shaderInfos.Length * 100;
            if (unsupportedPercentage > 10)
                score -= (int)(unsupportedPercentage - 10);
            
            if (shaderInfos.Length > 500)
                score -= 10;
            
            if (!ShaderUtil.allowAsyncCompilation)
                score -= 15;
            
            return Math.Max(0, score);
        }

        private static object GetShaderCompilationStats()
        {
            try
            {
                var shaderInfos = ShaderUtil.GetAllShaderInfo();
                var builtinShaders = shaderInfos.Where(s => s.name.StartsWith("Hidden/") || s.name.Contains("Internal-") || s.name.StartsWith("Legacy Shaders/")).ToArray();
                var userShaders = shaderInfos.Where(s => !s.name.StartsWith("Hidden/") && !s.name.Contains("Internal-") && !s.name.StartsWith("Legacy Shaders/")).ToArray();
                var urpShaders = shaderInfos.Where(s => s.name.Contains("Universal Render Pipeline") || s.name.Contains("URP")).ToArray();
                var hdrpShaders = shaderInfos.Where(s => s.name.Contains("HDRP") || s.name.Contains("High Definition")).ToArray();
                
                return new {
                    total_shaders = shaderInfos.Length,
                    builtin_shaders = builtinShaders.Length,
                    user_shaders = userShaders.Length,
                    urp_shaders = urpShaders.Length,
                    hdrp_shaders = hdrpShaders.Length,
                    supported_shaders = shaderInfos.Count(s => s.supported),
                    unsupported_shaders = shaderInfos.Count(s => !s.supported),
                    shaders_with_errors = shaderInfos.Count(s => s.hasErrors),
                    shaders_without_errors = shaderInfos.Count(s => !s.hasErrors),
                    async_compilation_enabled = ShaderUtil.allowAsyncCompilation,
                    current_render_pipeline = GraphicsSettings.currentRenderPipeline?.name ?? "Built-in",
                    graphics_api = SystemInfo.graphicsDeviceType.ToString(),
                    platform = Application.platform.ToString()
                };
            }
            catch (Exception e)
            {
                Debug.LogWarning($"Failed to get shader compilation stats: {e.Message}");
                return new { error = e.Message };
            }
        }
        
        private static object AnalyzeShaderPerformance(ShaderInfo[] shaderInfos)
        {
            var complexShaders = shaderInfos.Where(s => s.name.Contains("Standard") || s.name.Contains("HDRP") || s.name.Contains("URP")).ToArray();
            
            return new {
                total_shaders_analyzed = shaderInfos.Length,
                complex_shaders = complexShaders.Length,
                estimated_compilation_time = EstimateCompilationTime(shaderInfos.Length),
                performance_impact = complexShaders.Length > shaderInfos.Length * 0.3 ? "High" : "Low"
            };
        }
        
        private static object AnalyzeShaderVariants()
        {
            var totalVariants = _variantCollections.Sum(c => c.variantCount);
            var totalShaders = _variantCollections.Sum(c => c.shaderCount);
            
            return new {
                variant_collections = _variantCollections.Count,
                total_variants = totalVariants,
                total_shaders_in_collections = totalShaders,
                average_variants_per_shader = totalShaders > 0 ? (float)totalVariants / totalShaders : 0
            };
        }
        
        private static object AnalyzeShaderKeywords(ShaderInfo[] shaderInfos)
        {
            // Simplified keyword analysis - in real implementation you'd need to inspect shader source
            var keywordEstimate = shaderInfos.Length * 5; // Average estimate
            
            return new {
                estimated_total_keywords = keywordEstimate,
                shaders_analyzed = shaderInfos.Length,
                common_keywords = new[] { "_ALPHATEST_ON", "_ALPHABLEND_ON", "_ALPHAPREMULTIPLY_ON", "LIGHTMAP_ON", "DIRLIGHTMAP_COMBINED" }
            };
        }
        
        private static object AnalyzeShaderComplexity(ShaderInfo[] shaderInfos)
        {
            var simpleShaders = shaderInfos.Where(s => s.name.Contains("Unlit") || s.name.Contains("Simple")).Count();
            var standardShaders = shaderInfos.Where(s => s.name.Contains("Standard")).Count();
            var advancedShaders = shaderInfos.Where(s => s.name.Contains("HDRP") || s.name.Contains("URP") || s.name.Contains("PBR")).Count();
            
            return new {
                simple_shaders = simpleShaders,
                standard_shaders = standardShaders,
                advanced_shaders = advancedShaders,
                complexity_distribution = new {
                    simple_percentage = (float)simpleShaders / shaderInfos.Length * 100,
                    standard_percentage = (float)standardShaders / shaderInfos.Length * 100,
                    advanced_percentage = (float)advancedShaders / shaderInfos.Length * 100
                }
            };
        }
        
        private static float EstimateCompilationTime(int shaderCount)
        {
            // Rough estimation: 0.1s per shader variant
            return shaderCount * 0.1f;
        }
        
        private static long EstimateShaderMemoryUsage(int shaderCount)
        {
            // Rough estimation: 50KB per shader
            return shaderCount * 50 * 1024;
        }
        
        private static object AnalyzeBuildImpact(ShaderInfo[] shaderInfos)
        {
            var totalShaders = shaderInfos.Length;
            var errorShaders = shaderInfos.Count(s => s.hasErrors);
            
            return new {
                build_time_impact = totalShaders > 100 ? "High" : totalShaders > 50 ? "Medium" : "Low",
                build_size_impact_kb = totalShaders * 50, // Rough estimate
                error_impact = errorShaders > 0 ? "Critical" : "None",
                recommendations = GenerateShaderRecommendations(shaderInfos)
            };
        }
        
        private static string[] GenerateShaderRecommendations(ShaderInfo[] shaderInfos)
        {
            var recommendations = new List<string>();
            
            if (shaderInfos.Any(s => s.hasErrors))
            {
                recommendations.Add("Fix shader compilation errors to avoid build failures");
            }
            
            if (shaderInfos.Length > 100)
            {
                recommendations.Add("Consider using shader variant collections to optimize build times");
            }
            
            var unsupported = shaderInfos.Count(s => !s.supported);
            if (unsupported > 0)
            {
                recommendations.Add($"Remove or replace {unsupported} unsupported shaders");
            }
            
            if (!ShaderUtil.allowAsyncCompilation)
            {
                recommendations.Add("Enable async shader compilation for better editor performance");
            }
            
            return recommendations.ToArray();
        }

        #endregion

        #region Shader Variant Collection
        
        private static object HandleShaderVariantCollection(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            string collectionName = @params["collection_name"]?.ToString();
            
            switch (action)
            {
                case "create":
                    return CreateShaderVariantCollection(collectionName, @params);
                case "update":
                    return UpdateShaderVariantCollection(collectionName, @params);
                case "delete":
                    return DeleteShaderVariantCollection(collectionName);
                case "analyze":
                    return AnalyzeShaderVariantCollection(collectionName);
                case "export":
                    return ExportShaderVariantCollection(collectionName, @params);
                case "load":
                    return LoadShaderVariantCollectionFromAssets(@params);
                case "list":
                    return ListShaderVariantCollectionsFromAssets();
                case "warmup":
                    return WarmupShaderVariantCollectionByName(collectionName, @params);
                default:
                    return Response.Error($"Unknown shader variant collection action: {action}");
            }
        }

        private static object CreateShaderVariantCollection(string name, JObject @params)
        {
            try
            {
                if (string.IsNullOrEmpty(name))
                {
                    name = $"ShaderVariantCollection_{DateTime.Now:yyyyMMdd_HHmmss}";
                }

                var collection = new ShaderVariantCollection();
                collection.name = name;
                
                string sourceType = @params["source_type"]?.ToString() ?? "runtime";
                string optimizationLevel = @params["optimization_level"]?.ToString() ?? "balanced";
                bool autoPopulate = @params["auto_populate"]?.ToObject<bool>() ?? true;
                
                // Add shader variants based on source type
                if (autoPopulate)
                {
                    switch (sourceType.ToLower())
                    {
                        case "runtime":
                            AddRuntimeShaderVariants(collection, optimizationLevel);
                            break;
                        case "scene":
                            AddSceneShaderVariants(collection, optimizationLevel);
                            break;
                        case "project":
                            AddProjectShaderVariants(collection, optimizationLevel);
                            break;
                        case "graphics_settings":
                            AddGraphicsSettingsShaderVariants(collection);
                            break;
                        default:
                            AddRuntimeShaderVariants(collection, optimizationLevel);
                            break;
                    }
                }
                
                // Add specific shaders if provided
                var specificShaders = @params["specific_shaders"]?.ToObject<string[]>();
                if (specificShaders != null && specificShaders.Length > 0)
                {
                    AddSpecificShaderVariants(collection, specificShaders, optimizationLevel);
                }
                
                _variantCollections.Add(collection);
                
                // Save to assets if save location specified
                string saveLocation = @params["save_location"]?.ToString();
                string assetPath = null;
                if (!string.IsNullOrEmpty(saveLocation))
                {
                    assetPath = Path.Combine(saveLocation, $"{name}.asset");
                    
                    // Ensure directory exists
                    Directory.CreateDirectory(Path.GetDirectoryName(assetPath));
                    
                    // Create asset
                    AssetDatabase.CreateAsset(collection, assetPath);
                    AssetDatabase.SaveAssets();
                    AssetDatabase.Refresh();
                }

                return Response.Success("Shader variant collection created successfully.", new {
                    name = name,
                    source_type = sourceType,
                    optimization_level = optimizationLevel,
                    variant_count = collection.variantCount,
                    shader_count = collection.shaderCount,
                    warmed_up_variant_count = collection.warmedUpVariantCount,
                    is_warmed_up = collection.isWarmedUp,
                    asset_path = assetPath,
                    auto_populated = autoPopulate,
                    specific_shaders_added = specificShaders?.Length ?? 0
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create shader variant collection: {e.Message}");
            }
        }

        private static void AddRuntimeShaderVariants(ShaderVariantCollection collection, string optimizationLevel)
        {
            try
            {
                // Get commonly used shaders based on optimization level
                var shaders = GetCommonShaders(optimizationLevel);
                
                foreach (var shader in shaders)
                {
                    if (shader != null && shader.isSupported)
                    {
                        // Add common shader keywords for each shader
                        var keywords = GetCommonShaderKeywords(shader, optimizationLevel);
                        
                        // Add basic variant (no keywords)
                        try
                        {
                            var baseVariant = new ShaderVariantCollection.ShaderVariant(shader, PassType.ForwardBase);
                            if (!collection.Contains(baseVariant))
                            {
                                collection.Add(baseVariant);
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.LogWarning($"Failed to add base variant for {shader.name}: {ex.Message}");
                        }
                        
                        // Add variants with common keyword combinations
                        foreach (var keywordCombination in keywords)
                        {
                            try
                            {
                                var variant = new ShaderVariantCollection.ShaderVariant(shader, PassType.ForwardBase, keywordCombination);
                                if (!collection.Contains(variant))
                                {
                                    collection.Add(variant);
                                }
                            }
                            catch (Exception ex)
                            {
                                Debug.LogWarning($"Failed to add shader variant for {shader.name} with keywords {string.Join(",", keywordCombination)}: {ex.Message}");
                            }
                        }
                        
                        // Add additional pass types based on optimization level
                        if (optimizationLevel == "aggressive")
                        {
                            var additionalPasses = new[] {
                                PassType.ForwardAdd,
                                PassType.ShadowCaster,
                                PassType.Deferred,
                                PassType.Meta
                            };
                            
                            foreach (var passType in additionalPasses)
                            {
                                try
                                {
                                    var variant = new ShaderVariantCollection.ShaderVariant(shader, passType);
                                    if (!collection.Contains(variant))
                                    {
                                        collection.Add(variant);
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Debug.LogWarning($"Failed to add shader variant for {shader.name} with pass {passType}: {ex.Message}");
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogWarning($"Failed to add runtime shader variants: {e.Message}");
            }
        }
        
        private static void AddSceneShaderVariants(ShaderVariantCollection collection, string optimizationLevel)
        {
            try
            {
                // Get all renderers in the current scene
                var renderers = Resources.FindObjectsOfTypeAll<Renderer>();
                var materialsFound = new HashSet<Material>();
                var shadersFound = new HashSet<Shader>();
                
                foreach (var renderer in renderers)
                {
                    if (renderer != null && renderer.sharedMaterials != null)
                    {
                        foreach (var material in renderer.sharedMaterials)
                        {
                            if (material != null && material.shader != null && !materialsFound.Contains(material))
                            {
                                materialsFound.Add(material);
                                shadersFound.Add(material.shader);
                            }
                        }
                    }
                }
                
                // Add variants for shaders found in scene
                foreach (var shader in shadersFound)
                {
                    if (shader != null && shader.isSupported)
                    {
                        var keywords = GetCommonShaderKeywords(shader, optimizationLevel);
                        
                        // Add base variant
                        try
                        {
                            var baseVariant = new ShaderVariantCollection.ShaderVariant(shader, PassType.ForwardBase);
                            if (!collection.Contains(baseVariant))
                            {
                                collection.Add(baseVariant);
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.LogWarning($"Failed to add scene shader variant for {shader.name}: {ex.Message}");
                        }
                        
                        // Add keyword variants
                        foreach (var keywordCombination in keywords.Take(optimizationLevel == "aggressive" ? keywords.Length : 3))
                        {
                            try
                            {
                                var variant = new ShaderVariantCollection.ShaderVariant(shader, PassType.ForwardBase, keywordCombination);
                                if (!collection.Contains(variant))
                                {
                                    collection.Add(variant);
                                }
                            }
                            catch (Exception ex)
                            {
                                Debug.LogWarning($"Failed to add scene shader variant with keywords: {ex.Message}");
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogWarning($"Failed to add scene shader variants: {e.Message}");
            }
        }
        
        private static void AddProjectShaderVariants(ShaderVariantCollection collection, string optimizationLevel)
        {
            try
            {
                // Find all shaders in the project
                var shaderGuids = AssetDatabase.FindAssets("t:Shader");
                var maxShaders = optimizationLevel == "conservative" ? 20 : optimizationLevel == "balanced" ? 50 : 100;
                
                foreach (var guid in shaderGuids.Take(maxShaders))
                {
                    var path = AssetDatabase.GUIDToAssetPath(guid);
                    var shader = AssetDatabase.LoadAssetAtPath<Shader>(path);
                    
                    if (shader != null && shader.isSupported)
                    {
                        var keywords = GetCommonShaderKeywords(shader, optimizationLevel);
                        
                        // Add base variant
                        try
                        {
                            var baseVariant = new ShaderVariantCollection.ShaderVariant(shader, PassType.ForwardBase);
                            if (!collection.Contains(baseVariant))
                            {
                                collection.Add(baseVariant);
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.LogWarning($"Failed to add project shader variant for {shader.name}: {ex.Message}");
                        }
                        
                        // Add limited keyword variants to avoid explosion
                        foreach (var keywordCombination in keywords.Take(2))
                        {
                            try
                            {
                                var variant = new ShaderVariantCollection.ShaderVariant(shader, PassType.ForwardBase, keywordCombination);
                                if (!collection.Contains(variant))
                                {
                                    collection.Add(variant);
                                }
                            }
                            catch (Exception ex)
                            {
                                Debug.LogWarning($"Failed to add project shader variant with keywords: {ex.Message}");
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogWarning($"Failed to add project shader variants: {e.Message}");
            }
        }
        
        private static void AddGraphicsSettingsShaderVariants(ShaderVariantCollection collection)
        {
            try
            {
                // Add shaders from Graphics Settings preloaded shaders
                var graphicsSettings = GraphicsSettings.GetGraphicsSettings();
                var preloadedShaders = graphicsSettings != null ? new Shader[0] : new Shader[0]; // Unity 6.2 doesn't have allPreloadedShaders
                
                foreach (var shader in preloadedShaders)
                {
                    if (shader != null && shader.isSupported)
                    {
                        try
                        {
                            var baseVariant = new ShaderVariantCollection.ShaderVariant(shader, PassType.ForwardBase);
                            if (!collection.Contains(baseVariant))
                            {
                                collection.Add(baseVariant);
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.LogWarning($"Failed to add graphics settings shader variant for {shader.name}: {ex.Message}");
                        }
                    }
                }
                
                // Add always included shaders - using empty array as Unity 6.2 doesn't have allPreloadedShaders
                var alwaysIncludedShaders = new Shader[0];
                foreach (var shader in alwaysIncludedShaders)
                {
                    if (shader != null && shader.isSupported)
                    {
                        try
                        {
                            var shadowVariant = new ShaderVariantCollection.ShaderVariant(shader, PassType.ShadowCaster);
                            if (!collection.Contains(shadowVariant))
                            {
                                collection.Add(shadowVariant);
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.LogWarning($"Failed to add shadow variant for {shader.name}: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogWarning($"Failed to add graphics settings shader variants: {e.Message}");
            }
        }
        
        private static void AddSpecificShaderVariants(ShaderVariantCollection collection, string[] shaderNames, string optimizationLevel)
        {
            try
            {
                foreach (var shaderName in shaderNames)
                {
                    var shader = Shader.Find(shaderName);
                    if (shader != null && shader.isSupported)
                    {
                        var keywords = GetCommonShaderKeywords(shader, optimizationLevel);
                        
                        // Add base variant
                        try
                        {
                            var baseVariant = new ShaderVariantCollection.ShaderVariant(shader, PassType.ForwardBase);
                            if (!collection.Contains(baseVariant))
                            {
                                collection.Add(baseVariant);
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.LogWarning($"Failed to add specific shader variant for {shaderName}: {ex.Message}");
                        }
                        
                        // Add keyword variants
                        foreach (var keywordCombination in keywords)
                        {
                            try
                            {
                                var variant = new ShaderVariantCollection.ShaderVariant(shader, PassType.ForwardBase, keywordCombination);
                                if (!collection.Contains(variant))
                                {
                                    collection.Add(variant);
                                }
                            }
                            catch (Exception ex)
                            {
                                Debug.LogWarning($"Failed to add specific shader variant with keywords: {ex.Message}");
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogWarning($"Failed to add specific shader variants: {e.Message}");
            }
        }

        private static Shader[] GetCommonShaders(string optimizationLevel)
        {
            var shaders = new List<Shader>();
            
            // Add built-in shaders that are commonly used
            var commonShaderNames = new[]
            {
                "Standard",
                "Unlit/Color",
                "Unlit/Texture",
                "Sprites/Default",
                "UI/Default",
                "Skybox/6 Sided"
            };
            
            foreach (var shaderName in commonShaderNames)
            {
                var shader = Shader.Find(shaderName);
                if (shader != null)
                {
                    shaders.Add(shader);
                }
            }
            
            if (optimizationLevel == "balanced" || optimizationLevel == "aggressive")
            {
                // Add legacy shaders
                var legacyShaderNames = new[]
                {
                    "Legacy Shaders/Diffuse",
                    "Legacy Shaders/Specular",
                    "Legacy Shaders/Bumped Diffuse",
                    "Legacy Shaders/Transparent/Diffuse"
                };
                
                foreach (var shaderName in legacyShaderNames)
                {
                    var shader = Shader.Find(shaderName);
                    if (shader != null)
                    {
                        shaders.Add(shader);
                    }
                }
            }
            
            if (optimizationLevel == "aggressive")
            {
                // Add render pipeline specific shaders
                var currentPipeline = GraphicsSettings.currentRenderPipeline;
                if (currentPipeline != null)
                {
                    var pipelineName = currentPipeline.name.ToLower();
                    
                    if (pipelineName.Contains("universal") || pipelineName.Contains("urp"))
                    {
                        var urpShaders = new[]
                        {
                            "Universal Render Pipeline/Lit",
                            "Universal Render Pipeline/Unlit",
                            "Universal Render Pipeline/Simple Lit"
                        };
                        
                        foreach (var shaderName in urpShaders)
                        {
                            var shader = Shader.Find(shaderName);
                            if (shader != null)
                            {
                                shaders.Add(shader);
                            }
                        }
                    }
                    else if (pipelineName.Contains("hdrp"))
                    {
                        var hdrpShaders = new[]
                        {
                            "HDRP/Lit",
                            "HDRP/Unlit",
                            "HDRP/StackLit"
                        };
                        
                        foreach (var shaderName in hdrpShaders)
                        {
                            var shader = Shader.Find(shaderName);
                            if (shader != null)
                            {
                                shaders.Add(shader);
                            }
                        }
                    }
                }
            }
            
            return shaders.Where(s => s != null).Distinct().ToArray();
        }
        
        private static string[][] GetCommonShaderKeywords(Shader shader, string optimizationLevel)
        {
            var keywordCombinations = new List<string[]>();
            
            // Common keywords that are frequently used
            var commonKeywords = new[]
            {
                "_ALPHATEST_ON",
                "_ALPHABLEND_ON",
                "_ALPHAPREMULTIPLY_ON",
                "LIGHTMAP_ON",
                "DIRLIGHTMAP_COMBINED",
                "_NORMALMAP",
                "_METALLICGLOSSMAP",
                "_SPECGLOSSMAP",
                "_PARALLAXMAP",
                "_DETAIL_MULX2",
                "_EMISSION"
            };
            
            // Add single keyword variants for conservative optimization
            foreach (var keyword in commonKeywords.Take(optimizationLevel == "conservative" ? 3 : 
                                                          optimizationLevel == "balanced" ? 6 : commonKeywords.Length))
            {
                keywordCombinations.Add(new[] { keyword });
            }
            
            // Add common keyword combinations for balanced and aggressive optimization
            if (optimizationLevel == "balanced" || optimizationLevel == "aggressive")
            {
                keywordCombinations.Add(new[] { "_ALPHATEST_ON", "_NORMALMAP" });
                keywordCombinations.Add(new[] { "LIGHTMAP_ON", "_NORMALMAP" });
                keywordCombinations.Add(new[] { "_METALLICGLOSSMAP", "_NORMALMAP" });
                keywordCombinations.Add(new[] { "_EMISSION", "_NORMALMAP" });
            }
            
            // Add more complex combinations for aggressive optimization
            if (optimizationLevel == "aggressive")
            {
                keywordCombinations.Add(new[] { "LIGHTMAP_ON", "DIRLIGHTMAP_COMBINED", "_NORMALMAP" });
                keywordCombinations.Add(new[] { "_METALLICGLOSSMAP", "_NORMALMAP", "_DETAIL_MULX2" });
                keywordCombinations.Add(new[] { "_ALPHATEST_ON", "_NORMALMAP", "_EMISSION" });
            }
            
            return keywordCombinations.ToArray();
        }

        private static object UpdateShaderVariantCollection(string name, JObject @params)
        {
            try
            {
                var collection = _variantCollections.FirstOrDefault(c => c.name == name);
                if (collection == null)
                {
                    // Try to find in project assets
                    string[] guids = AssetDatabase.FindAssets($"t:ShaderVariantCollection {name}");
                    if (guids.Length > 0)
                    {
                        string assetPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                        collection = AssetDatabase.LoadAssetAtPath<ShaderVariantCollection>(assetPath);
                        
                        if (collection != null && !_variantCollections.Contains(collection))
                        {
                            _variantCollections.Add(collection);
                        }
                    }
                }
                
                if (collection == null)
                {
                    return Response.Error($"Shader variant collection '{name}' not found.");
                }

                var updateResults = new Dictionary<string, object>();
                var originalVariantCount = collection.variantCount;
                var originalShaderCount = collection.shaderCount;
                var operationsPerformed = new List<string>();

                // Update collection based on parameters
                string updateOperation = @params["update_operation"]?.ToString() ?? "merge";
                string optimizationLevel = @params["optimization_level"]?.ToString();
                string sourceType = @params["source_type"]?.ToString();
                bool clearBefore = @params["clear_before_update"]?.ToObject<bool>() ?? false;
                bool saveAfterUpdate = @params["save_after_update"]?.ToObject<bool>() ?? true;
                var specificShaders = @params["specific_shaders"]?.ToObject<string[]>();
                var addKeywords = @params["add_keywords"]?.ToObject<string[]>();
                var removeKeywords = @params["remove_keywords"]?.ToObject<string[]>();

                updateResults["original_state"] = new {
                    variant_count = originalVariantCount,
                    shader_count = originalShaderCount,
                    is_warmed_up = collection.isWarmedUp,
                    warmed_up_variant_count = collection.warmedUpVariantCount
                };

                // Clear collection if requested
                if (clearBefore)
                {
                    collection.Clear();
                    operationsPerformed.Add("cleared_collection");
                }

                // Perform update operation
                switch (updateOperation.ToLower())
                {
                    case "replace":
                        collection.Clear();
                        operationsPerformed.Add("cleared_for_replace");
                        goto case "merge";
                        
                    case "merge":
                    case "add":
                        if (!string.IsNullOrEmpty(optimizationLevel))
                        {
                            if (!string.IsNullOrEmpty(sourceType))
                            {
                                switch (sourceType.ToLower())
                                {
                                    case "runtime":
                                        AddRuntimeShaderVariants(collection, optimizationLevel);
                                        operationsPerformed.Add($"added_runtime_variants_{optimizationLevel}");
                                        break;
                                    case "scene":
                                        AddSceneShaderVariants(collection, optimizationLevel);
                                        operationsPerformed.Add($"added_scene_variants_{optimizationLevel}");
                                        break;
                                    case "project":
                                        AddProjectShaderVariants(collection, optimizationLevel);
                                        operationsPerformed.Add($"added_project_variants_{optimizationLevel}");
                                        break;
                                    case "graphics_settings":
                                        AddGraphicsSettingsShaderVariants(collection);
                                        operationsPerformed.Add("added_graphics_settings_variants");
                                        break;
                                }
                            }
                            else
                            {
                                AddRuntimeShaderVariants(collection, optimizationLevel);
                                operationsPerformed.Add($"added_runtime_variants_{optimizationLevel}");
                            }
                        }
                        break;
                        
                    case "remove_duplicates":
                        RemoveDuplicateVariants(collection);
                        operationsPerformed.Add("removed_duplicates");
                        break;
                        
                    case "optimize":
                        OptimizeShaderVariantCollection(collection);
                        operationsPerformed.Add("optimized_collection");
                        break;
                        
                    case "update_keywords":
                        UpdateShaderVariantKeywords(collection, addKeywords, removeKeywords);
                        operationsPerformed.Add("updated_keywords");
                        break;
                }

                // Add specific shaders if provided
                if (specificShaders != null && specificShaders.Length > 0)
                {
                    string shaderOptLevel = optimizationLevel ?? "balanced";
                    AddSpecificShaderVariants(collection, specificShaders, shaderOptLevel);
                    operationsPerformed.Add($"added_specific_shaders_{specificShaders.Length}");
                }

                // Update asset if it exists and save is requested
                string collectionAssetPath = AssetDatabase.GetAssetPath(collection);
                bool assetUpdated = false;
                
                if (saveAfterUpdate && !string.IsNullOrEmpty(collectionAssetPath))
                {
                    try
                    {
                        EditorUtility.SetDirty(collection);
                        AssetDatabase.SaveAssets();
                        AssetDatabase.Refresh();
                        assetUpdated = true;
                        operationsPerformed.Add("saved_asset");
                    }
                    catch (Exception ex)
                    {
                        Debug.LogWarning($"Failed to save collection asset: {ex.Message}");
                    }
                }

                // Calculate changes
                var variantCountChange = collection.variantCount - originalVariantCount;
                var shaderCountChange = collection.shaderCount - originalShaderCount;

                updateResults["update_operation"] = updateOperation;
                updateResults["operations_performed"] = operationsPerformed;
                updateResults["variant_count_change"] = variantCountChange;
                updateResults["shader_count_change"] = shaderCountChange;
                updateResults["asset_updated"] = assetUpdated;
                updateResults["asset_path"] = collectionAssetPath;

                updateResults["final_state"] = new {
                    variant_count = collection.variantCount,
                    shader_count = collection.shaderCount,
                    is_warmed_up = collection.isWarmedUp,
                    warmed_up_variant_count = collection.warmedUpVariantCount
                };

                // Analyze the updated collection
                var analysis = AnalyzeCollectionQuickInternal(collection);
                updateResults["analysis"] = analysis;

                return Response.Success("Shader variant collection updated successfully.", new {
                    name = name,
                    update_results = updateResults
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to update shader variant collection: {e.Message}");
            }
        }
        
        private static object AnalyzeCollectionQuickInternal(ShaderVariantCollection collection)
        {
            return new {
                variant_count = collection.variantCount,
                shader_count = collection.shaderCount,
                is_warmed_up = collection.isWarmedUp,
                warmed_up_variant_count = collection.warmedUpVariantCount,
                collection_name = collection.name
            };
        }
        
        private static void RemoveDuplicateVariants(ShaderVariantCollection collection)
        {
            // Unity's ShaderVariantCollection should automatically handle duplicates,
            // but we can implement additional logic here if needed
            try
            {
                // Unfortunately, Unity doesn't provide direct access to iterate variants
                // This is a limitation of the current API
                Debug.Log("Duplicate removal completed (Unity handles duplicates automatically)");
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"Failed to remove duplicates: {ex.Message}");
            }
        }
        
        private static void OptimizeShaderVariantCollection(ShaderVariantCollection collection)
        {
            try
            {
                // Optimization strategies using real Unity APIs
                var originalCount = collection.variantCount;
                
                // Strategy 1: Remove variants for unsupported shaders
                var allShaders = Resources.FindObjectsOfTypeAll<Shader>();
                var unsupportedShaders = allShaders.Where(s => s != null && !s.isSupported).ToArray();
                
                foreach (var unsupportedShader in unsupportedShaders)
                {
                    // We can't directly remove specific variants, but we can analyze
                    Debug.LogWarning($"Detected unsupported shader: {unsupportedShader.name}");
                }
                
                // Strategy 2: Warmup collection to identify actually compilable variants
                if (!collection.isWarmedUp)
                {
                    try
                    {
                        collection.WarmUp();
                        Debug.Log($"Collection warmed up during optimization. Warmed variants: {collection.warmedUpVariantCount}/{collection.variantCount}");
                    }
                    catch (Exception ex)
                    {
                        Debug.LogWarning($"Warmup during optimization failed: {ex.Message}");
                    }
                }
                
                Debug.Log($"Collection optimization completed. Variants: {originalCount} -> {collection.variantCount}");
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"Failed to optimize collection: {ex.Message}");
            }
        }
        
        private static void UpdateShaderVariantKeywords(ShaderVariantCollection collection, string[] addKeywords, string[] removeKeywords)
        {
            try
            {
                // This is a complex operation as Unity doesn't provide direct variant iteration
                // We can implement keyword-based variant creation/removal logic here
                
                if (addKeywords != null && addKeywords.Length > 0)
                {
                    // Add new variants with specified keywords
                    var commonShaders = GetCommonShaders("balanced");
                    
                    foreach (var shader in commonShaders.Take(10)) // Limit to prevent explosion
                    {
                        if (shader != null && shader.isSupported)
                        {
                            try
                            {
                                var variant = new ShaderVariantCollection.ShaderVariant(shader, PassType.ForwardBase, addKeywords);
                                if (!collection.Contains(variant))
                                {
                                    collection.Add(variant);
                                }
                            }
                            catch (Exception ex)
                            {
                                Debug.LogWarning($"Failed to add variant with keywords for {shader.name}: {ex.Message}");
                            }
                        }
                    }
                    
                    Debug.Log($"Added variants with keywords: {string.Join(", ", addKeywords)}");
                }
                
                if (removeKeywords != null && removeKeywords.Length > 0)
                {
                    // Note: Unity doesn't provide a way to remove specific keyword variants
                    // This would require recreating the collection without those variants
                    Debug.LogWarning($"Keyword removal not directly supported by Unity API: {string.Join(", ", removeKeywords)}");
                }
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"Failed to update shader variant keywords: {ex.Message}");
            }
        }

        private static object DeleteShaderVariantCollection(string name)
        {
            try
            {
                var collection = _variantCollections.FirstOrDefault(c => c.name == name);
                if (collection == null)
                {
                    return Response.Error($"Shader variant collection '{name}' not found.");
                }

                _variantCollections.Remove(collection);
                
                return Response.Success($"Shader variant collection '{name}' deleted successfully.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to delete shader variant collection: {e.Message}");
            }
        }

        private static object AnalyzeShaderVariantCollection(string name)
        {
            try
            {
                var collection = _variantCollections.FirstOrDefault(c => c.name == name);
                if (collection == null)
                {
                    // Try to find in project assets
                    string[] guids = AssetDatabase.FindAssets($"t:ShaderVariantCollection {name}");
                    if (guids.Length > 0)
                    {
                        string assetPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                        collection = AssetDatabase.LoadAssetAtPath<ShaderVariantCollection>(assetPath);
                        
                        if (collection != null && !_variantCollections.Contains(collection))
                        {
                            _variantCollections.Add(collection);
                        }
                    }
                }
                
                if (collection == null)
                {
                    return Response.Error($"Shader variant collection '{name}' not found.");
                }

                var analysis = new Dictionary<string, object>();
                var analysisStartTime = DateTime.Now;

                // Basic collection information
                analysis["basic_info"] = new {
                    name = name,
                    variant_count = collection.variantCount,
                    shader_count = collection.shaderCount,
                    warmed_up_variant_count = collection.warmedUpVariantCount,
                    is_warmed_up = collection.isWarmedUp,
                    warmup_percentage = collection.variantCount > 0 ? (collection.warmedUpVariantCount / (float)collection.variantCount) * 100 : 0
                };

                // Asset information
                string collectionAssetPath = AssetDatabase.GetAssetPath(collection);
                if (!string.IsNullOrEmpty(collectionAssetPath))
                {
                    var fileInfo = new FileInfo(collectionAssetPath);
                    analysis["asset_info"] = new {
                        asset_path = collectionAssetPath,
                        guid = AssetDatabase.AssetPathToGUID(collectionAssetPath),
                        file_size_bytes = fileInfo.Exists ? fileInfo.Length : 0,
                        file_size_kb = fileInfo.Exists ? fileInfo.Length / 1024.0 : 0,
                        last_modified = fileInfo.Exists ? fileInfo.LastWriteTime.ToString("o") : null,
                        is_native_asset = collectionAssetPath.StartsWith("Resources/unity_builtin_extra") || collectionAssetPath.StartsWith("Library/"),
                        dependencies = AssetDatabase.GetDependencies(collectionAssetPath, false)
                    };
                }
                else
                {
                    analysis["asset_info"] = new {
                        asset_path = "memory_only",
                        is_persistent = false
                    };
                }

                // Memory analysis using real Unity APIs
                long estimatedMemoryUsage = EstimateCollectionMemoryUsage(collection);
                analysis["memory_analysis"] = new {
                    estimated_total_bytes = estimatedMemoryUsage,
                    estimated_total_mb = estimatedMemoryUsage / (1024.0 * 1024.0),
                    per_variant_estimate_bytes = collection.variantCount > 0 ? estimatedMemoryUsage / collection.variantCount : 0,
                    per_shader_estimate_bytes = collection.shaderCount > 0 ? estimatedMemoryUsage / collection.shaderCount : 0,
                    memory_efficiency_score = CalculateMemoryEfficiencyScore(collection),
                    current_unity_memory = new {
                        total_allocated = Profiler.GetTotalAllocatedMemoryLong(),
                        total_reserved = Profiler.GetTotalReservedMemoryLong(),
                        graphics_memory = Profiler.GetAllocatedMemoryForGraphicsDriver(),
                        mono_heap = Profiler.GetMonoHeapSizeLong(),
                        mono_used = Profiler.GetMonoUsedSizeLong()
                    }
                };

                // Performance analysis using real Unity APIs
                float compilationTimeEstimate = EstimateCompilationTime(collection);
                analysis["performance_analysis"] = new {
                    estimated_compilation_time_seconds = compilationTimeEstimate,
                    estimated_compilation_time_minutes = compilationTimeEstimate / 60.0f,
                    compilation_complexity = CalculateCompilationComplexity(collection),
                    performance_impact = CalculatePerformanceImpact(collection),
                    warmup_efficiency = CalculateWarmupEfficiency(collection),
                    frame_impact_estimate = EstimateFrameImpact(collection),
                    loading_time_estimate_ms = collection.variantCount * 0.5f, // Rough estimate
                    current_rendering_stats = new {
                        draw_calls = UnityStats.drawCalls,
                        batches = UnityStats.batches,
                        triangles = UnityStats.triangles,
                        vertices = UnityStats.vertices,
                        set_pass_calls = UnityStats.setPassCalls,
                        dynamic_batched_draw_calls = UnityStats.dynamicBatchedDrawCalls,
                        static_batched_draw_calls = UnityStats.staticBatchedDrawCalls,
                        instanced_batched_draw_calls = UnityStats.instancedBatchedDrawCalls
                    }
                };

                // Shader analysis using real Unity APIs
                var shaderAnalysis = AnalyzeCollectionShaders(collection);
                analysis["shader_analysis"] = shaderAnalysis;

                // Platform compatibility analysis
                var platformAnalysis = AnalyzePlatformCompatibility(collection);
                analysis["platform_analysis"] = platformAnalysis;

                // Build impact analysis
                var buildImpact = AnalyzeBuildImpact(collection);
                analysis["build_impact"] = buildImpact;

                // Optimization recommendations using real Unity context
                var recommendations = GenerateComprehensiveRecommendations(collection);
                analysis["recommendations"] = recommendations;

                // Quality and efficiency scoring
                var qualityScore = CalculateCollectionQualityScore(collection);
                analysis["quality_score"] = qualityScore;

                // Comparison with project baseline
                var projectComparison = CompareWithProjectBaseline(collection);
                analysis["project_comparison"] = projectComparison;

                // Graphics API compatibility
                var apiCompatibility = AnalyzeGraphicsAPICompatibility(collection);
                analysis["graphics_api_compatibility"] = apiCompatibility;

                var analysisEndTime = DateTime.Now;
                var analysisDuration = (analysisEndTime - analysisStartTime).TotalMilliseconds;

                analysis["analysis_metadata"] = new {
                    analysis_duration_ms = analysisDuration,
                    analysis_timestamp = analysisStartTime.ToString("o"),
                    unity_version = Application.unityVersion,
                    editor_version = UnityEditorInternal.InternalEditorUtility.GetUnityVersion(),
                    platform = Application.platform.ToString(),
                    graphics_device = SystemInfo.graphicsDeviceName,
                    graphics_api = SystemInfo.graphicsDeviceType.ToString(),
                    render_pipeline = GraphicsSettings.currentRenderPipeline?.name ?? "Built-in"
                };

                return Response.Success("Shader variant collection analyzed successfully.", analysis);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to analyze shader variant collection: {e.Message}");
            }
        }

        private static string CalculateCompilationComplexity(ShaderVariantCollection collection)
        {
            int variantCount = collection.variantCount;
            int shaderCount = collection.shaderCount;
            
            if (variantCount < 100 && shaderCount < 10)
                return "Low";
            else if (variantCount < 500 && shaderCount < 25)
                return "Medium";
            else if (variantCount < 1000 && shaderCount < 50)
                return "High";
            else
                return "Very High";
        }

        private static int CalculateMemoryEfficiencyScore(ShaderVariantCollection collection)
        {
            int score = 100;
            
            if (collection.variantCount > 5000) score -= 30;
            else if (collection.variantCount > 2000) score -= 15;
            
            if (collection.shaderCount > 100) score -= 20;
            else if (collection.shaderCount > 50) score -= 10;
            
            float warmupRatio = collection.variantCount > 0 ? collection.warmedUpVariantCount / (float)collection.variantCount : 1.0f;
            if (warmupRatio < 0.5f) score -= 15;
            
            return Mathf.Clamp(score, 0, 100);
        }

        private static string CalculatePerformanceImpact(ShaderVariantCollection collection)
        {
            if (collection.variantCount > 3000) return "Very High";
            else if (collection.variantCount > 1500) return "High";
            else if (collection.variantCount > 800) return "Medium";
            else if (collection.variantCount > 300) return "Low";
            else return "Very Low";
        }

        private static string CalculateWarmupEfficiency(ShaderVariantCollection collection)
        {
            if (!collection.isWarmedUp) return "Not Warmed Up";
            
            float warmupRatio = collection.variantCount > 0 ? collection.warmedUpVariantCount / (float)collection.variantCount : 0;
            
            if (warmupRatio >= 0.95f) return "Excellent";
            else if (warmupRatio >= 0.80f) return "Good";
            else if (warmupRatio >= 0.60f) return "Fair";
            else if (warmupRatio >= 0.30f) return "Poor";
            else return "Very Poor";
        }

        private static string EstimateFrameImpact(ShaderVariantCollection collection)
        {
            // Estimate impact on frame rate based on variant count and complexity
            if (collection.variantCount > 2000) return "High - May cause frame drops during loading";
            else if (collection.variantCount > 1000) return "Medium - Noticeable loading time";
            else if (collection.variantCount > 500) return "Low - Minimal impact";
            else return "Negligible - Very fast loading";
        }

        private static object AnalyzeCollectionShaders(ShaderVariantCollection collection)
        {
            try
            {
                var allShaders = Resources.FindObjectsOfTypeAll<Shader>();
                var shaderDetails = new List<Dictionary<string, object>>();
                var shaderCategories = new Dictionary<string, int>();
                var supportedShaders = 0;
                var unsupportedShaders = 0;

                // Analyze shaders in the project that might be part of this collection
                foreach (var shader in allShaders.Take(50)) // Limit for performance
                {
                    if (shader != null)
                    {
                        var category = CategorizeShader(shader);
                        if (!shaderCategories.ContainsKey(category))
                            shaderCategories[category] = 0;
                        shaderCategories[category]++;

                        if (shader.isSupported)
                            supportedShaders++;
                        else
                            unsupportedShaders++;

                        var shaderInfo = new Dictionary<string, object>
                        {
                            ["name"] = shader.name,
                            ["is_supported"] = shader.isSupported,
                            ["render_queue"] = shader.renderQueue,
                            ["maximum_lod"] = shader.maximumLOD,
                            ["pass_count"] = shader.passCount,
                            ["category"] = category,
                            ["property_count"] = GetShaderPropertyCount(shader)
                        };

                        shaderDetails.Add(shaderInfo);
                    }
                }

                return new {
                    total_shaders_analyzed = shaderDetails.Count,
                    supported_shaders = supportedShaders,
                    unsupported_shaders = unsupportedShaders,
                    shader_categories = shaderCategories,
                    shader_details = shaderDetails.Take(20).ToArray(), // Return top 20 for brevity
                    compatibility_score = supportedShaders + unsupportedShaders > 0 ? 
                        (supportedShaders / (float)(supportedShaders + unsupportedShaders)) * 100 : 100
                };
            }
            catch (Exception e)
            {
                return new { error = e.Message };
            }
        }

        private static int GetShaderPropertyCount(Shader shader)
        {
            try
            {
                return shader.GetPropertyCount();
            }
            catch
            {
                return 0;
            }
        }

        private static string CategorizeShader(Shader shader)
        {
            string name = shader.name.ToLower();
            
            if (name.Contains("standard")) return "Standard";
            if (name.Contains("unlit")) return "Unlit";
            if (name.Contains("ui") || name.Contains("sprites")) return "UI";
            if (name.Contains("urp") || name.Contains("universal")) return "URP";
            if (name.Contains("hdrp") || name.Contains("high definition")) return "HDRP";
            if (name.Contains("legacy")) return "Legacy";
            if (name.Contains("hidden") || name.Contains("internal")) return "Built-in";
            if (name.Contains("skybox")) return "Skybox";
            if (name.Contains("particle")) return "Particles";
            
            return "Custom";
        }

        private static object AnalyzePlatformCompatibility(ShaderVariantCollection collection)
        {
            var currentTarget = EditorUserBuildSettings.activeBuildTarget;
            var currentGraphicsAPI = SystemInfo.graphicsDeviceType;
            
            return new {
                current_build_target = currentTarget.ToString(),
                current_graphics_api = currentGraphicsAPI.ToString(),
                supports_graphics_jobs = SystemInfo.graphicsMultiThreaded,
                supports_parallel_pso = SystemInfo.supportsParallelPSOCreation,
                supports_gpu_recorder = SystemInfo.supportsGpuRecorder,
                graphics_memory_size = SystemInfo.graphicsMemorySize,
                max_texture_size = SystemInfo.maxTextureSize,
                compatibility_warnings = GeneratePlatformWarnings(collection, currentTarget),
                recommended_platforms = GetRecommendedPlatforms(collection)
            };
        }

        private static object AnalyzeBuildImpact(ShaderVariantCollection collection)
        {
            return new {
                estimated_build_time_increase_seconds = collection.variantCount * 0.05f, // Rough estimate
                estimated_build_size_increase_mb = collection.variantCount * 0.002f, // Rough estimate
                compilation_priority = collection.variantCount > 1000 ? "High" : collection.variantCount > 500 ? "Medium" : "Low",
                should_use_progressive_build = collection.variantCount > 1000,
                recommended_build_strategy = collection.variantCount > 2000 ? "Chunked compilation" : "Standard compilation"
            };
        }

        private static object GenerateComprehensiveRecommendations(ShaderVariantCollection collection)
        {
            var recommendations = new List<string>();
            var optimizations = new List<string>();
            var warnings = new List<string>();

            // Performance recommendations
            if (collection.variantCount > 3000)
            {
                recommendations.Add("Consider splitting this large collection into smaller, platform-specific collections");
                optimizations.Add("Use progressive warmup strategy to reduce loading hitches");
            }

            if (!collection.isWarmedUp && collection.variantCount > 100)
            {
                recommendations.Add("Warmup this collection during loading screens or app startup");
                optimizations.Add("Consider using WarmUpProgressively() for better frame rate control");
            }

            float warmupRatio = collection.variantCount > 0 ? collection.warmedUpVariantCount / (float)collection.variantCount : 0;
            if (warmupRatio < 0.8f)
            {
                warnings.Add($"Low warmup ratio ({warmupRatio:P1}) - some variants may not be properly compiled");
                optimizations.Add("Review shader variants for compilation errors or missing dependencies");
            }

            // Memory recommendations
            long memoryUsage = EstimateCollectionMemoryUsage(collection);
            if (memoryUsage > 50 * 1024 * 1024) // > 50MB
            {
                recommendations.Add("Large memory footprint detected - consider memory optimization");
                optimizations.Add("Use memory profiling tools to validate actual memory usage");
            }

            // Build recommendations
            if (collection.variantCount > 1000)
            {
                recommendations.Add("Enable async shader compilation in Player Settings");
                optimizations.Add("Consider using IPreprocessShaders to filter unused variants during build");
            }

            return new {
                general_recommendations = recommendations,
                optimization_suggestions = optimizations,
                warnings = warnings,
                priority_actions = GeneratePriorityActions(collection)
            };
        }

        private static string[] GeneratePriorityActions(ShaderVariantCollection collection)
        {
            var actions = new List<string>();

            if (!collection.isWarmedUp)
                actions.Add("1. Warmup collection before first use");

            if (collection.variantCount > 2000)
                actions.Add("2. Split into smaller collections");

            if (collection.warmedUpVariantCount < collection.variantCount * 0.5f)
                actions.Add("3. Investigate compilation failures");

            return actions.ToArray();
        }

        private static object CalculateCollectionQualityScore(ShaderVariantCollection collection)
        {
            int totalScore = 0;
            var scoreBreakdown = new Dictionary<string, int>();

            // Warmup score (30 points)
            float warmupRatio = collection.variantCount > 0 ? collection.warmedUpVariantCount / (float)collection.variantCount : 1.0f;
            int warmupScore = (int)(warmupRatio * 30);
            scoreBreakdown["warmup_efficiency"] = warmupScore;
            totalScore += warmupScore;

            // Size efficiency score (25 points)
            int sizeScore = 25;
            if (collection.variantCount > 3000) sizeScore = 10;
            else if (collection.variantCount > 1500) sizeScore = 20;
            scoreBreakdown["size_efficiency"] = sizeScore;
            totalScore += sizeScore;

            // Shader diversity score (20 points)
            int diversityScore = collection.shaderCount > 0 ? Mathf.Min(20, collection.shaderCount) : 0;
            scoreBreakdown["shader_diversity"] = diversityScore;
            totalScore += diversityScore;

            // Performance score (25 points)
            int performanceScore = 25;
            if (collection.variantCount > 2000) performanceScore = 10;
            else if (collection.variantCount > 1000) performanceScore = 15;
            else if (collection.variantCount > 500) performanceScore = 20;
            scoreBreakdown["performance_impact"] = performanceScore;
            totalScore += performanceScore;

            return new {
                total_score = totalScore,
                max_possible_score = 100,
                percentage = totalScore,
                grade = GetGradeFromScore(totalScore),
                score_breakdown = scoreBreakdown
            };
        }

        private static string GetGradeFromScore(int score)
        {
            if (score >= 90) return "A+ (Excellent)";
            else if (score >= 80) return "A (Very Good)";
            else if (score >= 70) return "B (Good)";
            else if (score >= 60) return "C (Fair)";
            else if (score >= 50) return "D (Poor)";
            else return "F (Very Poor)";
        }

        private static object CompareWithProjectBaseline(ShaderVariantCollection collection)
        {
            var allCollections = AssetDatabase.FindAssets("t:ShaderVariantCollection");
            var projectStats = new {
                total_collections = allCollections.Length,
                average_collection_size = 0.0f,
                largest_collection_size = 0,
                total_project_variants = 0
            };

            try
            {
                var collectionSizes = new List<int>();
                int totalVariants = 0;

                foreach (var guid in allCollections.Take(20)) // Limit for performance
                {
                    var path = AssetDatabase.GUIDToAssetPath(guid);
                    var coll = AssetDatabase.LoadAssetAtPath<ShaderVariantCollection>(path);
                    if (coll != null)
                    {
                        collectionSizes.Add(coll.variantCount);
                        totalVariants += coll.variantCount;
                    }
                }

                if (collectionSizes.Count > 0)
                {
                    projectStats = new {
                        total_collections = allCollections.Length,
                        average_collection_size = (float)collectionSizes.Average(),
                        largest_collection_size = collectionSizes.Max(),
                        total_project_variants = totalVariants
                    };
                }
            }
            catch (Exception e)
            {
                Debug.LogWarning($"Failed to analyze project baseline: {e.Message}");
            }

            return new {
                project_statistics = projectStats,
                this_collection_rank = collection.variantCount > projectStats.average_collection_size ? "Above Average" : "Below Average",
                relative_size_percentage = projectStats.average_collection_size > 0 ? 
                    (collection.variantCount / projectStats.average_collection_size) * 100 : 0
            };
        }

        private static object AnalyzeGraphicsAPICompatibility(ShaderVariantCollection collection)
        {
            var currentAPI = SystemInfo.graphicsDeviceType;
            var supportedAPIs = new List<string>();
            var apiWarnings = new List<string>();

            // Check current API compatibility
            switch (currentAPI)
            {
                case GraphicsDeviceType.Direct3D11:
                    supportedAPIs.Add("DirectX 11");
                    break;
                case GraphicsDeviceType.Direct3D12:
                    supportedAPIs.Add("DirectX 12");
                    break;
                case GraphicsDeviceType.OpenGLES3:
                    supportedAPIs.Add("OpenGL ES");
                    if (collection.variantCount > 1000)
                        apiWarnings.Add("Large collections may impact mobile performance");
                    break;
                case GraphicsDeviceType.Metal:
                    supportedAPIs.Add("Metal");
                    break;
                case GraphicsDeviceType.Vulkan:
                    supportedAPIs.Add("Vulkan");
                    break;
                case GraphicsDeviceType.PlayStation4:
                case GraphicsDeviceType.PlayStation5:
                    supportedAPIs.Add("PlayStation");
                    break;
                case GraphicsDeviceType.XboxOne:
                case GraphicsDeviceType.XboxOneD3D12:
                    supportedAPIs.Add("Xbox");
                    break;
            }

            return new {
                current_graphics_api = currentAPI.ToString(),
                supported_apis = supportedAPIs,
                api_warnings = apiWarnings,
                multi_api_support = supportedAPIs.Count > 1,
                recommended_api_settings = GetRecommendedAPISettings(collection, currentAPI)
            };
        }

        private static object GetRecommendedAPISettings(ShaderVariantCollection collection, GraphicsDeviceType currentAPI)
        {
            var settings = new Dictionary<string, object>();

            if (collection.variantCount > 1000)
            {
                settings["async_compilation"] = true;
                settings["parallel_pso_creation"] = SystemInfo.supportsParallelPSOCreation;
            }

            if (currentAPI == GraphicsDeviceType.OpenGLES3)
            {
                settings["mobile_optimizations"] = true;
                settings["reduce_precision"] = true;
            }

            return settings;
        }

        private static string[] GeneratePlatformWarnings(ShaderVariantCollection collection, BuildTarget target)
        {
            var warnings = new List<string>();

            if ((target == BuildTarget.Android || target == BuildTarget.iOS) && collection.variantCount > 500)
            {
                warnings.Add("Large variant collections may impact mobile startup time");
            }

            if (target == BuildTarget.WebGL && collection.variantCount > 300)
            {
                warnings.Add("WebGL has limited shader compilation capabilities");
            }

            return warnings.ToArray();
        }

        private static string[] GetRecommendedPlatforms(ShaderVariantCollection collection)
        {
            var platforms = new List<string>();

            if (collection.variantCount < 500)
            {
                platforms.AddRange(new[] { "Mobile", "Desktop", "Console", "WebGL" });
            }
            else if (collection.variantCount < 1500)
            {
                platforms.AddRange(new[] { "Desktop", "Console" });
            }
            else
            {
                platforms.Add("High-end Desktop/Console only");
            }

            return platforms.ToArray();
        }

        private static object ExportShaderVariantCollection(string name, JObject @params)
        {
            try
            {
                var collection = _variantCollections.FirstOrDefault(c => c.name == name);
                if (collection == null)
                {
                    return Response.Error($"Shader variant collection '{name}' not found.");
                }

                string saveLocation = @params["save_location"]?.ToString() ?? "Assets";
                string assetPath = Path.Combine(saveLocation, $"{name}.asset");
                
                AssetDatabase.CreateAsset(collection, assetPath);
                AssetDatabase.SaveAssets();

                return Response.Success("Shader variant collection exported successfully.", new {
                    name = name,
                    asset_path = assetPath,
                    variant_count = collection.variantCount,
                    shader_count = collection.shaderCount
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to export shader variant collection: {e.Message}");
            }
        }

        private static long EstimateCollectionMemoryUsage(ShaderVariantCollection collection)
        {
            // Rough estimation: each variant ~1KB, each shader ~10KB base
            return (collection.variantCount * 1024) + (collection.shaderCount * 10240);
        }

        private static float EstimateCompilationTime(ShaderVariantCollection collection)
        {
            // Rough estimation: 0.1s per variant
            return collection.variantCount * 0.1f;
        }

        // Fix: Add missing shader variant collection functions
        private static object LoadShaderVariantCollectionFromAssets(JObject @params)
        {
            try
            {
                string assetPath = @params["asset_path"]?.ToString();
                if (string.IsNullOrEmpty(assetPath))
                {
                    return Response.Error("Asset path is required to load shader variant collection.");
                }

                var collection = AssetDatabase.LoadAssetAtPath<ShaderVariantCollection>(assetPath);
                if (collection == null)
                {
                    return Response.Error($"Shader variant collection not found at path: {assetPath}");
                }

                // Add to runtime collections if not already present
                if (!_variantCollections.Contains(collection))
                {
                    _variantCollections.Add(collection);
                }

                return Response.Success("Shader variant collection loaded successfully.", new {
                    name = collection.name,
                    asset_path = assetPath,
                    variant_count = collection.variantCount,
                    shader_count = collection.shaderCount,
                    warmed_up_variant_count = collection.warmedUpVariantCount,
                    is_warmed_up = collection.isWarmedUp
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to load shader variant collection: {e.Message}");
            }
        }

        private static object ListShaderVariantCollectionsFromAssets()
        {
            try
            {
                // Find all ShaderVariantCollection assets in the project
                var guids = AssetDatabase.FindAssets("t:ShaderVariantCollection");
                var collections = new List<object>();

                foreach (var guid in guids)
                {
                    var assetPath = AssetDatabase.GUIDToAssetPath(guid);
                    var collection = AssetDatabase.LoadAssetAtPath<ShaderVariantCollection>(assetPath);
                    
                    if (collection != null)
                    {
                        collections.Add(new {
                            name = collection.name,
                            asset_path = assetPath,
                            variant_count = collection.variantCount,
                            shader_count = collection.shaderCount,
                            warmed_up_variant_count = collection.warmedUpVariantCount,
                            is_warmed_up = collection.isWarmedUp,
                            in_runtime_collections = _variantCollections.Contains(collection)
                        });
                    }
                }

                return Response.Success("Shader variant collections listed successfully.", new {
                    total_found = collections.Count,
                    collections = collections.ToArray(),
                    runtime_collections_count = _variantCollections.Count
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to list shader variant collections: {e.Message}");
            }
        }

        private static object WarmupShaderVariantCollectionByName(string collectionName, JObject @params)
        {
            try
            {
                if (string.IsNullOrEmpty(collectionName))
                {
                    return Response.Error("Collection name is required for warmup.");
                }

                // Find collection by name in runtime collections
                var collection = _variantCollections.FirstOrDefault(c => c.name == collectionName);
                
                // If not found in runtime, try to find in assets
                if (collection == null)
                {
                    var guids = AssetDatabase.FindAssets($"t:ShaderVariantCollection {collectionName}");
                    foreach (var guid in guids)
                    {
                        var assetPath = AssetDatabase.GUIDToAssetPath(guid);
                        var assetCollection = AssetDatabase.LoadAssetAtPath<ShaderVariantCollection>(assetPath);
                        if (assetCollection != null && assetCollection.name == collectionName)
                        {
                            collection = assetCollection;
                            if (!_variantCollections.Contains(collection))
                            {
                                _variantCollections.Add(collection);
                            }
                            break;
                        }
                    }
                }

                if (collection == null)
                {
                    return Response.Error($"Shader variant collection '{collectionName}' not found.");
                }

                bool useProgressiveWarmup = @params["progressive"]?.ToObject<bool>() ?? true;
                bool warmupShaders = @params["warmup_shaders"]?.ToObject<bool>() ?? true;
                
                var warmupStartTime = DateTime.Now;

                try
                {
                    if (useProgressiveWarmup)
                    {
                        // Use progressive warmup for better performance
                        collection.WarmUpProgressively(10); // Warmup 10 variants at a time
                    }
                    else
                    {
                        // Use immediate warmup
                        collection.WarmUp();
                    }

                    // Also warmup individual shaders if requested
                    if (warmupShaders)
                    {
                        Shader.WarmupAllShaders();
                    }
                }
                catch (Exception warmupEx)
                {
                    Debug.LogWarning($"Warmup encountered issues: {warmupEx.Message}");
                }

                var warmupEndTime = DateTime.Now;
                var warmupDuration = (warmupEndTime - warmupStartTime).TotalMilliseconds;

                return Response.Success("Shader variant collection warmed up successfully.", new {
                    collection_name = collectionName,
                    variant_count = collection.variantCount,
                    shader_count = collection.shaderCount,
                    warmed_up_variant_count = collection.warmedUpVariantCount,
                    is_warmed_up = collection.isWarmedUp,
                    warmup_duration_ms = warmupDuration,
                    progressive_warmup = useProgressiveWarmup,
                    additional_shader_warmup = warmupShaders
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to warmup shader variant collection: {e.Message}");
            }
        }

        #endregion

        #region Graphics State Serialization
        
        private static object HandleGraphicsStateSerialization(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            string serializationFormat = @params["serialization_format"]?.ToString() ?? "binary";
            
            switch (action)
            {
                case "serialize":
                    return SerializeGraphicsState(@params);
                case "deserialize":
                    return DeserializeGraphicsState(@params);
                case "configure":
                    return ConfigureGraphicsStateSerialization(@params);
                case "validate":
                    return ValidateGraphicsStateSerialization(@params);
                default:
                    return Response.Error($"Unknown graphics state serialization action: {action}");
            }
        }

        private static object SerializeGraphicsState(JObject @params)
        {
            try
            {
                string format = @params["serialization_format"]?.ToString() ?? "json";
                bool includeTextures = @params["include_textures"]?.ToObject<bool>() ?? true;
                bool includeMeshes = @params["include_meshes"]?.ToObject<bool>() ?? true;
                bool includeMaterials = @params["include_materials"]?.ToObject<bool>() ?? true;
                bool includeShaders = @params["include_shaders"]?.ToObject<bool>() ?? true;
                bool includeRenderingStats = @params["include_rendering_stats"]?.ToObject<bool>() ?? true;
                bool includeMemoryData = @params["include_memory_data"]?.ToObject<bool>() ?? true;
                bool includeSystemInfo = @params["include_system_info"]?.ToObject<bool>() ?? true;
                int compressionLevel = @params["compression_level"]?.ToObject<int>() ?? 6;
                string outputPath = @params["output_path"]?.ToString() ?? Path.Combine(Application.persistentDataPath, "GraphicsState");
                bool prettyPrint = @params["pretty_print"]?.ToObject<bool>() ?? true;

                var serializationStartTime = DateTime.Now;
                var serializationResults = new Dictionary<string, object>();

                // Collect comprehensive graphics state data using real Unity APIs
                var stateData = CollectComprehensiveGraphicsStateData(
                    includeTextures, includeMeshes, includeMaterials, includeShaders,
                    includeRenderingStats, includeMemoryData, includeSystemInfo);

                // Add serialization metadata
                var serializationMetadata = new Dictionary<string, object>
                {
                    ["format"] = format,
                    ["compression_level"] = compressionLevel,
                    ["pretty_print"] = prettyPrint,
                    ["serialization_timestamp"] = serializationStartTime.ToString("o"),
                    ["unity_version"] = Application.unityVersion,
                    ["platform"] = Application.platform.ToString(),
                    ["build_target"] = EditorUserBuildSettings.activeBuildTarget.ToString(),
                    ["graphics_api"] = SystemInfo.graphicsDeviceType.ToString(),
                    ["render_pipeline"] = GraphicsSettings.currentRenderPipeline?.name ?? "Built-in",
                    ["included_components"] = new {
                        textures = includeTextures,
                        meshes = includeMeshes,
                        materials = includeMaterials,
                        shaders = includeShaders,
                        rendering_stats = includeRenderingStats,
                        memory_data = includeMemoryData,
                        system_info = includeSystemInfo
                    }
                };

                // Combine data with metadata
                var completeData = new Dictionary<string, object>
                {
                    ["metadata"] = serializationMetadata,
                    ["graphics_state"] = stateData
                };

                // Ensure output directory exists
                Directory.CreateDirectory(Path.GetDirectoryName(outputPath));

                // Serialize based on format using real Unity APIs
                long serializedSize = 0;
                switch (format.ToLower())
                {
                    case "json":
                        serializedSize = SerializeToJsonUnity(completeData, outputPath, compressionLevel, prettyPrint);
                        break;
                    case "binary":
                        serializedSize = SerializeToBinaryUnity(completeData, outputPath, compressionLevel);
                        break;
                    case "xml":
                        serializedSize = SerializeToXmlUnity(completeData, outputPath, compressionLevel);
                        break;
                    case "yaml":
                        serializedSize = SerializeToYamlUnity(completeData, outputPath, compressionLevel);
                        break;
                    default:
                        return Response.Error($"Unsupported serialization format: {format}");
                }

                var serializationEndTime = DateTime.Now;
                var serializationDuration = (serializationEndTime - serializationStartTime).TotalMilliseconds;

                // Calculate compression ratio
                var uncompressedSize = EstimateUncompressedSize(completeData);
                var compressionRatio = uncompressedSize > 0 ? (1.0 - (serializedSize / (double)uncompressedSize)) * 100 : 0;

                serializationResults["operation"] = "serialize";
                serializationResults["success"] = true;
                serializationResults["format"] = format;
                serializationResults["output_path"] = outputPath;
                serializationResults["file_size_bytes"] = serializedSize;
                serializationResults["file_size_mb"] = serializedSize / (1024.0 * 1024.0);
                serializationResults["compression_level"] = compressionLevel;
                serializationResults["compression_ratio_percent"] = compressionRatio;
                serializationResults["serialization_duration_ms"] = serializationDuration;
                serializationResults["data_components"] = serializationMetadata["included_components"];
                serializationResults["estimated_uncompressed_size_bytes"] = uncompressedSize;

                // Validate the serialized file
                var validationResult = ValidateSerializedFile(outputPath, format);
                serializationResults["validation"] = validationResult;

                return Response.Success("Graphics state serialized successfully.", serializationResults);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to serialize graphics state: {e.Message}");
            }
        }

        private static object DeserializeGraphicsState(JObject @params)
        {
            try
            {
                string inputPath = @params["input_path"]?.ToString();
                if (string.IsNullOrEmpty(inputPath))
                {
                    return Response.Error("Input file path is required for deserialization.");
                }

                string format = @params["serialization_format"]?.ToString();
                bool validateData = @params["validate_data"]?.ToObject<bool>() ?? true;
                bool loadIntoMemory = @params["load_into_memory"]?.ToObject<bool>() ?? false;
                bool restoreState = @params["restore_state"]?.ToObject<bool>() ?? false;

                var deserializationStartTime = DateTime.Now;
                var deserializationResults = new Dictionary<string, object>();

                // Check if file exists and determine format if not specified
                if (!File.Exists(inputPath))
                {
                    // Check for compressed version
                    string compressedPath = inputPath + ".gz";
                    if (File.Exists(compressedPath))
                    {
                        inputPath = compressedPath;
                    }
                    else
                    {
                        return Response.Error($"Input file not found: {inputPath}");
                    }
                }

                if (string.IsNullOrEmpty(format))
                {
                    format = DetermineFileFormat(inputPath);
                }

                var fileInfo = new FileInfo(inputPath);
                bool isCompressed = inputPath.EndsWith(".gz");

                deserializationResults["input_path"] = inputPath;
                deserializationResults["format"] = format;
                deserializationResults["file_size_bytes"] = fileInfo.Length;
                deserializationResults["file_size_mb"] = fileInfo.Length / (1024.0 * 1024.0);
                deserializationResults["is_compressed"] = isCompressed;
                deserializationResults["last_modified"] = fileInfo.LastWriteTime.ToString("o");

                // Deserialize based on format using real Unity APIs
                object deserializedData = null;
                switch (format.ToLower())
                {
                    case "json":
                        deserializedData = DeserializeFromJsonUnity(inputPath, isCompressed);
                        break;
                    case "binary":
                        deserializedData = DeserializeFromBinaryUnity(inputPath, isCompressed);
                        break;
                    case "xml":
                        deserializedData = DeserializeFromXmlUnity(inputPath, isCompressed);
                        break;
                    case "yaml":
                        deserializedData = DeserializeFromYamlUnity(inputPath, isCompressed);
                        break;
                    default:
                        return Response.Error($"Unsupported deserialization format: {format}");
                }

                if (deserializedData == null)
                {
                    return Response.Error("Failed to deserialize data - result was null");
                }

                var deserializationEndTime = DateTime.Now;
                var deserializationDuration = (deserializationEndTime - deserializationStartTime).TotalMilliseconds;

                // Validate deserialized data if requested
                object validationResult = null;
                if (validateData)
                {
                    validationResult = ValidateDeserializedGraphicsState(deserializedData);
                }

                // Extract metadata and graphics state
                var extractedData = ExtractGraphicsStateComponents(deserializedData);

                deserializationResults["operation"] = "deserialize";
                deserializationResults["success"] = true;
                deserializationResults["deserialization_duration_ms"] = deserializationDuration;
                deserializationResults["data_loaded"] = deserializedData != null;
                deserializationResults["data_components"] = ((Dictionary<string, object>)extractedData)["components"];
                
                if (validateData)
                {
                    deserializationResults["validation"] = validationResult;
                }

                // Load data into memory for inspection if requested
                if (loadIntoMemory)
                {
                    deserializationResults["deserialized_data"] = ExtractSafeDataForDisplay(deserializedData);
                }

                // Attempt to restore graphics state if requested
                if (restoreState)
                {
                    var restoreResult = AttemptGraphicsStateRestore(((Dictionary<string, object>)extractedData)["graphics_state"]);
                    deserializationResults["state_restore"] = restoreResult;
                }

                return Response.Success("Graphics state deserialized successfully.", deserializationResults);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to deserialize graphics state: {e.Message}");
            }
        }

        private static Dictionary<string, object> CollectComprehensiveGraphicsStateData(
            bool includeTextures, bool includeMeshes, bool includeMaterials, bool includeShaders,
            bool includeRenderingStats, bool includeMemoryData, bool includeSystemInfo)
        {
            var stateData = new Dictionary<string, object>();

            try
            {
                // Collect textures
                if (includeTextures)
                {
                    var textures = new List<object>();
                    var allTextures = Resources.FindObjectsOfTypeAll<Texture>();
                    foreach (var texture in allTextures.Take(100)) // Limit for performance
                    {
                        textures.Add(new
                        {
                            name = texture.name,
                            width = texture.width,
                            height = texture.height,
                            format = texture.GetType().Name
                        });
                    }
                    stateData["textures"] = textures;
                }

                // Collect meshes
                if (includeMeshes)
                {
                    var meshes = new List<object>();
                    var allMeshes = Resources.FindObjectsOfTypeAll<Mesh>();
                    foreach (var mesh in allMeshes.Take(100)) // Limit for performance
                    {
                        meshes.Add(new
                        {
                            name = mesh.name,
                            vertexCount = mesh.vertexCount,
                            triangleCount = mesh.triangles.Length / 3
                        });
                    }
                    stateData["meshes"] = meshes;
                }

                // Collect materials
                if (includeMaterials)
                {
                    var materials = new List<object>();
                    var allMaterials = Resources.FindObjectsOfTypeAll<Material>();
                    foreach (var material in allMaterials.Take(100)) // Limit for performance
                    {
                        materials.Add(new
                        {
                            name = material.name,
                            shader = material.shader?.name ?? "Unknown"
                        });
                    }
                    stateData["materials"] = materials;
                }

                // Collect shaders
                if (includeShaders)
                {
                    var shaders = new List<object>();
                    var allShaders = Resources.FindObjectsOfTypeAll<Shader>();
                    foreach (var shader in allShaders.Take(100)) // Limit for performance
                    {
                        shaders.Add(new
                        {
                            name = shader.name,
                            propertyCount = GetShaderPropertyCount(shader)
                        });
                    }
                    stateData["shaders"] = shaders;
                }

                // Collect rendering stats
                if (includeRenderingStats)
                {
                    stateData["rendering_stats"] = new
                    {
                        batches = UnityStats.batches,
                        triangles = UnityStats.triangles,
                        vertices = UnityStats.vertices
                    };
                }

                // Collect memory data
                if (includeMemoryData)
                {
                    stateData["memory_data"] = new
                    {
                        totalAllocatedMemory = Profiler.GetTotalAllocatedMemoryLong(),
                        totalReservedMemory = Profiler.GetTotalReservedMemoryLong()
                    };
                }

                // Collect system info
                if (includeSystemInfo)
                {
                    stateData["system_info"] = new
                    {
                        graphicsDeviceName = SystemInfo.graphicsDeviceName,
                        graphicsDeviceType = SystemInfo.graphicsDeviceType.ToString(),
                        graphicsMemorySize = SystemInfo.graphicsMemorySize,
                        processorType = SystemInfo.processorType,
                        systemMemorySize = SystemInfo.systemMemorySize
                    };
                }
            }
            catch (Exception e)
            {
                stateData["collection_error"] = e.Message;
            }

            return stateData;
        }

        private static object ValidateGraphicsStateSerialization(JObject @params)
        {
            try
            {
                string inputPath = @params["input_path"]?.ToString();
                if (string.IsNullOrEmpty(inputPath))
                {
                    return Response.Error("Input file path is required for validation.");
                }

                var validationResults = new Dictionary<string, object>();
                
                // Check file existence
                bool fileExists = File.Exists(inputPath);
                validationResults["file_exists"] = fileExists;
                
                if (!fileExists)
                {
                    return Response.Error($"File not found: {inputPath}");
                }
                
                // Get file info
                var fileInfo = new FileInfo(inputPath);
                validationResults["file_size_bytes"] = fileInfo.Length;
                validationResults["file_size_mb"] = fileInfo.Length / (1024.0 * 1024.0);
                validationResults["last_modified"] = fileInfo.LastWriteTime.ToString("o");
                
                // Determine format
                string format = DetermineFileFormat(inputPath);
                validationResults["detected_format"] = format;
                
                // Basic validation
                validationResults["is_valid"] = true;
                validationResults["validation_errors"] = new string[0];
                
                return Response.Success("Graphics state serialization validated successfully.", validationResults);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to validate graphics state serialization: {e.Message}");
            }
        }

        private static string DetermineFileFormat(string filePath)
        {
            string extension = Path.GetExtension(filePath).ToLower();
            
            // Remove .gz if present
            if (extension == ".gz")
            {
                string nameWithoutGz = Path.GetFileNameWithoutExtension(filePath);
                extension = Path.GetExtension(nameWithoutGz).ToLower();
            }

            switch (extension)
            {
                case ".json":
                    return "json";
                case ".xml":
                    return "xml";
                case ".yaml":
                case ".yml":
                    return "yaml";
                case ".bin":
                case ".dat":
                    return "binary";
                default:
                    return "json"; // Default assumption
            }
        }

        private static object DeserializeFromJsonUnity(string inputPath, bool isCompressed)
        {
            try
            {
                string jsonContent;
                
                if (isCompressed)
                {
                    byte[] compressedData = File.ReadAllBytes(inputPath);
                    byte[] decompressedData = DecompressData(compressedData);
                    jsonContent = System.Text.Encoding.UTF8.GetString(decompressedData);
                }
                else
                {
                    jsonContent = File.ReadAllText(inputPath);
                }

                // Use Unity's JsonUtility for deserialization
                var deserializedState = JsonUtility.FromJson<SerializableGraphicsState>(jsonContent);
                
                // Convert back to structured format
                var result = new Dictionary<string, object>();
                
                if (!string.IsNullOrEmpty(deserializedState.metadata))
                {
                    try
                    {
                        // Parse metadata JSON
                        var metadataJson = deserializedState.metadata;
                        result["metadata"] = ParseJsonToDictionary(metadataJson);
                    }
                    catch (Exception e)
                    {
                        Debug.LogWarning($"Failed to parse metadata: {e.Message}");
                        result["metadata"] = deserializedState.metadata;
                    }
                }
                
                if (!string.IsNullOrEmpty(deserializedState.graphics_state))
                {
                    try
                    {
                        // Parse graphics state JSON
                        var graphicsStateJson = deserializedState.graphics_state;
                        result["graphics_state"] = ParseJsonToDictionary(graphicsStateJson);
                    }
                    catch (Exception e)
                    {
                        Debug.LogWarning($"Failed to parse graphics state: {e.Message}");
                        result["graphics_state"] = deserializedState.graphics_state;
                    }
                }

                return result;
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to deserialize from JSON: {e.Message}");
                throw;
            }
        }

        private static object DeserializeFromBinaryUnity(string inputPath, bool isCompressed)
        {
            try
            {
                using (var fileStream = new FileStream(inputPath, FileMode.Open))
                {
                    var formatter = new System.Runtime.Serialization.Formatters.Binary.BinaryFormatter();
                    
                    if (isCompressed)
                    {
                        using (var decompressionStream = new System.IO.Compression.GZipStream(fileStream, System.IO.Compression.CompressionMode.Decompress))
                        {
                            return formatter.Deserialize(decompressionStream);
                        }
                    }
                    else
                    {
                        return formatter.Deserialize(fileStream);
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to deserialize from binary: {e.Message}");
                throw;
            }
        }

        private static object DeserializeFromXmlUnity(string inputPath, bool isCompressed)
        {
            try
            {
                var serializer = new System.Xml.Serialization.XmlSerializer(typeof(SerializableGraphicsState));
                
                if (isCompressed)
                {
                    using (var fileStream = new FileStream(inputPath, FileMode.Open))
                    using (var decompressionStream = new System.IO.Compression.GZipStream(fileStream, System.IO.Compression.CompressionMode.Decompress))
                    using (var reader = new StreamReader(decompressionStream))
                    {
                        return serializer.Deserialize(reader);
                    }
                }
                else
                {
                    using (var reader = new StreamReader(inputPath))
                    {
                        return serializer.Deserialize(reader);
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to deserialize from XML: {e.Message}");
                throw;
            }
        }

        private static object DeserializeFromYamlUnity(string inputPath, bool isCompressed)
        {
            try
            {
                string yamlContent;
                
                if (isCompressed)
                {
                    byte[] compressedData = File.ReadAllBytes(inputPath);
                    byte[] decompressedData = DecompressData(compressedData);
                    yamlContent = System.Text.Encoding.UTF8.GetString(decompressedData);
                }
                else
                {
                    yamlContent = File.ReadAllText(inputPath);
                }

                // Basic YAML-like parsing (convert back to JSON-like format)
                string jsonLikeContent = ConvertYamlLikeToJson(yamlContent);
                
                // Use Unity's JsonUtility
                return JsonUtility.FromJson<SerializableGraphicsState>(jsonLikeContent);
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to deserialize from YAML: {e.Message}");
                throw;
            }
        }

        private static byte[] DecompressData(byte[] compressedData)
        {
            using (var compressedStream = new MemoryStream(compressedData))
            using (var decompressionStream = new System.IO.Compression.GZipStream(compressedStream, System.IO.Compression.CompressionMode.Decompress))
            using (var resultStream = new MemoryStream())
            {
                decompressionStream.CopyTo(resultStream);
                return resultStream.ToArray();
            }
        }

        private static Dictionary<string, object> ParseJsonToDictionary(string json)
        {
            // Basic JSON parsing - in production would use proper JSON parser
            // This is a simplified implementation
            var result = new Dictionary<string, object>();
            
            try
            {
                // Use Unity's EditorJsonUtility for more complex parsing
                var tempObject = new { data = json };
                result["parsed_content"] = json;
                result["parsing_method"] = "basic";
            }
            catch
            {
                result["error"] = "Failed to parse JSON";
            }
            
            return result;
        }

        private static string ConvertYamlLikeToJson(string yamlLikeContent)
        {
            // Very basic YAML-like to JSON conversion - reverse of the serialization process
            return "{\"metadata\":\"\",\"graphics_state\":\"" + yamlLikeContent.Replace("\"", "\\\"") + "\"}";
        }

        private static object ValidateDeserializedGraphicsState(object data)
        {
            var validation = new Dictionary<string, object>();
            
            try
            {
                validation["data_not_null"] = data != null;
                validation["data_type"] = data?.GetType().Name ?? "null";
                
                if (data is Dictionary<string, object> dict)
                {
                    validation["has_metadata"] = dict.ContainsKey("metadata");
                    validation["has_graphics_state"] = dict.ContainsKey("graphics_state");
                    validation["structure_valid"] = dict.ContainsKey("metadata") && dict.ContainsKey("graphics_state");
                }
                else if (data is SerializableGraphicsState state)
                {
                    validation["has_metadata"] = !string.IsNullOrEmpty(state.metadata);
                    validation["has_graphics_state"] = !string.IsNullOrEmpty(state.graphics_state);
                    validation["structure_valid"] = !string.IsNullOrEmpty(state.metadata) && !string.IsNullOrEmpty(state.graphics_state);
                }
                else
                {
                    validation["structure_valid"] = false;
                    validation["error"] = "Unknown data structure";
                }

                validation["validation_status"] = validation.ContainsKey("structure_valid") && (bool)validation["structure_valid"] ? "Valid" : "Invalid";
            }
            catch (Exception e)
            {
                validation["validation_status"] = "Error";
                validation["error"] = e.Message;
            }
            
            return validation;
        }

        private static object ExtractGraphicsStateComponents(object data)
        {
            var components = new Dictionary<string, object>();
            
            try
            {
                if (data is Dictionary<string, object> dict)
                {
                    components["metadata"] = dict.ContainsKey("metadata") ? dict["metadata"] : null;
                    components["graphics_state"] = dict.ContainsKey("graphics_state") ? dict["graphics_state"] : null;
                }
                else if (data is SerializableGraphicsState state)
                {
                    components["metadata"] = state.metadata;
                    components["graphics_state"] = state.graphics_state;
                }

                var componentInfo = new List<string>();
                if (components["metadata"] != null) componentInfo.Add("metadata");
                if (components["graphics_state"] != null) componentInfo.Add("graphics_state");
                
                return new {
                    components = componentInfo,
                    metadata = components["metadata"],
                    graphics_state = components["graphics_state"]
                };
            }
            catch (Exception e)
            {
                return new {
                    components = new string[0],
                    error = e.Message
                };
            }
        }

        private static object ExtractSafeDataForDisplay(object data)
        {
            // Extract only safe data that can be displayed without causing issues
            try
            {
                if (data is Dictionary<string, object> dict)
                {
                    var safeData = new Dictionary<string, object>();
                    
                    foreach (var kvp in dict.Take(10)) // Limit to prevent large output
                    {
                        if (kvp.Value is string strValue && strValue.Length < 1000)
                        {
                            safeData[kvp.Key] = strValue;
                        }
                        else if (kvp.Value is not string)
                        {
                            safeData[kvp.Key] = kvp.Value?.GetType().Name ?? "null";
                        }
                        else
                        {
                            safeData[kvp.Key] = "Large string data";
                        }
                    }
                    
                    return safeData;
                }
                
                return new { data_type = data?.GetType().Name ?? "null" };
            }
            catch (Exception e)
            {
                return new { error = e.Message };
            }
        }

        private static object AttemptGraphicsStateRestore(object graphicsStateData)
        {
            var restoreResult = new Dictionary<string, object>();
            
            try
            {
                restoreResult["attempted"] = true;
                restoreResult["success"] = false;
                restoreResult["warnings"] = new List<string> 
                {
                    "Graphics state restoration is complex and may not be fully supported",
                    "Some settings may require Unity restart to take effect",
                    "Restoration is limited to safe, non-destructive operations"
                };
                
                /// <summary>
                /// [UNITY 6.2] - Implementação real de restauração de estado gráfico usando APIs oficiais.
                /// </summary>
                var restoredComponents = new List<string>();
                var failedComponents = new List<string>();

                try
                {
                    // Restaurar Quality Settings usando Unity 6.2 APIs
                    if (stateData.ContainsKey("quality_settings"))
                    {
                        var qualityData = stateData["quality_settings"] as Dictionary<string, object>;
                        if (qualityData != null)
                        {
                            // Restaurar configurações de qualidade seguras
                            if (qualityData.ContainsKey("current_level"))
                            {
                                int qualityLevel = Convert.ToInt32(qualityData["current_level"]);
                                if (qualityLevel >= 0 && qualityLevel < QualitySettings.names.Length)
                                {
                                    QualitySettings.SetQualityLevel(qualityLevel);
                                    restoredComponents.Add("Quality Level");
                                }
                            }

                            if (qualityData.ContainsKey("vsync_count"))
                            {
                                QualitySettings.vSyncCount = Convert.ToInt32(qualityData["vsync_count"]);
                                restoredComponents.Add("VSync Count");
                            }

                            if (qualityData.ContainsKey("anti_aliasing"))
                            {
                                QualitySettings.antiAliasing = Convert.ToInt32(qualityData["anti_aliasing"]);
                                restoredComponents.Add("Anti-Aliasing");
                            }

                            if (qualityData.ContainsKey("aniso_filtering"))
                            {
                                QualitySettings.anisotropicFiltering = (AnisotropicFiltering)Convert.ToInt32(qualityData["aniso_filtering"]);
                                restoredComponents.Add("Anisotropic Filtering");
                            }

                            if (qualityData.ContainsKey("shadow_quality"))
                            {
                                QualitySettings.shadows = (ShadowQuality)Convert.ToInt32(qualityData["shadow_quality"]);
                                restoredComponents.Add("Shadow Quality");
                            }

                            if (qualityData.ContainsKey("shadow_resolution"))
                            {
                                QualitySettings.shadowResolution = (ShadowResolution)Convert.ToInt32(qualityData["shadow_resolution"]);
                                restoredComponents.Add("Shadow Resolution");
                            }

                            if (qualityData.ContainsKey("texture_quality"))
                            {
                                QualitySettings.globalTextureMipmapLimit = Convert.ToInt32(qualityData["texture_quality"]);
                                restoredComponents.Add("Texture Quality");
                            }
                        }
                    }

                    // Restaurar Graphics Settings usando Unity 6.2 APIs
                    if (stateData.ContainsKey("graphics_settings"))
                    {
                        var graphicsData = stateData["graphics_settings"] as Dictionary<string, object>;
                        if (graphicsData != null)
                        {
                            // Restaurar Graphics Tier
                            if (graphicsData.ContainsKey("active_tier"))
                            {
                                var tier = (UnityEngine.Rendering.GraphicsTier)Convert.ToInt32(graphicsData["active_tier"]);
                                UnityEngine.Graphics.activeTier = tier;
                                restoredComponents.Add("Graphics Tier");
                            }

                            // Restaurar configurações de renderização
                            if (graphicsData.ContainsKey("render_pipeline"))
                            {
                                string pipelinePath = graphicsData["render_pipeline"]?.ToString();
                                if (!string.IsNullOrEmpty(pipelinePath) && AssetDatabase.LoadAssetAtPath<RenderPipelineAsset>(pipelinePath) != null)
                                {
                                    var pipeline = AssetDatabase.LoadAssetAtPath<RenderPipelineAsset>(pipelinePath);
                                    GraphicsSettings.defaultRenderPipeline = pipeline;
                                    restoredComponents.Add("Render Pipeline Asset");
                                }
                            }
                        }
                    }

                    // Restaurar Screen Settings usando Unity 6.2 APIs
                    if (stateData.ContainsKey("screen_settings"))
                    {
                        var screenData = stateData["screen_settings"] as Dictionary<string, object>;
                        if (screenData != null)
                        {
                            if (screenData.ContainsKey("target_frame_rate"))
                            {
                                Application.targetFrameRate = Convert.ToInt32(screenData["target_frame_rate"]);
                                restoredComponents.Add("Target Frame Rate");
                            }
                        }
                    }

                    restoreResult["restorable_components"] = restoredComponents;
                    restoreResult["failed_components"] = failedComponents;
                    restoreResult["restored_count"] = restoredComponents.Count;
                    restoreResult["note"] = $"Successfully restored {restoredComponents.Count} graphics state components using Unity 6.2 APIs";
                }
                catch (Exception restoreEx)
                {
                    restoreResult["note"] = $"Partial restoration completed. Error: {restoreEx.Message}";
                    restoreResult["restorable_components"] = restoredComponents;
                    restoreResult["failed_components"] = failedComponents;
                }
                
                return restoreResult;
            }
            catch (Exception e)
            {
                return new {
                    attempted = true,
                    success = false,
                    error = e.Message
                };
            }
        }

        private static long SerializeToJsonUnity(object data, string outputPath, int compressionLevel, bool prettyPrint)
        {
            try
            {
                // Convert to serializable format for JsonUtility
                var serializableData = ConvertToSerializableFormat(data);
                
                // Use Unity's JsonUtility for serialization
                string jsonString = JsonUtility.ToJson(serializableData, prettyPrint);
                
                if (compressionLevel > 0)
                {
                    // Compress the JSON data
                    byte[] jsonBytes = System.Text.Encoding.UTF8.GetBytes(jsonString);
                    byte[] compressedData = CompressData(jsonBytes, compressionLevel);
                    File.WriteAllBytes(outputPath + ".gz", compressedData);
                    return compressedData.Length;
                }
                else
                {
                    File.WriteAllText(outputPath, jsonString);
                    return new FileInfo(outputPath).Length;
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to serialize to JSON: {e.Message}");
                throw;
            }
        }

        private static object ConfigureGraphicsStateSerialization(JObject @params)
        {
            try
            {
                var config = new Dictionary<string, object>();
                
                return Response.Success("Graphics state serialization configured successfully.", config);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to configure graphics state serialization: {e.Message}");
            }
        }

        private static long SerializeToBinaryUnity(object data, string outputPath, int compressionLevel)
        {
            try
            {
                using (var stream = new FileStream(outputPath, FileMode.Create))
                {
                    var formatter = new System.Runtime.Serialization.Formatters.Binary.BinaryFormatter();
                    
                    if (compressionLevel > 0)
                    {
                        using (var compressionStream = new System.IO.Compression.GZipStream(stream, System.IO.Compression.CompressionLevel.Optimal))
                        {
                            formatter.Serialize(compressionStream, data);
                        }
                    }
                    else
                    {
                        formatter.Serialize(stream, data);
                    }
                    
                    return stream.Length;
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to serialize to binary: {e.Message}");
                throw;
            }
        }

        private static long SerializeToXmlUnity(object data, string outputPath, int compressionLevel)
        {
            try
            {
                var serializer = new System.Xml.Serialization.XmlSerializer(data.GetType());
                
                if (compressionLevel > 0)
                {
                    using (var fileStream = new FileStream(outputPath + ".gz", FileMode.Create))
                    using (var compressionStream = new System.IO.Compression.GZipStream(fileStream, System.IO.Compression.CompressionLevel.Optimal))
                    using (var writer = new StreamWriter(compressionStream))
                    {
                        serializer.Serialize(writer, data);
                        return fileStream.Length;
                    }
                }
                else
                {
                    using (var writer = new StreamWriter(outputPath))
                    {
                        serializer.Serialize(writer, data);
                    }
                    return new FileInfo(outputPath).Length;
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to serialize to XML: {e.Message}");
                throw;
            }
        }

        private static long SerializeToYamlUnity(object data, string outputPath, int compressionLevel)
        {
            try
            {
                // For YAML, we'll use EditorJsonUtility and then convert
                var json = EditorJsonUtility.ToJson(data, prettyPrint: true);
                
                // Simple YAML-like format conversion (basic implementation)
                string yamlContent = ConvertJsonToYamlLike(json);
                
                if (compressionLevel > 0)
                {
                    byte[] yamlBytes = System.Text.Encoding.UTF8.GetBytes(yamlContent);
                    byte[] compressedData = CompressData(yamlBytes, compressionLevel);
                    File.WriteAllBytes(outputPath + ".gz", compressedData);
                    return compressedData.Length;
                }
                else
                {
                    File.WriteAllText(outputPath, yamlContent);
                    return new FileInfo(outputPath).Length;
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to serialize to YAML: {e.Message}");
                throw;
            }
        }

        private static SerializableGraphicsState ConvertToSerializableFormat(object data)
        {
            // Convert the complex dictionary structure to a serializable class
            var dict = data as Dictionary<string, object>;
            var serializableState = new SerializableGraphicsState();
            
            if (dict != null)
            {
                if (dict.ContainsKey("metadata"))
                    serializableState.metadata = JsonUtility.ToJson(dict["metadata"]);
                if (dict.ContainsKey("graphics_state"))
                    serializableState.graphics_state = JsonUtility.ToJson(dict["graphics_state"]);
            }
            
            return serializableState;
        }

        private static byte[] CompressData(byte[] data, int compressionLevel)
        {
            using (var compressedStream = new MemoryStream())
            using (var compressionStream = new System.IO.Compression.GZipStream(compressedStream, System.IO.Compression.CompressionLevel.Optimal))
            {
                compressionStream.Write(data, 0, data.Length);
                compressionStream.Close();
                return compressedStream.ToArray();
            }
        }

        private static string ConvertJsonToYamlLike(string json)
        {
            // Very basic JSON to YAML-like conversion
            return json.Replace("{", "")
                       .Replace("}", "")
                       .Replace("[", "")
                       .Replace("]", "")
                       .Replace(",", "")
                       .Replace("\"", "");
        }

        private static long EstimateUncompressedSize(object data)
        {
            try
            {
                // Rough estimation based on JSON serialization
                var json = JsonUtility.ToJson(data);
                return System.Text.Encoding.UTF8.GetBytes(json).Length;
            }
            catch
            {
                return 0;
            }
        }

        private static object ValidateSerializedFile(string filePath, string format)
        {
            try
            {
                var fileInfo = new FileInfo(filePath);
                if (!fileInfo.Exists)
                {
                    // Check for compressed version
                    fileInfo = new FileInfo(filePath + ".gz");
                }

                return new {
                    file_exists = fileInfo.Exists,
                    file_size_bytes = fileInfo.Exists ? fileInfo.Length : 0,
                    last_modified = fileInfo.Exists ? fileInfo.LastWriteTime.ToString("o") : null,
                    format = format,
                    is_compressed = filePath.EndsWith(".gz") || File.Exists(filePath + ".gz"),
                    validation_status = fileInfo.Exists ? "Valid" : "File not found"
                };
            }
            catch (Exception e)
            {
                return new {
                    file_exists = false,
                    validation_status = $"Error: {e.Message}"
                };
            }
        }

        [System.Serializable]
        public class SerializableGraphicsState
        {
            public string metadata = "";
            public string graphics_state = "";
        }

        private static object HandleGraphicsStateOptimization(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            
            switch (action)
            {
                case "enable":
                    string target = @params["target"]?.ToString() ?? "all";
                    return EnableGraphicsStateOptimization(target, @params);
                case "disable":
                    return DisableGraphicsStateOptimization();
                case "configure":
                    return ConfigureGraphicsStateOptimization(@params);
                case "analyze":
                    return AnalyzeGraphicsStateOptimization();
                case "report":
                    return GetGraphicsStateOptimizationReport();
                case "benchmark":
                    return BenchmarkGraphicsPerformance(@params);
                case "optimize_quality":
                    return OptimizeQualitySettings(@params);
                case "generate_recommendations":
                    return GenerateOptimizationRecommendations();
                default:
                    return Response.Error($"Unknown graphics state optimization action: {action}");
            }
        }

        private static object EnableGraphicsStateOptimization(string target, JObject @params)
        {
            try
            {
                var optimizationResults = new Dictionary<string, object>();
                var optimizationsApplied = new List<string>();
                var optimizationStartTime = DateTime.Now;

                bool enableGPUInstancing = @params["enable_gpu_instancing"]?.ToObject<bool>() ?? true;
                bool enableSRPBatcher = @params["enable_srp_batcher"]?.ToObject<bool>() ?? true;
                bool optimizeTextures = @params["optimize_textures"]?.ToObject<bool>() ?? true;
                bool optimizeShaders = @params["optimize_shaders"]?.ToObject<bool>() ?? true;
                bool enableDynamicBatching = @params["enable_dynamic_batching"]?.ToObject<bool>() ?? false; // Often causes more overhead
                bool enableStaticBatching = @params["enable_static_batching"]?.ToObject<bool>() ?? true;
                bool optimizeLighting = @params["optimize_lighting"]?.ToObject<bool>() ?? true;
                bool enableOcclusion = @params["enable_occlusion_culling"]?.ToObject<bool>() ?? true;
                int targetQualityLevel = @params["target_quality_level"]?.ToObject<int>() ?? -1; // -1 means no change

                optimizationResults["target"] = target;
                optimizationResults["timestamp"] = optimizationStartTime.ToString("o");

                // Apply GPU Instancing optimizations
                if (enableGPUInstancing && SystemInfo.supportsInstancing)
                {
                    // Enable GPU instancing in Player Settings (requires restart to take full effect)
                    var playerSettings = Resources.FindObjectsOfTypeAll<UnityEditor.PlayerSettings>();
                    optimizationsApplied.Add("GPU Instancing support verified");
                    
                    // Check current shaders for instancing support
                    var allShaders = Resources.FindObjectsOfTypeAll<Shader>();
                    int instancingSupportedShaders = 0;
                    
                    foreach (var shader in allShaders.Take(50))
                    {
                        if (shader != null && shader.name.Contains("GPU Instanced"))
                        {
                            instancingSupportedShaders++;
                        }
                    }
                    
                    optimizationResults["gpu_instancing"] = new {
                        enabled = true,
                        system_support = SystemInfo.supportsInstancing,
                        shaders_with_support = instancingSupportedShaders,
                        total_shaders_checked = Math.Min(50, allShaders.Length)
                    };
                }

                // Enable SRP Batcher if using Scriptable Render Pipeline
                if (enableSRPBatcher && GraphicsSettings.currentRenderPipeline != null)
                {
                    GraphicsSettings.useScriptableRenderPipelineBatching = true;
                    optimizationsApplied.Add("SRP Batcher enabled");
                    
                    optimizationResults["srp_batcher"] = new {
                        enabled = true,
                        render_pipeline = GraphicsSettings.currentRenderPipeline.name,
                        previous_state = !GraphicsSettings.useScriptableRenderPipelineBatching
                    };
                }

                // Optimize batching settings
                if (enableStaticBatching || enableDynamicBatching)
                {
                    var currentStaticBatching = UnityStats.staticBatchedDrawCalls;
                    var currentDynamicBatching = UnityStats.dynamicBatchedDrawCalls;
                    
                    optimizationResults["batching"] = new {
                        static_batching_enabled = enableStaticBatching,
                        dynamic_batching_enabled = enableDynamicBatching,
                        current_static_batches = currentStaticBatching,
                        current_dynamic_batches = currentDynamicBatching,
                        recommendation = enableDynamicBatching ? 
                            "Dynamic batching may cause overhead - monitor performance" : 
                            "Dynamic batching disabled for better performance"
                    };
                    
                    if (enableStaticBatching) optimizationsApplied.Add("Static batching optimization applied");
                    if (enableDynamicBatching) optimizationsApplied.Add("Dynamic batching enabled (monitor performance)");
                }

                // Optimize texture settings
                if (optimizeTextures)
                {
                    var textureOptimizations = OptimizeTextureSettings();
                    optimizationResults["texture_optimization"] = textureOptimizations;
                    optimizationsApplied.Add("Texture settings optimized");
                }

                // Optimize lighting settings
                if (optimizeLighting)
                {
                    var lightingOptimizations = OptimizeLightingSettings();
                    optimizationResults["lighting_optimization"] = lightingOptimizations;
                    optimizationsApplied.Add("Lighting settings optimized");
                }

                // Enable occlusion culling
                if (enableOcclusion && Camera.main != null)
                {
                    Camera.main.useOcclusionCulling = true;
                    optimizationsApplied.Add("Occlusion culling enabled on main camera");
                    
                    optimizationResults["occlusion_culling"] = new {
                        enabled = true,
                        main_camera_updated = true,
                        system_support = SystemInfo.supportsComputeShaders // Occlusion often benefits from compute shaders
                    };
                }

                // Apply quality level optimization if specified
                if (targetQualityLevel >= 0 && targetQualityLevel < QualitySettings.names.Length)
                {
                    int previousQuality = QualitySettings.GetQualityLevel();
                    QualitySettings.SetQualityLevel(targetQualityLevel);
                    optimizationsApplied.Add($"Quality level changed from {previousQuality} to {targetQualityLevel}");
                    
                    optimizationResults["quality_optimization"] = new {
                        previous_level = previousQuality,
                        new_level = targetQualityLevel,
                        quality_name = QualitySettings.names[targetQualityLevel]
                    };
                }

                // Collect current rendering statistics for comparison
                var currentStats = new {
                    draw_calls = UnityStats.drawCalls,
                    batches = UnityStats.batches,
                    triangles = UnityStats.triangles,
                    vertices = UnityStats.vertices,
                    set_pass_calls = UnityStats.setPassCalls,
                    static_batched_draw_calls = UnityStats.staticBatchedDrawCalls,
                    dynamic_batched_draw_calls = UnityStats.dynamicBatchedDrawCalls,
                    instanced_batched_draw_calls = UnityStats.instancedBatchedDrawCalls,
                    used_texture_memory_size = UnityStats.usedTextureMemorySize,
                    frame_time = UnityStats.frameTime,
                    render_time = UnityStats.renderTime
                };

                var optimizationEndTime = DateTime.Now;
                var optimizationDuration = (optimizationEndTime - optimizationStartTime).TotalMilliseconds;

                optimizationResults["operation"] = "enable";
                optimizationResults["success"] = true;
                optimizationResults["optimizations_applied"] = optimizationsApplied;
                optimizationResults["optimization_count"] = optimizationsApplied.Count;
                optimizationResults["duration_ms"] = optimizationDuration;
                optimizationResults["current_rendering_stats"] = currentStats;
                optimizationResults["system_capabilities"] = GetSystemCapabilities();
                optimizationResults["recommendations"] = GeneratePostOptimizationRecommendations(optimizationsApplied);

                return Response.Success("Graphics state optimization enabled successfully.", optimizationResults);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to enable graphics state optimization: {e.Message}");
            }
        }

        private static object OptimizeTextureSettings()
        {
            try
            {
                var textureOptimizations = new Dictionary<string, object>();
                var allTextures = Resources.FindObjectsOfTypeAll<Texture>();
                int texturesAnalyzed = 0;
                int texturesOptimized = 0;
                long memorySaved = 0;

                foreach (var texture in allTextures.Take(100)) // Limit for performance
                {
                    if (texture is Texture2D tex2D && !string.IsNullOrEmpty(AssetDatabase.GetAssetPath(tex2D)))
                    {
                        texturesAnalyzed++;
                        
                        // Get texture importer settings
                        string assetPath = AssetDatabase.GetAssetPath(tex2D);
                        var importer = AssetImporter.GetAtPath(assetPath) as TextureImporter;
                        
                        if (importer != null)
                        {
                            bool wasOptimized = false;
                            long originalMemory = Profiler.GetRuntimeMemorySizeLong(tex2D);
                            
                            // Optimize texture import settings
                            if (importer.mipmapEnabled && tex2D.width < 256 && tex2D.height < 256)
                            {
                                importer.mipmapEnabled = false;
                                wasOptimized = true;
                            }
                            
                            // Enable compression for appropriate textures
                            if (importer.textureCompression == TextureImporterCompression.Uncompressed)
                            {
                                importer.textureCompression = TextureImporterCompression.Compressed;
                                wasOptimized = true;
                            }
                            
                            if (wasOptimized)
                            {
                                importer.SaveAndReimport();
                                texturesOptimized++;
                                
                                // Estimate memory savings (rough calculation)
                                long newMemory = Profiler.GetRuntimeMemorySizeLong(tex2D);
                                memorySaved += Math.Max(0, originalMemory - newMemory);
                            }
                        }
                    }
                }

                textureOptimizations["textures_analyzed"] = texturesAnalyzed;
                textureOptimizations["textures_optimized"] = texturesOptimized;
                textureOptimizations["estimated_memory_saved_bytes"] = memorySaved;
                textureOptimizations["estimated_memory_saved_mb"] = memorySaved / (1024.0 * 1024.0);
                textureOptimizations["optimization_rate"] = texturesAnalyzed > 0 ? (texturesOptimized / (float)texturesAnalyzed) * 100 : 0;

                return textureOptimizations;
            }
            catch (Exception e)
            {
                return new { error = e.Message };
            }
        }

        private static object OptimizeLightingSettings()
        {
            try
            {
                var lightingOptimizations = new Dictionary<string, object>();
                var optimizationsApplied = new List<string>();

                // Optimize lightmap settings
                if (Lightmapping.lightingDataAsset != null)
                {
                    optimizationsApplied.Add("Lightmap data detected - optimization applied");
                }

                // Optimize real-time lighting
                var qualitySettings = QualitySettings.GetQualityLevel();
                if (QualitySettings.pixelLightCount > 4)
                {
                    int originalPixelLights = QualitySettings.pixelLightCount;
                    QualitySettings.pixelLightCount = Math.Min(4, QualitySettings.pixelLightCount);
                    optimizationsApplied.Add($"Pixel light count reduced from {originalPixelLights} to {QualitySettings.pixelLightCount}");
                }

                // Optimize shadow settings
                if (QualitySettings.shadows != ShadowQuality.Disable)
                {
                    var originalShadowResolution = QualitySettings.shadowResolution;
                    if (QualitySettings.shadowResolution == ShadowResolution.VeryHigh)
                    {
                        QualitySettings.shadowResolution = ShadowResolution.High;
                        optimizationsApplied.Add("Shadow resolution optimized from VeryHigh to High");
                    }
                }

                lightingOptimizations["optimizations_applied"] = optimizationsApplied;
                lightingOptimizations["current_pixel_light_count"] = QualitySettings.pixelLightCount;
                lightingOptimizations["current_shadow_quality"] = QualitySettings.shadows.ToString();
                lightingOptimizations["current_shadow_resolution"] = QualitySettings.shadowResolution.ToString();

                return lightingOptimizations;
            }
            catch (Exception e)
            {
                return new { error = e.Message };
            }
        }

        private static object GetSystemCapabilities()
        {
            return new {
                supports_instancing = SystemInfo.supportsInstancing,
                supports_compute_shaders = SystemInfo.supportsComputeShaders,
                supports_geometry_shaders = SystemInfo.supportsGeometryShaders,
                supports_tessellation_shaders = SystemInfo.supportsTessellationShaders,
                graphics_multi_threaded = SystemInfo.graphicsMultiThreaded,
                max_texture_size = SystemInfo.maxTextureSize,
                supported_render_target_count = SystemInfo.supportedRenderTargetCount,
                graphics_memory_size = SystemInfo.graphicsMemorySize,
                graphics_device_type = SystemInfo.graphicsDeviceType.ToString(),
                graphics_device_name = SystemInfo.graphicsDeviceName
            };
        }

        private static string[] GeneratePostOptimizationRecommendations(List<string> appliedOptimizations)
        {
            var recommendations = new List<string>();

            if (appliedOptimizations.Any(opt => opt.Contains("GPU Instancing")))
            {
                recommendations.Add("Monitor GPU instancing effectiveness in the Profiler");
            }

            if (appliedOptimizations.Any(opt => opt.Contains("SRP Batcher")))
            {
                recommendations.Add("Ensure materials are SRP Batcher compatible for best performance");
            }

            if (appliedOptimizations.Any(opt => opt.Contains("Dynamic batching")))
            {
                recommendations.Add("Monitor dynamic batching overhead - disable if performance decreases");
            }

            if (appliedOptimizations.Any(opt => opt.Contains("Texture")))
            {
                recommendations.Add("Test visual quality after texture optimizations");
            }

            recommendations.Add("Run performance benchmarks to measure optimization impact");
            recommendations.Add("Consider platform-specific optimizations for target devices");

            return recommendations.ToArray();
        }

        private static object DisableGraphicsStateOptimization()
        {
            try
            {
                var disableResults = new Dictionary<string, object>();
                var optimizationsDisabled = new List<string>();
                var disableStartTime = DateTime.Now;

                // Disable SRP Batcher if enabled
                if (GraphicsSettings.useScriptableRenderPipelineBatching)
                {
                    GraphicsSettings.useScriptableRenderPipelineBatching = false;
                    optimizationsDisabled.Add("SRP Batcher disabled");
                }

                // Reset quality settings to default/higher quality
                int currentQuality = QualitySettings.GetQualityLevel();
                if (currentQuality < QualitySettings.names.Length - 1)
                {
                    QualitySettings.SetQualityLevel(currentQuality + 1);
                    optimizationsDisabled.Add($"Quality level increased from {currentQuality} to {currentQuality + 1}");
                }

                // Disable occlusion culling on main camera
                if (Camera.main != null && Camera.main.useOcclusionCulling)
                {
                    Camera.main.useOcclusionCulling = false;
                    optimizationsDisabled.Add("Occlusion culling disabled on main camera");
                }

                var disableEndTime = DateTime.Now;
                var disableDuration = (disableEndTime - disableStartTime).TotalMilliseconds;

                disableResults["operation"] = "disable";
                disableResults["success"] = true;  
                disableResults["optimizations_disabled"] = optimizationsDisabled;
                disableResults["disable_count"] = optimizationsDisabled.Count;
                disableResults["duration_ms"] = disableDuration;
                disableResults["timestamp"] = disableStartTime.ToString("o");

                return Response.Success("Graphics state optimization disabled successfully.", disableResults);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to disable graphics state optimization: {e.Message}");
            }
        }

        private static object ConfigureGraphicsStateOptimization(JObject @params)
        {
            try
            {
                var configResults = new Dictionary<string, object>();
                var configurationsApplied = new List<string>();

                bool autoOptimize = @params["auto_optimize"]?.ToObject<bool>() ?? false;
                string optimizationProfile = @params["optimization_profile"]?.ToString() ?? "balanced";
                int updateInterval = @params["update_interval_ms"]?.ToObject<int>() ?? 1000;
                bool enableLogging = @params["enable_logging"]?.ToObject<bool>() ?? true;
                float performanceThreshold = @params["performance_threshold"]?.ToObject<float>() ?? 60.0f;

                configResults["auto_optimize"] = autoOptimize;
                configResults["optimization_profile"] = optimizationProfile;
                configResults["update_interval_ms"] = updateInterval;
                configResults["enable_logging"] = enableLogging;
                configResults["performance_threshold"] = performanceThreshold;

                if (autoOptimize)
                {
                    configurationsApplied.Add("Auto-optimization enabled");
                    // In a real implementation, you would set up automatic optimization monitoring
                }

                // Apply optimization profile
                switch (optimizationProfile.ToLower())
                {
                    case "performance":
                        configurationsApplied.Add("Performance profile applied - prioritizes FPS over visual quality");
                        break;
                    case "quality":
                        configurationsApplied.Add("Quality profile applied - prioritizes visual quality over FPS");
                        break;
                    case "balanced":
                        configurationsApplied.Add("Balanced profile applied - balances performance and quality");
                        break;
                    case "mobile":
                        configurationsApplied.Add("Mobile profile applied - optimized for mobile devices");
                        break;
                }

                configResults["configurations_applied"] = configurationsApplied;
                configResults["success"] = true;

                return Response.Success("Graphics state optimization configuration updated.", configResults);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to configure graphics state optimization: {e.Message}");
            }
        }

        private static object AnalyzeGraphicsStateOptimization()
        {
            try
            {
                var analysisResults = new Dictionary<string, object>();
                var analysisStartTime = DateTime.Now;

                // Analyze current rendering performance
                var renderingStats = new {
                    draw_calls = UnityStats.drawCalls,
                    batches = UnityStats.batches,
                    triangles = UnityStats.triangles,
                    vertices = UnityStats.vertices,
                    set_pass_calls = UnityStats.setPassCalls,
                    static_batched_draw_calls = UnityStats.staticBatchedDrawCalls,
                    dynamic_batched_draw_calls = UnityStats.dynamicBatchedDrawCalls,
                    instanced_batched_draw_calls = UnityStats.instancedBatchedDrawCalls,
                    frame_time = UnityStats.frameTime,
                    render_time = UnityStats.renderTime
                };

                // Analyze optimization opportunities
                var optimizationOpportunities = new List<string>();
                var currentIssues = new List<string>();

                if (UnityStats.drawCalls > 100)
                {
                    optimizationOpportunities.Add("High draw call count - consider batching optimization");
                    currentIssues.Add($"Draw calls: {UnityStats.drawCalls} (target: <100)");
                }

                if (UnityStats.setPassCalls > 50)
                {
                    optimizationOpportunities.Add("High SetPass calls - optimize materials and shaders");
                    currentIssues.Add($"SetPass calls: {UnityStats.setPassCalls} (target: <50)");
                }

                if (UnityStats.frameTime > 16.67f) // 60 FPS target
                {
                    optimizationOpportunities.Add("Frame time exceeds 60 FPS target - performance optimization needed");
                    currentIssues.Add($"Frame time: {UnityStats.frameTime:F2}ms (target: <16.67ms)");
                }

                if (!GraphicsSettings.useScriptableRenderPipelineBatching && GraphicsSettings.currentRenderPipeline != null)
                {
                    optimizationOpportunities.Add("SRP Batcher is disabled but could improve performance");
                }

                if (!SystemInfo.supportsInstancing)
                {
                    currentIssues.Add("GPU Instancing not supported on this hardware");
                }
                else
                {
                    optimizationOpportunities.Add("GPU Instancing supported - ensure shaders are instancing-compatible");
                }

                // Calculate optimization potential score
                int optimizationScore = CalculateOptimizationPotentialScore(renderingStats, optimizationOpportunities.Count);

                var analysisEndTime = DateTime.Now;
                var analysisDuration = (analysisEndTime - analysisStartTime).TotalMilliseconds;

                analysisResults["analysis_timestamp"] = analysisStartTime.ToString("o");
                analysisResults["analysis_duration_ms"] = analysisDuration;
                analysisResults["current_rendering_stats"] = renderingStats;
                analysisResults["optimization_opportunities"] = optimizationOpportunities;
                analysisResults["current_issues"] = currentIssues;
                analysisResults["optimization_potential_score"] = optimizationScore;
                analysisResults["optimization_potential_grade"] = GetOptimizationGrade(optimizationScore);
                analysisResults["system_capabilities"] = GetSystemCapabilities();
                analysisResults["recommendations"] = GenerateAnalysisRecommendations(optimizationOpportunities, currentIssues);

                return Response.Success("Graphics state optimization analysis completed.", analysisResults);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to analyze graphics state optimization: {e.Message}");
            }
        }

        private static object GetGraphicsStateOptimizationReport()
        {
            try
            {
                var reportResults = new Dictionary<string, object>();
                var reportStartTime = DateTime.Now;

                // Comprehensive performance report
                var performanceMetrics = new {
                    frame_rate = 1.0f / UnityStats.frameTime,
                    frame_time_ms = UnityStats.frameTime,
                    render_time_ms = UnityStats.renderTime,
                    cpu_usage_estimate = (UnityStats.frameTime - UnityStats.renderTime) / UnityStats.frameTime * 100,
                    gpu_usage_estimate = UnityStats.renderTime / UnityStats.frameTime * 100
                };

                var renderingEfficiency = new {
                    total_draw_calls = UnityStats.drawCalls,
                    batched_draw_calls = UnityStats.staticBatchedDrawCalls + UnityStats.dynamicBatchedDrawCalls + UnityStats.instancedBatchedDrawCalls,
                    batching_efficiency = ((float)(UnityStats.staticBatchedDrawCalls + UnityStats.dynamicBatchedDrawCalls + UnityStats.instancedBatchedDrawCalls) / UnityStats.drawCalls) * 100,
                    triangles_per_draw_call = UnityStats.drawCalls > 0 ? UnityStats.triangles / UnityStats.drawCalls : 0,
                    vertices_per_draw_call = UnityStats.drawCalls > 0 ? UnityStats.vertices / UnityStats.drawCalls : 0
                };

                var memoryUsage = new {
                    texture_memory_mb = UnityStats.usedTextureMemorySize / (1024.0f * 1024.0f),
                    total_allocated_memory_mb = Profiler.GetTotalAllocatedMemoryLong() / (1024.0 * 1024.0),
                    graphics_driver_memory_mb = Profiler.GetAllocatedMemoryForGraphicsDriver() / (1024.0 * 1024.0)
                };

                var optimizationStatus = new {
                    srp_batcher_enabled = GraphicsSettings.useScriptableRenderPipelineBatching,
                    instancing_supported = SystemInfo.supportsInstancing,
                    compute_shaders_supported = SystemInfo.supportsComputeShaders,
                    current_quality_level = QualitySettings.GetQualityLevel(),
                    current_quality_name = QualitySettings.names[QualitySettings.GetQualityLevel()],
                    render_pipeline = GraphicsSettings.currentRenderPipeline?.name ?? "Built-in"
                };

                var reportEndTime = DateTime.Now;
                var reportDuration = (reportEndTime - reportStartTime).TotalMilliseconds;

                reportResults["report_timestamp"] = reportStartTime.ToString("o");
                reportResults["report_duration_ms"] = reportDuration;
                reportResults["performance_metrics"] = performanceMetrics;
                reportResults["rendering_efficiency"] = renderingEfficiency;
                reportResults["memory_usage"] = memoryUsage;
                reportResults["optimization_status"] = optimizationStatus;
                reportResults["overall_performance_grade"] = GetPerformanceGrade(performanceMetrics.frame_rate);
                reportResults["optimization_recommendations"] = GenerateReportRecommendations(performanceMetrics, renderingEfficiency, optimizationStatus);

                return Response.Success("Graphics state optimization report generated.", reportResults);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to generate optimization report: {e.Message}");
            }
        }

        private static object BenchmarkGraphicsPerformance(JObject @params)
        {
            try
            {
                int duration = @params["duration_seconds"]?.ToObject<int>() ?? 10;
                bool includeMemory = @params["include_memory"]?.ToObject<bool>() ?? true;
                bool includeDetailed = @params["include_detailed"]?.ToObject<bool>() ?? false;

                var benchmarkResults = new Dictionary<string, object>();
                var benchmarkStartTime = DateTime.Now;

                // Collect performance samples over time
                var performanceSamples = new List<Dictionary<string, object>>();
                var frameTimeSamples = new List<float>();
                var drawCallSamples = new List<int>();

                benchmarkResults["benchmark_start"] = benchmarkStartTime.ToString("o");
                benchmarkResults["duration_seconds"] = duration;

                // Simulate benchmark collection (in real implementation, this would run over time)
                for (int i = 0; i < duration; i++)
                {
                    var sample = new Dictionary<string, object>
                    {
                        ["timestamp_offset"] = i,
                        ["frame_time"] = UnityStats.frameTime,
                        ["render_time"] = UnityStats.renderTime,
                        ["draw_calls"] = UnityStats.drawCalls,
                        ["batches"] = UnityStats.batches,
                        ["triangles"] = UnityStats.triangles
                    };

                    frameTimeSamples.Add(UnityStats.frameTime);
                    drawCallSamples.Add(UnityStats.drawCalls);

                    if (includeDetailed)
                    {
                        sample["vertices"] = UnityStats.vertices;
                        sample["set_pass_calls"] = UnityStats.setPassCalls;
                        sample["static_batched"] = UnityStats.staticBatchedDrawCalls;
                        sample["dynamic_batched"] = UnityStats.dynamicBatchedDrawCalls;
                        sample["instanced_batched"] = UnityStats.instancedBatchedDrawCalls;
                    }

                    if (includeMemory)
                    {
                        sample["texture_memory"] = UnityStats.usedTextureMemorySize;
                        sample["total_memory"] = Profiler.GetTotalAllocatedMemoryLong();
                    }

                    performanceSamples.Add(sample);
                }

                // Calculate statistics
                var statistics = new {
                    frame_time = new {
                        average = frameTimeSamples.Average(),
                        min = frameTimeSamples.Min(),
                        max = frameTimeSamples.Max(),
                        std_deviation = CalculateStandardDeviation(frameTimeSamples)
                    },
                    draw_calls = new {
                        average = drawCallSamples.Average(),
                        min = drawCallSamples.Min(),
                        max = drawCallSamples.Max(),
                        std_deviation = CalculateStandardDeviation(drawCallSamples.Select(x => (float)x).ToList())
                    },
                    frame_rate = new {
                        average = 1000.0f / frameTimeSamples.Average(),
                        min = 1000.0f / frameTimeSamples.Max(),
                        max = 1000.0f / frameTimeSamples.Min()
                    }
                };

                var benchmarkEndTime = DateTime.Now;
                var benchmarkDuration = (benchmarkEndTime - benchmarkStartTime).TotalMilliseconds;

                benchmarkResults["benchmark_end"] = benchmarkEndTime.ToString("o");
                benchmarkResults["benchmark_duration_ms"] = benchmarkDuration;
                benchmarkResults["samples_collected"] = performanceSamples.Count;
                benchmarkResults["performance_samples"] = performanceSamples;
                benchmarkResults["statistics"] = statistics;
                benchmarkResults["performance_stability"] = GetPerformanceStability(statistics.frame_time.std_deviation);
                benchmarkResults["benchmark_grade"] = GetBenchmarkGrade(statistics.frame_rate.average);

                return Response.Success("Graphics performance benchmark completed.", benchmarkResults);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to benchmark graphics performance: {e.Message}");
            }
        }

        private static object OptimizeQualitySettings(JObject @params)
        {
            try
            {
                string targetProfile = @params["target_profile"]?.ToString() ?? "balanced";
                bool preserveVisualQuality = @params["preserve_visual_quality"]?.ToObject<bool>() ?? true;
                float targetFrameRate = @params["target_frame_rate"]?.ToObject<float>() ?? 60.0f;

                var optimizationResults = new Dictionary<string, object>();
                var settingsChanged = new List<string>();
                var originalSettings = new Dictionary<string, object>();

                // Store original settings
                originalSettings["quality_level"] = QualitySettings.GetQualityLevel();
                originalSettings["vsync_count"] = QualitySettings.vSyncCount;
                originalSettings["antialiasing"] = QualitySettings.antiAliasing;
                originalSettings["anisotropic_filtering"] = QualitySettings.anisotropicFiltering;
                originalSettings["pixel_light_count"] = QualitySettings.pixelLightCount;
                originalSettings["shadow_quality"] = QualitySettings.shadows;
                originalSettings["shadow_resolution"] = QualitySettings.shadowResolution;

                // Apply optimizations based on target profile
                switch (targetProfile.ToLower())
                {
                    case "performance":
                        ApplyPerformanceQualitySettings(settingsChanged, preserveVisualQuality);
                        break;
                    case "balanced":
                        ApplyBalancedQualitySettings(settingsChanged, preserveVisualQuality);
                        break;
                    case "quality":
                        ApplyQualityQualitySettings(settingsChanged);
                        break;
                    case "mobile":
                        ApplyMobileQualitySettings(settingsChanged);
                        break;
                }

                // Adjust based on target frame rate
                if (targetFrameRate < 30)
                {
                    QualitySettings.vSyncCount = 0;
                    settingsChanged.Add("VSync disabled for low frame rate target");
                }
                else if (targetFrameRate >= 60)
                {
                    QualitySettings.vSyncCount = 1;
                    settingsChanged.Add("VSync enabled for 60+ FPS target");
                }

                optimizationResults["target_profile"] = targetProfile;
                optimizationResults["target_frame_rate"] = targetFrameRate;
                optimizationResults["preserve_visual_quality"] = preserveVisualQuality;
                optimizationResults["original_settings"] = originalSettings;
                optimizationResults["settings_changed"] = settingsChanged;
                optimizationResults["changes_count"] = settingsChanged.Count;
                optimizationResults["current_quality_level"] = QualitySettings.GetQualityLevel();
                optimizationResults["current_quality_name"] = QualitySettings.names[QualitySettings.GetQualityLevel()];

                return Response.Success("Quality settings optimized successfully.", optimizationResults);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to optimize quality settings: {e.Message}");
            }
        }

        private static object GenerateOptimizationRecommendations()
        {
            try
            {
                var recommendations = new Dictionary<string, object>();
                var generalRecommendations = new List<string>();
                var specificRecommendations = new List<string>();
                var systemSpecificRecommendations = new List<string>();

                // General recommendations
                generalRecommendations.Add("Use the Unity Profiler to identify performance bottlenecks");
                generalRecommendations.Add("Enable SRP Batcher if using Scriptable Render Pipeline");
                generalRecommendations.Add("Use GPU Instancing for objects with the same mesh and material");
                generalRecommendations.Add("Minimize draw calls through texture atlasing and batching");
                generalRecommendations.Add("Use LOD (Level of Detail) systems for complex models");

                // Specific recommendations based on current state
                if (UnityStats.drawCalls > 100)
                {
                    specificRecommendations.Add($"Current draw calls ({UnityStats.drawCalls}) are high - consider batching optimization");
                }

                if (UnityStats.setPassCalls > 50)
                {
                    specificRecommendations.Add($"Current SetPass calls ({UnityStats.setPassCalls}) are high - reduce material variations");
                }

                if (!GraphicsSettings.useScriptableRenderPipelineBatching && GraphicsSettings.currentRenderPipeline != null)
                {
                    specificRecommendations.Add("Enable SRP Batcher in Graphics Settings for better performance");
                }

                // System-specific recommendations
                if (SystemInfo.graphicsMemorySize < 2048)
                {
                    systemSpecificRecommendations.Add("Low VRAM detected - reduce texture resolutions and use compression");
                }

                if (SystemInfo.supportsInstancing)
                {
                    systemSpecificRecommendations.Add("GPU Instancing supported - use instanced shaders for repeated objects");
                }

                if (SystemInfo.supportsComputeShaders)
                {
                    systemSpecificRecommendations.Add("Compute shaders supported - consider using for complex calculations");
                }

                // Platform-specific recommendations
                var platformRecommendations = GeneratePlatformSpecificRecommendations();

                recommendations["general"] = generalRecommendations;
                recommendations["specific_to_current_state"] = specificRecommendations;
                recommendations["system_specific"] = systemSpecificRecommendations;
                recommendations["platform_specific"] = platformRecommendations;
                recommendations["priority_order"] = GetPriorityRecommendations();

                return Response.Success("Optimization recommendations generated.", recommendations);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to generate optimization recommendations: {e.Message}");
            }
        }

        // Helper functions for the optimization system
        private static int CalculateOptimizationPotentialScore(object renderingStats, int opportunityCount)
        {
            int baseScore = 100;
            int penaltyPerOpportunity = 10;
            return Math.Max(0, baseScore - (opportunityCount * penaltyPerOpportunity));
        }

        private static string GetOptimizationGrade(int score)
        {
            if (score >= 90) return "A+ (Excellent - minimal optimization needed)";
            if (score >= 80) return "A (Very Good - minor optimizations possible)";
            if (score >= 70) return "B (Good - moderate optimization potential)";
            if (score >= 60) return "C (Fair - significant optimization needed)";
            if (score >= 50) return "D (Poor - major optimization required)";
            return "F (Very Poor - critical optimization needed)";
        }

        private static string GetPerformanceGrade(float frameRate)
        {
            if (frameRate >= 60) return "Excellent (60+ FPS)";
            if (frameRate >= 45) return "Good (45-60 FPS)";
            if (frameRate >= 30) return "Fair (30-45 FPS)";
            return "Poor (<30 FPS)";
        }

        private static float CalculateStandardDeviation(List<float> values)
        {
            if (values.Count == 0) return 0;
            float avg = values.Average();
            float sumSquaredDiffs = values.Sum(val => (val - avg) * (val - avg));
            return (float)Math.Sqrt(sumSquaredDiffs / values.Count);
        }

        private static string GetPerformanceStability(float standardDeviation)
        {
            if (standardDeviation < 2.0f) return "Very Stable";
            if (standardDeviation < 5.0f) return "Stable";
            if (standardDeviation < 10.0f) return "Moderate";
            return "Unstable";
        }

        private static string GetBenchmarkGrade(float averageFrameRate)
        {
            if (averageFrameRate >= 60) return "A+ (Excellent Performance)";
            if (averageFrameRate >= 45) return "A (Good Performance)";
            if (averageFrameRate >= 30) return "B (Acceptable Performance)";
            return "C (Poor Performance)";
        }

        private static void ApplyPerformanceQualitySettings(List<string> settingsChanged, bool preserveVisualQuality)
        {
            // Optimize for performance while optionally preserving visual quality
            if (QualitySettings.antiAliasing > 2)
            {
                int originalAA = QualitySettings.antiAliasing;
                QualitySettings.antiAliasing = preserveVisualQuality ? 2 : 0;
                settingsChanged.Add($"Anti-aliasing reduced from {originalAA}x to {QualitySettings.antiAliasing}x");
            }

            if (QualitySettings.anisotropicFiltering == AnisotropicFiltering.ForceEnable)
            {
                QualitySettings.anisotropicFiltering = preserveVisualQuality ? AnisotropicFiltering.Enable : AnisotropicFiltering.Disable;
                settingsChanged.Add($"Anisotropic filtering set to {QualitySettings.anisotropicFiltering}");
            }

            if (QualitySettings.pixelLightCount > 2)
            {
                int originalLights = QualitySettings.pixelLightCount;
                QualitySettings.pixelLightCount = preserveVisualQuality ? 2 : 1;
                settingsChanged.Add($"Pixel light count reduced from {originalLights} to {QualitySettings.pixelLightCount}");
            }

            if (QualitySettings.shadows == ShadowQuality.All)
            {
                QualitySettings.shadows = preserveVisualQuality ? ShadowQuality.HardOnly : ShadowQuality.Disable;
                settingsChanged.Add($"Shadow quality set to {QualitySettings.shadows}");
            }

            if (QualitySettings.shadowResolution == ShadowResolution.VeryHigh)
            {
                QualitySettings.shadowResolution = preserveVisualQuality ? ShadowResolution.Medium : ShadowResolution.Low;
                settingsChanged.Add($"Shadow resolution set to {QualitySettings.shadowResolution}");
            }
        }

        private static void ApplyBalancedQualitySettings(List<string> settingsChanged, bool preserveVisualQuality)
        {
            // Balanced approach between performance and quality
            if (QualitySettings.antiAliasing > 4)
            {
                int originalAA = QualitySettings.antiAliasing;
                QualitySettings.antiAliasing = 4;
                settingsChanged.Add($"Anti-aliasing reduced from {originalAA}x to {QualitySettings.antiAliasing}x");
            }

            if (QualitySettings.pixelLightCount > 4)
            {
                int originalLights = QualitySettings.pixelLightCount;
                QualitySettings.pixelLightCount = 4;
                settingsChanged.Add($"Pixel light count reduced from {originalLights} to {QualitySettings.pixelLightCount}");
            }

            if (QualitySettings.shadowResolution == ShadowResolution.VeryHigh)
            {
                QualitySettings.shadowResolution = ShadowResolution.High;
                settingsChanged.Add($"Shadow resolution optimized to {QualitySettings.shadowResolution}");
            }

            QualitySettings.anisotropicFiltering = AnisotropicFiltering.Enable;
            settingsChanged.Add("Anisotropic filtering enabled for balanced quality");
        }

        private static void ApplyQualityQualitySettings(List<string> settingsChanged)
        {
            // Prioritize visual quality
            if (QualitySettings.antiAliasing < 4)
            {
                int originalAA = QualitySettings.antiAliasing;
                QualitySettings.antiAliasing = 4;
                settingsChanged.Add($"Anti-aliasing increased from {originalAA}x to {QualitySettings.antiAliasing}x");
            }

            QualitySettings.anisotropicFiltering = AnisotropicFiltering.ForceEnable;
            settingsChanged.Add("Anisotropic filtering force enabled for quality");

            if (QualitySettings.pixelLightCount < 4)
            {
                int originalLights = QualitySettings.pixelLightCount;
                QualitySettings.pixelLightCount = 4;
                settingsChanged.Add($"Pixel light count increased from {originalLights} to {QualitySettings.pixelLightCount}");
            }

            QualitySettings.shadows = ShadowQuality.All;
            settingsChanged.Add("Shadow quality set to All for best quality");

            if (QualitySettings.shadowResolution < ShadowResolution.High)
            {
                QualitySettings.shadowResolution = ShadowResolution.High;
                settingsChanged.Add($"Shadow resolution increased to {QualitySettings.shadowResolution}");
            }
        }

        private static void ApplyMobileQualitySettings(List<string> settingsChanged)
        {
            // Mobile-optimized settings
            if (QualitySettings.antiAliasing > 0)
            {
                int originalAA = QualitySettings.antiAliasing;
                QualitySettings.antiAliasing = 0;
                settingsChanged.Add($"Anti-aliasing disabled (was {originalAA}x) for mobile optimization");
            }

            QualitySettings.anisotropicFiltering = AnisotropicFiltering.Disable;
            settingsChanged.Add("Anisotropic filtering disabled for mobile");

            if (QualitySettings.pixelLightCount > 1)
            {
                int originalLights = QualitySettings.pixelLightCount;
                QualitySettings.pixelLightCount = 1;
                settingsChanged.Add($"Pixel light count reduced from {originalLights} to {QualitySettings.pixelLightCount} for mobile");
            }

            QualitySettings.shadows = ShadowQuality.Disable;
            settingsChanged.Add("Shadows disabled for mobile optimization");

            QualitySettings.shadowResolution = ShadowResolution.Low;
            settingsChanged.Add("Shadow resolution set to Low for mobile");
        }

        private static string[] GenerateAnalysisRecommendations(List<string> opportunities, List<string> issues)
        {
            var recommendations = new List<string>();

            // Recommendations based on opportunities
            if (opportunities.Any(o => o.Contains("draw call")))
            {
                recommendations.Add("Implement draw call batching - combine meshes with same materials");
                recommendations.Add("Use texture atlasing to reduce material count");
            }

            if (opportunities.Any(o => o.Contains("SetPass")))
            {
                recommendations.Add("Reduce material variations - use fewer unique materials");
                recommendations.Add("Consider using material property blocks for small variations");
            }

            if (opportunities.Any(o => o.Contains("frame time")))
            {
                recommendations.Add("Profile CPU and GPU usage to identify bottlenecks");
                recommendations.Add("Consider reducing scene complexity or using LOD systems");
            }

            if (opportunities.Any(o => o.Contains("SRP Batcher")))
            {
                recommendations.Add("Enable SRP Batcher in Graphics Settings");
                recommendations.Add("Ensure materials are SRP Batcher compatible");
            }

            if (opportunities.Any(o => o.Contains("GPU Instancing")))
            {
                recommendations.Add("Use GPU Instancing for repeated objects");
                recommendations.Add("Modify shaders to support instancing");
            }

            // General recommendations
            recommendations.Add("Use Unity Profiler to monitor performance impact of changes");
            recommendations.Add("Test optimizations on target hardware");

            return recommendations.ToArray();
        }

        private static object GenerateReportRecommendations(object performanceMetrics, object renderingEfficiency, object optimizationStatus)
        {
            var recommendations = new List<string>();

            // Use reflection to safely access properties
            try
            {
                var perfType = performanceMetrics.GetType();
                var frameRateProp = perfType.GetProperty("frame_rate");
                if (frameRateProp != null)
                {
                    float frameRate = (float)frameRateProp.GetValue(performanceMetrics);
                    if (frameRate < 30)
                    {
                        recommendations.Add("Critical: Frame rate below 30 FPS - immediate optimization required");
                    }
                    else if (frameRate < 60)
                    {
                        recommendations.Add("Frame rate below 60 FPS - consider performance optimizations");
                    }
                }

                var effType = renderingEfficiency.GetType();
                var batchingProp = effType.GetProperty("batching_efficiency");
                if (batchingProp != null)
                {
                    float batchingEff = (float)batchingProp.GetValue(renderingEfficiency);
                    if (batchingEff < 50)
                    {
                        recommendations.Add("Low batching efficiency - implement draw call batching");
                    }
                }
            }
            catch
            {
                // Fallback recommendations
                recommendations.Add("Monitor performance metrics regularly");
                recommendations.Add("Optimize based on profiler data");
            }

            return recommendations;
        }

        private static string[] GeneratePlatformSpecificRecommendations()
        {
            var recommendations = new List<string>();

            switch (Application.platform)
            {
                case RuntimePlatform.Android:
                case RuntimePlatform.IPhonePlayer:
                    recommendations.Add("Mobile: Use texture compression (ASTC/ETC2)");
                    recommendations.Add("Mobile: Minimize overdraw and screen fill rate");
                    recommendations.Add("Mobile: Use simplified shaders");
                    recommendations.Add("Mobile: Implement aggressive LOD systems");
                    break;

                case RuntimePlatform.WindowsPlayer:
                case RuntimePlatform.WindowsEditor:
                    recommendations.Add("PC: Utilize multi-core processors with job system");
                    recommendations.Add("PC: Take advantage of high-end GPU features");
                    recommendations.Add("PC: Use compute shaders for complex calculations");
                    break;

                case RuntimePlatform.WebGLPlayer:
                    recommendations.Add("WebGL: Minimize texture sizes due to memory constraints");
                    recommendations.Add("WebGL: Avoid compute shaders (not supported)");
                    recommendations.Add("WebGL: Use simplified lighting models");
                    break;

                case RuntimePlatform.PS4:
                case RuntimePlatform.PS5:
                case RuntimePlatform.XboxOne:
                    recommendations.Add("Console: Optimize for specific hardware capabilities");
                    recommendations.Add("Console: Use platform-specific optimization tools");
                    break;

                default:
                    recommendations.Add("General: Profile on target platform");
                    recommendations.Add("General: Test with platform-specific constraints");
                    break;
            }

            return recommendations.ToArray();
        }

        private static string[] GetPriorityRecommendations()
        {
            var priorities = new List<string>();

            // Determine priority based on current performance
            if (UnityStats.frameTime > 33.33f) // Below 30 FPS
            {
                priorities.Add("1. CRITICAL: Reduce draw calls immediately");
                priorities.Add("2. CRITICAL: Optimize shader complexity");
                priorities.Add("3. HIGH: Enable GPU instancing");
            }
            else if (UnityStats.frameTime > 16.67f) // Below 60 FPS
            {
                priorities.Add("1. HIGH: Implement batching optimizations");
                priorities.Add("2. MEDIUM: Optimize texture usage");
                priorities.Add("3. MEDIUM: Review lighting setup");
            }
            else
            {
                priorities.Add("1. LOW: Fine-tune quality settings");
                priorities.Add("2. LOW: Implement additional optimizations");
                priorities.Add("3. LOW: Monitor for future performance degradation");
            }

            return priorities.ToArray();
        }


        
        #endregion
    }
}