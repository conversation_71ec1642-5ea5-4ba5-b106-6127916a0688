from mcp.server.fastmcp import FastMC<PERSON>, Context
from typing import Dict, Any, List, Optional
from unity_connection import get_unity_connection

def register_session_matchmaking_tools(mcp: FastMCP):
    """Register Session Matchmaking tools with the MCP server."""

    @mcp.tool()
    def session_matchmaking(
        ctx: Context,
        action: str,
        matchmaking_mode: Optional[str] = None,
        player_skill_level: Optional[float] = None,
        preferred_region: Optional[str] = None,
        game_mode: Optional[str] = None,
        max_players: Optional[int] = None,
        skill_tolerance: Optional[float] = None,
        latency_threshold: Optional[int] = None,
        queue_timeout: Optional[int] = None,
        custom_rules: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Sistema de Matchmaking para MOBA AURACRON usando Unity 6.2 Netcode.

        Funcionalidades:
        - start_matchmaking: Iniciar busca por partida
        - cancel_matchmaking: Cancelar busca
        - create_custom_lobby: Criar lobby customizado
        - join_lobby: Entrar em lobby
        - configure_matchmaking_rules: Configurar regras
        - get_queue_status: Obter status da fila
        - analyze_match_quality: Analisar qualidade da partida

        Args:
            action: Operação a executar
            matchmaking_mode: Modo (ranked, casual, custom)
            player_skill_level: Nível de skill do jogador (0-100)
            preferred_region: Região preferida
            game_mode: Modo de jogo (5v5, 3v3, 1v1)
            max_players: Máximo de jogadores
            skill_tolerance: Tolerância de skill (0.0-1.0)
            latency_threshold: Limite de latência em ms
            queue_timeout: Timeout da fila em segundos
            custom_rules: Regras customizadas

        Returns:
            Dictionary com resultados ('success', 'message', 'data')
        """
        try:
            params = {
                "action": action,
                "matchmaking_mode": matchmaking_mode,
                "player_skill_level": player_skill_level,
                "preferred_region": preferred_region,
                "game_mode": game_mode,
                "max_players": max_players,
                "skill_tolerance": skill_tolerance,
                "latency_threshold": latency_threshold,
                "queue_timeout": queue_timeout,
                "custom_rules": custom_rules
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("session_matchmaking", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Matchmaking operation completed successfully."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Failed to execute matchmaking operation.")
                }

        except Exception as e:
            return {"success": False, "message": f"Python error in session matchmaking: {str(e)}"}
