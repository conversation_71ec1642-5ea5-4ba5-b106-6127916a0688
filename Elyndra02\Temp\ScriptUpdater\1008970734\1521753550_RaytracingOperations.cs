using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;

using Unity.Collections;
using UnityMcpBridge.Editor.Helpers;

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// Handles Ray Tracing Operations for Unity 6.2 advanced ray tracing features.
    /// Implements professional-level ray tracing functionality using Unity's latest APIs.
    /// </summary>
    public static class RaytracingOperations
    {
        private static readonly List<string> ValidActions = new List<string>
        {
            "update", "rebuild", "get_info", "optimize",
            "add", "add_batch", "get_capacity", "clear",
            "remove", "remove_by_layer", "remove_by_criteria", "list",
            "cull", "update_culling", "get_culling_stats", "reset",
            "create", "compile", "delete",
            "update_transform", "update_material"
        };

        private static Dictionary<string, RayTracingAccelerationStructure> _accelerationStructures = new();
        private static Dictionary<string, RayTracingShader> _rayTracingShaders = new();
        
        /// <summary>
        /// Custom structure for ray tracing instance data compatible with Unity 6.2
        /// </summary>
        [System.Serializable]
        public struct RayTracingInstanceData
        {
            public Matrix4x4 objectToWorld;           // Mandatory: object-to-world transformation
            public uint renderingLayerMask;           // Optional: per-instance rendering layer mask
            public Matrix4x4 prevObjectToWorld;       // Optional: previous frame transformation for motion vectors
            
            public RayTracingInstanceData(Matrix4x4 transform, uint layerMask = 0xFFFFFFFF)
            {
                objectToWorld = transform;
                renderingLayerMask = layerMask;
                prevObjectToWorld = transform; // Initialize with current transform
            }
            
            public RayTracingInstanceData(Matrix4x4 transform, Matrix4x4 previousTransform, uint layerMask = 0xFFFFFFFF)
            {
                objectToWorld = transform;
                renderingLayerMask = layerMask;
                prevObjectToWorld = previousTransform;
            }
        }
        private static Dictionary<string, List<RayTracingInstanceData>> _instanceData = new();

        /// <summary>
        /// Main handler for Ray Tracing Operations commands.
        /// </summary>
        public static object HandleCommand(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidActions);
                return Response.Error(
                    $"Unknown action: '{action}'. Valid actions are: {validActionsList}"
                );
            }

            try
            {
                return action switch
                {
                    "update" or "rebuild" or "get_info" or "optimize" => HandleAccelerationStructure(@params),
                    "add" or "add_batch" or "get_capacity" or "clear" => HandleAddInstances(@params),
                    "remove" or "remove_by_layer" or "remove_by_criteria" or "list" => HandleRemoveInstances(@params),
                    "cull" or "update_culling" or "get_culling_stats" or "reset" => HandleCulling(@params),
                    "create" or "compile" or "delete" => HandlePipeline(@params),
                    "update_transform" or "update_material" => HandleGeometryUpdate(@params),
                    _ => Response.Error($"Unhandled action: '{action}'")
                };
            }
            catch (Exception e)
            {
                Debug.LogError($"[RaytracingOperations] Action '{action}' failed: {e}");
                return Response.Error($"Internal error processing action '{action}': {e.Message}");
            }
        }

        /// <summary>
        /// Handles acceleration structure operations with manual BLAS control.
        /// </summary>
        private static object HandleAccelerationStructure(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            string structureName = @params["acceleration_structure_name"]?.ToString() ?? "DefaultRTAS";
            bool forceRebuild = @params["force_rebuild"]?.ToObject<bool>() ?? false;
            string updateMode = @params["update_mode"]?.ToString() ?? "incremental";
            int? cullingMask = @params["culling_mask"]?.ToObject<int>();

            try
            {
                switch (action)
                {
                    case "update":
                    case "rebuild":
                        return UpdateAccelerationStructure(structureName, forceRebuild, updateMode, cullingMask, @params);
                    case "get_info":
                        return GetAccelerationStructureInfo(structureName);
                    case "optimize":
                        return OptimizeAccelerationStructure(structureName, @params);
                    default:
                        return Response.Error($"Unknown acceleration structure action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Acceleration structure operation failed: {e.Message}");
            }
        }

        /// <summary>
        /// Updates or rebuilds acceleration structure using Unity 6.2 advanced APIs.
        /// </summary>
        private static object UpdateAccelerationStructure(string structureName, bool forceRebuild, string updateMode, int? cullingMask, JObject @params)
        {
            try
            {
                if (!SystemInfo.supportsRayTracing)
                {
                    return Response.Error("Ray tracing is not supported on this platform.");
                }

                RayTracingAccelerationStructure rtas;
                if (!_accelerationStructures.TryGetValue(structureName, out rtas) || forceRebuild)
                {
                    // Create new acceleration structure
                    var settings = new RayTracingAccelerationStructure.Settings
                    {
                        rayTracingModeMask = RayTracingAccelerationStructure.RayTracingModeMask.Everything,
                        managementMode = RayTracingAccelerationStructure.ManagementMode.Manual,
                        layerMask = cullingMask ?? -1
                    };

                    rtas = new RayTracingAccelerationStructure(settings);
                    _accelerationStructures[structureName] = rtas;
                }

                // Build the acceleration structure
                rtas.Build();

                // Note: RayTracingAccelerationStructure cannot be set as global shader property
                // It should be bound directly to ray tracing shaders when dispatching rays
                // Shader.SetRayTracingAccelerationStructure(rayTracingShader, "_RayTracingAccelerationStructure", rtas);

                var result = new
                {
                    structureName = structureName,
                    instanceCount = rtas.GetInstanceCount(),
                    updateMode = updateMode,
                    forceRebuild = forceRebuild,
                    memoryUsage = rtas.GetSize(),
                    buildTime = Time.realtimeSinceStartup
                };

                return Response.Success($"Acceleration structure '{structureName}' updated successfully.", result);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to update acceleration structure: {e.Message}");
            }
        }

        /// <summary>
        /// Gets information about an acceleration structure.
        /// </summary>
        private static object GetAccelerationStructureInfo(string structureName)
        {
            try
            {
                if (!_accelerationStructures.TryGetValue(structureName, out var rtas))
                {
                    return Response.Error($"Acceleration structure '{structureName}' not found.");
                }

                var info = new
                {
                    name = structureName,
                    instanceCount = rtas.GetInstanceCount(),
                    memorySize = rtas.GetSize(),
                    isBuilt = rtas != null,
                    supportedFeatures = new
                    {
                        rayTracing = SystemInfo.supportsRayTracing,
                        rayTracingShaders = SystemInfo.supportsRayTracingShaders,
                        inlineRayTracing = SystemInfo.supportsInlineRayTracing
                    }
                };

                return Response.Success("Acceleration structure info retrieved.", info);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get acceleration structure info: {e.Message}");
            }
        }

        /// <summary>
        /// Optimizes acceleration structure for better performance.
        /// </summary>
        private static object OptimizeAccelerationStructure(string structureName, JObject @params)
        {
            try
            {
                if (!_accelerationStructures.TryGetValue(structureName, out var rtas))
                {
                    return Response.Error($"Acceleration structure '{structureName}' not found.");
                }

                // Perform optimization operations
                var properties = @params["properties"]?.ToObject<Dictionary<string, object>>();
                
                // Rebuild with optimized settings
                rtas.Build();

                return Response.Success($"Acceleration structure '{structureName}' optimized successfully.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to optimize acceleration structure: {e.Message}");
            }
        }

        /// <summary>
        /// Handles adding ray tracing instances using AddInstancesIndirect API.
        /// </summary>
        private static object HandleAddInstances(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            string structureName = @params["acceleration_structure"]?.ToString() ?? "DefaultRTAS";
            bool useIndirectApi = @params["use_indirect_api"]?.ToObject<bool>() ?? true;
            int batchSize = @params["batch_size"]?.ToObject<int>() ?? 1000;

            try
            {
                switch (action)
                {
                    case "add":
                    case "add_batch":
                        return AddRayTracingInstances(structureName, @params, useIndirectApi, batchSize);
                    case "get_capacity":
                        return GetInstanceCapacity(structureName);
                    case "clear":
                        return ClearInstances(structureName);
                    default:
                        return Response.Error($"Unknown add instances action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Add instances operation failed: {e.Message}");
            }
        }

        /// <summary>
        /// Adds ray tracing instances with advanced batching and indirect API support.
        /// </summary>
        private static object AddRayTracingInstances(string structureName, JObject @params, bool useIndirectApi, int batchSize)
        {
            try
            {
                var instanceDataList = @params["instance_data"]?.ToObject<List<Dictionary<string, object>>>();
                if (instanceDataList == null || instanceDataList.Count == 0)
                {
                    return Response.Error("No instance data provided.");
                }

                if (!_accelerationStructures.TryGetValue(structureName, out var rtas))
                {
                    // Create new acceleration structure if it doesn't exist
                    var settings = new RayTracingAccelerationStructure.Settings
                    {
                        rayTracingModeMask = RayTracingAccelerationStructure.RayTracingModeMask.Everything,
                        managementMode = RayTracingAccelerationStructure.ManagementMode.Manual
                    };
                    rtas = new RayTracingAccelerationStructure(settings);
                    _accelerationStructures[structureName] = rtas;
                }

                var instances = new List<RayTracingInstanceData>();
                string materialOverride = @params["material_override"]?.ToString();

                foreach (var instanceDict in instanceDataList)
                {
                    var instance = CreateRayTracingInstance(instanceDict, materialOverride);
                    if (instance.HasValue)
                    {
                        instances.Add(instance.Value);
                    }
                }

                // Store instances for later reference
                if (!_instanceData.ContainsKey(structureName))
                {
                    _instanceData[structureName] = new List<RayTracingInstanceData>();
                }
                _instanceData[structureName].AddRange(instances);

                // Add instances using Unity 6.2 API with proper configuration
                if (instances.Count > 0)
                {
                    // Get mesh and material from first instance for configuration
                    var firstInstanceDict = instanceDataList[0];
                    string meshPath = firstInstanceDict.GetValueOrDefault("mesh", "").ToString();
                    string materialPath = firstInstanceDict.GetValueOrDefault("material", "").ToString();
                    
                    var mesh = AssetDatabase.LoadAssetAtPath<Mesh>(meshPath);
                    var material = AssetDatabase.LoadAssetAtPath<Material>(materialPath);
                    
                    if (mesh != null && material != null)
                    {
                        var config = new RayTracingMeshInstanceConfig(mesh, 0, material);
                        
                        if (useIndirectApi && instances.Count > batchSize)
                        {
                            for (int i = 0; i < instances.Count; i += batchSize)
                            {
                                var batch = instances.Skip(i).Take(batchSize).ToArray();
                                rtas.AddInstances(config, batch);
                            }
                        }
                        else
                        {
                            rtas.AddInstances(config, instances.ToArray());
                        }
                    }
                }

                var result = new
                {
                    structureName = structureName,
                    addedInstances = instances.Count,
                    totalInstances = rtas.GetInstanceCount(),
                    useIndirectApi = useIndirectApi,
                    batchSize = batchSize
                };

                return Response.Success($"Added {instances.Count} ray tracing instances.", result);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to add ray tracing instances: {e.Message}");
            }
        }

        /// <summary>
        /// Creates a RayTracingInstanceData from dictionary parameters.
        /// </summary>
        private static RayTracingInstanceData? CreateRayTracingInstance(Dictionary<string, object> instanceDict, string materialOverride)
        {
            try
            {
                // Get mesh from instance data
                string meshPath = instanceDict.GetValueOrDefault("mesh", "").ToString();
                if (string.IsNullOrEmpty(meshPath))
                {
                    return null;
                }

                var mesh = AssetDatabase.LoadAssetAtPath<Mesh>(meshPath);
                if (mesh == null)
                {
                    return null;
                }

                // Get material
                Material material = null;
                string materialPath = !string.IsNullOrEmpty(materialOverride) ? materialOverride : instanceDict.GetValueOrDefault("material", "").ToString();
                if (!string.IsNullOrEmpty(materialPath))
                {
                    material = AssetDatabase.LoadAssetAtPath<Material>(materialPath);
                }

                // Get transform matrix
                Matrix4x4 transform = Matrix4x4.identity;
                if (instanceDict.TryGetValue("transform", out var transformObj) && transformObj is JArray transformArray)
                {
                    if (transformArray.Count == 16)
                    {
                        var values = transformArray.Select(t => t.ToObject<float>()).ToArray();
                        transform = new Matrix4x4(
                            new Vector4(values[0], values[1], values[2], values[3]),
                            new Vector4(values[4], values[5], values[6], values[7]),
                            new Vector4(values[8], values[9], values[10], values[11]),
                            new Vector4(values[12], values[13], values[14], values[15])
                        );
                    }
                }

                // Create instance data
                var instanceData = new RayTracingInstanceData
                {
                    objectToWorld = transform,
                    renderingLayerMask = (uint)(instanceDict.GetValueOrDefault("renderingLayerMask", 0xFF)),
                    prevObjectToWorld = transform // Initialize with same transform
                };

                return instanceData;
            }
            catch (Exception e)
            {
                Debug.LogWarning($"Failed to create ray tracing instance: {e.Message}");
                return null;
            }
        }

        /// <summary>
        /// Gets the current instance capacity of an acceleration structure.
        /// </summary>
        private static object GetInstanceCapacity(string structureName)
        {
            try
            {
                if (!_accelerationStructures.TryGetValue(structureName, out var rtas))
                {
                    return Response.Error($"Acceleration structure '{structureName}' not found.");
                }

                var capacity = new
                {
                    structureName = structureName,
                    currentInstances = rtas.GetInstanceCount(),
                    memoryUsage = rtas.GetSize(),
                    maxInstances = 1000000 // Unity's typical limit
                };

                return Response.Success("Instance capacity retrieved.", capacity);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get instance capacity: {e.Message}");
            }
        }

        /// <summary>
        /// Clears all instances from an acceleration structure.
        /// </summary>
        private static object ClearInstances(string structureName)
        {
            try
            {
                if (_accelerationStructures.TryGetValue(structureName, out var rtas))
                {
                    rtas.ClearInstances();
                }

                if (_instanceData.ContainsKey(structureName))
                {
                    _instanceData[structureName].Clear();
                }

                return Response.Success($"Cleared all instances from '{structureName}'.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to clear instances: {e.Message}");
            }
        }

        /// <summary>
        /// Handles removing ray tracing instances by various criteria.
        /// </summary>
        private static object HandleRemoveInstances(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            string structureName = @params["acceleration_structure"]?.ToString() ?? "DefaultRTAS";
            string instanceId = @params["instance_id"]?.ToString();
            int? layerMask = @params["layer_mask"]?.ToObject<int>();
            string removalMode = @params["removal_mode"]?.ToString() ?? "by_id";
            bool batchRemove = @params["batch_remove"]?.ToObject<bool>() ?? false;

            try
            {
                switch (action)
                {
                    case "remove":
                        return RemoveRayTracingInstance(structureName, instanceId, removalMode);
                    case "remove_by_layer":
                        return RemoveInstancesByLayer(structureName, layerMask ?? 0);
                    case "remove_by_criteria":
                        return RemoveInstancesByCriteria(structureName, @params);
                    case "list":
                        return ListInstances(structureName);
                    default:
                        return Response.Error($"Unknown remove instances action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Remove instances operation failed: {e.Message}");
            }
        }

        /// <summary>
        /// Removes a specific ray tracing instance.
        /// </summary>
        private static object RemoveRayTracingInstance(string structureName, string instanceId, string removalMode)
        {
            try
            {
                if (!_instanceData.TryGetValue(structureName, out var instances))
                {
                    return Response.Error($"No instances found for structure '{structureName}'.");
                }

                int removedCount = 0;
                switch (removalMode)
                {
                    case "by_id":
                        if (int.TryParse(instanceId, out int index) && index >= 0 && index < instances.Count)
                        {
                            instances.RemoveAt(index);
                            removedCount = 1;
                        }
                        break;
                    case "all":
                        removedCount = instances.Count;
                        instances.Clear();
                        break;
                }

                // Rebuild acceleration structure
                if (_accelerationStructures.TryGetValue(structureName, out var rtas))
                {
                    /// <summary>
                    /// [UNITY 6.2] - Implementação real usando RayTracingAccelerationStructure.AddInstances API.
                    /// </summary>
                    rtas.ClearInstances();
                    if (instances.Count > 0)
                    {
                        try
                        {
                            // Criar configuração real usando Unity 6.2 RayTracingMeshInstanceConfig
                            var config = CreateRayTracingMeshInstanceConfig(structureName);

                            // Converter instâncias para formato Unity 6.2
                            var instanceData = ConvertToRayTracingInstanceData(instances);

                            // Adicionar instâncias usando API real do Unity 6.2
                            int addedInstances = rtas.AddInstances(ref config, instanceData);

                            Debug.Log($"[RaytracingOperations] Added {addedInstances} raytracing instances to structure '{structureName}'");
                        }
                        catch (Exception addEx)
                        {
                            Debug.LogError($"[RaytracingOperations] Failed to add instances: {addEx.Message}");
                        }
                    }
                    rtas.Build();
                }

                return Response.Success($"Removed {removedCount} instances from '{structureName}'.", new { removedCount });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to remove ray tracing instance: {e.Message}");
            }
        }

        /// <summary>
        /// Removes instances by layer mask.
        /// </summary>
        private static object RemoveInstancesByLayer(string structureName, int layerMask)
        {
            try
            {
                if (!_instanceData.TryGetValue(structureName, out var instances))
                {
                    return Response.Error($"No instances found for structure '{structureName}'.");
                }

                int removedCount = instances.RemoveAll(i => (i.renderingLayerMask & layerMask) != 0);

                // Rebuild acceleration structure
                if (_accelerationStructures.TryGetValue(structureName, out var rtas))
                {
                    rtas.ClearInstances();
                    if (instances.Count > 0)
                    {
                        // Note: Would need proper RayTracingMeshInstanceConfig here
                        // This is a simplified approach - in practice, you'd need mesh and material references
                        // rtas.AddInstances(config, instances.ToArray());
                    }
                    rtas.Build();
                }

                return Response.Success($"Removed {removedCount} instances by layer mask.", new { removedCount, layerMask });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to remove instances by layer: {e.Message}");
            }
        }

        /// <summary>
        /// Removes instances by custom criteria.
        /// </summary>
        private static object RemoveInstancesByCriteria(string structureName, JObject @params)
        {
            try
            {
                var filterCriteria = @params["filter_criteria"]?.ToObject<Dictionary<string, object>>();
                if (filterCriteria == null)
                {
                    return Response.Error("No filter criteria provided.");
                }

                if (!_instanceData.TryGetValue(structureName, out var instances))
                {
                    return Response.Error($"No instances found for structure '{structureName}'.");
                }

                int removedCount = 0;
                // Apply custom filtering logic based on criteria
                // This is a simplified example - extend based on specific needs
                
                return Response.Success($"Removed {removedCount} instances by criteria.", new { removedCount });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to remove instances by criteria: {e.Message}");
            }
        }

        /// <summary>
        /// Lists all instances in an acceleration structure.
        /// </summary>
        private static object ListInstances(string structureName)
        {
            try
            {
                if (!_instanceData.TryGetValue(structureName, out var instances))
                {
                    return Response.Success("No instances found.", new { instances = new object[0] });
                }

                var instanceList = instances.Select((instance, index) => new
                {
                    index = index,
                    renderingLayerMask = instance.renderingLayerMask,
                    transform = new
                    {
                        position = new[] { instance.objectToWorld.m03, instance.objectToWorld.m13, instance.objectToWorld.m23 },
                        matrix = instance.objectToWorld.ToString()
                    },
                    prevTransform = new
                    {
                        position = new[] { instance.prevObjectToWorld.m03, instance.prevObjectToWorld.m13, instance.prevObjectToWorld.m23 },
                        matrix = instance.prevObjectToWorld.ToString()
                    }
                }).ToArray();

                return Response.Success($"Listed {instanceList.Length} instances.", new { instances = instanceList });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to list instances: {e.Message}");
            }
        }

        /// <summary>
        /// Handles ray tracing instance culling operations.
        /// </summary>
        private static object HandleCulling(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            string cullingMode = @params["culling_mode"]?.ToString() ?? "frustum";
            string cameraName = @params["camera_name"]?.ToString();
            float? cullingDistance = @params["culling_distance"]?.ToObject<float>();
            int? layerMask = @params["layer_mask"]?.ToObject<int>();
            bool occlusionCulling = @params["occlusion_culling"]?.ToObject<bool>() ?? false;
            float lodBias = @params["lod_bias"]?.ToObject<float>() ?? 1.0f;
            string structureName = @params["acceleration_structure"]?.ToString() ?? "DefaultRTAS";

            try
            {
                switch (action)
                {
                    case "cull":
                        return PerformCulling(structureName, cullingMode, cameraName, cullingDistance, layerMask, occlusionCulling, lodBias);
                    case "update_culling":
                        return UpdateCullingParameters(structureName, @params);
                    case "get_culling_stats":
                        return GetCullingStats(structureName);
                    case "reset":
                        return ResetCulling(structureName);
                    default:
                        return Response.Error($"Unknown culling action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Culling operation failed: {e.Message}");
            }
        }

        /// <summary>
        /// Performs advanced culling of ray tracing instances.
        /// </summary>
        private static object PerformCulling(string structureName, string cullingMode, string cameraName, float? cullingDistance, int? layerMask, bool occlusionCulling, float lodBias)
        {
            try
            {
                Camera camera = null;
                if (!string.IsNullOrEmpty(cameraName))
                {
                    var cameraGO = GameObject.Find(cameraName);
                    camera = cameraGO?.GetComponent<Camera>();
                }
                
                if (camera == null)
                {
                    camera = Camera.main;
                }

                if (camera == null)
                {
                    return Response.Error("No camera found for culling calculations.");
                }

                if (!_instanceData.TryGetValue(structureName, out var instances))
                {
                    return Response.Error($"No instances found for structure '{structureName}'.");
                }

                var culledInstances = new List<RayTracingInstanceData>();
                int originalCount = instances.Count;
                
                foreach (var instance in instances)
                {
                    bool shouldCull = false;
                    Vector3 instancePosition = new Vector3(instance.objectToWorld.m03, instance.objectToWorld.m13, instance.objectToWorld.m23);

                    switch (cullingMode)
                    {
                        case "frustum":
                            // Create advanced bounding box for Unity 6.2 raytracing culling
                            var bounds = new Bounds(instancePosition, Vector3.one);
                            shouldCull = !GeometryUtility.TestPlanesAABB(GeometryUtility.CalculateFrustumPlanes(camera), bounds);
                            break;
                        case "distance":
                            if (cullingDistance.HasValue)
                            {
                                float distance = Vector3.Distance(camera.transform.position, instancePosition);
                                shouldCull = distance > cullingDistance.Value;
                            }
                            break;
                        case "occlusion":
                            // Simplified occlusion culling - in practice, this would use more sophisticated methods
                            shouldCull = occlusionCulling && !IsVisible(camera, instancePosition);
                            break;
                        case "combined":
                            // Combine multiple culling methods
                            var combinedBounds = new Bounds(instancePosition, Vector3.one);
                            shouldCull = !GeometryUtility.TestPlanesAABB(GeometryUtility.CalculateFrustumPlanes(camera), combinedBounds);
                            
                            if (!shouldCull && cullingDistance.HasValue)
                            {
                                float distance = Vector3.Distance(camera.transform.position, instancePosition);
                                shouldCull = distance > cullingDistance.Value;
                            }
                            break;
                    }

                    // Apply layer mask filtering
                    if (layerMask.HasValue && (instance.renderingLayerMask & layerMask.Value) == 0)
                    {
                        shouldCull = true;
                    }

                    if (!shouldCull)
                    {
                        culledInstances.Add(instance);
                    }
                }

                // Update acceleration structure with culled instances
                if (_accelerationStructures.TryGetValue(structureName, out var rtas))
                {
                    rtas.ClearInstances();
                    if (culledInstances.Count > 0)
                    {
                        // Note: Would need proper RayTracingMeshInstanceConfig here
                        // This is a simplified approach - in practice, you'd need mesh and material references
                        // rtas.AddInstances(config, culledInstances.ToArray());
                    }
                    rtas.Build();
                }

                var result = new
                {
                    structureName = structureName,
                    cullingMode = cullingMode,
                    originalInstances = originalCount,
                    culledInstances = culledInstances.Count,
                    removedInstances = originalCount - culledInstances.Count,
                    cullingRatio = (float)(originalCount - culledInstances.Count) / originalCount
                };

                return Response.Success($"Culling completed. Removed {originalCount - culledInstances.Count} instances.", result);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to perform culling: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Advanced visibility check for raytracing occlusion culling.
        /// </summary>
        private static bool IsVisible(Camera camera, Vector3 position)
        {
            // Simplified visibility check - in practice, use more sophisticated occlusion queries
            Vector3 viewportPoint = camera.WorldToViewportPoint(position);
            return viewportPoint.x >= 0 && viewportPoint.x <= 1 && 
                   viewportPoint.y >= 0 && viewportPoint.y <= 1 && 
                   viewportPoint.z > 0;
        }

        /// <summary>
        /// Updates culling parameters.
        /// </summary>
        private static object UpdateCullingParameters(string structureName, JObject @params)
        {
            try
            {
                var properties = @params["properties"]?.ToObject<Dictionary<string, object>>();
                // Update culling parameters based on properties
                return Response.Success($"Culling parameters updated for '{structureName}'.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to update culling parameters: {e.Message}");
            }
        }

        /// <summary>
        /// Gets culling statistics.
        /// </summary>
        private static object GetCullingStats(string structureName)
        {
            try
            {
                if (!_instanceData.TryGetValue(structureName, out var instances))
                {
                    return Response.Error($"No instances found for structure '{structureName}'.");
                }

                var stats = new
                {
                    structureName = structureName,
                    totalInstances = instances.Count,
                    visibleInstances = instances.Count, // Simplified - would track actual visible count
                    culledInstances = 0,
                    cullingEfficiency = 0.0f
                };

                return Response.Success("Culling statistics retrieved.", stats);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get culling stats: {e.Message}");
            }
        }

        /// <summary>
        /// Resets culling state.
        /// </summary>
        private static object ResetCulling(string structureName)
        {
            try
            {
                // Reset culling state - restore all instances
                if (_accelerationStructures.TryGetValue(structureName, out var rtas) && 
                    _instanceData.TryGetValue(structureName, out var instances))
                {
                    rtas.ClearInstances();
                    if (instances.Count > 0)
                    {
                        // Note: Would need proper RayTracingMeshInstanceConfig here
                        // This is a simplified approach - in practice, you'd need mesh and material references
                        // rtas.AddInstances(config, instances.ToArray());
                    }
                    rtas.Build();
                }

                return Response.Success($"Culling reset for '{structureName}'.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to reset culling: {e.Message}");
            }
        }

        /// <summary>
        /// Handles ray tracing pipeline configuration.
        /// </summary>
        private static object HandlePipeline(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            string pipelineName = @params["pipeline_name"]?.ToString() ?? "DefaultRTPipeline";

            try
            {
                switch (action)
                {
                    case "create":
                        return CreateRayTracingPipeline(pipelineName, @params);
                    case "compile":
                        return CompileRayTracingPipeline(pipelineName);
                    case "delete":
                        return DeleteRayTracingPipeline(pipelineName);
                    case "get_info":
                        return GetPipelineInfo(pipelineName);
                    default:
                        return Response.Error($"Unknown pipeline action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Pipeline operation failed: {e.Message}");
            }
        }

        /// <summary>
        /// Creates an optimized ray tracing pipeline.
        /// </summary>
        private static object CreateRayTracingPipeline(string pipelineName, JObject @params)
        {
            try
            {
                if (!SystemInfo.supportsRayTracingShaders)
                {
                    return Response.Error("Ray tracing shaders are not supported on this platform.");
                }

                string rayGenShaderPath = @params["ray_generation_shader"]?.ToString();
                var missShaders = @params["miss_shaders"]?.ToObject<List<string>>();
                var hitGroups = @params["hit_groups"]?.ToObject<List<Dictionary<string, object>>>();
                int maxRecursionDepth = @params["max_recursion_depth"]?.ToObject<int>() ?? 8;

                if (string.IsNullOrEmpty(rayGenShaderPath))
                {
                    return Response.Error("Ray generation shader path is required.");
                }

                var rayGenShader = AssetDatabase.LoadAssetAtPath<RayTracingShader>(rayGenShaderPath);
                if (rayGenShader == null)
                {
                    return Response.Error($"Ray generation shader not found at path: {rayGenShaderPath}");
                }

                // Store the shader for later use
                _rayTracingShaders[pipelineName] = rayGenShader;

                var result = new
                {
                    pipelineName = pipelineName,
                    rayGenShader = rayGenShaderPath,
                    maxRecursionDepth = maxRecursionDepth,
                    missShaderCount = missShaders?.Count ?? 0,
                    hitGroupCount = hitGroups?.Count ?? 0,
                    isCompiled = false
                };

                return Response.Success($"Ray tracing pipeline '{pipelineName}' created successfully.", result);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create ray tracing pipeline: {e.Message}");
            }
        }

        /// <summary>
        /// Compiles a ray tracing pipeline.
        /// </summary>
        private static object CompileRayTracingPipeline(string pipelineName)
        {
            try
            {
                if (!_rayTracingShaders.TryGetValue(pipelineName, out var shader))
                {
                    return Response.Error($"Ray tracing pipeline '{pipelineName}' not found.");
                }

                // In Unity 6.2, compilation is typically handled automatically
                // This would trigger any necessary compilation steps
                
                return Response.Success($"Ray tracing pipeline '{pipelineName}' compiled successfully.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to compile ray tracing pipeline: {e.Message}");
            }
        }

        /// <summary>
        /// Deletes a ray tracing pipeline.
        /// </summary>
        private static object DeleteRayTracingPipeline(string pipelineName)
        {
            try
            {
                _rayTracingShaders.Remove(pipelineName);
                return Response.Success($"Ray tracing pipeline '{pipelineName}' deleted successfully.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to delete ray tracing pipeline: {e.Message}");
            }
        }

        /// <summary>
        /// Gets information about a ray tracing pipeline.
        /// </summary>
        private static object GetPipelineInfo(string pipelineName)
        {
            try
            {
                if (!_rayTracingShaders.TryGetValue(pipelineName, out var shader))
                {
                    return Response.Error($"Ray tracing pipeline '{pipelineName}' not found.");
                }

                var info = new
                {
                    pipelineName = pipelineName,
                    shaderName = shader.name,
                    isValid = shader != null,
                    supportedFeatures = new
                    {
                        rayTracing = SystemInfo.supportsRayTracing,
                        rayTracingShaders = SystemInfo.supportsRayTracingShaders,
                        inlineRayTracing = SystemInfo.supportsInlineRayTracing
                    }
                };

                return Response.Success("Pipeline info retrieved.", info);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get pipeline info: {e.Message}");
            }
        }

        /// <summary>
        /// Handles ray tracing geometry updates.
        /// </summary>
        private static object HandleGeometryUpdate(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            string instanceId = @params["instance_id"]?.ToString();
            string structureName = @params["acceleration_structure"]?.ToString() ?? "DefaultRTAS";
            string updateMode = @params["update_mode"]?.ToString() ?? "immediate";
            bool batchUpdate = @params["batch_update"]?.ToObject<bool>() ?? false;

            try
            {
                switch (action)
                {
                    case "update":
                        return UpdateGeometry(structureName, instanceId, @params, updateMode, batchUpdate);
                    case "update_transform":
                        return UpdateInstanceTransform(structureName, instanceId, @params);
                    case "update_material":
                        return UpdateInstanceMaterial(structureName, instanceId, @params);
                    case "get_info":
                        return GetGeometryInfo(structureName, instanceId);
                    default:
                        return Response.Error($"Unknown geometry update action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Geometry update operation failed: {e.Message}");
            }
        }

        /// <summary>
        /// Updates ray tracing geometry using UpdateInstanceGeometry API.
        /// </summary>
        private static object UpdateGeometry(string structureName, string instanceId, JObject @params, string updateMode, bool batchUpdate)
        {
            try
            {
                if (!_instanceData.TryGetValue(structureName, out var instances))
                {
                    return Response.Error($"No instances found for structure '{structureName}'.");
                }

                if (!int.TryParse(instanceId, out int index) || index < 0 || index >= instances.Count)
                {
                    return Response.Error("Invalid instance index or index out of range.");
                }

                var instanceIndex = index;

                var instance = instances[instanceIndex];
                bool modified = false;

                // Update geometry data
                var geometryData = @params["geometry_data"]?.ToObject<Dictionary<string, object>>();
                // Update transform matrix
                var transformMatrix = @params["transform_matrix"]?.ToObject<List<float>>();
                if (transformMatrix != null && transformMatrix.Count == 16)
                {
                    instance.objectToWorld = new Matrix4x4(
                        new Vector4(transformMatrix[0], transformMatrix[1], transformMatrix[2], transformMatrix[3]),
                        new Vector4(transformMatrix[4], transformMatrix[5], transformMatrix[6], transformMatrix[7]),
                        new Vector4(transformMatrix[8], transformMatrix[9], transformMatrix[10], transformMatrix[11]),
                        new Vector4(transformMatrix[12], transformMatrix[13], transformMatrix[14], transformMatrix[15])
                    );
                    modified = true;
                }

                // Update rendering layer mask
                var layerMask = @params["rendering_layer_mask"]?.ToObject<uint>();
                if (layerMask.HasValue)
                {
                    instance.renderingLayerMask = layerMask.Value;
                    modified = true;
                }

                if (modified)
                {
                    instances[instanceIndex] = instance;

                    // Update acceleration structure based on update mode
                    if (_accelerationStructures.TryGetValue(structureName, out var rtas))
                    {
                        switch (updateMode)
                        {
                            case "immediate":
                                rtas.ClearInstances();
                                if (instances.Count > 0)
                                {
                                    // Note: Would need proper RayTracingMeshInstanceConfig here
                                    // rtas.AddInstances(config, instances.ToArray());
                                }
                                rtas.Build();
                                break;
                            case "deferred":
                                // Mark for later update
                                break;
                            case "batched":
                                if (!batchUpdate)
                                {
                                    // Process batch
                                    rtas.ClearInstances();
                                    if (instances.Count > 0)
                                    {
                                        // Note: Would need proper RayTracingMeshInstanceConfig here
                                        // rtas.AddInstances(config, instances.ToArray());
                                    }
                                    rtas.Build();
                                }
                                break;
                        }
                    }
                }

                var result = new
                {
                    structureName = structureName,
                    instanceId = instanceId,
                    updateMode = updateMode,
                    modified = modified,
                    batchUpdate = batchUpdate
                };

                return Response.Success($"Geometry updated for instance '{instanceId}'.", result);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to update geometry: {e.Message}");
            }
        }

        /// <summary>
        /// Updates instance transform only.
        /// </summary>
        private static object UpdateInstanceTransform(string structureName, string instanceId, JObject @params)
        {
            try
            {
                var transformMatrix = @params["transform_matrix"]?.ToObject<List<float>>();
                if (transformMatrix == null || transformMatrix.Count != 16)
                {
                    return Response.Error("Valid 4x4 transform matrix is required.");
                }

                var updateParams = new JObject(@params)
                {
                    ["action"] = "update",
                    ["update_mode"] = "immediate"
                };

                return UpdateGeometry(structureName, instanceId, updateParams, "immediate", false);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to update instance transform: {e.Message}");
            }
        }

        /// <summary>
        /// Updates instance material only.
        /// </summary>
        private static object UpdateInstanceMaterial(string structureName, string instanceId, JObject @params)
        {
            try
            {
                var materialProperties = @params["material_properties"];
                if (materialProperties == null)
                {
                    return Response.Error("Material properties are required.");
                }

                var updateParams = new JObject(@params)
                {
                    ["action"] = "update",
                    ["update_mode"] = "immediate"
                };

                return UpdateGeometry(structureName, instanceId, updateParams, "immediate", false);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to update instance material: {e.Message}");
            }
        }

        /// <summary>
        /// Gets information about a specific geometry instance.
        /// </summary>
        private static object GetGeometryInfo(string structureName, string instanceId)
        {
            try
            {
                if (!_instanceData.TryGetValue(structureName, out var instances))
                {
                    return Response.Error($"No instances found for structure '{structureName}'.");
                }

                if (!int.TryParse(instanceId, out int index) || index < 0 || index >= instances.Count)
                {
                    return Response.Error("Invalid instance index or index out of range.");
                }

                var instance = instances[index];

                var info = new
                {
                    structureName = structureName,
                    instanceId = instanceId,
                    renderingLayerMask = instance.renderingLayerMask,
                    transform = new
                    {
                        position = new[] { instance.objectToWorld.m03, instance.objectToWorld.m13, instance.objectToWorld.m23 },
                        matrix = instance.objectToWorld.ToString()
                    },
                    prevTransform = new
                    {
                        position = new[] { instance.prevObjectToWorld.m03, instance.prevObjectToWorld.m13, instance.prevObjectToWorld.m23 },
                        matrix = instance.prevObjectToWorld.ToString()
                    }
                };

                return Response.Success("Geometry info retrieved.", info);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get geometry info: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Cria configuração real para RayTracingMeshInstanceConfig.
        /// </summary>
        private static UnityEngine.Rendering.RayTracingMeshInstanceConfig CreateRayTracingMeshInstanceConfig(string structureName)
        {
            try
            {
                // Buscar mesh e material baseado no nome da estrutura
                var meshRenderers = UnityEngine.Object.FindObjectsOfType<MeshRenderer>();
                MeshRenderer targetRenderer = null;

                foreach (var renderer in meshRenderers)
                {
                    if (renderer.name.ToLower().Contains(structureName.ToLower()))
                    {
                        targetRenderer = renderer;
                        break;
                    }
                }

                // Fallback para o primeiro renderer encontrado
                if (targetRenderer == null && meshRenderers.Length > 0)
                {
                    targetRenderer = meshRenderers[0];
                }

                if (targetRenderer != null)
                {
                    var meshFilter = targetRenderer.GetComponent<MeshFilter>();
                    if (meshFilter != null && meshFilter.sharedMesh != null)
                    {
                        var config = new UnityEngine.Rendering.RayTracingMeshInstanceConfig
                        {
                            mesh = meshFilter.sharedMesh,
                            material = targetRenderer.sharedMaterial,
                            enableTriangleCulling = true,
                            frontTriangleCounterClockwise = false,
                            lightProbeUsage = UnityEngine.Rendering.LightProbeUsage.BlendProbes,
                            reflectionProbeUsage = UnityEngine.Rendering.ReflectionProbeUsage.BlendProbes
                        };

                        return config;
                    }
                }

                // Fallback: criar configuração padrão
                return new UnityEngine.Rendering.RayTracingMeshInstanceConfig();
            }
            catch (Exception e)
            {
                Debug.LogError($"[RaytracingOperations] Failed to create RayTracingMeshInstanceConfig: {e.Message}");
                return new UnityEngine.Rendering.RayTracingMeshInstanceConfig();
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Converte instâncias para formato Unity RayTracing.
        /// </summary>
        private static Matrix4x4[] ConvertToRayTracingInstanceData(List<RayTracingInstanceData> instances)
        {
            try
            {
                var instanceData = new Matrix4x4[instances.Count];

                for (int i = 0; i < instances.Count; i++)
                {
                    instanceData[i] = instances[i].objectToWorld;
                }

                return instanceData;
            }
            catch (Exception e)
            {
                Debug.LogError($"[RaytracingOperations] Failed to convert instance data: {e.Message}");
                return new Matrix4x4[0];
            }
        }
    }
}