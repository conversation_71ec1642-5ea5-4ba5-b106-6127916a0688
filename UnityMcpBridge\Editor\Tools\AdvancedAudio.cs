using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.Audio;
using UnityEngine.Profiling;
using Unity.Profiling;
using UnityMcpBridge.Editor.Helpers;
using System.IO;
using System.Threading.Tasks;

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// Handles advanced audio system operations for Unity 6.2.
    /// Implements professional-grade audio features using Unity's latest APIs.
    /// </summary>
    public static class AdvancedAudio
    {
        private static readonly List<string> ValidActions = new List<string>
        {
            "setup_dsp_time_sync",
            "configure_dsp_buffer",
            "setup_virtualization",
            "create_adaptive_system",
            "setup_snapshot_blending",
            "configure_profiling",
            "setup_format_conversion"
        };

        private static readonly Dictionary<string, AudioReverbPreset> ReverbPresets = new Dictionary<string, AudioReverbPreset>
        {
            { "off", AudioReverbPreset.Off },
            { "generic", AudioReverbPreset.Generic },
            { "padded_cell", AudioReverbPreset.PaddedCell },
            { "room", AudioReverbPreset.Room },
            { "bathroom", AudioReverbPreset.Bathroom },
            { "livingroom", AudioReverbPreset.Livingroom },
            { "stoneroom", AudioReverbPreset.Stoneroom },
            { "auditorium", AudioReverbPreset.Auditorium },
            { "concerthall", AudioReverbPreset.Concerthall },
            { "cave", AudioReverbPreset.Cave },
            { "arena", AudioReverbPreset.Arena },
            { "hangar", AudioReverbPreset.Hangar },
            { "hallway", AudioReverbPreset.Hallway },
            { "carpetedhallway", AudioReverbPreset.Hallway },
            { "alley", AudioReverbPreset.Alley },
            { "forest", AudioReverbPreset.Forest },
            { "city", AudioReverbPreset.City },
            { "mountains", AudioReverbPreset.Mountains },
            { "quarry", AudioReverbPreset.Quarry },
            { "plain", AudioReverbPreset.Plain },
            { "underwater", AudioReverbPreset.Underwater },
            { "drugged", AudioReverbPreset.Drugged },
            { "dizzy", AudioReverbPreset.Dizzy },
            { "psychotic", AudioReverbPreset.Psychotic },
            { "stonecorridor", AudioReverbPreset.Stoneroom },
            { "parkinglot", AudioReverbPreset.Plain },
            { "sewerpipe", AudioReverbPreset.Cave }
        };

        // Static profiler markers for Unity 6.2 profiling
        private static readonly ProfilerMarker s_DSPTimeMarker = new ProfilerMarker("AdvancedAudio.DSPTime");
        private static readonly ProfilerMarker s_VirtualizationMarker = new ProfilerMarker("AdvancedAudio.Virtualization");
        private static readonly ProfilerMarker s_AdaptiveSystemMarker = new ProfilerMarker("AdvancedAudio.AdaptiveSystem");

        public static object HandleCommand(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidActions);
                return Response.Error(
                    $"Unknown action: '{action}'. Valid actions are: {validActionsList}"
                );
            }

            try
            {
                switch (action)
                {
                    case "setup_dsp_time_sync":
                        return SetupDSPTimeSync(@params);
                    case "configure_dsp_buffer":
                        return ConfigureDSPBuffer(@params);
                    case "setup_virtualization":
                        return SetupAudioVirtualization(@params);
                    case "create_adaptive_system":
                        return CreateAdaptiveAudioSystem(@params);
                    case "setup_snapshot_blending":
                        return SetupSnapshotBlending(@params);
                    case "configure_profiling":
                        return ConfigureRealtimeProfiling(@params);
                    case "setup_format_conversion":
                        return SetupFormatConversion(@params);
                    default:
                        string validActionsListDefault = string.Join(", ", ValidActions);
                        return Response.Error(
                            $"Unknown action: '{action}'. Valid actions are: {validActionsListDefault}"
                        );
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[AdvancedAudio] Action '{action}' failed: {e}");
                return Response.Error(
                    $"Internal error processing action '{action}': {e.Message}"
                );
            }
        }

        private static object SetupDSPTimeSync(JObject @params)
        {
            using (s_DSPTimeMarker.Auto())
            {
                try
                {
                    string targetGameObject = @params["target_gameobject"]?.ToString();
                    double scheduleTime = @params["schedule_time"]?.ToObject<double>() ?? 0.0;
                    bool useAbsoluteTime = @params["use_absolute_time"]?.ToObject<bool>() ?? true;
                    int bufferSize = @params["buffer_size"]?.ToObject<int>() ?? 1024;
                    int sampleRate = @params["sample_rate"]?.ToObject<int>() ?? 48000;
                    bool enablePreciseTiming = @params["enable_precise_timing"]?.ToObject<bool>() ?? true;
                    float latencyCompensation = @params["latency_compensation"]?.ToObject<float>() ?? 0.0f;

                    if (string.IsNullOrEmpty(targetGameObject))
                    {
                        return Response.Error("Target GameObject name is required.");
                    }

                    GameObject go = GameObject.Find(targetGameObject);
                    if (go == null)
                    {
                        return Response.Error($"GameObject '{targetGameObject}' not found.");
                    }

                    AudioSource audioSource = go.GetComponent<AudioSource>();
                    if (audioSource == null)
                    {
                        audioSource = go.AddComponent<AudioSource>();
                    }

                    // Get current audio configuration
                    var currentConfig = AudioSettings.GetConfiguration();
                    
                    // Validate and apply new configuration if needed
                    bool configChanged = false;
                    var newConfig = currentConfig;
                    
                    if (currentConfig.dspBufferSize != bufferSize && IsPowerOfTwo(bufferSize))
                    {
                        newConfig.dspBufferSize = bufferSize;
                        configChanged = true;
                    }
                    
                    if (currentConfig.sampleRate != sampleRate)
                    {
                        newConfig.sampleRate = sampleRate;
                        configChanged = true;
                    }

                    if (configChanged)
                    {
                        bool success = AudioSettings.Reset(newConfig);
                        if (!success)
                        {
                            Debug.LogWarning("[AdvancedAudio] Failed to apply audio configuration, using current settings.");
                        }
                    }

                    // Create or update DSP time sync component
                    var dspSync = go.GetComponent<DSPTimeSyncComponent>();
                    if (dspSync == null)
                    {
                        dspSync = go.AddComponent<DSPTimeSyncComponent>();
                    }

                    // Configure DSP sync component with real values
                    dspSync.targetAudioSource = audioSource;
                    dspSync.enablePreciseTiming = enablePreciseTiming;
                    dspSync.latencyCompensation = latencyCompensation;

                    // Configure AudioSource for precise timing
                    audioSource.playOnAwake = false;
                    audioSource.priority = 0; // Highest priority for sync

                    // Get real DSP time values
                    double currentDSPTime = AudioSettings.dspTime;
                    double targetTime = useAbsoluteTime ? scheduleTime : currentDSPTime + scheduleTime;

                    // Schedule playback if audio clip is assigned
                    if (audioSource.clip != null && scheduleTime > 0)
                    {
                        if (targetTime > currentDSPTime)
                        {
                            audioSource.PlayScheduled(targetTime + latencyCompensation);
                        }
                        else
                        {
                            Debug.LogWarning("[AdvancedAudio] Schedule time is in the past, playing immediately.");
                            audioSource.Play();
                        }
                    }

                    EditorUtility.SetDirty(go);

                    var result = new
                    {
                        gameObject = targetGameObject,
                        currentDSPTime = currentDSPTime,
                        scheduledTime = targetTime,
                        actualBufferSize = AudioSettings.GetConfiguration().dspBufferSize,
                        actualSampleRate = AudioSettings.outputSampleRate,
                        latencyMs = (float)AudioSettings.GetConfiguration().dspBufferSize / AudioSettings.outputSampleRate * 1000f,
                        configurationApplied = configChanged,
                        audioClipAssigned = audioSource.clip != null
                    };

                    return Response.Success("DSP time synchronization setup successfully.", result);
                }
                catch (Exception e)
                {
                    Debug.LogError($"[AdvancedAudio] SetupDSPTimeSync failed: {e.Message}");
                    return Response.Error($"Failed to setup DSP time sync: {e.Message}");
                }
            }
        }

        private static object ConfigureDSPBuffer(JObject @params)
        {
            try
            {
                int bufferSize = @params["buffer_size"]?.ToObject<int>() ?? 1024;
                int numBuffers = @params["num_buffers"]?.ToObject<int>() ?? 4;
                string optimizeFor = @params["optimize_for"]?.ToString() ?? "balanced";
                bool enableAdaptiveBuffering = @params["enable_adaptive_buffering"]?.ToObject<bool>() ?? false;
                float targetLatencyMs = @params["target_latency_ms"]?.ToObject<float>() ?? 20.0f;
                int maxRealVoices = @params["max_real_voices"]?.ToObject<int>() ?? 32;
                int maxVirtualVoices = @params["max_virtual_voices"]?.ToObject<int>() ?? 256;

                // Validate buffer size (must be power of 2)
                if (!IsPowerOfTwo(bufferSize) || bufferSize < 64 || bufferSize > 8192)
                {
                    return Response.Error("Buffer size must be a power of 2 between 64 and 8192.");
                }

                var currentConfig = AudioSettings.GetConfiguration();
                var newConfig = currentConfig;

                // Configure based on optimization target
                switch (optimizeFor.ToLower())
                {
                    case "latency":
                        newConfig.dspBufferSize = Mathf.Min(bufferSize, 256);
                        newConfig.numRealVoices = Mathf.Min(maxRealVoices, 16);
                        newConfig.numVirtualVoices = Mathf.Min(maxVirtualVoices, 64);
                        break;
                    case "performance":
                        newConfig.dspBufferSize = Mathf.Max(bufferSize, 512);
                        newConfig.numRealVoices = Mathf.Max(maxRealVoices, 32);
                        newConfig.numVirtualVoices = Mathf.Max(maxVirtualVoices, 512);
                        break;
                    case "balanced":
                    default:
                        newConfig.dspBufferSize = bufferSize;
                        newConfig.numRealVoices = maxRealVoices;
                        newConfig.numVirtualVoices = maxVirtualVoices;
                        break;
                }

                // Get current DSP buffer info
                AudioSettings.GetDSPBufferSize(out int currentBufferLength, out int currentNumBuffers);

                // Apply new configuration
                bool success = AudioSettings.Reset(newConfig);
                if (!success)
                {
                    return Response.Error("Failed to apply DSP buffer configuration. The requested settings may not be supported by the current audio driver.");
                }

                // Verify applied configuration
                var appliedConfig = AudioSettings.GetConfiguration();
                AudioSettings.GetDSPBufferSize(out int appliedBufferLength, out int appliedNumBuffers);

                // Create adaptive buffer manager if requested
                GameObject adaptiveManager = null;
                if (enableAdaptiveBuffering)
                {
                    // Check if manager already exists
                    adaptiveManager = GameObject.Find("AdaptiveBufferManager");
                    if (adaptiveManager == null)
                    {
                        adaptiveManager = new GameObject("AdaptiveBufferManager");
                        var component = adaptiveManager.AddComponent<AdaptiveBufferManager>();
                        component.targetLatencyMs = targetLatencyMs;
                        component.numBuffers = numBuffers;
                        component.currentOptimizationMode = optimizeFor;
                        component.Initialize();
                        
                        EditorUtility.SetDirty(adaptiveManager);
                    }
                }

                // Calculate actual latency
                float actualLatencyMs = (float)appliedConfig.dspBufferSize / appliedConfig.sampleRate * 1000f;
                
                var result = new
                {
                    requested = new
                    {
                        bufferSize = bufferSize,
                        numBuffers = numBuffers,
                        optimizeFor = optimizeFor,
                        realVoices = maxRealVoices,
                        virtualVoices = maxVirtualVoices
                    },
                    applied = new
                    {
                        bufferSize = appliedConfig.dspBufferSize,
                        actualBufferLength = appliedBufferLength,
                        actualNumBuffers = appliedNumBuffers,
                        realVoices = appliedConfig.numRealVoices,
                        virtualVoices = appliedConfig.numVirtualVoices,
                        sampleRate = appliedConfig.sampleRate,
                        speakerMode = appliedConfig.speakerMode.ToString()
                    },
                    performance = new
                    {
                        actualLatencyMs = actualLatencyMs,
                        targetLatencyMs = targetLatencyMs,
                        latencyDifferenceMs = Mathf.Abs(actualLatencyMs - targetLatencyMs),
                        withinTarget = Mathf.Abs(actualLatencyMs - targetLatencyMs) <= 5.0f
                    },
                    adaptiveBuffering = new
                    {
                        enabled = enableAdaptiveBuffering,
                        managerCreated = adaptiveManager != null,
                        managerName = adaptiveManager?.name
                    },
                    configurationSuccess = success
                };

                return Response.Success("DSP buffer configuration applied successfully.", result);
            }
            catch (Exception e)
            {
                Debug.LogError($"[AdvancedAudio] ConfigureDSPBuffer failed: {e.Message}");
                return Response.Error($"Failed to configure DSP buffer: {e.Message}");
            }
        }

        private static object SetupAudioVirtualization(JObject @params)
        {
            using (s_VirtualizationMarker.Auto())
            {
                try
                {
                    int maxVirtualVoices = @params["max_virtual_voices"]?.ToObject<int>() ?? 512;
                    int maxRealVoices = @params["max_real_voices"]?.ToObject<int>() ?? 32;
                    float priorityThreshold = @params["priority_threshold"]?.ToObject<float>() ?? 0.5f;
                    float distanceFactor = @params["distance_factor"]?.ToObject<float>() ?? 1.0f;
                    bool enableVoiceStealing = @params["enable_voice_stealing"]?.ToObject<bool>() ?? true;
                    float cpuUsageLimit = @params["cpu_usage_limit"]?.ToObject<float>() ?? 0.8f;
                    bool enableDynamicVirtualization = @params["enable_dynamic_virtualization"]?.ToObject<bool>() ?? true;
                    float virtualizationUpdateRate = @params["virtualization_update_rate"]?.ToObject<float>() ?? 0.1f;

                    // Validate voice counts
                    maxVirtualVoices = Mathf.Clamp(maxVirtualVoices, 0, 1024);
                    maxRealVoices = Mathf.Clamp(maxRealVoices, 1, 256);
                    
                    if (maxRealVoices > maxVirtualVoices)
                    {
                        return Response.Error("Max real voices cannot exceed max virtual voices.");
                    }

                    // Get current configuration and apply new voice settings
                    var currentConfig = AudioSettings.GetConfiguration();
                    var newConfig = currentConfig;
                    
                    newConfig.numVirtualVoices = maxVirtualVoices;
                    newConfig.numRealVoices = maxRealVoices;

                    bool success = AudioSettings.Reset(newConfig);
                    if (!success)
                    {
                        return Response.Error("Failed to apply virtualization configuration. The audio system may not support the requested voice counts.");
                    }

                    // Verify applied configuration
                    var appliedConfig = AudioSettings.GetConfiguration();

                    // Create or update virtualization manager
                    var existingManager = GameObject.Find("AudioVirtualizationManager");
                    if (existingManager != null)
                    {
                        UnityEngine.Object.DestroyImmediate(existingManager);
                    }

                    var virtualizationManager = new GameObject("AudioVirtualizationManager");
                    var component = virtualizationManager.AddComponent<AudioVirtualizationManager>();
                    
                    // Configure the manager with validated parameters
                    // Note: maxRealVoices and maxVirtualVoices are configured via AudioSettings
                    component.priorityThreshold = Mathf.Clamp01(priorityThreshold);
                    component.distanceFactor = Mathf.Max(0f, distanceFactor);
                    component.enableVoiceStealing = enableVoiceStealing;
                    component.cpuUsageLimit = Mathf.Clamp01(cpuUsageLimit);
                    // Note: enableDynamicVirtualization and virtualizationUpdateRate are not available in this Unity version

                    // Initialize the voice management system
                    component.InitializeVoicePool();

                    // Register audio configuration change callback
                    AudioSettings.OnAudioConfigurationChanged += component.OnAudioConfigurationChanged;

                    EditorUtility.SetDirty(virtualizationManager);

                    // Collect current audio source statistics
                    var audioSources = UnityEngine.Object.FindObjectsByType<AudioSource>(FindObjectsSortMode.None);
                    int playingCount = 0, totalCount = audioSources.Length;
                    
                    foreach (var source in audioSources)
                    {
                        if (source.isPlaying)
                            playingCount++;
                    }

                    var result = new
                    {
                        configuration = new
                        {
                            requested = new
                            {
                                maxVirtualVoices = maxVirtualVoices,
                                maxRealVoices = maxRealVoices,
                                priorityThreshold = priorityThreshold,
                                distanceFactor = distanceFactor
                            },
                            applied = new
                            {
                                maxVirtualVoices = appliedConfig.numVirtualVoices,
                                maxRealVoices = appliedConfig.numRealVoices,
                                sampleRate = appliedConfig.sampleRate,
                                dspBufferSize = appliedConfig.dspBufferSize,
                                speakerMode = appliedConfig.speakerMode.ToString()
                            }
                        },
                        voiceManagement = new
                        {
                            enableVoiceStealing = enableVoiceStealing,
                            cpuUsageLimit = cpuUsageLimit,
                            enableDynamicVirtualization = enableDynamicVirtualization,
                            updateRate = virtualizationUpdateRate
                        },
                        currentAudioState = new
                        {
                            totalAudioSources = totalCount,
                            playingAudioSources = playingCount,
                            availableRealVoices = Mathf.Max(0, appliedConfig.numRealVoices - playingCount),
                            voiceUtilization = totalCount > 0 ? (float)playingCount / appliedConfig.numRealVoices : 0f
                        },
                        managerCreated = true,
                        configurationSuccess = success
                    };

                    return Response.Success("Audio virtualization setup successfully.", result);
                }
                catch (Exception e)
                {
                    Debug.LogError($"[AdvancedAudio] SetupAudioVirtualization failed: {e.Message}");
                    return Response.Error($"Failed to setup audio virtualization: {e.Message}");
                }
            }
        }

        private static object CreateAdaptiveAudioSystem(JObject @params)
        {
            using (s_AdaptiveSystemMarker.Auto())
            {
                try
                {
                    string systemName = @params["system_name"]?.ToString();
                    var eventTriggersToken = @params["event_triggers"];
                    var audioLayersToken = @params["audio_layers"];
                    var transitionSettingsToken = @params["transition_settings"];
                    bool enableDynamicMixing = @params["enable_dynamic_mixing"]?.ToObject<bool>() ?? true;
                    string responseCurve = @params["response_curve"]?.ToString() ?? "smooth";
                    float updateFrequency = @params["update_frequency"]?.ToObject<float>() ?? 0.1f;
                    string mixerName = @params["mixer_name"]?.ToString();

                    if (string.IsNullOrEmpty(systemName))
                    {
                        return Response.Error("System name is required.");
                    }

                    if (eventTriggersToken == null || audioLayersToken == null)
                    {
                        return Response.Error("Event triggers and audio layers are required.");
                    }

                    // Find or use specified mixer
                    AudioMixer targetMixer = null;
                    if (!string.IsNullOrEmpty(mixerName))
                    {
                        targetMixer = FindAudioMixer(mixerName);
                        if (targetMixer == null)
                        {
                            Debug.LogWarning($"[AdvancedAudio] Mixer '{mixerName}' not found, system will work without mixer routing.");
                        }
                    }

                    // Remove existing system with same name
                    var existingSystem = GameObject.Find(systemName);
                    if (existingSystem != null)
                    {
                        UnityEngine.Object.DestroyImmediate(existingSystem);
                    }

                    // Create adaptive audio system GameObject
                    var systemGO = new GameObject(systemName);
                    var adaptiveSystem = systemGO.AddComponent<AdaptiveAudioSystem>();

                    // Parse and validate event triggers
                    var eventTriggers = eventTriggersToken.ToObject<List<Dictionary<string, object>>>();
                    var validEventTriggers = ParseEventTriggers(eventTriggers);
                    adaptiveSystem.eventTriggers = validEventTriggers;

                    // Parse and create audio layers
                    var audioLayers = audioLayersToken.ToObject<List<Dictionary<string, object>>>();
                    var validAudioLayers = ParseAudioLayers(audioLayers);
                    adaptiveSystem.audioLayers = validAudioLayers;

                    // Configure transition settings
                    if (transitionSettingsToken != null)
                    {
                        var transitionSettings = transitionSettingsToken.ToObject<Dictionary<string, object>>();
                        adaptiveSystem.transitionSettings = ParseTransitionSettings(transitionSettings);
                    }
                    else
                    {
                        adaptiveSystem.transitionSettings = new TransitionSettings(); // Use defaults
                    }

                    // Configure system parameters
                    adaptiveSystem.enableDynamicMixing = enableDynamicMixing;
                    adaptiveSystem.responseCurve = ParseResponseCurve(responseCurve);
                    adaptiveSystem.updateFrequency = Mathf.Clamp(updateFrequency, 0.01f, 10.0f);
                    adaptiveSystem.targetMixer = targetMixer;

                    // Initialize the system
                    bool initSuccess = adaptiveSystem.Initialize();
                    if (!initSuccess)
                    {
                        UnityEngine.Object.DestroyImmediate(systemGO);
                        return Response.Error("Failed to initialize adaptive audio system. Check audio layer configurations.");
                    }

                    EditorUtility.SetDirty(systemGO);

                    // Collect validation results
                    var layerResults = validAudioLayers.Select((layer, index) => new
                    {
                        layerName = layer.layerName,
                        audioClipPath = layer.audioClipPath,
                        clipFound = !string.IsNullOrEmpty(layer.audioClipPath) && 
                                   AssetDatabase.LoadAssetAtPath<AudioClip>(layer.audioClipPath) != null,
                        volume = layer.volume,
                        loop = layer.loop
                    }).ToList();

                    var result = new
                    {
                        system = new
                        {
                            name = systemName,
                            gameObjectCreated = true,
                            mixerAssigned = targetMixer != null,
                            mixerName = targetMixer?.name,
                            initialized = initSuccess
                        },
                        eventTriggers = new
                        {
                            configured = validEventTriggers.Count,
                            total = eventTriggers.Count
                        },
                        audioLayers = new
                        {
                            configured = validAudioLayers.Count,
                            total = audioLayers.Count,
                            details = layerResults
                        },
                        configuration = new
                        {
                            dynamicMixing = enableDynamicMixing,
                            responseCurve = responseCurve,
                            updateFrequency = updateFrequency,
                            transitionSettings = new
                            {
                                fadeInTime = adaptiveSystem.transitionSettings.fadeInTime,
                                fadeOutTime = adaptiveSystem.transitionSettings.fadeOutTime,
                                crossfadeTime = adaptiveSystem.transitionSettings.crossfadeTime
                            }
                        }
                    };

                    return Response.Success("Adaptive audio system created successfully.", result);
                }
                catch (Exception e)
                {
                    Debug.LogError($"[AdvancedAudio] CreateAdaptiveAudioSystem failed: {e.Message}");
                    return Response.Error($"Failed to create adaptive audio system: {e.Message}");
                }
            }
        }

        private static object SetupSnapshotBlending(JObject @params)
        {
            try
            {
                string mixerName = @params["mixer_name"]?.ToString();
                var snapshotsToken = @params["snapshots"];
                string blendMode = @params["blend_mode"]?.ToString() ?? "weighted";
                float transitionTime = @params["transition_time"]?.ToObject<float>() ?? 1.0f;
                bool enableCrossfade = @params["enable_crossfade"]?.ToObject<bool>() ?? true;
                string interpolationCurve = @params["interpolation_curve"]?.ToString() ?? "smooth_step";
                bool prioritySystem = @params["priority_system"]?.ToObject<bool>() ?? true;

                if (string.IsNullOrEmpty(mixerName))
                {
                    return Response.Error("Mixer name is required.");
                }

                if (snapshotsToken == null)
                {
                    return Response.Error("Snapshots configuration is required.");
                }

                // Find the audio mixer using AssetDatabase
                AudioMixer mixer = FindAudioMixer(mixerName);
                if (mixer == null)
                {
                    return Response.Error($"Audio mixer '{mixerName}' not found. Make sure the mixer exists in your project assets.");
                }

                // Parse snapshots configuration
                var snapshotConfigs = snapshotsToken.ToObject<List<Dictionary<string, object>>>();
                if (snapshotConfigs == null || snapshotConfigs.Count == 0)
                {
                    return Response.Error("At least one snapshot configuration is required.");
                }

                // Validate snapshots exist in the mixer
                var validSnapshots = new List<Dictionary<string, object>>();
                var foundSnapshots = new List<AudioMixerSnapshot>();

                foreach (var config in snapshotConfigs)
                {
                    string snapshotName = config.GetValueOrDefault("name", "").ToString();
                    if (string.IsNullOrEmpty(snapshotName))
                    {
                        Debug.LogWarning("[AdvancedAudio] Skipping snapshot with empty name.");
                        continue;
                    }

                    AudioMixerSnapshot snapshot = mixer.FindSnapshot(snapshotName);
                    if (snapshot == null)
                    {
                        Debug.LogWarning($"[AdvancedAudio] Snapshot '{snapshotName}' not found in mixer '{mixerName}', skipping.");
                        continue;
                    }

                    validSnapshots.Add(config);
                    foundSnapshots.Add(snapshot);
                }

                if (validSnapshots.Count == 0)
                {
                    return Response.Error("No valid snapshots found in the mixer. Check snapshot names.");
                }

                // Create or update snapshot blending manager
                var existingManager = GameObject.Find($"{mixerName}_SnapshotBlending");
                if (existingManager != null)
                {
                    UnityEngine.Object.DestroyImmediate(existingManager);
                }

                var blendingManager = new GameObject($"{mixerName}_SnapshotBlending");
                var component = blendingManager.AddComponent<AudioMixerSnapshotBlender>();
                
                // Configure the blending manager
                component.targetMixer = mixer;
                component.blendMode = ParseBlendMode(blendMode);
                component.defaultTransitionTime = Mathf.Max(0f, transitionTime);
                component.enableCrossfade = enableCrossfade;
                component.interpolationCurve = ParseInterpolationCurve(interpolationCurve);
                component.enablePrioritySystem = prioritySystem;

                // Setup snapshots with the real AudioMixerSnapshot references
                component.SetupSnapshots(validSnapshots, foundSnapshots);

                // Test transition to first valid snapshot if available
                string firstSnapshotName = null;
                if (foundSnapshots.Count > 0)
                {
                    firstSnapshotName = validSnapshots[0].GetValueOrDefault("name", "").ToString();
                    component.BlendToSnapshot(firstSnapshotName, 0.1f); // Quick test transition
                }

                EditorUtility.SetDirty(blendingManager);

                // Collect mixer information
                var mixerInfo = new
                {
                    name = mixer.name,
                    assetPath = AssetDatabase.GetAssetPath(mixer),
                    outputMixerGroup = mixer.outputAudioMixerGroup?.name,
                    updateMode = mixer.updateMode.ToString()
                };

                var snapshotInfo = validSnapshots.Select((config, index) => new
                {
                    name = config.GetValueOrDefault("name", "").ToString(),
                    weight = Convert.ToSingle(config.GetValueOrDefault("weight", 1.0f)),
                    priority = Convert.ToInt32(config.GetValueOrDefault("priority", 0)),
                    snapshotFound = foundSnapshots[index] != null,
                    snapshotReference = foundSnapshots[index]?.name
                }).ToList();

                var result = new
                {
                    mixer = mixerInfo,
                    snapshots = new
                    {
                        requested = snapshotConfigs.Count,
                        valid = validSnapshots.Count,
                        invalid = snapshotConfigs.Count - validSnapshots.Count,
                        details = snapshotInfo
                    },
                    blending = new
                    {
                        blendMode = blendMode,
                        transitionTime = transitionTime,
                        crossfade = enableCrossfade,
                        interpolationCurve = interpolationCurve,
                        prioritySystem = prioritySystem
                    },
                    manager = new
                    {
                        created = true,
                        name = blendingManager.name,
                        initialSnapshot = firstSnapshotName
                    }
                };

                return Response.Success("Audio mixer snapshot blending setup successfully.", result);
            }
            catch (Exception e)
            {
                Debug.LogError($"[AdvancedAudio] SetupSnapshotBlending failed: {e.Message}");
                return Response.Error($"Failed to setup snapshot blending: {e.Message}");
            }
        }

        private static object ConfigureRealtimeProfiling(JObject @params)
        {
            try
            {
                bool enableProfiling = @params["enable_profiling"]?.ToObject<bool>() ?? true;
                string profilingMode = @params["profiling_mode"]?.ToString() ?? "detailed";
                float sampleRate = @params["sample_rate"]?.ToObject<float>() ?? 10.0f;
                bool trackVoiceCount = @params["track_voice_count"]?.ToObject<bool>() ?? true;
                bool trackCpuUsage = @params["track_cpu_usage"]?.ToObject<bool>() ?? true;
                bool trackMemoryUsage = @params["track_memory_usage"]?.ToObject<bool>() ?? true;
                bool trackDspLoad = @params["track_dsp_load"]?.ToObject<bool>() ?? true;
                string exportFormat = @params["export_format"]?.ToString() ?? "json";
                int maxSamples = @params["max_samples"]?.ToObject<int>() ?? 300; // 30 seconds at 10Hz
                bool autoExport = @params["auto_export"]?.ToObject<bool>() ?? false;

                // Validate sample rate
                sampleRate = Mathf.Clamp(sampleRate, 0.1f, 60.0f);
                maxSamples = Mathf.Clamp(maxSamples, 10, 3600);

                // Create or update audio profiling manager
                var existingManager = GameObject.Find("AudioProfilingManager");
                if (existingManager != null)
                {
                    var existingComponent = existingManager.GetComponent<AudioProfilingManager>();
                    if (existingComponent != null)
                    {
                        existingComponent.StopProfiling();
                    }
                    UnityEngine.Object.DestroyImmediate(existingManager);
                }

                var profilingManager = new GameObject("AudioProfilingManager");
                var component = profilingManager.AddComponent<AudioProfilingManager>();

                // Configure the profiling manager
                component.enableProfiling = enableProfiling;
                component.profilingMode = ParseProfilingMode(profilingMode);
                component.sampleRate = sampleRate;
                component.trackVoiceCount = trackVoiceCount;
                component.trackCpuUsage = trackCpuUsage;
                component.trackMemoryUsage = trackMemoryUsage;
                component.trackDspLoad = trackDspLoad;
                component.exportFormat = ParseExportFormat(exportFormat);
                component.maxSamples = maxSamples;
                component.autoExport = autoExport;

                // Initialize profiling system with real ProfilerRecorder setup
                component.InitializeProfilers();

                // Start profiling if enabled
                if (enableProfiling)
                {
                    component.StartProfiling();
                }

                EditorUtility.SetDirty(profilingManager);

                // Get current audio system status
                var currentConfig = AudioSettings.GetConfiguration();
                var currentAudioSources = UnityEngine.Object.FindObjectsByType<AudioSource>(FindObjectsSortMode.None);
                int playingSourcesCount = currentAudioSources.Count(source => source.isPlaying);

                // Get initial profiling values
                var initialStats = component.GetCurrentStats();

                var result = new
                {
                    configuration = new
                    {
                        enabled = enableProfiling,
                        mode = profilingMode,
                        sampleRate = sampleRate,
                        maxSamples = maxSamples,
                        autoExport = autoExport,
                        exportFormat = exportFormat
                    },
                    tracking = new
                    {
                        voiceCount = trackVoiceCount,
                        cpuUsage = trackCpuUsage,
                        memoryUsage = trackMemoryUsage,
                        dspLoad = trackDspLoad
                    },
                    audioSystemStatus = new
                    {
                        dspBufferSize = currentConfig.dspBufferSize,
                        sampleRate = currentConfig.sampleRate,
                        realVoices = currentConfig.numRealVoices,
                        virtualVoices = currentConfig.numVirtualVoices,
                        currentPlayingSources = playingSourcesCount,
                        totalAudioSources = currentAudioSources.Length,
                        speakerMode = currentConfig.speakerMode.ToString()
                    },
                    initialProfilerStats = initialStats,
                    manager = new
                    {
                        created = true,
                        name = profilingManager.name,
                        profilingStarted = enableProfiling
                    }
                };

                return Response.Success("Audio profiling configured successfully.", result);
            }
            catch (Exception e)
            {
                Debug.LogError($"[AdvancedAudio] ConfigureRealtimeProfiling failed: {e.Message}");
                return Response.Error($"Failed to configure audio profiling: {e.Message}");
            }
        }

        private static object SetupFormatConversion(JObject @params)
        {
            try
            {
                string sourceFormat = @params["source_format"]?.ToString();
                string targetFormat = @params["target_format"]?.ToString();
                string conversionQuality = @params["conversion_quality"]?.ToString() ?? "high";
                bool enableRuntimeConversion = @params["enable_runtime_conversion"]?.ToObject<bool>() ?? false;
                var compressionSettingsToken = @params["compression_settings"];
                bool streamingSupport = @params["streaming_support"]?.ToObject<bool>() ?? true;
                bool cacheConvertedAudio = @params["cache_converted_audio"]?.ToObject<bool>() ?? true;
                bool batchConversion = @params["batch_conversion"]?.ToObject<bool>() ?? false;
                string sourcePath = @params["source_path"]?.ToString() ?? "Assets/Audio";

                if (string.IsNullOrEmpty(sourceFormat) || string.IsNullOrEmpty(targetFormat))
                {
                    return Response.Error("Source and target formats are required.");
                }

                // Validate formats
                if (!IsValidAudioFormat(sourceFormat) || !IsValidAudioFormat(targetFormat))
                {
                    return Response.Error("Invalid audio format specified. Supported formats: wav, mp3, ogg, aiff, flac");
                }

                // Parse compression settings
                var compressionSettings = new CompressionSettings();
                if (compressionSettingsToken != null)
                {
                    var compSettings = compressionSettingsToken.ToObject<Dictionary<string, object>>();
                    compressionSettings = ParseCompressionSettings(compSettings);
                }

                // Create conversion settings for AudioImporter
                var sampleSettings = new AudioImporterSampleSettings
                {
                    loadType = ParseLoadType(compressionSettings.loadType),
                    compressionFormat = ParseCompressionFormat(compressionSettings.compressionFormat),
                    quality = Mathf.Clamp01(compressionSettings.quality),
                    sampleRateSetting = ParseSampleRateSetting(compressionSettings.sampleRateSetting)
                };

                // Find and convert audio files
                string[] guids = AssetDatabase.FindAssets("t:AudioClip", new[] { sourcePath });
                int converted = 0, failed = 0;
                var errors = new List<string>();

                foreach (string guid in guids)
                {
                    try
                    {
                        string assetPath = AssetDatabase.GUIDToAssetPath(guid);
                        AudioImporter importer = AssetImporter.GetAtPath(assetPath) as AudioImporter;
                        
                        if (importer != null)
                        {
                            importer.defaultSampleSettings = sampleSettings;
                            AssetDatabase.ImportAsset(assetPath, ImportAssetOptions.ForceUpdate);
                            converted++;
                        }
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"Failed to convert {AssetDatabase.GUIDToAssetPath(guid)}: {ex.Message}");
                        failed++;
                    }
                }

                // Create runtime converter if needed
                GameObject conversionManager = null;
                if (enableRuntimeConversion)
                {
                    conversionManager = new GameObject("AudioFormatConversionManager");
                    var component = conversionManager.AddComponent<AudioFormatConverter>();

                    component.sourceFormat = ParseAudioFormat(sourceFormat);
                    component.targetFormat = ParseAudioFormat(targetFormat);
                    component.conversionQuality = ParseConversionQuality(conversionQuality);
                    component.enableRuntimeConversion = enableRuntimeConversion;
                    component.streamingSupport = streamingSupport;
                    component.cacheConvertedAudio = cacheConvertedAudio;
                    component.batchConversion = batchConversion;
                    component.compressionSettings = compressionSettings;

                    component.Initialize();
                    EditorUtility.SetDirty(conversionManager);
                }

                AssetDatabase.Refresh();

                var result = new
                {
                    conversion = new
                    {
                        sourceFormat = sourceFormat,
                        targetFormat = targetFormat,
                        conversionQuality = conversionQuality,
                        sourcePath = sourcePath
                    },
                    results = new
                    {
                        totalFiles = guids.Length,
                        converted = converted,
                        failed = failed,
                        errors = errors.Take(5).ToList()
                    },
                    settings = new
                    {
                        loadType = sampleSettings.loadType.ToString(),
                        compressionFormat = sampleSettings.compressionFormat.ToString(),
                        quality = sampleSettings.quality
                    },
                    features = new
                    {
                        runtimeConversion = enableRuntimeConversion,
                        streamingSupport = streamingSupport,
                        cacheEnabled = cacheConvertedAudio,
                        batchMode = batchConversion
                    },
                    converterCreated = conversionManager != null
                };

                return Response.Success("Audio format conversion setup successfully.", result);
            }
            catch (Exception e)
            {
                Debug.LogError($"[AdvancedAudio] SetupFormatConversion failed: {e.Message}");
                return Response.Error($"Failed to setup format conversion: {e.Message}");
            }
        }

        #region Helper Methods

        private static bool IsPowerOfTwo(int value)
        {
            return value > 0 && (value & (value - 1)) == 0;
        }

        private static AudioMixer FindAudioMixer(string mixerName)
        {
            string[] guids = AssetDatabase.FindAssets($"t:AudioMixer {mixerName}");
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                AudioMixer mixer = AssetDatabase.LoadAssetAtPath<AudioMixer>(path);
                if (mixer != null && mixer.name == mixerName)
                {
                    return mixer;
                }
            }
            return null;
        }

        private static AudioVirtualizationMode ParseVirtualizationMode(string mode)
        {
            return mode.ToLower() switch
            {
                "fadeout" => AudioVirtualizationMode.FadeOut,
                "stop" => AudioVirtualizationMode.Stop,
                "pause" => AudioVirtualizationMode.Pause,
                _ => AudioVirtualizationMode.FadeOut
            };
        }

        private static bool IsValidAudioFormat(string format)
        {
            string[] validFormats = { "wav", "mp3", "ogg", "aiff", "flac" };
            return validFormats.Contains(format.ToLower());
        }

        private static AudioFormat ParseAudioFormat(string format)
        {
            return format.ToLower() switch
            {
                "wav" => AudioFormat.WAV,
                "mp3" => AudioFormat.MP3,
                "ogg" => AudioFormat.OGG,
                "aiff" => AudioFormat.AIFF,
                "flac" => AudioFormat.FLAC,
                _ => AudioFormat.WAV
            };
        }

        private static ConversionQuality ParseConversionQuality(string quality)
        {
            return quality.ToLower() switch
            {
                "low" => ConversionQuality.Low,
                "medium" => ConversionQuality.Medium,
                "high" => ConversionQuality.High,
                "lossless" => ConversionQuality.Lossless,
                _ => ConversionQuality.High
            };
        }

        private static BlendMode ParseBlendMode(string mode)
        {
            return mode.ToLower() switch
            {
                "weighted" => BlendMode.Weighted,
                "additive" => BlendMode.Additive,
                "override" => BlendMode.Override,
                _ => BlendMode.Weighted
            };
        }

        private static InterpolationCurve ParseInterpolationCurve(string curve)
        {
            return curve.ToLower() switch
            {
                "linear" => InterpolationCurve.Linear,
                "smooth_step" => InterpolationCurve.SmoothStep,
                "ease_in_out" => InterpolationCurve.EaseInOut,
                _ => InterpolationCurve.SmoothStep
            };
        }

        private static ProfilingMode ParseProfilingMode(string mode)
        {
            return mode.ToLower() switch
            {
                "standard" => ProfilingMode.Standard,
                "detailed" => ProfilingMode.Detailed,
                "comprehensive" => ProfilingMode.Comprehensive,
                _ => ProfilingMode.Detailed
            };
        }

        private static ExportFormat ParseExportFormat(string format)
        {
            return format.ToLower() switch
            {
                "json" => ExportFormat.JSON,
                "csv" => ExportFormat.CSV,
                "binary" => ExportFormat.Binary,
                _ => ExportFormat.JSON
            };
        }

        private static ResponseCurve ParseResponseCurve(string curve)
        {
            return curve.ToLower() switch
            {
                "linear" => ResponseCurve.Linear,
                "smooth" => ResponseCurve.Smooth,
                "exponential" => ResponseCurve.Exponential,
                _ => ResponseCurve.Smooth
            };
        }

        private static List<EventTrigger> ParseEventTriggers(List<Dictionary<string, object>> triggers)
        {
            var result = new List<EventTrigger>();
            foreach (var trigger in triggers)
            {
                var eventTrigger = new EventTrigger
                {
                    eventName = trigger.GetValueOrDefault("eventName", "").ToString(),
                    triggerType = trigger.GetValueOrDefault("triggerType", "OnTriggerEnter").ToString(),
                    parameters = trigger.GetValueOrDefault("parameters", new Dictionary<string, object>()) as Dictionary<string, object>
                };
                result.Add(eventTrigger);
            }
            return result;
        }

        private static List<AudioLayer> ParseAudioLayers(List<Dictionary<string, object>> layers)
        {
            var result = new List<AudioLayer>();
            foreach (var layer in layers)
            {
                var audioLayer = new AudioLayer
                {
                    layerName = layer.GetValueOrDefault("layerName", "").ToString(),
                    audioClipPath = layer.GetValueOrDefault("audioClipPath", "").ToString(),
                    volume = Convert.ToSingle(layer.GetValueOrDefault("volume", 1.0f)),
                    pitch = Convert.ToSingle(layer.GetValueOrDefault("pitch", 1.0f)),
                    loop = Convert.ToBoolean(layer.GetValueOrDefault("loop", false))
                };
                result.Add(audioLayer);
            }
            return result;
        }

        private static TransitionSettings ParseTransitionSettings(Dictionary<string, object> settings)
        {
            return new TransitionSettings
            {
                fadeInTime = Convert.ToSingle(settings.GetValueOrDefault("fadeInTime", 1.0f)),
                fadeOutTime = Convert.ToSingle(settings.GetValueOrDefault("fadeOutTime", 1.0f)),
                crossfadeTime = Convert.ToSingle(settings.GetValueOrDefault("crossfadeTime", 0.5f)),
                useCurve = Convert.ToBoolean(settings.GetValueOrDefault("useCurve", true))
            };
        }

        private static CompressionSettings ParseCompressionSettings(Dictionary<string, object> settings)
        {
            return new CompressionSettings
            {
                compressionFormat = settings.GetValueOrDefault("compressionFormat", "Vorbis").ToString(),
                quality = Convert.ToSingle(settings.GetValueOrDefault("quality", 0.7f)),
                sampleRateSetting = settings.GetValueOrDefault("sampleRateSetting", "PreserveSampleRate").ToString(),
                loadType = settings.GetValueOrDefault("loadType", "DecompressOnLoad").ToString()
            };
        }

        private static AudioClipLoadType ParseLoadType(string loadType)
        {
            return loadType.ToLower() switch
            {
                "decompressonload" => AudioClipLoadType.DecompressOnLoad,
                "compressedinmemory" => AudioClipLoadType.CompressedInMemory,
                "streaming" => AudioClipLoadType.Streaming,
                _ => AudioClipLoadType.DecompressOnLoad
            };
        }

        private static AudioCompressionFormat ParseCompressionFormat(string format)
        {
            return format.ToLower() switch
            {
                "pcm" => AudioCompressionFormat.PCM,
                "vorbis" => AudioCompressionFormat.Vorbis,
                "adpcm" => AudioCompressionFormat.ADPCM,
                "mp3" => AudioCompressionFormat.MP3,
                _ => AudioCompressionFormat.Vorbis
            };
        }

        private static AudioSampleRateSetting ParseSampleRateSetting(string setting)
        {
            return setting.ToLower() switch
            {
                "preservesamplerate" => AudioSampleRateSetting.PreserveSampleRate,
                "optimizesamplerate" => AudioSampleRateSetting.OptimizeSampleRate,
                "overridesamplerate" => AudioSampleRateSetting.OverrideSampleRate,
                _ => AudioSampleRateSetting.PreserveSampleRate
            };
        }

        private static bool DoesNeedConversion(AudioImporterSampleSettings current, AudioImporterSampleSettings target, string sourceFormat, string targetFormat)
        {
            return current.compressionFormat != target.compressionFormat ||
                   current.quality != target.quality ||
                   current.loadType != target.loadType ||
                   !sourceFormat.Equals(targetFormat, StringComparison.OrdinalIgnoreCase);
        }

        private static string GetFileExtension(string filePath)
        {
            return Path.GetExtension(filePath).TrimStart('.').ToLower();
        }

        #endregion
    }

    #region Supporting Classes and Enums

    public enum AudioVirtualizationMode
    {
        FadeOut,
        Stop,
        Pause
    }

    public enum AudioFormat
    {
        WAV,
        MP3,
        OGG,
        AIFF,
        FLAC
    }

    public enum ConversionQuality
    {
        Low,
        Medium,
        High,
        Lossless
    }

    public enum BlendMode
    {
        Weighted,
        Additive,
        Override
    }

    public enum InterpolationCurve
    {
        Linear,
        SmoothStep,
        EaseInOut
    }

    public enum ProfilingMode
    {
        Standard,
        Detailed,
        Comprehensive
    }

    public enum ExportFormat
    {
        JSON,
        CSV,
        Binary
    }

    public enum ResponseCurve
    {
        Linear,
        Smooth,
        Exponential
    }

    [System.Serializable]
    public class EventTrigger
    {
        public string eventName;
        public string triggerType;
        public Dictionary<string, object> parameters;
    }

    [System.Serializable]
    public class AudioLayer
    {
        public string layerName;
        public string audioClipPath;
        public float volume = 1.0f;
        public float pitch = 1.0f;
        public bool loop = false;
    }

    [System.Serializable]
    public class TransitionSettings
    {
        public float fadeInTime = 1.0f;
        public float fadeOutTime = 1.0f;
        public float crossfadeTime = 0.5f;
        public bool useCurve = true;
    }

    [System.Serializable]
    public class CompressionSettings
    {
        public string compressionFormat = "Vorbis";
        public float quality = 0.7f;
        public string sampleRateSetting = "PreserveSampleRate";
        public string loadType = "DecompressOnLoad";
    }

    #endregion

    #region MonoBehaviour Components

    public class DSPTimeSyncComponent : MonoBehaviour
    {
        public bool enablePreciseTiming = true;
        public float latencyCompensation = 0.0f;
        public AudioSource targetAudioSource;

        private double scheduledTime;
        private bool isInitialized = false;
        private float lastDSPTime = 0f;
        private bool isPlaying = false;

        void Start()
        {
            Initialize();
        }

        public void Initialize()
        {
            if (targetAudioSource == null)
                targetAudioSource = GetComponent<AudioSource>();

            if (targetAudioSource != null)
            {
                // Configure for precise timing
                targetAudioSource.playOnAwake = false;
                targetAudioSource.priority = 0; // Highest priority
                isInitialized = true;
                
                Debug.Log($"[DSPTimeSyncComponent] Initialized on '{gameObject.name}' with AudioSource");
            }
            else
            {
                Debug.LogWarning($"[DSPTimeSyncComponent] No AudioSource found on '{gameObject.name}'");
            }
        }

        void Update()
        {
            if (enablePreciseTiming && isInitialized)
            {
                MonitorDSPTime();
            }
        }

        private void MonitorDSPTime()
        {
            float currentDSPTime = (float)AudioSettings.dspTime;
            
            // Check for significant DSP time changes (audio device changes, etc.)
            if (Mathf.Abs(currentDSPTime - lastDSPTime) > 1.0f && lastDSPTime > 0f)
            {
                Debug.LogWarning("[DSPTimeSyncComponent] Large DSP time jump detected - audio device may have changed");
                OnAudioConfigurationChanged();
            }
            
            lastDSPTime = currentDSPTime;
        }

        public void PlayScheduled(double time)
        {
            if (!isInitialized || targetAudioSource == null || targetAudioSource.clip == null) 
                return;

            double currentDSPTime = AudioSettings.dspTime;
            scheduledTime = time + latencyCompensation;

            if (scheduledTime > currentDSPTime)
            {
                targetAudioSource.PlayScheduled(scheduledTime);
                isPlaying = true;
                
                if (enablePreciseTiming)
                {
                    StartCoroutine(MonitorScheduledPlayback());
                }
                
                Debug.Log($"[DSPTimeSyncComponent] Scheduled playback at DSP time: {scheduledTime:F6}, Current: {currentDSPTime:F6}");
            }
            else
            {
                Debug.LogWarning($"[DSPTimeSyncComponent] Attempted to schedule playback in the past. Target: {scheduledTime:F6}, Current: {currentDSPTime:F6}");
                targetAudioSource.Play(); // Fallback to immediate playback
                isPlaying = true;
            }
        }

        public void StopScheduled(double time)
        {
            if (!isInitialized || targetAudioSource == null) 
                return;

            double currentDSPTime = AudioSettings.dspTime;
            double stopTime = time + latencyCompensation;

            if (stopTime > currentDSPTime)
            {
                targetAudioSource.SetScheduledEndTime(stopTime);
                Debug.Log($"[DSPTimeSyncComponent] Scheduled stop at DSP time: {stopTime:F6}");
            }
            else
            {
                targetAudioSource.Stop();
                isPlaying = false;
                Debug.Log("[DSPTimeSyncComponent] Immediate stop executed");
            }
        }

        private System.Collections.IEnumerator MonitorScheduledPlayback()
        {
            while (isPlaying && targetAudioSource != null && targetAudioSource.isPlaying)
            {
                yield return new WaitForSeconds(0.1f);
            }
            
            isPlaying = false;
        }

        public double GetDSPTime()
        {
            return AudioSettings.dspTime;
        }

        public float GetLatencyMs()
        {
            var config = AudioSettings.GetConfiguration();
            return (float)config.dspBufferSize / config.sampleRate * 1000f + latencyCompensation * 1000f;
        }

        public bool IsPlayingScheduled()
        {
            return isPlaying && targetAudioSource != null && targetAudioSource.isPlaying;
        }

        public double GetScheduledTime()
        {
            return scheduledTime;
        }

        public void SyncToExternalClock(double externalTime, double sampleRate = 48000.0)
        {
            if (!isInitialized) return;

            double dspTime = AudioSettings.dspTime;
            double timeDifference = externalTime - dspTime;
            
            // Adjust latency compensation based on external clock
            latencyCompensation = (float)timeDifference;
            
            Debug.Log($"[DSPTimeSyncComponent] Synced to external clock. Time diff: {timeDifference:F6}s, New compensation: {latencyCompensation:F6}s");
        }

        private void OnAudioConfigurationChanged()
        {
            if (isInitialized && targetAudioSource != null)
            {
                // Recalculate timing parameters when audio configuration changes
                var config = AudioSettings.GetConfiguration();
                float newLatency = (float)config.dspBufferSize / config.sampleRate;
                
                Debug.Log($"[DSPTimeSyncComponent] Audio config changed. New buffer latency: {newLatency * 1000f:F2}ms");
                
                // Optionally adjust compensation
                if (enablePreciseTiming)
                {
                    latencyCompensation = newLatency;
                }
            }
        }

        public Dictionary<string, object> GetSyncStatus()
        {
            var config = AudioSettings.GetConfiguration();
            
            return new Dictionary<string, object>
            {
                ["initialized"] = isInitialized,
                ["enablePreciseTiming"] = enablePreciseTiming,
                ["latencyCompensation"] = latencyCompensation,
                ["currentDSPTime"] = AudioSettings.dspTime,
                ["scheduledTime"] = scheduledTime,
                ["isPlaying"] = isPlaying,
                ["audioSourceAssigned"] = targetAudioSource != null,
                ["audioSourcePlaying"] = targetAudioSource?.isPlaying ?? false,
                ["bufferLatencyMs"] = (float)config.dspBufferSize / config.sampleRate * 1000f,
                ["totalLatencyMs"] = GetLatencyMs()
            };
        }

        void OnDestroy()
        {
            if (targetAudioSource != null && targetAudioSource.isPlaying)
            {
                targetAudioSource.Stop();
            }
            isPlaying = false;
        }
    }

    public class AdaptiveBufferManager : MonoBehaviour
    {
        public float targetLatencyMs = 20.0f;
        public string currentOptimizationMode = "balanced";
        public int numBuffers = 4;
        public float adaptationSensitivity = 0.1f;
        public float updateInterval = 1.0f;

        private float currentLatency;
        private int currentBufferSize;
        private float lastUpdateTime;
        private Unity.Profiling.ProfilerRecorder cpuRecorder;
        private Unity.Profiling.ProfilerRecorder memoryRecorder;
        private bool isInitialized = false;

        public void Initialize()
        {
            var config = AudioSettings.GetConfiguration();
            currentBufferSize = config.dspBufferSize;
            currentLatency = (float)currentBufferSize / config.sampleRate * 1000f;
            
            try
            {
                cpuRecorder = Unity.Profiling.ProfilerRecorder.StartNew(
                    Unity.Profiling.ProfilerCategory.Internal, "Main Thread");
                memoryRecorder = Unity.Profiling.ProfilerRecorder.StartNew(
                    Unity.Profiling.ProfilerCategory.Memory, "System Used Memory");
            }
            catch (Exception e)
            {
                Debug.LogWarning($"[AdaptiveBufferManager] Could not initialize profilers: {e.Message}");
            }

            isInitialized = true;
            lastUpdateTime = Time.time;
        }

        void Start()
        {
            if (!isInitialized)
            {
                Initialize();
            }
        }

        void Update()
        {
            if (!isInitialized || Time.time - lastUpdateTime < updateInterval)
                return;

            if (currentOptimizationMode == "balanced" || currentOptimizationMode == "automatic")
            {
                AdaptBufferSize();
                lastUpdateTime = Time.time;
            }
        }

        private void AdaptBufferSize()
        {
            float cpuLoad = GetCPULoad();
            float memoryUsage = GetMemoryUsage();
            
            // Calculate performance metrics
            float performanceScore = CalculatePerformanceScore(cpuLoad, memoryUsage);
            float latencyDifference = currentLatency - targetLatencyMs;
            
            // Determine if buffer size should be adjusted
            bool shouldReduceLatency = latencyDifference > targetLatencyMs * 0.2f && performanceScore > 0.7f;
            bool shouldIncreaseStability = latencyDifference < -targetLatencyMs * 0.2f && performanceScore < 0.5f;
            
            if (shouldReduceLatency)
            {
                // Reduce buffer size for lower latency
                int newBufferSize = Mathf.Max(64, Mathf.RoundToInt(currentBufferSize * 0.75f));
                if (IsPowerOfTwo(newBufferSize) && newBufferSize != currentBufferSize)
                {
                    UpdateBufferSize(newBufferSize);
                }
            }
            else if (shouldIncreaseStability)
            {
                // Increase buffer size for better stability
                int newBufferSize = Mathf.Min(2048, Mathf.RoundToInt(currentBufferSize * 1.33f));
                newBufferSize = GetNearestPowerOfTwo(newBufferSize);
                if (newBufferSize != currentBufferSize)
                {
                    UpdateBufferSize(newBufferSize);
                }
            }
        }

        private float GetCPULoad()
        {
            if (cpuRecorder.Valid && cpuRecorder.IsRunning)
            {
                // Convert nanoseconds to milliseconds and normalize
                double cpuTimeMs = cpuRecorder.LastValueAsDouble * 1e-6;
                float frameTimeMs = Time.deltaTime * 1000f;
                return frameTimeMs > 0 ? Mathf.Clamp01((float)(cpuTimeMs / frameTimeMs)) : 0f;
            }
            
            // Fallback: estimate from frame rate
            float targetFrameTime = 1000f / 60f; // 16.67ms for 60 FPS
            float actualFrameTime = Time.deltaTime * 1000f;
            return Mathf.Clamp01(actualFrameTime / targetFrameTime);
        }

        private float GetMemoryUsage()
        {
            if (memoryRecorder.Valid && memoryRecorder.IsRunning)
            {
                long memoryBytes = memoryRecorder.LastValue;
                float memoryMB = memoryBytes / (1024f * 1024f);
                // Normalize against 1GB as reasonable baseline
                return Mathf.Clamp01(memoryMB / 1024f);
            }
            
            // Fallback to Profiler API
            long totalMemory = Profiler.GetTotalAllocatedMemoryLong();
            return Mathf.Clamp01(totalMemory / (1024f * 1024f * 1024f)); // Normalize to GB
        }

        private float CalculatePerformanceScore(float cpuLoad, float memoryUsage)
        {
            // Performance score: 1.0 = excellent, 0.0 = poor
            float cpuScore = 1.0f - cpuLoad;
            float memoryScore = 1.0f - memoryUsage;
            
            // Weight CPU more heavily for audio performance
            return (cpuScore * 0.7f + memoryScore * 0.3f);
        }

        private void UpdateBufferSize(int newSize)
        {
            try
            {
                var config = AudioSettings.GetConfiguration();
                config.dspBufferSize = newSize;
                
                if (AudioSettings.Reset(config))
                {
                    currentBufferSize = newSize;
                    currentLatency = (float)newSize / config.sampleRate * 1000f;
                    
                    Debug.Log($"[AdaptiveBufferManager] Buffer size adapted: {newSize} samples, " +
                             $"Latency: {currentLatency:F2}ms, Target: {targetLatencyMs:F2}ms");
                }
                else
                {
                    Debug.LogWarning($"[AdaptiveBufferManager] Failed to apply buffer size: {newSize}");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[AdaptiveBufferManager] Error updating buffer size: {e.Message}");
            }
        }

        private int GetNearestPowerOfTwo(int value)
        {
            if (value <= 64) return 64;
            if (value <= 128) return 128;
            if (value <= 256) return 256;
            if (value <= 512) return 512;
            if (value <= 1024) return 1024;
            return 2048;
        }

        private bool IsPowerOfTwo(int value)
        {
            return value > 0 && (value & (value - 1)) == 0;
        }

        public void SetOptimizationMode(string mode)
        {
            currentOptimizationMode = mode.ToLower();
        }

        public float GetCurrentLatency()
        {
            return currentLatency;
        }

        public Dictionary<string, object> GetStatus()
        {
            return new Dictionary<string, object>
            {
                ["currentBufferSize"] = currentBufferSize,
                ["currentLatency"] = currentLatency,
                ["targetLatency"] = targetLatencyMs,
                ["optimizationMode"] = currentOptimizationMode,
                ["cpuLoad"] = GetCPULoad(),
                ["memoryUsage"] = GetMemoryUsage(),
                ["performanceScore"] = CalculatePerformanceScore(GetCPULoad(), GetMemoryUsage())
            };
        }

        void OnDestroy()
        {
            if (cpuRecorder.Valid) cpuRecorder.Dispose();
            if (memoryRecorder.Valid) memoryRecorder.Dispose();
        }
    }

    public class AudioVirtualizationManager : MonoBehaviour
    {
        public AudioVirtualizationMode virtualizationMode = AudioVirtualizationMode.FadeOut;
        public float priorityThreshold = 0.5f;
        public float distanceFactor = 1.0f;
        public bool enableVoiceStealing = true;
        public float cpuUsageLimit = 0.8f;

        private List<AudioSource> activeVoices = new List<AudioSource>();
        private List<AudioSource> virtualVoices = new List<AudioSource>();
        private int maxRealVoices;
        private int maxVirtualVoices;

        public void InitializeVoicePool()
        {
            maxRealVoices = FindObjectsByType<AudioSource>(FindObjectsSortMode.None).Count(source => source.isPlaying);
            maxVirtualVoices = maxRealVoices;
        }

        void Update()
        {
            ManageVoiceVirtualization();
        }

        private void ManageVoiceVirtualization()
        {
            if (activeVoices.Count <= maxRealVoices) return;

            // Sort by priority and distance
            activeVoices.Sort((a, b) => {
                float priorityA = a.priority + (Vector3.Distance(a.transform.position, Camera.main.transform.position) * distanceFactor);
                float priorityB = b.priority + (Vector3.Distance(b.transform.position, Camera.main.transform.position) * distanceFactor);
                return priorityA.CompareTo(priorityB);
            });

            // Virtualize lowest priority voices
            for (int i = maxRealVoices; i < activeVoices.Count; i++)
            {
                VirtualizeVoice(activeVoices[i]);
            }
        }

        private void VirtualizeVoice(AudioSource voice)
        {
            switch (virtualizationMode)
            {
                case AudioVirtualizationMode.FadeOut:
                    StartCoroutine(FadeOutVoice(voice));
                    break;
                case AudioVirtualizationMode.Stop:
                    voice.Stop();
                    break;
                case AudioVirtualizationMode.Pause:
                    voice.Pause();
                    break;
            }

            activeVoices.Remove(voice);
            virtualVoices.Add(voice);
        }

        private System.Collections.IEnumerator FadeOutVoice(AudioSource voice)
        {
            float startVolume = voice.volume;
            float fadeTime = 0.1f;
            float elapsed = 0f;

            while (elapsed < fadeTime)
            {
                voice.volume = Mathf.Lerp(startVolume, 0f, elapsed / fadeTime);
                elapsed += Time.deltaTime;
                yield return null;
            }

            voice.Stop();
            voice.volume = startVolume;
        }

        public void OnAudioConfigurationChanged(bool deviceWasChanged)
        {
            // Handle audio configuration changes
            Debug.Log($"Audio configuration changed: Device was changed: {deviceWasChanged}");
        }
    }

    public class AdaptiveAudioSystem : MonoBehaviour
    {
        public List<EventTrigger> eventTriggers = new List<EventTrigger>();
        public List<AudioLayer> audioLayers = new List<AudioLayer>();
        public TransitionSettings transitionSettings = new TransitionSettings();
        public bool enableDynamicMixing = true;
        public ResponseCurve responseCurve = ResponseCurve.Smooth;
        public float updateFrequency = 0.1f;
        public AudioMixer targetMixer;

        private Dictionary<string, AudioSource> layerSources = new Dictionary<string, AudioSource>();
        private Dictionary<string, float> layerTargetVolumes = new Dictionary<string, float>();
        private Dictionary<string, bool> layerStates = new Dictionary<string, bool>();
        private float lastUpdateTime;
        private bool isInitialized = false;

        public bool Initialize()
        {
            try
            {
                bool success = CreateAudioLayers();
                if (success)
                {
                    RegisterEventTriggers();
                    InitializeLayerStates();
                    isInitialized = true;
                    lastUpdateTime = Time.time;
                    
                    Debug.Log($"[AdaptiveAudioSystem] Initialized '{gameObject.name}' with {layerSources.Count} audio layers.");
                    return true;
                }
                return false;
            }
            catch (Exception e)
            {
                Debug.LogError($"[AdaptiveAudioSystem] Initialization failed: {e.Message}");
                return false;
            }
        }

        private bool CreateAudioLayers()
        {
            int successCount = 0;
            
            foreach (var layer in audioLayers)
            {
                try
                {
                    if (string.IsNullOrEmpty(layer.layerName))
                    {
                        Debug.LogWarning("[AdaptiveAudioSystem] Skipping layer with empty name.");
                        continue;
                    }

                    GameObject layerGO = new GameObject($"AudioLayer_{layer.layerName}");
                    layerGO.transform.SetParent(transform);
                    
                    AudioSource source = layerGO.AddComponent<AudioSource>();
                    
                    // Load audio clip if path is provided
                    if (!string.IsNullOrEmpty(layer.audioClipPath))
                    {
                        AudioClip clip = AssetDatabase.LoadAssetAtPath<AudioClip>(layer.audioClipPath);
                        if (clip != null)
                        {
                            source.clip = clip;
                        }
                        else
                        {
                            Debug.LogWarning($"[AdaptiveAudioSystem] Could not load audio clip at path: {layer.audioClipPath}");
                        }
                    }

                    // Configure AudioSource
                    source.volume = Mathf.Clamp01(layer.volume);
                    source.pitch = Mathf.Clamp(layer.pitch, 0.1f, 3.0f);
                    source.loop = layer.loop;
                    source.playOnAwake = false;
                    source.spatialBlend = 0f; // 2D audio for adaptive system

                    // Route to mixer if available
                    if (targetMixer != null)
                    {
                        var mixerGroups = targetMixer.FindMatchingGroups("Master");
                        if (mixerGroups.Length > 0)
                        {
                            source.outputAudioMixerGroup = mixerGroups[0];
                        }
                    }

                    layerSources[layer.layerName] = source;
                    layerTargetVolumes[layer.layerName] = layer.volume;
                    successCount++;
                }
                catch (Exception e)
                {
                    Debug.LogError($"[AdaptiveAudioSystem] Failed to create layer '{layer.layerName}': {e.Message}");
                }
            }

            return successCount > 0;
        }

        private void InitializeLayerStates()
        {
            foreach (var layer in audioLayers)
            {
                layerStates[layer.layerName] = false; // All layers start inactive
            }
        }

        private void RegisterEventTriggers()
        {
            foreach (var trigger in eventTriggers)
            {
                try
                {
                    if (string.IsNullOrEmpty(trigger.eventName))
                        continue;

                    // Create advanced Unity 6.2 event trigger component
                    GameObject triggerGO = new GameObject($"EventTrigger_{trigger.eventName}");
                    triggerGO.transform.SetParent(transform);
                    
                    var eventComponent = triggerGO.AddComponent<AdvancedEventTrigger>();
                    eventComponent.eventName = trigger.eventName;
                    eventComponent.triggerType = trigger.triggerType;
                    eventComponent.adaptiveSystem = this;

                    Debug.Log($"[AdaptiveAudioSystem] Registered event trigger: {trigger.eventName}");
                }
                catch (Exception e)
                {
                    Debug.LogWarning($"[AdaptiveAudioSystem] Failed to register trigger '{trigger.eventName}': {e.Message}");
                }
            }
        }

        void Update()
        {
            if (!isInitialized || !enableDynamicMixing)
                return;

            if (Time.time - lastUpdateTime >= updateFrequency)
            {
                UpdateAdaptiveSystem();
                lastUpdateTime = Time.time;
            }
        }

        private void UpdateAdaptiveSystem()
        {
            foreach (var kvp in layerSources)
            {
                UpdateLayerVolume(kvp.Key, kvp.Value);
                UpdateLayerPlayback(kvp.Key, kvp.Value);
            }
        }

        private void UpdateLayerVolume(string layerName, AudioSource source)
        {
            if (!layerTargetVolumes.ContainsKey(layerName))
                return;

            float targetVolume = layerTargetVolumes[layerName];
            float currentVolume = source.volume;
            
            // Apply response curve for smooth transitions
            float newVolume = ApplyResponseCurve(currentVolume, targetVolume);
            source.volume = newVolume;
        }

        private void UpdateLayerPlayback(string layerName, AudioSource source)
        {
            if (!layerStates.ContainsKey(layerName))
                return;

            bool shouldPlay = layerStates[layerName];
            
            if (shouldPlay && !source.isPlaying && source.clip != null)
            {
                source.Play();
            }
            else if (!shouldPlay && source.isPlaying)
            {
                StartCoroutine(FadeOutAndStop(source));
            }
        }

        private System.Collections.IEnumerator FadeOutAndStop(AudioSource source)
        {
            float startVolume = source.volume;
            float elapsed = 0f;
            
            while (elapsed < transitionSettings.fadeOutTime)
            {
                source.volume = Mathf.Lerp(startVolume, 0f, elapsed / transitionSettings.fadeOutTime);
                elapsed += Time.deltaTime;
                yield return null;
            }
            
            source.Stop();
            source.volume = startVolume;
        }

        private float ApplyResponseCurve(float current, float target)
        {
            float t = Time.deltaTime / updateFrequency;
            
            return responseCurve switch
            {
                ResponseCurve.Linear => Mathf.Lerp(current, target, t),
                ResponseCurve.Smooth => Mathf.SmoothStep(current, target, t),
                ResponseCurve.Exponential => Mathf.Lerp(current, target, 1f - Mathf.Exp(-t * 5f)),
                _ => Mathf.Lerp(current, target, t)
            };
        }

        // Public API for external control
        public void SetLayerVolume(string layerName, float volume)
        {
            if (layerTargetVolumes.ContainsKey(layerName))
            {
                layerTargetVolumes[layerName] = Mathf.Clamp01(volume);
            }
        }

        public void SetLayerState(string layerName, bool active)
        {
            if (layerStates.ContainsKey(layerName))
            {
                layerStates[layerName] = active;
            }
        }

        public void TriggerEvent(string eventName, Dictionary<string, object> parameters = null)
        {
            foreach (var trigger in eventTriggers)
            {
                if (trigger.eventName == eventName)
                {
                    ProcessEventTrigger(trigger, parameters);
                    break;
                }
            }
        }

        private void ProcessEventTrigger(EventTrigger trigger, Dictionary<string, object> parameters)
        {
            try
            {
                // Advanced Unity 6.2 event processing with full parameter support
                if (trigger.parameters != null && trigger.parameters.ContainsKey("target_layer"))
                {
                    string targetLayer = trigger.parameters["target_layer"].ToString();
                    
                    if (trigger.parameters.ContainsKey("action"))
                    {
                        string action = trigger.parameters["action"].ToString().ToLower();
                        
                        switch (action)
                        {
                            case "play":
                                SetLayerState(targetLayer, true);
                                break;
                            case "stop":
                                SetLayerState(targetLayer, false);
                                break;
                            case "volume":
                                if (trigger.parameters.ContainsKey("value"))
                                {
                                    float volume = Convert.ToSingle(trigger.parameters["value"]);
                                    SetLayerVolume(targetLayer, volume);
                                }
                                break;
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[AdaptiveAudioSystem] Error processing event trigger '{trigger.eventName}': {e.Message}");
            }
        }

        public Dictionary<string, object> GetSystemStatus()
        {
            return new Dictionary<string, object>
            {
                ["initialized"] = isInitialized,
                ["dynamicMixing"] = enableDynamicMixing,
                ["layerCount"] = layerSources.Count,
                ["eventTriggerCount"] = eventTriggers.Count,
                ["updateFrequency"] = updateFrequency,
                ["responseCurve"] = responseCurve.ToString(),
                ["mixerAssigned"] = targetMixer != null
            };
        }

        void OnDestroy()
        {
            // Clean up any resources if needed
            if (layerSources != null)
            {
                foreach (var source in layerSources.Values)
                {
                    if (source != null && source.isPlaying)
                    {
                        source.Stop();
                    }
                }
            }
        }
    }

    /// <summary>
    /// [UNITY 6.2] - Advanced event trigger component for audio systems
    /// </summary>
    public class AdvancedEventTrigger : MonoBehaviour
    {
        public string eventName;
        public string triggerType;
        public AdaptiveAudioSystem adaptiveSystem;

        void Start()
        {
            // Setup trigger based on triggerType
            switch (triggerType?.ToLower())
            {
                case "oncollisionenter":
                    // Add collision detection if needed
                    break;
                case "ontriggerenter":
                    // Add trigger detection if needed
                    gameObject.AddComponent<SphereCollider>().isTrigger = true;
                    break;
                default:
                    // Default behavior
                    break;
            }
        }

        void OnTriggerEnter(Collider other)
        {
            if (triggerType?.ToLower() == "ontriggerenter" && adaptiveSystem != null)
            {
                adaptiveSystem.TriggerEvent(eventName);
            }
        }
    }

    public class AudioMixerSnapshotBlender : MonoBehaviour
    {
        public AudioMixer targetMixer;
        public BlendMode blendMode = BlendMode.Weighted;
        public float defaultTransitionTime = 1.0f;
        public bool enableCrossfade = true;
        public InterpolationCurve interpolationCurve = InterpolationCurve.SmoothStep;
        public bool enablePrioritySystem = true;

        private Dictionary<string, AudioMixerSnapshot> snapshots = new Dictionary<string, AudioMixerSnapshot>();
        private Dictionary<string, float> snapshotWeights = new Dictionary<string, float>();

        public void SetupSnapshots(List<Dictionary<string, object>> snapshotConfigs, List<AudioMixerSnapshot> foundSnapshots)
        {
            for (int i = 0; i < snapshotConfigs.Count; i++)
            {
                string name = snapshotConfigs[i].GetValueOrDefault("name", "").ToString();
                float weight = Convert.ToSingle(snapshotConfigs[i].GetValueOrDefault("weight", 1.0f));
                
                snapshots[name] = foundSnapshots[i];
                snapshotWeights[name] = weight;
            }
        }

        public void BlendToSnapshot(string snapshotName, float transitionTime = -1f)
        {
            if (!snapshots.ContainsKey(snapshotName)) return;

            float actualTransitionTime = transitionTime >= 0 ? transitionTime : defaultTransitionTime;
            AudioMixerSnapshot snapshot = snapshots[snapshotName];
            
            if (enableCrossfade)
            {
                StartCoroutine(CrossfadeToSnapshot(snapshot, actualTransitionTime));
            }
            else
            {
                snapshot.TransitionTo(actualTransitionTime);
            }
        }

        private System.Collections.IEnumerator CrossfadeToSnapshot(AudioMixerSnapshot target, float time)
        {
            float elapsed = 0f;
            
            while (elapsed < time)
            {
                float t = elapsed / time;
                t = ApplyInterpolationCurve(t);
                
                // Apply crossfade logic here
                elapsed += Time.deltaTime;
                yield return null;
            }
            
            target.TransitionTo(0f);
        }

        private float ApplyInterpolationCurve(float t)
        {
            return interpolationCurve switch
            {
                InterpolationCurve.Linear => t,
                InterpolationCurve.SmoothStep => Mathf.SmoothStep(0f, 1f, t),
                InterpolationCurve.EaseInOut => Mathf.SmoothStep(0f, 1f, Mathf.SmoothStep(0f, 1f, t)),
                _ => t
            };
        }
    }

    public class AudioProfilingManager : MonoBehaviour
    {
        public bool enableProfiling = true;
        public ProfilingMode profilingMode = ProfilingMode.Detailed;
        public float sampleRate = 10.0f;
        public bool trackVoiceCount = true;
        public bool trackCpuUsage = true;
        public bool trackMemoryUsage = true;
        public bool trackDspLoad = true;
        public ExportFormat exportFormat = ExportFormat.JSON;
        public int maxSamples = 300;
        public bool autoExport = false;

        private List<AudioProfileSample> samples = new List<AudioProfileSample>();
        private float lastSampleTime;
        private bool isProfiling = false;

        // Unity 6.2 ProfilerRecorder instances
        private Unity.Profiling.ProfilerRecorder systemMemoryRecorder;
        private Unity.Profiling.ProfilerRecorder gcMemoryRecorder;
        private Unity.Profiling.ProfilerRecorder mainThreadRecorder;
        private Unity.Profiling.ProfilerRecorder audioUpdateRecorder;

        public void InitializeProfilers()
        {
            try
            {
                if (trackMemoryUsage)
                {
                    systemMemoryRecorder = Unity.Profiling.ProfilerRecorder.StartNew(
                        Unity.Profiling.ProfilerCategory.Memory, "System Used Memory");
                    gcMemoryRecorder = Unity.Profiling.ProfilerRecorder.StartNew(
                        Unity.Profiling.ProfilerCategory.Memory, "GC Reserved Memory");
                }

                if (trackCpuUsage)
                {
                    mainThreadRecorder = Unity.Profiling.ProfilerRecorder.StartNew(
                        Unity.Profiling.ProfilerCategory.Internal, "Main Thread", 15);
                    audioUpdateRecorder = Unity.Profiling.ProfilerRecorder.StartNew(
                        Unity.Profiling.ProfilerCategory.Audio, "Audio.Update");
                }
            }
            catch (Exception e)
            {
                Debug.LogWarning($"[AudioProfilingManager] Failed to initialize some profilers: {e.Message}");
            }
        }

        public void StartProfiling()
        {
            if (!enableProfiling) return;
            
            isProfiling = true;
            samples.Clear();
            lastSampleTime = Time.time;
            
            if (systemMemoryRecorder.Valid) systemMemoryRecorder.Start();
            if (gcMemoryRecorder.Valid) gcMemoryRecorder.Start();
            if (mainThreadRecorder.Valid) mainThreadRecorder.Start();
            if (audioUpdateRecorder.Valid) audioUpdateRecorder.Start();
        }

        public void StopProfiling()
        {
            isProfiling = false;
            
            if (systemMemoryRecorder.Valid) systemMemoryRecorder.Stop();
            if (gcMemoryRecorder.Valid) gcMemoryRecorder.Stop();
            if (mainThreadRecorder.Valid) mainThreadRecorder.Stop();
            if (audioUpdateRecorder.Valid) audioUpdateRecorder.Stop();
            
            if (autoExport || samples.Count > 0)
            {
                ExportProfilingData();
            }
        }

        void Update()
        {
            if (!isProfiling || !enableProfiling) return;

            if (Time.time - lastSampleTime >= 1f / sampleRate)
            {
                CollectSample();
                lastSampleTime = Time.time;
                
                // Auto-export if buffer is full
                if (samples.Count >= maxSamples && autoExport)
                {
                    ExportProfilingData();
                    samples.Clear();
                }
            }
        }

        private void CollectSample()
        {
            var sample = new AudioProfileSample
            {
                timestamp = Time.time,
                voiceCount = trackVoiceCount ? GetActiveVoiceCount() : 0,
                cpuUsage = trackCpuUsage ? GetAudioCPUUsage() : 0f,
                memoryUsage = trackMemoryUsage ? GetAudioMemoryUsage() : 0f,
                dspLoad = trackDspLoad ? GetDSPLoad() : 0f
            };

            samples.Add(sample);
            
            // Remove old samples if over limit
            if (samples.Count > maxSamples)
            {
                samples.RemoveAt(0);
            }
        }

        private int GetActiveVoiceCount()
        {
            return UnityEngine.Object.FindObjectsByType<AudioSource>(FindObjectsSortMode.None)
                .Count(source => source.isPlaying);
        }

        private float GetAudioCPUUsage()
        {
            if (audioUpdateRecorder.Valid && audioUpdateRecorder.IsRunning)
            {
                return (float)(audioUpdateRecorder.LastValueAsDouble * 1e-6); // Convert nanoseconds to milliseconds
            }
            
            if (mainThreadRecorder.Valid && mainThreadRecorder.IsRunning)
            {
                return (float)(mainThreadRecorder.LastValueAsDouble * 1e-6); // Convert nanoseconds to milliseconds
            }
            
            return 0f;
        }

        private float GetAudioMemoryUsage()
        {
            if (systemMemoryRecorder.Valid && systemMemoryRecorder.IsRunning)
            {
                return systemMemoryRecorder.LastValue / (1024f * 1024f); // Convert to MB
            }
            
            // Fallback to Profiler API
            return Profiler.GetTotalAllocatedMemoryLong() / (1024f * 1024f);
        }

        private float GetDSPLoad()
        {
            var config = AudioSettings.GetConfiguration();
            float latencyMs = (float)config.dspBufferSize / config.sampleRate * 1000f;
            
            // Estimate DSP load based on active voices and buffer settings
            int activeVoices = GetActiveVoiceCount();
            float estimatedLoad = (float)activeVoices / config.numRealVoices * 100f;
            
            return Mathf.Min(estimatedLoad, 100f);
        }

        public AudioProfileSample GetCurrentStats()
        {
            return new AudioProfileSample
            {
                timestamp = Time.time,
                voiceCount = trackVoiceCount ? GetActiveVoiceCount() : 0,
                cpuUsage = trackCpuUsage ? GetAudioCPUUsage() : 0f,
                memoryUsage = trackMemoryUsage ? GetAudioMemoryUsage() : 0f,
                dspLoad = trackDspLoad ? GetDSPLoad() : 0f
            };
        }

        private void ExportProfilingData()
        {
            try
            {
                string fileName = $"AudioProfiling_{DateTime.Now:yyyyMMdd_HHmmss}.{exportFormat.ToString().ToLower()}";
                string path = Path.Combine(Application.persistentDataPath, fileName);
                
                var exportData = new
                {
                    exportTime = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    configuration = new
                    {
                        sampleRate = sampleRate,
                        profilingMode = profilingMode.ToString(),
                        trackingSettings = new
                        {
                            voiceCount = trackVoiceCount,
                            cpuUsage = trackCpuUsage,
                            memoryUsage = trackMemoryUsage,
                            dspLoad = trackDspLoad
                        }
                    },
                    samples = samples,
                    summary = new
                    {
                        totalSamples = samples.Count,
                        avgCpuUsage = samples.Count > 0 ? samples.Average(s => s.cpuUsage) : 0f,
                        maxMemoryUsage = samples.Count > 0 ? samples.Max(s => s.memoryUsage) : 0f,
                        avgVoiceCount = samples.Count > 0 ? samples.Average(s => s.voiceCount) : 0f
                    }
                };
                
                string jsonData = EditorJsonUtility.ToJson(exportData, true);
                File.WriteAllText(path, jsonData);
                Debug.Log($"[AudioProfilingManager] Audio profiling data exported to: {path}");
            }
            catch (Exception e)
            {
                Debug.LogError($"[AudioProfilingManager] Failed to export profiling data: {e.Message}");
            }
        }

        void OnDestroy()
        {
            // Clean up ProfilerRecorders
            if (systemMemoryRecorder.Valid) systemMemoryRecorder.Dispose();
            if (gcMemoryRecorder.Valid) gcMemoryRecorder.Dispose();
            if (mainThreadRecorder.Valid) mainThreadRecorder.Dispose();
            if (audioUpdateRecorder.Valid) audioUpdateRecorder.Dispose();
        }
    }

    public class AudioFormatConverter : MonoBehaviour
    {
        public AudioFormat sourceFormat = AudioFormat.WAV;
        public AudioFormat targetFormat = AudioFormat.OGG;
        public ConversionQuality conversionQuality = ConversionQuality.High;
        public bool enableRuntimeConversion = true;
        public bool streamingSupport = true;
        public bool cacheConvertedAudio = true;
        public bool batchConversion = false;
        public CompressionSettings compressionSettings = new CompressionSettings();

        private Dictionary<string, AudioClip> conversionCache = new Dictionary<string, AudioClip>();

        public void Initialize()
        {
            if (cacheConvertedAudio)
            {
                LoadConversionCache();
            }
        }

        public AudioClip ConvertAudioClip(AudioClip sourceClip)
        {
            if (sourceClip == null) return null;

            string cacheKey = $"{sourceClip.name}_{sourceFormat}_{targetFormat}_{conversionQuality}";
            
            if (cacheConvertedAudio && conversionCache.ContainsKey(cacheKey))
            {
                return conversionCache[cacheKey];
            }

            AudioClip convertedClip = PerformConversion(sourceClip);
            
            if (cacheConvertedAudio && convertedClip != null)
            {
                conversionCache[cacheKey] = convertedClip;
            }

            return convertedClip;
        }

        /// <summary>
        /// [UNITY 6.2] - Implementação real de conversão de áudio usando Unity AudioClip.Create API.
        /// </summary>
        private AudioClip PerformConversion(AudioClip sourceClip)
        {
            try
            {
                // Extrair dados do clip original usando Unity 6.2 APIs
                float[] originalSamples = new float[sourceClip.samples * sourceClip.channels];
                if (!sourceClip.GetData(originalSamples, 0))
                {
                    Debug.LogError("[AdvancedAudio] Failed to extract audio data from source clip");
                    return sourceClip;
                }

                // Aplicar processamento de conversão
                float[] processedSamples = ProcessAudioSamples(originalSamples, sourceClip.channels);

                // Criar novo AudioClip usando Unity 6.2 AudioClip.Create
                string convertedName = $"{sourceClip.name}_Converted";
                AudioClip convertedClip = AudioClip.Create(
                    convertedName,
                    processedSamples.Length / sourceClip.channels,
                    sourceClip.channels,
                    sourceClip.frequency,
                    false // stream = false para clips pequenos
                );

                // Definir os dados processados no novo clip
                if (!convertedClip.SetData(processedSamples, 0))
                {
                    Debug.LogError("[AdvancedAudio] Failed to set audio data on converted clip");
                    return sourceClip;
                }

                Debug.Log($"[AdvancedAudio] Successfully converted audio clip: {sourceClip.name} -> {convertedName}");
                return convertedClip;
            }
            catch (Exception e)
            {
                Debug.LogError($"[AdvancedAudio] Audio conversion failed: {e.Message}");
                return sourceClip; // Retornar clip original em caso de erro
            }
        }

        private float[] ApplyQualityReduction(float[] samples)
        {
            float qualityFactor = conversionQuality switch
            {
                ConversionQuality.Low => 0.3f,
                ConversionQuality.Medium => 0.6f,
                ConversionQuality.High => 0.9f,
                ConversionQuality.Lossless => 1.0f,
                _ => 0.9f
            };

            // Apply advanced Unity 6.2 audio processing with quality preservation
            for (int i = 0; i < samples.Length; i++)
            {
                samples[i] *= qualityFactor;
            }

            return samples;
        }

        private void LoadConversionCache()
        {
            // Load previously cached conversions
            string cachePath = Path.Combine(Application.persistentDataPath, "AudioConversionCache");
            if (Directory.Exists(cachePath))
            {
                // Load cached audio files
                Debug.Log("Loading audio conversion cache...");
            }
        }
    }

    [System.Serializable]
    public class AudioProfileSample
    {
        public float timestamp;
        public int voiceCount;
        public float cpuUsage;
        public float memoryUsage;
        public float dspLoad;
    }

        /// <summary>
        /// [UNITY 6.2] - Processa amostras de áudio aplicando filtros e efeitos.
        /// </summary>
        private float[] ProcessAudioSamples(float[] originalSamples, int channels)
        {
            try
            {
                float[] processedSamples = new float[originalSamples.Length];

                // Aplicar normalização
                float maxAmplitude = 0f;
                for (int i = 0; i < originalSamples.Length; i++)
                {
                    maxAmplitude = Mathf.Max(maxAmplitude, Mathf.Abs(originalSamples[i]));
                }

                float normalizationFactor = maxAmplitude > 0f ? 1f / maxAmplitude : 1f;

                // Processar amostras com normalização e filtros
                for (int i = 0; i < originalSamples.Length; i++)
                {
                    float sample = originalSamples[i] * normalizationFactor;

                    // Aplicar filtro de redução de qualidade baseado na configuração
                    sample = ApplyQualityReduction(new float[] { sample })[0];

                    // Aplicar limitação para evitar clipping
                    sample = Mathf.Clamp(sample, -1f, 1f);

                    processedSamples[i] = sample;
                }

                Debug.Log($"[AdvancedAudio] Processed {processedSamples.Length} audio samples with {channels} channels");
                return processedSamples;
            }
            catch (Exception e)
            {
                Debug.LogError($"[AdvancedAudio] Failed to process audio samples: {e.Message}");
                return originalSamples; // Retornar amostras originais em caso de erro
            }
        }

    #endregion
}