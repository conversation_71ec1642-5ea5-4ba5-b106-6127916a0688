"""
Advanced AI Runtime tools for Unity Inference Engine integration.
Provides comprehensive AI model loading, optimization, and inference capabilities.
"""

from mcp.server.fastmcp import FastMC<PERSON>, Context
from typing import Dict, Any, List, Optional, Union
from unity_connection import get_unity_connection
import json

def register_advanced_ai_tools(mcp: FastMCP):
    """Register all Advanced AI tools with the MCP server."""

    @mcp.tool()
    def advanced_ai_system(
        ctx: Context,
        action: str,
        system_name: Optional[str] = None,
        behavior_type: Optional[str] = None,
        agent_count: Optional[int] = None,
        spawn_radius: Optional[float] = None,
        target_position: Optional[List[float]] = None,
        formation_type: Optional[str] = None,
        ai_difficulty: Optional[str] = None,
        enable_learning: Optional[bool] = None,
        pathfinding_algorithm: Optional[str] = None,
        obstacle_avoidance: Optional[bool] = None,
        sensor_range: Optional[float] = None,
        decision_tree_config: Optional[Dict[str, Any]] = None,
        state_machine_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Sistema avançado de IA com NavMesh, comportamentos e simulação de multidão.

        Funcionalidades incluem:
        - setup_navmesh_runtime: Configurar NavMesh em runtime
        - configure_ai_agents: Configurar agentes de IA
        - create_behavior_system: Criar sistema de comportamento
        - setup_pathfinding: Configurar pathfinding
        - optimize_navigation: Otimizar navegação
        - setup_crowd_simulation: Configurar simulação de multidão
        - configure_obstacle_avoidance: Configurar desvio de obstáculos
        - create_decision_tree: Criar árvore de decisão
        - setup_state_machine: Configurar máquina de estados
        - configure_sensor_system: Configurar sistema de sensores
        - setup_formation_system: Configurar sistema de formação

        Args:
            action: Ação a executar
            system_name: Nome do sistema
            behavior_type: Tipo de comportamento (state_machine, behavior_tree, utility)
            agent_count: Número de agentes para simulação
            spawn_radius: Raio de spawn dos agentes
            target_position: Posição alvo [x, y, z]
            formation_type: Tipo de formação (line, circle, wedge, column)
            ai_difficulty: Dificuldade da IA (easy, medium, hard, adaptive)
            enable_learning: Habilitar aprendizado de máquina
            pathfinding_algorithm: Algoritmo de pathfinding (astar, dijkstra, jps)
            obstacle_avoidance: Habilitar desvio de obstáculos
            sensor_range: Alcance dos sensores
            decision_tree_config: Configuração da árvore de decisão
            state_machine_config: Configuração da máquina de estados

        Returns:
            Dictionary com resultados da operação
        """
        try:
            params = {
                "action": action,
                "system_name": system_name,
                "behavior_type": behavior_type,
                "agent_count": agent_count,
                "spawn_radius": spawn_radius,
                "target_position": target_position,
                "formation_type": formation_type,
                "ai_difficulty": ai_difficulty,
                "enable_learning": enable_learning,
                "pathfinding_algorithm": pathfinding_algorithm,
                "obstacle_avoidance": obstacle_avoidance,
                "sensor_range": sensor_range,
                "decision_tree_config": decision_tree_config,
                "state_machine_config": state_machine_config
            }
            params = {k: v for k, v in params.items() if v is not None}

            response = get_unity_connection().send_command("advanced_ai", params)

            if response.get("success"):
                return {
                    "success": True,
                    "message": response.get("message", "Advanced AI operation completed successfully."),
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False,
                    "message": response.get("error", "Failed to execute advanced AI operation.")
                }

        except Exception as e:
            return {"success": False, "message": f"Python error in advanced AI: {str(e)}"}
    
    @mcp.tool()
    def setup_inference_runtime(
        ctx: Context,
        action: str = "setup",
        model_path: Optional[str] = None,
        backend_type: str = "GPUCompute",
        worker_type: str = "ComputePrecompiled",
        model_format: str = "ONNX",
        optimization_level: str = "O2",
        memory_pool_size: int = 1024,
        enable_profiling: bool = False,
        batch_size: int = 1
    ) -> Dict[str, Any]:
        """Setup and manage Unity Inference Engine Runtime.

        Handles Advanced AI Runtime operations using Unity Inference Engine (Unity 6.2+).
        Supports model loading, configuration, testing, and optimization.

        Args:
            action: Operation to perform ('setup', 'load_model', 'configure', 'test', 'optimize')
            model_path: Path to the ONNX model file
            backend_type: Backend type ('GPUCompute', 'CPU', 'GPUPixel')
            worker_type: Worker type for inference execution
            model_format: Model format (default: 'ONNX')
            optimization_level: Optimization level ('O1', 'O2', 'O3')
            memory_pool_size: Memory pool size in MB
            enable_profiling: Enable performance profiling
            batch_size: Batch size for inference

        Returns:
            Dictionary with operation results including system compatibility and performance metrics
        """
        try:
            params = {
                "commandType": "setup_inference_runtime",
                "action": action,
                "model_path": model_path,
                "backend_type": backend_type,
                "worker_type": worker_type,
                "model_format": model_format,
                "optimization_level": optimization_level,
                "memory_pool_size": memory_pool_size,
                "enable_profiling": enable_profiling,
                "batch_size": batch_size
            }
            
            # Remove None values to let C# apply defaults
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("advanced_ai", params)
            
            if response.get("success"):
                return {
                    "success": True,
                    "message": response.get("message", "Inference runtime operation completed successfully."),
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False,
                    "message": response.get("error", "Failed to execute inference runtime operation.")
                }
                
        except Exception as e:
            return {"success": False, "message": f"Python error: {str(e)}"}

    @mcp.tool()
    def optimize_inference(
        ctx: Context,
        action: str,
        model_name: Optional[str] = None,
        optimization_type: str = "speed",
        quantization_mode: str = "dynamic",
        precision: str = "fp16",
        enable_tensorrt: bool = False,
        enable_onnx_runtime: bool = True,
        cache_compiled_models: bool = True,
        async_execution: bool = True,
        thread_count: int = 4
    ) -> Dict[str, Any]:
        """Optimize inference performance with advanced techniques.

        Provides comprehensive inference optimization including quantization, backend switching,
        and performance profiling with real Unity Inference Engine integration.

        Args:
            action: Optimization action ('optimize', 'benchmark', 'profile', 'cache')
            model_name: Name of the loaded model to optimize
            optimization_type: Type of optimization ('speed', 'memory', 'balanced')
            quantization_mode: Quantization mode ('dynamic', 'static')
            precision: Target precision ('fp16', 'int8', 'int16')
            enable_tensorrt: Enable TensorRT optimization (if available)
            enable_onnx_runtime: Enable ONNX Runtime optimizations
            cache_compiled_models: Enable model compilation caching
            async_execution: Enable asynchronous execution
            thread_count: Number of threads for CPU backend

        Returns:
            Dictionary with optimization results and performance metrics
        """
        try:
            params = {
                "commandType": "optimize_inference",
                "action": action,
                "model_name": model_name,
                "optimization_type": optimization_type,
                "quantization_mode": quantization_mode,
                "precision": precision,
                "enable_tensorrt": enable_tensorrt,
                "enable_onnx_runtime": enable_onnx_runtime,
                "cache_compiled_models": cache_compiled_models,
                "async_execution": async_execution,
                "thread_count": thread_count
            }
            
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("advanced_ai", params)
            
            if response.get("success"):
                return {
                    "success": True,
                    "message": response.get("message", "Inference optimization completed successfully."),
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False,
                    "message": response.get("error", "Failed to optimize inference.")
                }
                
        except Exception as e:
            return {"success": False, "message": f"Python error: {str(e)}"}

    @mcp.tool()
    def setup_frame_slicing(
        ctx: Context,
        action: str,
        model_name: Optional[str] = None,
        max_frame_time: float = 16.67,
        slice_size: int = 100,
        priority_system: bool = True,
        adaptive_slicing: bool = True,
        frame_budget_ms: float = 5.0,
        queue_management: str = "priority",
        fallback_strategy: str = "skip_frame"
    ) -> Dict[str, Any]:
        """Setup frame slicing for real-time AI inference.

        Implements frame slicing using Unity Inference Engine's ScheduleIterable() for
        maintaining consistent frame rates while running AI inference.

        Args:
            action: Frame slicing action ('setup', 'configure', 'start', 'stop', 'monitor')
            model_name: Name of the model for frame slicing
            max_frame_time: Maximum frame time in milliseconds
            slice_size: Number of operations per slice
            priority_system: Enable priority-based slicing
            adaptive_slicing: Enable adaptive slice size adjustment
            frame_budget_ms: AI frame budget in milliseconds
            queue_management: Queue management strategy ('priority', 'fifo', 'lifo')
            fallback_strategy: Fallback when budget exceeded ('skip_frame', 'reduce_quality')

        Returns:
            Dictionary with frame slicing configuration and performance data
        """
        try:
            params = {
                "commandType": "setup_frame_slicing",
                "action": action,
                "model_name": model_name,
                "max_frame_time": max_frame_time,
                "slice_size": slice_size,
                "priority_system": priority_system,
                "adaptive_slicing": adaptive_slicing,
                "frame_budget_ms": frame_budget_ms,
                "queue_management": queue_management,
                "fallback_strategy": fallback_strategy
            }
            
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("advanced_ai", params)
            
            if response.get("success"):
                return {
                    "success": True,
                    "message": response.get("message", "Frame slicing operation completed successfully."),
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False,
                    "message": response.get("error", "Failed to setup frame slicing.")
                }
                
        except Exception as e:
            return {"success": False, "message": f"Python error: {str(e)}"}

    @mcp.tool()
    def configure_ai_quantization(
        ctx: Context,
        action: str,
        model_path: Optional[str] = None,
        quantization_type: str = "dynamic",
        target_precision: str = "int8",
        calibration_dataset: Optional[str] = None,
        optimization_level: str = "balanced",
        preserve_accuracy: bool = True,
        quantize_weights: bool = True,
        quantize_activations: bool = True,
        custom_quantization_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Configure AI model quantization for size and speed optimization.

        Implements model quantization using Unity Inference Engine's ModelQuantizer API
        with support for various precision levels and accuracy preservation.

        Args:
            action: Quantization action ('quantize', 'calibrate', 'validate', 'export')
            model_path: Path to the model file for quantization
            quantization_type: Type of quantization ('dynamic', 'static')
            target_precision: Target precision ('int8', 'int16', 'fp16', 'fp32')
            calibration_dataset: Dataset for quantization calibration
            optimization_level: Optimization level ('balanced', 'aggressive', 'conservative')
            preserve_accuracy: Preserve model accuracy during quantization
            quantize_weights: Apply quantization to model weights
            quantize_activations: Apply quantization to activations
            custom_quantization_config: Custom quantization configuration

        Returns:
            Dictionary with quantization results and performance analysis
        """
        try:
            params = {
                "commandType": "configure_ai_quantization",
                "action": action,
                "model_path": model_path,
                "quantization_type": quantization_type,
                "target_precision": target_precision,
                "calibration_dataset": calibration_dataset,
                "optimization_level": optimization_level,
                "preserve_accuracy": preserve_accuracy,
                "quantize_weights": quantize_weights,
                "quantize_activations": quantize_activations
            }
            
            if custom_quantization_config:
                params.update(custom_quantization_config)
            
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("advanced_ai", params)
            
            if response.get("success"):
                return {
                    "success": True,
                    "message": response.get("message", "AI quantization operation completed successfully."),
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False,
                    "message": response.get("error", "Failed to configure AI quantization.")
                }
                
        except Exception as e:
            return {"success": False, "message": f"Python error: {str(e)}"}

    @mcp.tool()
    def setup_custom_backend_dispatching(
        ctx: Context,
        action: str,
        backend_priority: Optional[List[str]] = None,
        fallback_strategy: str = "auto",
        device_detection: bool = True,
        performance_monitoring: bool = True,
        dynamic_switching: bool = True,
        custom_backends: Optional[Dict[str, Any]] = None,
        load_balancing: bool = False,
        backend_specific_optimizations: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Setup custom backend dispatching for optimal AI performance.

        Implements intelligent backend selection and switching based on system capabilities
        and performance metrics with real Unity system information.

        Args:
            action: Backend action ('setup', 'configure', 'test', 'monitor', 'switch')
            backend_priority: Priority list of backends (['GPUCompute', 'CPU', 'GPUPixel'])
            fallback_strategy: Fallback strategy when primary backend fails
            device_detection: Enable automatic device capability detection
            performance_monitoring: Enable backend performance monitoring
            dynamic_switching: Enable dynamic backend switching
            custom_backends: Custom backend configurations
            load_balancing: Enable load balancing across backends
            backend_specific_optimizations: Backend-specific optimization settings

        Returns:
            Dictionary with backend configuration and performance analysis
        """
        try:
            params = {
                "commandType": "setup_custom_backend_dispatching",
                "action": action,
                "backend_priority": backend_priority or ["GPUCompute", "CPU"],
                "fallback_strategy": fallback_strategy,
                "device_detection": device_detection,
                "performance_monitoring": performance_monitoring,
                "dynamic_switching": dynamic_switching,
                "load_balancing": load_balancing
            }
            
            if custom_backends:
                params["custom_backends"] = custom_backends
            
            if backend_specific_optimizations:
                params.update(backend_specific_optimizations)
            
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("advanced_ai", params)
            
            if response.get("success"):
                return {
                    "success": True,
                    "message": response.get("message", "Backend dispatching operation completed successfully."),
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False,
                    "message": response.get("error", "Failed to setup custom backend dispatching.")
                }
                
        except Exception as e:
            return {"success": False, "message": f"Python error: {str(e)}"}

    @mcp.tool()
    def create_natural_language_processing(
        ctx: Context,
        action: str,
        nlp_model_type: str = "transformer",
        model_path: Optional[str] = None,
        language: str = "en",
        max_sequence_length: int = 512,
        vocabulary_size: int = 50000,
        embedding_dimension: int = 768,
        attention_heads: int = 12,
        enable_tokenization: bool = True,
        preprocessing_pipeline: Optional[List[str]] = None,
        # Support multiple text input parameter names for flexibility
        inputText: Optional[str] = None,
        input_text: Optional[str] = None,
        text: Optional[str] = None,
        content: Optional[str] = None
    ) -> Dict[str, Any]:
        """Create and manage Natural Language Processing with Unity Inference Engine.

        Implements comprehensive NLP capabilities including text classification, sentiment analysis,
        named entity recognition, and text generation using real Unity Inference Engine integration.

        Args:
            action: NLP action ('create', 'load', 'process', 'train', 'evaluate')
            nlp_model_type: Type of NLP model ('transformer', 'lstm', 'gru', 'bert')
            model_path: Path to the NLP model file
            language: Target language for processing ('en', 'pt', 'es', 'fr', etc.)
            max_sequence_length: Maximum sequence length for processing
            vocabulary_size: Size of the vocabulary
            embedding_dimension: Dimension of embeddings
            attention_heads: Number of attention heads (for transformer models)
            enable_tokenization: Enable text tokenization
            preprocessing_pipeline: List of preprocessing steps
            inputText: Input text for processing (supports multiple parameter names)
            input_text: Alternative parameter name for input text
            text: Alternative parameter name for input text
            content: Alternative parameter name for input text

        Returns:
            Dictionary with NLP processing results and analysis
        """
        try:
            # Support multiple text input parameter names (C# improvement)
            input_text_value = inputText or input_text or text or content
            
            params = {
                "commandType": "create_natural_language_processing",
                "action": action,
                "nlp_model_type": nlp_model_type,
                "model_path": model_path,
                "language": language,
                "max_sequence_length": max_sequence_length,
                "vocabulary_size": vocabulary_size,
                "embedding_dimension": embedding_dimension,
                "attention_heads": attention_heads,
                "enable_tokenization": enable_tokenization,
                "inputText": input_text_value,
                "input_text": input_text_value,
                "text": input_text_value,
                "content": input_text_value
            }
            
            if preprocessing_pipeline:
                params["preprocessing_pipeline"] = preprocessing_pipeline
            
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("advanced_ai", params)
            
            if response.get("success"):
                return {
                    "success": True,
                    "message": response.get("message", "NLP operation completed successfully."),
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False,
                    "message": response.get("error", "Failed to execute NLP operation.")
                }
                
        except Exception as e:
            return {"success": False, "message": f"Python error: {str(e)}"}

    @mcp.tool()
    def setup_object_recognition(
        ctx: Context,
        action: str,
        model_type: str = "yolo",
        model_path: Optional[str] = None,
        confidence_threshold: float = 0.5,
        nms_threshold: float = 0.4,
        max_detections: int = 100,
        input_resolution: Optional[List[int]] = None,
        class_labels: Optional[List[str]] = None,
        real_time_processing: bool = True,
        batch_processing: bool = False,
        input_image_path: Optional[str] = None
    ) -> Dict[str, Any]:
        """Setup object recognition and computer vision with Unity Inference Engine.

        Implements comprehensive object detection and recognition capabilities including
        YOLO, R-CNN, and SSD models with real-time processing support.

        Args:
            action: Object recognition action ('setup', 'load', 'detect', 'train', 'evaluate', 'optimize')
            model_type: Type of object detection model ('yolo', 'rcnn', 'ssd', 'faster_rcnn')
            model_path: Path to the object detection model file
            confidence_threshold: Minimum confidence for detections (0.0-1.0)
            nms_threshold: Non-maximum suppression threshold (0.0-1.0)
            max_detections: Maximum number of detections per image
            input_resolution: Input resolution [width, height] for the model
            class_labels: List of class labels for detection
            real_time_processing: Enable real-time processing optimization
            batch_processing: Enable batch processing for multiple images
            input_image_path: Path to input image for detection testing

        Returns:
            Dictionary with object recognition results and performance metrics
        """
        try:
            params = {
                "commandType": "setup_object_recognition",
                "action": action,
                "model_type": model_type,
                "model_path": model_path,
                "confidence_threshold": confidence_threshold,
                "nms_threshold": nms_threshold,
                "max_detections": max_detections,
                "input_resolution": input_resolution or [640, 640],
                "real_time_processing": real_time_processing,
                "batch_processing": batch_processing,
                "input_image_path": input_image_path
            }
            
            if class_labels:
                params["class_labels"] = class_labels
            
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("advanced_ai", params)
            
            if response.get("success"):
                return {
                    "success": True,
                    "message": response.get("message", "Object recognition operation completed successfully."),
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False,
                    "message": response.get("error", "Failed to setup object recognition.")
                }
                
        except Exception as e:
            return {"success": False, "message": f"Python error: {str(e)}"}

    @mcp.tool()
    def configure_sensor_data_classification(
        ctx: Context,
        action: str,
        sensor_types: Optional[List[str]] = None,
        model_path: Optional[str] = None,
        classification_type: str = "multiclass",
        feature_extraction: str = "auto",
        sampling_rate: float = 100.0,
        window_size: int = 128,
        overlap_ratio: float = 0.5,
        preprocessing_steps: Optional[List[str]] = None,
        real_time_classification: bool = True
    ) -> Dict[str, Any]:
        """Configure sensor data classification with Unity Input system integration.

        Implements comprehensive sensor data classification using Unity's Input system
        with support for accelerometer, gyroscope, and other sensors including desktop simulation.

        Args:
            action: Sensor classification action ('configure', 'load', 'classify', 'train', 'calibrate', 'monitor')
            sensor_types: List of sensor types (['accelerometer', 'gyroscope', 'compass'])
            model_path: Path to the sensor classification model
            classification_type: Type of classification ('multiclass', 'binary', 'regression')
            feature_extraction: Feature extraction method ('auto', 'manual', 'fft')
            sampling_rate: Sensor sampling rate in Hz
            window_size: Size of the data window for classification
            overlap_ratio: Overlap ratio between windows (0.0-1.0)
            preprocessing_steps: List of preprocessing steps
            real_time_classification: Enable real-time classification

        Returns:
            Dictionary with sensor classification results and system sensor status
        """
        try:
            params = {
                "commandType": "configure_sensor_data_classification",
                "action": action,
                "sensor_types": sensor_types or ["accelerometer", "gyroscope"],
                "model_path": model_path,
                "classification_type": classification_type,
                "feature_extraction": feature_extraction,
                "sampling_rate": sampling_rate,
                "window_size": window_size,
                "overlap_ratio": overlap_ratio,
                "real_time_classification": real_time_classification
            }
            
            if preprocessing_steps:
                params["preprocessing_steps"] = preprocessing_steps
            
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("advanced_ai", params)
            
            if response.get("success"):
                return {
                    "success": True,
                    "message": response.get("message", "Sensor data classification operation completed successfully."),
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False,
                    "message": response.get("error", "Failed to configure sensor data classification.")
                }
                
        except Exception as e:
            return {"success": False, "message": f"Python error: {str(e)}"}

    # Enhanced convenience functions with improved error handling and validation
    @mcp.tool()
    def advanced_ai_quick_setup(
        ctx: Context,
        ai_type: str = "general",
        model_path: Optional[str] = None,
        optimization_level: str = "balanced",
        enable_real_time: bool = True
    ) -> Dict[str, Any]:
        """Quick setup for Advanced AI with automatic configuration.
        
        Provides one-click setup for common AI scenarios with automatic model validation,
        backend selection, and optimization based on system capabilities.
        
        Args:
            ai_type: Type of AI to setup ('general', 'nlp', 'vision', 'sensor')
            model_path: Optional path to specific model (auto-selected if not provided)
            optimization_level: Optimization level ('performance', 'balanced', 'memory')
            enable_real_time: Enable real-time processing optimizations
            
        Returns:
            Dictionary with comprehensive setup results and recommendations
        """
        try:
            params = {
                "commandType": "quick_setup",
                "ai_type": ai_type,
                "model_path": model_path,
                "optimization_level": optimization_level,
                "enable_real_time": enable_real_time
            }
            
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("advanced_ai", params)
            
            if response.get("success"):
                return {
                    "success": True,
                    "message": response.get("message", "Advanced AI quick setup completed successfully."),
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False,
                    "message": response.get("error", "Failed to complete quick setup.")
                }
                
        except Exception as e:
            return {"success": False, "message": f"Python error: {str(e)}"}

    @mcp.tool()
    def advanced_ai_system_info(
        ctx: Context,
        include_performance_metrics: bool = True,
        include_compatibility_check: bool = True,
        include_recommendations: bool = True
    ) -> Dict[str, Any]:
        """Get comprehensive Advanced AI system information and compatibility.
        
        Provides detailed system analysis including hardware capabilities, software compatibility,
        performance metrics, and optimization recommendations.
        
        Args:
            include_performance_metrics: Include detailed performance metrics
            include_compatibility_check: Include compatibility analysis
            include_recommendations: Include optimization recommendations
            
        Returns:
            Dictionary with complete system analysis and recommendations
        """
        try:
            params = {
                "commandType": "system_info",
                "include_performance_metrics": include_performance_metrics,
                "include_compatibility_check": include_compatibility_check,
                "include_recommendations": include_recommendations
            }
            
            response = get_unity_connection().send_command("advanced_ai", params)
            
            if response.get("success"):
                return {
                    "success": True,
                    "message": response.get("message", "System information retrieved successfully."),
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False,
                    "message": response.get("error", "Failed to retrieve system information.")
                }
                
        except Exception as e:
            return {"success": False, "message": f"Python error: {str(e)}"}


# Legacy functions for backward compatibility
def setup_inference_runtime_legacy(
    action: str = "setup",
    model_path: Optional[str] = None,
    backend_type: str = "GPUCompute",
    worker_type: str = "ComputePrecompiled",
    model_format: str = "ONNX",
    optimization_level: str = "O2",
    memory_pool_size: int = 1024,
    enable_profiling: bool = False,
    batch_size: int = 1
) -> Dict[str, Any]:
    """Legacy function for backward compatibility."""
    try:
        params = {
            "commandType": "setup_inference_runtime",
            "action": action,
            "model_path": model_path,
            "backend_type": backend_type,
            "worker_type": worker_type,
            "model_format": model_format,
            "optimization_level": optimization_level,
            "memory_pool_size": memory_pool_size,
            "enable_profiling": enable_profiling,
            "batch_size": batch_size
        }
        
        params = {k: v for k, v in params.items() if v is not None}
        response = get_unity_connection().send_command("advanced_ai", params)
        
        if response.get("success"):
            return {
                "success": True,
                "message": response.get("message", "Legacy inference runtime setup completed."),
                "data": response.get("data")
            }
        else:
            return {
                "success": False,
                "message": response.get("error", "Legacy setup failed.")
            }
            
    except Exception as e:
        return {"success": False, "message": f"Legacy function error: {str(e)}"}


def optimize_inference_legacy(
    model_path: str,
    optimization_level: str = "O2",
    enable_frame_slicing: bool = True,
    max_frame_time: float = 16.67,
    enable_quantization: bool = False,
    quantization_type: str = "INT8",
    enable_caching: bool = True,
    cache_size: int = 512
) -> Dict[str, Any]:
    """Legacy optimization function for backward compatibility."""
    try:
        params = {
            "commandType": "optimize_inference",
            "action": "optimize",
            "model_path": model_path,
            "optimization_level": optimization_level,
            "enable_frame_slicing": enable_frame_slicing,
            "max_frame_time": max_frame_time,
            "enable_quantization": enable_quantization,
            "quantization_type": quantization_type,
            "enable_caching": enable_caching,
            "cache_size": cache_size
        }
        
        response = get_unity_connection().send_command("advanced_ai", params)
        
        if response.get("success"):
            return {
                "success": True,
                "message": response.get("message", "Legacy optimization completed."),
                "data": response.get("data")
            }
        else:
            return {
                "success": False,
                "message": response.get("error", "Legacy optimization failed.")
            }
            
    except Exception as e:
        return {"success": False, "message": f"Legacy function error: {str(e)}"}