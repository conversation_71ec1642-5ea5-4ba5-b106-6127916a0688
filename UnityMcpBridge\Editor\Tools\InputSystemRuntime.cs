using System;
using System.Collections.Generic;
using System.Linq;
using System.IO;
using System.Reflection;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.InputSystem.Controls;
using UnityEngine.InputSystem.Layouts;
using UnityEngine.InputSystem.LowLevel;
using UnityEngine.InputSystem.Processors;
using UnityEngine.InputSystem.Users;
using UnityEngine.InputSystem.Utilities;
using UnityEngine.InputSystem.Interactions;
using UnityEngine.InputSystem.Composites;
using UnityEngine.UI;
using UnityMcpBridge.Editor.Helpers;

#if UNITY_2023_2_OR_NEWER
using UnityEngine.InputSystem.EnhancedTouch;
#endif

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// Handles Input System Runtime operations with advanced Unity 6.2 features.
    /// </summary>
    public static class InputSystemRuntime
    {
        private static readonly List<string> ValidSetupActions = new List<string>
        {
            "setup", "configure", "reset", "get_settings"
        };

        private static readonly List<string> ValidActionMapActions = new List<string>
        {
            "create", "modify", "delete", "list", "enable", "disable"
        };

        private static readonly List<string> ValidProcessorActions = new List<string>
        {
            "add", "remove", "list", "configure", "reset"
        };

        private static readonly List<string> ValidDeviceActions = new List<string>
        {
            "setup", "add_device", "remove_device", "configure", "list_devices"
        };

        private static readonly List<string> ValidRebindingActions = new List<string>
        {
            "create_ui", "start_rebinding", "cancel_rebinding", "save_bindings", "load_bindings"
        };

        private static readonly List<string> ValidRoutingActions = new List<string>
        {
            "setup", "add_route", "remove_route", "modify_route", "list_routes"
        };

        private static readonly List<string> ValidHapticActions = new List<string>
        {
            "start_haptic", "stop_haptic", "pause_haptic", "resume_haptic", "set_pattern"
        };

        /// <summary>
        /// Configura o Input System em runtime com parâmetros avançados.
        /// </summary>
        public static object HandleSetupInputSystemRuntime(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidSetupActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidSetupActions);
                return Response.Error($"Unknown action: '{action}'. Valid actions are: {validActionsList}");
            }

            try
            {
                switch (action)
                {
                    case "setup":
                        return SetupInputSystem(@params);
                    case "configure":
                        return ConfigureInputSystem(@params);
                    case "reset":
                        return ResetInputSystem();
                    case "get_settings":
                        return GetInputSystemSettings();
                    default:
                        return Response.Error($"Unknown action: '{action}'");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[InputSystemRuntime] Setup action '{action}' failed: {e}");
                return Response.Error($"Internal error processing setup action '{action}': {e.Message}");
            }
        }

        private static object SetupInputSystem(JObject @params)
        {
            try
            {
                var settings = InputSystem.settings;
                if (settings == null)
                {
                    return Response.Error("Input System settings not available.");
                }

                // Configurar Input Action Asset se fornecido
                string assetPath = @params["input_action_asset_path"]?.ToString();
                if (!string.IsNullOrEmpty(assetPath))
                {
                    var asset = AssetDatabase.LoadAssetAtPath<InputActionAsset>(assetPath);
                    if (asset != null)
                    {
                        asset.Enable();
                    }
                    else
                    {
                        Debug.LogWarning($"Input Action Asset not found at path: {assetPath}");
                    }
                }

                // Configurar comportamento em background
                if (@params["enable_background_behavior"]?.ToObject<bool>() == true)
                {
                    settings.backgroundBehavior = InputSettings.BackgroundBehavior.ResetAndDisableNonBackgroundDevices;
                }

                // Configurar modo de atualização
                string updateMode = @params["update_mode"]?.ToString();
                if (!string.IsNullOrEmpty(updateMode))
                {
                    switch (updateMode.ToLower())
                    {
                        case "dynamic":
                        case "processeventsindynamicupdate":
                            settings.updateMode = InputSettings.UpdateMode.ProcessEventsInDynamicUpdate;
                            break;
                        case "fixed":
                        case "processeventsinfixedupdate":
                            settings.updateMode = InputSettings.UpdateMode.ProcessEventsInFixedUpdate;
                            break;
                        case "manual":
                        case "processeventsmanually":
                            settings.updateMode = InputSettings.UpdateMode.ProcessEventsManually;
                            break;
                        default:
                            Debug.LogWarning($"Unknown update mode: {updateMode}");
                            break;
                    }
                }

                // Configurar compensação de orientação
                if (@params["compensate_for_screen_orientation"]?.ToObject<bool>() != null)
                {
                    settings.compensateForScreenOrientation = @params["compensate_for_screen_orientation"].ToObject<bool>();
                }

                // Configurar deadzones padrão
                if (@params["default_deadzone_min"]?.ToObject<float>() != null)
                {
                    float deadZoneMin = @params["default_deadzone_min"].ToObject<float>();
                    if (deadZoneMin >= 0f && deadZoneMin <= 1f)
                    {
                        settings.defaultDeadzoneMin = deadZoneMin;
                    }
                    else
                    {
                        Debug.LogWarning("Default deadzone min must be between 0 and 1");
                    }
                }

                if (@params["default_deadzone_max"]?.ToObject<float>() != null)
                {
                    float deadZoneMax = @params["default_deadzone_max"].ToObject<float>();
                    if (deadZoneMax >= 0f && deadZoneMax <= 1f)
                    {
                        settings.defaultDeadzoneMax = deadZoneMax;
                    }
                    else
                    {
                        Debug.LogWarning("Default deadzone max must be between 0 and 1");
                    }
                }

                // Configurar pontos de pressão do botão
                if (@params["default_button_press_point"]?.ToObject<float>() != null)
                {
                    float pressPoint = @params["default_button_press_point"].ToObject<float>();
                    if (pressPoint >= 0f && pressPoint <= 1f)
                    {
                        settings.defaultButtonPressPoint = pressPoint;
                    }
                    else
                    {
                        Debug.LogWarning("Default button press point must be between 0 and 1");
                    }
                }

                // Configurar tempos de tap
                if (@params["default_tap_time"]?.ToObject<float>() != null)
                {
                    float tapTime = @params["default_tap_time"].ToObject<float>();
                    if (tapTime > 0f)
                    {
                        settings.defaultTapTime = tapTime;
                    }
                    else
                    {
                        Debug.LogWarning("Default tap time must be greater than 0");
                    }
                }

                if (@params["default_slow_tap_time"]?.ToObject<float>() != null)
                {
                    float slowTapTime = @params["default_slow_tap_time"].ToObject<float>();
                    if (slowTapTime > 0f)
                    {
                        settings.defaultSlowTapTime = slowTapTime;
                    }
                    else
                    {
                        Debug.LogWarning("Default slow tap time must be greater than 0");
                    }
                }

                if (@params["default_hold_time"]?.ToObject<float>() != null)
                {
                    float holdTime = @params["default_hold_time"].ToObject<float>();
                    if (holdTime > 0f)
                    {
                        settings.defaultHoldTime = holdTime;
                    }
                    else
                    {
                        Debug.LogWarning("Default hold time must be greater than 0");
                    }
                }

                // Configurar limites de eventos
                if (@params["max_event_bytes_per_update"]?.ToObject<int>() != null)
                {
                    int maxBytes = @params["max_event_bytes_per_update"].ToObject<int>();
                    if (maxBytes > 0)
                    {
                        settings.maxEventBytesPerUpdate = maxBytes;
                    }
                    else
                    {
                        Debug.LogWarning("Max event bytes per update must be greater than 0");
                    }
                }

                if (@params["max_queued_events"]?.ToObject<int>() != null)
                {
                    int maxEvents = @params["max_queued_events"].ToObject<int>();
                    if (maxEvents > 0)
                    {
                        settings.maxQueuedEventsPerUpdate = maxEvents;
                    }
                    else
                    {
                        Debug.LogWarning("Max queued events must be greater than 0");
                    }
                }

                // Configurar filtering de noise on current (se disponível)
                bool enableNoiseFiltering = @params["filter_noise_on_current"]?.ToObject<bool>() ?? false;
                if (enableNoiseFiltering)
                {
                    // Esta propriedade pode não estar disponível em todas as versões
                    try
                    {
                        var noiseFilterProperty = typeof(InputSettings).GetProperty("filterNoiseOnCurrent");
                        if (noiseFilterProperty != null)
                        {
                            noiseFilterProperty.SetValue(settings, true);
                        }
                        else
                        {
                            Debug.Log("filterNoiseOnCurrent property not available in this Unity version");
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogWarning($"Failed to set noise filtering: {ex.Message}");
                    }
                }

                EditorUtility.SetDirty(settings);
                AssetDatabase.SaveAssets();

                return Response.Success("Input System configurado com sucesso.", new
                {
                    updateMode = settings.updateMode.ToString(),
                    backgroundBehavior = settings.backgroundBehavior.ToString(),
                    compensateForScreenOrientation = settings.compensateForScreenOrientation,
                    defaultDeadzoneMin = settings.defaultDeadzoneMin,
                    defaultDeadzoneMax = settings.defaultDeadzoneMax,
                    defaultButtonPressPoint = settings.defaultButtonPressPoint,
                    defaultTapTime = settings.defaultTapTime,
                    defaultSlowTapTime = settings.defaultSlowTapTime,
                    defaultHoldTime = settings.defaultHoldTime,
                    maxEventBytesPerUpdate = settings.maxEventBytesPerUpdate,
                    maxQueuedEventsPerUpdate = settings.maxQueuedEventsPerUpdate,
                    connectedDevices = InputSystem.devices.Count,
                    enabledActions = CountEnabledActions(),
                    unity62Info = GetUnity62SystemInfo()
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao configurar Input System: {e.Message}");
            }
        }

        private static object ConfigureInputSystem(JObject @params)
        {
            try
            {
                var settings = InputSystem.settings;
                if (settings == null)
                {
                    return Response.Error("Input System settings not available.");
                }

                // Configurações que podem ser alteradas em runtime
                bool enableAllDevices = @params["enable_all_devices"]?.ToObject<bool>() ?? false;
                if (enableAllDevices)
                {
                    foreach (var device in InputSystem.devices)
                    {
                        if (!device.enabled)
                        {
                            InputSystem.EnableDevice(device);
                        }
                    }
                }

                bool disableAllDevices = @params["disable_all_devices"]?.ToObject<bool>() ?? false;
                if (disableAllDevices)
                {
                    foreach (var device in InputSystem.devices)
                    {
                        if (device.enabled)
                        {
                            InputSystem.DisableDevice(device);
                        }
                    }
                }

                // Configurar polling frequency se disponível
                float pollingFreq = @params["polling_frequency"]?.ToObject<float>() ?? -1f;
                if (pollingFreq > 0f)
                {
                    try
                    {
                        InputSystem.pollingFrequency = pollingFreq;
                    }
                    catch (Exception ex)
                    {
                        Debug.LogWarning($"Failed to set polling frequency: {ex.Message}");
                    }
                }

                // Reset haptics se solicitado
                bool resetHaptics = @params["reset_haptics"]?.ToObject<bool>() ?? false;
                if (resetHaptics)
                {
                    InputSystem.ResetHaptics();
                }

                return Response.Success("Configurações do Input System aplicadas em runtime.", new
                {
                    enabledDevices = InputSystem.devices.Count(d => d.enabled),
                    totalDevices = InputSystem.devices.Count,
                    pollingFrequency = InputSystem.pollingFrequency,
                    configuredAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao configurar Input System em runtime: {e.Message}");
            }
        }

        private static object ResetInputSystem()
        {
            try
            {
                // Reset do Input System
                InputSystem.ResetHaptics();
                
                // Reabilitar todos os dispositivos
                int enabledCount = 0;
                foreach (var device in InputSystem.devices)
                {
                    if (!device.enabled)
                    {
                        InputSystem.EnableDevice(device);
                        enabledCount++;
                    }
                }

                // Reset das configurações para valores padrão
                var settings = InputSystem.settings;
                if (settings != null)
                {
                    settings.updateMode = InputSettings.UpdateMode.ProcessEventsInDynamicUpdate;
                    settings.backgroundBehavior = InputSettings.BackgroundBehavior.ResetAndDisableNonBackgroundDevices;
                    settings.compensateForScreenOrientation = true;
                    settings.defaultDeadzoneMin = 0.125f;
                    settings.defaultDeadzoneMax = 0.925f;
                    settings.defaultButtonPressPoint = 0.5f;
                    settings.defaultTapTime = 0.2f;
                    settings.defaultSlowTapTime = 0.5f;
                    settings.defaultHoldTime = 0.4f;
                    settings.maxEventBytesPerUpdate = 5 * 1024 * 1024; // 5MB
                    settings.maxQueuedEventsPerUpdate = 1000;

                    EditorUtility.SetDirty(settings);
                    AssetDatabase.SaveAssets();
                }

                return Response.Success("Input System resetado com sucesso.", new
                {
                    devicesEnabled = enabledCount,
                    totalDevices = InputSystem.devices.Count,
                    settingsReset = settings != null,
                    resetAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao resetar Input System: {e.Message}");
            }
        }

        private static object GetInputSystemSettings()
        {
            try
            {
                var settings = InputSystem.settings;
                if (settings == null)
                {
                    return Response.Error("Input System settings não disponível.");
                }

                var settingsData = new
                {
                    updateMode = settings.updateMode.ToString(),
                    backgroundBehavior = settings.backgroundBehavior.ToString(),
                    compensateForScreenOrientation = settings.compensateForScreenOrientation,
                    defaultDeadzoneMin = settings.defaultDeadzoneMin,
                    defaultDeadzoneMax = settings.defaultDeadzoneMax,
                    defaultButtonPressPoint = settings.defaultButtonPressPoint,
                    defaultTapTime = settings.defaultTapTime,
                    defaultSlowTapTime = settings.defaultSlowTapTime,
                    defaultHoldTime = settings.defaultHoldTime,
                    maxEventBytesPerUpdate = settings.maxEventBytesPerUpdate,
                    maxQueuedEventsPerUpdate = settings.maxQueuedEventsPerUpdate,
                    pollingFrequency = InputSystem.pollingFrequency,
                    supportedDevices = InputSystem.devices.Select(d => new
                    {
                        name = d.name,
                        displayName = d.displayName,
                        deviceClass = d.GetType().Name,
                        enabled = d.enabled,
                        canRunInBackground = d.canRunInBackground,
                        layout = d.layout,
                        deviceId = d.deviceId,
                        description = new
                        {
                            interfaceName = d.description.interfaceName,
                            product = d.description.product,
                            manufacturer = d.description.manufacturer
                        }
                    }).ToArray(),
                    enabledActionsCount = CountEnabledActions(),
                    retrievedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };

                return Response.Success("Configurações do Input System obtidas com sucesso.", settingsData);
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao obter configurações do Input System: {e.Message}");
            }
        }

        private static int CountEnabledActions()
        {
            int count = 0;
            try
            {
                // Contar todas as ações habilitadas no sistema
                var assets = Resources.FindObjectsOfTypeAll<InputActionAsset>();
                foreach (var asset in assets)
                {
                    foreach (var map in asset.actionMaps)
                    {
                        if (map.enabled)
                        {
                            count += map.actions.Count;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"Failed to count enabled actions: {ex.Message}");
            }
            return count;
        }

        /// <summary>
        /// Get Unity 6.2 Input System information and capabilities
        /// </summary>
        private static object GetUnity62SystemInfo()
        {
            try
            {
                return new
                {
                    inputSystemVersion = GetInputSystemVersion(),
                    unityVersion = Application.unityVersion,
                    supportedDeviceTypes = GetSupportedDeviceTypes(),
                    availableProcessors = GetAvailableProcessorTypes(),
                    platformSupport = GetPlatformSupport(),
                    enhancedFeatures = GetUnity62EnhancedFeatures(),
                    performanceMetrics = GetInputPerformanceMetrics()
                };
            }
            catch (Exception e)
            {
                return new { error = $"Could not retrieve Unity 6.2 system info: {e.Message}" };
            }
        }

        private static string GetInputSystemVersion()
        {
            try
            {
                var assembly = typeof(InputSystem).Assembly;
                var version = assembly.GetName().Version;
                return version?.ToString() ?? "1.6.0+";
            }
            catch
            {
                return "Unknown";
            }
        }

        private static string[] GetSupportedDeviceTypes()
        {
            try
            {
                return new[]
                {
                    "Keyboard", "Mouse", "Gamepad", "Touchscreen", "Joystick", "Pen",
                    "XRController", "TrackedDevice", "HID", "Sensor", "WebCamera"
                };
            }
            catch
            {
                return new[] { "Error retrieving device types" };
            }
        }

        private static string[] GetAvailableProcessorTypes()
        {
            try
            {
                return new[]
                {
                    "AxisDeadzone", "Clamp", "Invert", "Normalize", "Scale", "StickDeadzone",
                    "CompensateDirection", "CompensateRotation", "NormalizeVector2", "ScaleVector2",
                    "ScaleVector3", "NormalizeVector3", "InvertAxis", "ClampAxis", 
                    "ExponentialCurve", "SmoothStep", "Deadzone", "Sensitivity", "Curve",
                    "Delta", "Integral", "Acceleration", "Smoothing"
                };
            }
            catch
            {
                return new[] { "Error retrieving processors" };
            }
        }

        private static object GetPlatformSupport()
        {
            try
            {
                return new
                {
                    standalone = true,
                    mobile = true,
                    console = true,
                    webGL = true,
                    xr = true,
                    ar = true,
                    currentPlatform = Application.platform.ToString()
                };
            }
            catch
            {
                return new { error = "Could not determine platform support" };
            }
        }

        private static object GetUnity62EnhancedFeatures()
        {
            try
            {
                return new
                {
                    enhancedTouchSupport = IsFeatureSupported("enhancedTouch"),
                    multiUserSupport = InputUser.all.Count() > 0,
                    onScreenControlsSupport = IsFeatureSupported("onScreenControls"),
                    rebindingSupport = true,
                    compositesSupport = true,
                    interactionsSupport = true,
                    processorChainingSupport = true,
                    deviceSimulationSupport = Application.isEditor,
                    remoteInputSupport = Application.isEditor,
                    performanceMetricsSupport = true
                };
            }
            catch
            {
                return new { error = "Could not determine enhanced features" };
            }
        }

        private static bool IsFeatureSupported(string featureName)
        {
            try
            {
                // Unity 6.2 feature detection based on available assemblies and types
                var assemblies = System.AppDomain.CurrentDomain.GetAssemblies();
                foreach (var assembly in assemblies)
                {
                    if (assembly.FullName.Contains("InputSystem"))
                    {
                        var types = assembly.GetTypes();
                        if (types.Any(t => t.Name.ToLower().Contains(featureName.ToLower())))
                        {
                            return true;
                        }
                    }
                }
                return false;
            }
            catch
            {
                return false;
            }
        }

        private static object GetInputPerformanceMetrics()
        {
            try
            {
                var settings = InputSystem.settings;
                return new
                {
                    pollingFrequency = InputSystem.pollingFrequency,
                    maxEventBytesPerUpdate = settings?.maxEventBytesPerUpdate ?? 0,
                    maxQueuedEventsPerUpdate = settings?.maxQueuedEventsPerUpdate ?? 0,
                    totalDevicesConnected = InputSystem.devices.Count,
                    enabledDevicesCount = InputSystem.devices.Count(d => d.enabled),
                    disabledDevicesCount = InputSystem.devices.Count(d => !d.enabled),
                    currentUpdateMode = settings?.updateMode.ToString() ?? "Unknown",
                    memoryUsageEstimate = EstimateInputSystemMemoryUsage()
                };
            }
            catch
            {
                return new { error = "Could not retrieve performance metrics" };
            }
        }

        private static string EstimateInputSystemMemoryUsage()
        {
            try
            {
                // Rough estimation based on device count and settings
                var deviceCount = InputSystem.devices.Count;
                var settingsSize = InputSystem.settings?.maxEventBytesPerUpdate ?? 0;
                var estimatedBytes = (deviceCount * 1024) + settingsSize; // Rough estimate
                
                if (estimatedBytes < 1024)
                    return $"{estimatedBytes} bytes";
                else if (estimatedBytes < 1024 * 1024)
                    return $"{estimatedBytes / 1024:F1} KB";
                else
                    return $"{estimatedBytes / (1024 * 1024):F1} MB";
            }
            catch
            {
                return "Unknown";
            }
        }

        /// <summary>
        /// Cria e gerencia Action Maps dinamicamente.
        /// </summary>
        public static object HandleCreateInputActionMaps(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidActionMapActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidActionMapActions);
                return Response.Error($"Unknown action: '{action}'. Valid actions are: {validActionsList}");
            }

            try
            {
                switch (action)
                {
                    case "create":
                        return CreateActionMap(@params);
                    case "modify":
                        return ModifyActionMap(@params);
                    case "delete":
                        return DeleteActionMap(@params);
                    case "list":
                        return ListActionMaps(@params);
                    case "enable":
                        return EnableActionMap(@params);
                    case "disable":
                        return DisableActionMap(@params);
                    default:
                        return Response.Error($"Unknown action: '{action}'");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[InputSystemRuntime] Action Map action '{action}' failed: {e}");
                return Response.Error($"Internal error processing Action Map action '{action}': {e.Message}");
            }
        }

        private static object CreateActionMap(JObject @params)
        {
            try
            {
                string mapName = @params["map_name"]?.ToString();
                if (string.IsNullOrEmpty(mapName))
                {
                    return Response.Error("Map name is required.");
                }

                string assetPath = @params["asset_path"]?.ToString();
                InputActionAsset asset = null;

                if (!string.IsNullOrEmpty(assetPath))
                {
                    asset = AssetDatabase.LoadAssetAtPath<InputActionAsset>(assetPath);
                    if (asset == null)
                    {
                        return Response.Error($"Input Action Asset not found at path: {assetPath}");
                    }
                }
                else
                {
                    // Criar novo asset se não fornecido
                    asset = ScriptableObject.CreateInstance<InputActionAsset>();
                    string newAssetPath = $"Assets/InputActions_{mapName}.inputactions";
                    AssetDatabase.CreateAsset(asset, newAssetPath);
                }

                // Criar Action Map
                var actionMap = asset.AddActionMap(mapName);

                // Adicionar ações se fornecidas
                var actions = @params["actions"] as JArray;
                if (actions != null)
                {
                    foreach (var actionData in actions)
                    {
                        var actionObj = actionData as JObject;
                        string actionName = actionObj?["name"]?.ToString();
                        string actionType = actionObj?["type"]?.ToString() ?? "Button";
                        
                        if (!string.IsNullOrEmpty(actionName))
                        {
                            // Determinar tipo da ação
                            InputActionType actionTypeEnum = InputActionType.Button;
                            switch (actionType.ToLower())
                            {
                                case "button":
                                    actionTypeEnum = InputActionType.Button;
                                    break;
                                case "value":
                                    actionTypeEnum = InputActionType.Value;
                                    break;
                                case "passthrough":
                                    actionTypeEnum = InputActionType.PassThrough;
                                    break;
                            }
                            
                            var inputAction = actionMap.AddAction(actionName, actionTypeEnum);

                            // Adicionar bindings se fornecidos
                            var bindings = actionObj?["bindings"] as JArray;
                            if (bindings != null)
                            {
                                foreach (var bindingData in bindings)
                                {
                                    var bindingObj = bindingData as JObject;
                                    string path = bindingObj?["path"]?.ToString();
                                    if (!string.IsNullOrEmpty(path))
                                    {
                                        var binding = inputAction.AddBinding(path);
                                        
                                        // Configurar grupos de binding
                                        string groups = bindingObj?["groups"]?.ToString();
                                        if (!string.IsNullOrEmpty(groups))
                                        {
                                            binding.WithGroups(groups);
                                        }

                                        // Configurar processadores
                                        string processors = bindingObj?["processors"]?.ToString();
                                        if (!string.IsNullOrEmpty(processors))
                                        {
                                            binding.WithProcessors(processors);
                                        }

                                        // Configurar interações
                                        string interactions = bindingObj?["interactions"]?.ToString();
                                        if (!string.IsNullOrEmpty(interactions))
                                        {
                                            binding.WithInteractions(interactions);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // Configurar grupos de binding
                var bindingGroups = @params["binding_groups"] as JArray;
                if (bindingGroups != null)
                {
                    foreach (var group in bindingGroups)
                    {
                        string groupName = group.ToString();
                        if (!string.IsNullOrEmpty(groupName))
                        {
                            asset.AddControlScheme(groupName);
                        }
                    }
                }

                // Habilitar se especificado
                bool enabled = @params["enabled"]?.ToObject<bool>() ?? true;
                if (enabled)
                {
                    actionMap.Enable();
                }

                EditorUtility.SetDirty(asset);
                AssetDatabase.SaveAssets();

                return Response.Success($"Action Map '{mapName}' criado com sucesso.", new
                {
                    mapName = actionMap.name,
                    assetPath = AssetDatabase.GetAssetPath(asset),
                    enabled = actionMap.enabled,
                    actionCount = actionMap.actions.Count,
                    actions = actionMap.actions.Select(a => new
                    {
                        name = a.name,
                        type = a.type.ToString(),
                        bindingCount = a.bindings.Count
                    }).ToArray()
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao criar Action Map: {e.Message}");
            }
        }

        private static object ModifyActionMap(JObject @params)
        {
            try
            {
                string mapName = @params["map_name"]?.ToString();
                string assetPath = @params["asset_path"]?.ToString();

                if (string.IsNullOrEmpty(mapName) || string.IsNullOrEmpty(assetPath))
                {
                    return Response.Error("Map name and asset path are required.");
                }

                var asset = AssetDatabase.LoadAssetAtPath<InputActionAsset>(assetPath);
                if (asset == null)
                {
                    return Response.Error($"Input Action Asset not found at path: {assetPath}");
                }

                var actionMap = asset.FindActionMap(mapName);
                if (actionMap == null)
                {
                    return Response.Error($"Action Map '{mapName}' not found in asset.");
                }

                // Modificar ações se fornecidas
                var actions = @params["actions"] as JArray;
                if (actions != null)
                {
                    foreach (var actionData in actions)
                    {
                        var actionObj = actionData as JObject;
                        string actionName = actionObj?["name"]?.ToString();
                        string operation = actionObj?["operation"]?.ToString() ?? "add";
                        
                        if (!string.IsNullOrEmpty(actionName))
                        {
                            switch (operation.ToLower())
                            {
                                case "add":
                                    if (actionMap.FindAction(actionName) == null)
                                    {
                                        string actionType = actionObj?["type"]?.ToString() ?? "Button";
                                        InputActionType actionTypeEnum = InputActionType.Button;
                                        switch (actionType.ToLower())
                                        {
                                            case "button":
                                                actionTypeEnum = InputActionType.Button;
                                                break;
                                            case "value":
                                                actionTypeEnum = InputActionType.Value;
                                                break;
                                            case "passthrough":
                                                actionTypeEnum = InputActionType.PassThrough;
                                                break;
                                        }
                                        var newAction = actionMap.AddAction(actionName, actionTypeEnum);
                                    }
                                    break;
                                case "remove":
                                    var actionToRemove = actionMap.FindAction(actionName);
                                    if (actionToRemove != null)
                                    {
                                        actionMap.asset.RemoveAction(actionToRemove.name);
                                    }
                                    break;
                            }
                        }
                    }
                }

                EditorUtility.SetDirty(asset);
                AssetDatabase.SaveAssets();

                return Response.Success($"Action Map '{mapName}' modificado com sucesso.");
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao modificar Action Map: {e.Message}");
            }
        }

        private static object DeleteActionMap(JObject @params)
        {
            try
            {
                string mapName = @params["map_name"]?.ToString();
                string assetPath = @params["asset_path"]?.ToString();

                if (string.IsNullOrEmpty(mapName) || string.IsNullOrEmpty(assetPath))
                {
                    return Response.Error("Map name and asset path are required.");
                }

                var asset = AssetDatabase.LoadAssetAtPath<InputActionAsset>(assetPath);
                if (asset == null)
                {
                    return Response.Error($"Input Action Asset not found at path: {assetPath}");
                }

                var actionMap = asset.FindActionMap(mapName);
                if (actionMap == null)
                {
                    return Response.Error($"Action Map '{mapName}' not found in asset.");
                }

                asset.RemoveActionMap(actionMap);
                EditorUtility.SetDirty(asset);
                AssetDatabase.SaveAssets();

                return Response.Success($"Action Map '{mapName}' removido com sucesso.");
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao remover Action Map: {e.Message}");
            }
        }

        private static object ListActionMaps(JObject @params)
        {
            try
            {
                string assetPath = @params["asset_path"]?.ToString();
                
                if (!string.IsNullOrEmpty(assetPath))
                {
                    var asset = AssetDatabase.LoadAssetAtPath<InputActionAsset>(assetPath);
                    if (asset == null)
                    {
                        return Response.Error($"Input Action Asset not found at path: {assetPath}");
                    }

                    var actionMaps = asset.actionMaps.Select(map => new
                    {
                        name = map.name,
                        enabled = map.enabled,
                        actionCount = map.actions.Count,
                        actions = map.actions.Select(a => new
                        {
                            name = a.name,
                            type = a.type.ToString(),
                            bindingCount = a.bindings.Count
                        }).ToArray()
                    }).ToArray();

                    return Response.Success("Action Maps listados com sucesso.", actionMaps);
                }
                else
                {
                    // Listar todos os Action Maps ativos
                    var activeActionMaps = new List<object>();
                    
                    foreach (var user in InputUser.all)
                    {
                        if (user.actions != null && user.actions is InputActionAsset asset)
                        {
                            foreach (var actionMap in asset.actionMaps)
                            {
                                activeActionMaps.Add(new
                                {
                                    name = actionMap.name,
                                    enabled = actionMap.enabled,
                                    userId = user.id,
                                    actionCount = actionMap.actions.Count
                                });
                            }
                        }
                    }

                    return Response.Success("Action Maps ativos listados com sucesso.", activeActionMaps.ToArray());
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao listar Action Maps: {e.Message}");
            }
        }

        private static object EnableActionMap(JObject @params)
        {
            try
            {
                string mapName = @params["map_name"]?.ToString();
                string assetPath = @params["asset_path"]?.ToString();

                if (string.IsNullOrEmpty(mapName))
                {
                    return Response.Error("Map name is required.");
                }

                if (!string.IsNullOrEmpty(assetPath))
                {
                    var asset = AssetDatabase.LoadAssetAtPath<InputActionAsset>(assetPath);
                    if (asset == null)
                    {
                        return Response.Error($"Input Action Asset not found at path: {assetPath}");
                    }

                    var actionMap = asset.FindActionMap(mapName);
                    if (actionMap == null)
                    {
                        return Response.Error($"Action Map '{mapName}' not found in asset.");
                    }

                    actionMap.Enable();
                }
                else
                {
                    // Procurar em todos os usuários ativos
                    bool found = false;
                    foreach (var user in InputUser.all)
                    {
                        if (user.actions != null && user.actions is InputActionAsset asset)
                        {
                            var actionMap = asset.FindActionMap(mapName);
                            if (actionMap != null)
                            {
                                actionMap.Enable();
                                found = true;
                                break;
                            }
                        }
                    }

                    if (!found)
                    {
                        return Response.Error($"Action Map '{mapName}' not found in active users.");
                    }
                }

                return Response.Success($"Action Map '{mapName}' habilitado com sucesso.");
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao habilitar Action Map: {e.Message}");
            }
        }

        private static object DisableActionMap(JObject @params)
        {
            try
            {
                string mapName = @params["map_name"]?.ToString();
                string assetPath = @params["asset_path"]?.ToString();

                if (string.IsNullOrEmpty(mapName))
                {
                    return Response.Error("Map name is required.");
                }

                if (!string.IsNullOrEmpty(assetPath))
                {
                    var asset = AssetDatabase.LoadAssetAtPath<InputActionAsset>(assetPath);
                    if (asset == null)
                    {
                        return Response.Error($"Input Action Asset not found at path: {assetPath}");
                    }

                    var actionMap = asset.FindActionMap(mapName);
                    if (actionMap == null)
                    {
                        return Response.Error($"Action Map '{mapName}' not found in asset.");
                    }

                    actionMap.Disable();
                }
                else
                {
                    // Procurar em todos os usuários ativos
                    bool found = false;
                    foreach (var user in InputUser.all)
                    {
                        if (user.actions != null && user.actions is InputActionAsset asset)
                        {
                            var actionMap = asset.FindActionMap(mapName);
                            if (actionMap != null)
                            {
                                actionMap.Disable();
                                found = true;
                                break;
                            }
                        }
                    }

                    if (!found)
                    {
                        return Response.Error($"Action Map '{mapName}' not found in active users.");
                    }
                }

                return Response.Success($"Action Map '{mapName}' desabilitado com sucesso.");
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao desabilitar Action Map: {e.Message}");
            }
        }

        /// <summary>
        /// Configura processadores de input para ações específicas.
        /// </summary>
        public static object HandleConfigureInputProcessors(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidProcessorActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidProcessorActions);
                return Response.Error($"Unknown action: '{action}'. Valid actions are: {validActionsList}");
            }

            try
            {
                switch (action)
                {
                    case "add":
                        return AddInputProcessor(@params);
                    case "remove":
                        return RemoveInputProcessor(@params);
                    case "list":
                        return ListInputProcessors(@params);
                    case "configure":
                        return ConfigureInputProcessor(@params);
                    case "reset":
                        return ResetInputProcessors(@params);
                    default:
                        return Response.Error($"Unknown action: '{action}'");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[InputSystemRuntime] Processor action '{action}' failed: {e}");
                return Response.Error($"Internal error processing processor action '{action}': {e.Message}");
            }
        }

        private static object AddInputProcessor(JObject @params)
        {
            try
            {
                string assetPath = @params["asset_path"]?.ToString();
                string actionMapName = @params["action_map"]?.ToString();
                string actionName = @params["action_name"]?.ToString();
                string processorType = @params["processor_type"]?.ToString();
                int bindingIndex = @params["binding_index"]?.ToObject<int>() ?? 0;

                if (string.IsNullOrEmpty(assetPath) || string.IsNullOrEmpty(actionMapName) || 
                    string.IsNullOrEmpty(actionName) || string.IsNullOrEmpty(processorType))
                {
                    return Response.Error("Asset path, action map, action name, and processor type are required.");
                }

                var asset = AssetDatabase.LoadAssetAtPath<InputActionAsset>(assetPath);
                if (asset == null)
                {
                    return Response.Error($"Input Action Asset not found at path: {assetPath}");
                }

                var actionMap = asset.FindActionMap(actionMapName);
                if (actionMap == null)
                {
                    return Response.Error($"Action Map '{actionMapName}' not found in asset.");
                }

                var inputAction = actionMap.FindAction(actionName);
                if (inputAction == null)
                {
                    return Response.Error($"Action '{actionName}' not found in Action Map '{actionMapName}'.");
                }

                if (bindingIndex >= inputAction.bindings.Count)
                {
                    return Response.Error($"Binding index {bindingIndex} is out of range. Action has {inputAction.bindings.Count} bindings.");
                }

                // Configurar processador baseado no tipo
                var binding = inputAction.bindings[bindingIndex];
                string currentProcessors = binding.processors;
                string newProcessor = GetProcessorString(processorType, @params["processor_params"] as JObject);

                if (!string.IsNullOrEmpty(currentProcessors))
                {
                    currentProcessors += "," + newProcessor;
                }
                else
                {
                    currentProcessors = newProcessor;
                }

                // Aplicar o processador
                inputAction.ChangeBinding(bindingIndex).WithProcessors(currentProcessors);

                EditorUtility.SetDirty(asset);
                AssetDatabase.SaveAssets();

                return Response.Success($"Processador '{processorType}' adicionado com sucesso ao binding {bindingIndex} da ação '{actionName}'.", new
                {
                    actionName = inputAction.name,
                    bindingIndex = bindingIndex,
                    processorType = processorType,
                    allProcessors = currentProcessors
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao adicionar processador: {e.Message}");
            }
        }

        private static object RemoveInputProcessor(JObject @params)
        {
            try
            {
                string assetPath = @params["asset_path"]?.ToString();
                string actionMapName = @params["action_map"]?.ToString();
                string actionName = @params["action_name"]?.ToString();
                string processorType = @params["processor_type"]?.ToString();
                int bindingIndex = @params["binding_index"]?.ToObject<int>() ?? 0;

                if (string.IsNullOrEmpty(assetPath) || string.IsNullOrEmpty(actionMapName) || 
                    string.IsNullOrEmpty(actionName))
                {
                    return Response.Error("Asset path, action map, and action name are required.");
                }

                var asset = AssetDatabase.LoadAssetAtPath<InputActionAsset>(assetPath);
                if (asset == null)
                {
                    return Response.Error($"Input Action Asset not found at path: {assetPath}");
                }

                var actionMap = asset.FindActionMap(actionMapName);
                if (actionMap == null)
                {
                    return Response.Error($"Action Map '{actionMapName}' not found in asset.");
                }

                var inputAction = actionMap.FindAction(actionName);
                if (inputAction == null)
                {
                    return Response.Error($"Action '{actionName}' not found in Action Map '{actionMapName}'.");
                }

                if (bindingIndex >= inputAction.bindings.Count)
                {
                    return Response.Error($"Binding index {bindingIndex} is out of range. Action has {inputAction.bindings.Count} bindings.");
                }

                var binding = inputAction.bindings[bindingIndex];
                string currentProcessors = binding.processors;

                if (string.IsNullOrEmpty(currentProcessors))
                {
                    return Response.Error($"No processors found on binding {bindingIndex}.");
                }

                // Remover processador específico ou todos
                string newProcessors;
                if (string.IsNullOrEmpty(processorType))
                {
                    // Remover todos os processadores
                    newProcessors = "";
                }
                else
                {
                    // Remover processador específico
                    var processors = currentProcessors.Split(',').Select(p => p.Trim()).ToList();
                    processors.RemoveAll(p => p.StartsWith(processorType));
                    newProcessors = string.Join(",", processors);
                }

                inputAction.ChangeBinding(bindingIndex).WithProcessors(newProcessors);

                EditorUtility.SetDirty(asset);
                AssetDatabase.SaveAssets();

                return Response.Success($"Processador removido com sucesso do binding {bindingIndex} da ação '{actionName}'.", new
                {
                    actionName = inputAction.name,
                    bindingIndex = bindingIndex,
                    removedProcessor = processorType ?? "all",
                    remainingProcessors = newProcessors
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao remover processador: {e.Message}");
            }
        }

        private static object ListInputProcessors(JObject @params)
        {
            try
            {
                string assetPath = @params["asset_path"]?.ToString();
                string actionMapName = @params["action_map"]?.ToString();
                string actionName = @params["action_name"]?.ToString();

                if (string.IsNullOrEmpty(assetPath))
                {
                    // Listar todos os processadores disponíveis
                    var availableProcessors = new[]
                    {
                        "AxisDeadzone", "Clamp", "Invert", "Normalize", "Scale", "StickDeadzone",
                        "CompensateDirection", "CompensateRotation", "NormalizeVector2", "ScaleVector2",
                        "AxisDeadzoneProcessor", "ClampProcessor", "InvertProcessor", "NormalizeProcessor",
                        "ScaleProcessor", "StickDeadzoneProcessor"
                    };

                    return Response.Success("Processadores disponíveis listados com sucesso.", new
                    {
                        availableProcessors = availableProcessors
                    });
                }

                var asset = AssetDatabase.LoadAssetAtPath<InputActionAsset>(assetPath);
                if (asset == null)
                {
                    return Response.Error($"Input Action Asset not found at path: {assetPath}");
                }

                if (string.IsNullOrEmpty(actionMapName))
                {
                    // Listar processadores de todos os Action Maps
                    var allProcessors = new List<object>();
                    foreach (var map in asset.actionMaps)
                    {
                        foreach (var action in map.actions)
                        {
                            for (int i = 0; i < action.bindings.Count; i++)
                            {
                                var binding = action.bindings[i];
                                if (!string.IsNullOrEmpty(binding.processors))
                                {
                                    allProcessors.Add(new
                                    {
                                        actionMap = map.name,
                                        actionName = action.name,
                                        bindingIndex = i,
                                        bindingPath = binding.path,
                                        processors = binding.processors.Split(',').Select(p => p.Trim()).ToArray()
                                    });
                                }
                            }
                        }
                    }

                    return Response.Success("Processadores de todos os Action Maps listados com sucesso.", allProcessors.ToArray());
                }

                var actionMap = asset.FindActionMap(actionMapName);
                if (actionMap == null)
                {
                    return Response.Error($"Action Map '{actionMapName}' not found in asset.");
                }

                if (string.IsNullOrEmpty(actionName))
                {
                    // Listar processadores de todas as ações do Action Map
                    var mapProcessors = new List<object>();
                    foreach (var action in actionMap.actions)
                    {
                        for (int i = 0; i < action.bindings.Count; i++)
                        {
                            var binding = action.bindings[i];
                            if (!string.IsNullOrEmpty(binding.processors))
                            {
                                mapProcessors.Add(new
                                {
                                    actionName = action.name,
                                    bindingIndex = i,
                                    bindingPath = binding.path,
                                    processors = binding.processors.Split(',').Select(p => p.Trim()).ToArray()
                                });
                            }
                        }
                    }

                    return Response.Success($"Processadores do Action Map '{actionMapName}' listados com sucesso.", mapProcessors.ToArray());
                }

                var inputAction = actionMap.FindAction(actionName);
                if (inputAction == null)
                {
                    return Response.Error($"Action '{actionName}' not found in Action Map '{actionMapName}'.");
                }

                // Listar processadores da ação específica
                var actionProcessors = new List<object>();
                for (int i = 0; i < inputAction.bindings.Count; i++)
                {
                    var binding = inputAction.bindings[i];
                    actionProcessors.Add(new
                    {
                        bindingIndex = i,
                        bindingPath = binding.path,
                        processors = string.IsNullOrEmpty(binding.processors) ? 
                            new string[0] : 
                            binding.processors.Split(',').Select(p => p.Trim()).ToArray()
                    });
                }

                return Response.Success($"Processadores da ação '{actionName}' listados com sucesso.", actionProcessors.ToArray());
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao listar processadores: {e.Message}");
            }
        }

        private static object ConfigureInputProcessor(JObject @params)
        {
            try
            {
                string assetPath = @params["asset_path"]?.ToString();
                string actionMapName = @params["action_map"]?.ToString();
                string actionName = @params["action_name"]?.ToString();
                string processorType = @params["processor_type"]?.ToString();
                int bindingIndex = @params["binding_index"]?.ToObject<int>() ?? 0;
                var processorParams = @params["processor_params"] as JObject;

                if (string.IsNullOrEmpty(assetPath) || string.IsNullOrEmpty(actionMapName) || 
                    string.IsNullOrEmpty(actionName) || string.IsNullOrEmpty(processorType))
                {
                    return Response.Error("Asset path, action map, action name, and processor type are required.");
                }

                var asset = AssetDatabase.LoadAssetAtPath<InputActionAsset>(assetPath);
                if (asset == null)
                {
                    return Response.Error($"Input Action Asset not found at path: {assetPath}");
                }

                var actionMap = asset.FindActionMap(actionMapName);
                if (actionMap == null)
                {
                    return Response.Error($"Action Map '{actionMapName}' not found in asset.");
                }

                var inputAction = actionMap.FindAction(actionName);
                if (inputAction == null)
                {
                    return Response.Error($"Action '{actionName}' not found in Action Map '{actionMapName}'.");
                }

                if (bindingIndex >= inputAction.bindings.Count)
                {
                    return Response.Error($"Binding index {bindingIndex} is out of range. Action has {inputAction.bindings.Count} bindings.");
                }

                // Configurar processador com parâmetros específicos
                string processorString = GetProcessorString(processorType, processorParams);
                inputAction.ChangeBinding(bindingIndex).WithProcessors(processorString);

                EditorUtility.SetDirty(asset);
                AssetDatabase.SaveAssets();

                return Response.Success($"Processador '{processorType}' configurado com sucesso no binding {bindingIndex} da ação '{actionName}'.", new
                {
                    actionName = inputAction.name,
                    bindingIndex = bindingIndex,
                    processorType = processorType,
                    processorString = processorString
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao configurar processador: {e.Message}");
            }
        }

        private static object ResetInputProcessors(JObject @params)
        {
            try
            {
                string assetPath = @params["asset_path"]?.ToString();
                string actionMapName = @params["action_map"]?.ToString();
                string actionName = @params["action_name"]?.ToString();

                if (string.IsNullOrEmpty(assetPath))
                {
                    return Response.Error("Asset path is required.");
                }

                var asset = AssetDatabase.LoadAssetAtPath<InputActionAsset>(assetPath);
                if (asset == null)
                {
                    return Response.Error($"Input Action Asset not found at path: {assetPath}");
                }

                int resetCount = 0;

                if (string.IsNullOrEmpty(actionMapName))
                {
                    // Reset todos os processadores do asset
                    foreach (var map in asset.actionMaps)
                    {
                        foreach (var action in map.actions)
                        {
                            for (int i = 0; i < action.bindings.Count; i++)
                            {
                                var binding = action.bindings[i];
                                if (!string.IsNullOrEmpty(binding.processors))
                                {
                                    action.ChangeBinding(i).WithProcessors("");
                                    resetCount++;
                                }
                            }
                        }
                    }
                }
                else
                {
                    var actionMap = asset.FindActionMap(actionMapName);
                    if (actionMap == null)
                    {
                        return Response.Error($"Action Map '{actionMapName}' not found in asset.");
                    }

                    if (string.IsNullOrEmpty(actionName))
                    {
                        // Reset todos os processadores do Action Map
                        foreach (var action in actionMap.actions)
                        {
                            for (int i = 0; i < action.bindings.Count; i++)
                            {
                                var binding = action.bindings[i];
                                if (!string.IsNullOrEmpty(binding.processors))
                                {
                                    action.ChangeBinding(i).WithProcessors("");
                                    resetCount++;
                                }
                            }
                        }
                    }
                    else
                    {
                        // Reset processadores de uma ação específica
                        var inputAction = actionMap.FindAction(actionName);
                        if (inputAction == null)
                        {
                            return Response.Error($"Action '{actionName}' not found in Action Map '{actionMapName}'.");
                        }

                        for (int i = 0; i < inputAction.bindings.Count; i++)
                        {
                            var binding = inputAction.bindings[i];
                            if (!string.IsNullOrEmpty(binding.processors))
                            {
                                inputAction.ChangeBinding(i).WithProcessors("");
                                resetCount++;
                            }
                        }
                    }
                }

                EditorUtility.SetDirty(asset);
                AssetDatabase.SaveAssets();

                return Response.Success($"Processadores resetados com sucesso. {resetCount} bindings foram limpos.", new
                {
                    resetCount = resetCount,
                    scope = string.IsNullOrEmpty(actionMapName) ? "entire asset" : 
                           string.IsNullOrEmpty(actionName) ? $"action map '{actionMapName}'" : 
                           $"action '{actionName}' in map '{actionMapName}'"
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao resetar processadores: {e.Message}");
            }
        }

        private static string GetProcessorString(string processorType, JObject processorParams)
        {
            switch (processorType.ToLower())
            {
                case "axisdeadzone":
                    float min = processorParams?["min"]?.ToObject<float>() ?? 0.125f;
                    float max = processorParams?["max"]?.ToObject<float>() ?? 0.925f;
                    return $"AxisDeadzone(min={min},max={max})";

                case "clamp":
                    float clampMin = processorParams?["min"]?.ToObject<float>() ?? 0f;
                    float clampMax = processorParams?["max"]?.ToObject<float>() ?? 1f;
                    return $"Clamp(min={clampMin},max={clampMax})";

                case "invert":
                    return "Invert()";

                case "normalize":
                    float normalizeMin = processorParams?["min"]?.ToObject<float>() ?? 0f;
                    float normalizeMax = processorParams?["max"]?.ToObject<float>() ?? 1f;
                    float zero = processorParams?["zero"]?.ToObject<float>() ?? 0f;
                    return $"Normalize(min={normalizeMin},max={normalizeMax},zero={zero})";

                case "scale":
                    float factor = processorParams?["factor"]?.ToObject<float>() ?? 1f;
                    return $"Scale(factor={factor})";

                case "stickdeadzone":
                    float stickMin = processorParams?["min"]?.ToObject<float>() ?? 0.125f;
                    float stickMax = processorParams?["max"]?.ToObject<float>() ?? 0.925f;
                    return $"StickDeadzone(min={stickMin},max={stickMax})";

                case "compensatedirection":
                    return "CompensateDirection()";

                case "compensaterotation":
                    return "CompensateRotation()";

                case "normalizevector2":
                    return "NormalizeVector2()";

                case "scalevector2":
                    float x = processorParams?["x"]?.ToObject<float>() ?? 1f;
                    float y = processorParams?["y"]?.ToObject<float>() ?? 1f;
                    return $"ScaleVector2(x={x},y={y})";

                // Unity 6.2 Enhanced Processors
                case "scalevector3":
                    float x3 = processorParams?["x"]?.ToObject<float>() ?? 1f;
                    float y3 = processorParams?["y"]?.ToObject<float>() ?? 1f;
                    float z3 = processorParams?["z"]?.ToObject<float>() ?? 1f;
                    return $"ScaleVector3(x={x3},y={y3},z={z3})";

                case "normalizevector3":
                    return "NormalizeVector3()";

                case "invertaxis":
                    bool invertX = processorParams?["invertX"]?.ToObject<bool>() ?? false;
                    bool invertY = processorParams?["invertY"]?.ToObject<bool>() ?? false;
                    return $"InvertAxis(invertX={invertX.ToString().ToLower()},invertY={invertY.ToString().ToLower()})";

                case "clampaxis":
                    float clampAxisMin = processorParams?["min"]?.ToObject<float>() ?? -1f;
                    float clampAxisMax = processorParams?["max"]?.ToObject<float>() ?? 1f;
                    return $"ClampAxis(min={clampAxisMin},max={clampAxisMax})";

                case "exponentialcurve":
                    float exponent = processorParams?["exponent"]?.ToObject<float>() ?? 2f;
                    return $"ExponentialCurve(exponent={exponent})";

                case "smoothstep":
                    float edge0 = processorParams?["edge0"]?.ToObject<float>() ?? 0f;
                    float edge1 = processorParams?["edge1"]?.ToObject<float>() ?? 1f;
                    return $"SmoothStep(edge0={edge0},edge1={edge1})";

                case "deadzone":
                    float deadMin = processorParams?["min"]?.ToObject<float>() ?? 0.125f;
                    float deadMax = processorParams?["max"]?.ToObject<float>() ?? 0.925f;
                    return $"Deadzone(min={deadMin},max={deadMax})";

                case "sensitivity":
                    float sensitivity = processorParams?["sensitivity"]?.ToObject<float>() ?? 1f;
                    return $"Sensitivity(sensitivity={sensitivity})";

                case "curve":
                    string curveName = processorParams?["curve"]?.ToString() ?? "Linear";
                    return $"Curve(curve={curveName})";

                case "delta":
                    return "Delta()";

                case "integral":
                    return "Integral()";

                case "acceleration":
                    float accelSensitivity = processorParams?["sensitivity"]?.ToObject<float>() ?? 1f;
                    return $"Acceleration(sensitivity={accelSensitivity})";

                case "smoothing":
                    float smoothingFactor = processorParams?["factor"]?.ToObject<float>() ?? 0.5f;
                    return $"Smoothing(factor={smoothingFactor})";

                default:
                    // Para processadores customizados ou não reconhecidos
                    if (processorParams != null && processorParams.Count > 0)
                    {
                        var paramStrings = processorParams.Properties().Select(p => $"{p.Name}={p.Value}");
                        return $"{processorType}({string.Join(",", paramStrings)})";
                    }
                    return processorType;
             }
         }

        /// <summary>
        /// Gerencia dispositivos de input do sistema.
        /// </summary>
        public static object HandleSetupInputDeviceManagement(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidDeviceActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidDeviceActions);
                return Response.Error($"Unknown action: '{action}'. Valid actions are: {validActionsList}");
            }

            try
            {
                switch (action)
                {
                    case "setup":
                        return SetupDeviceManagement(@params);
                    case "add_device":
                        return AddInputDevice(@params);
                    case "remove_device":
                        return RemoveInputDevice(@params);
                    case "configure":
                        return ConfigureInputDevice(@params);
                    case "list_devices":
                        return ListInputDevices(@params);
                    default:
                        return Response.Error($"Unknown action: '{action}'");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[InputSystemRuntime] Device management action '{action}' failed: {e}");
                return Response.Error($"Internal error processing device management action '{action}': {e.Message}");
            }
        }

        private static object SetupDeviceManagement(JObject @params)
        {
            try
            {
                bool enableDeviceSimulation = @params["enable_simulation"]?.ToObject<bool>() ?? false;
                bool enableRemoteDevices = @params["enable_remote"]?.ToObject<bool>() ?? false;
                bool enableNativeBackends = @params["enable_native_backends"]?.ToObject<bool>() ?? true;
                
                /// <summary>
                /// [UNITY 6.2] - Configurar simulação de dispositivos usando APIs reais do Unity Input System.
                /// </summary>
                if (enableDeviceSimulation)
                {
#if UNITY_EDITOR
                    try
                    {
                        // Adicionar dispositivos virtuais usando InputSystem.AddDevice
                        var virtualMouse = InputSystem.AddDevice<Mouse>("VirtualMouse");
                        var virtualKeyboard = InputSystem.AddDevice<Keyboard>("VirtualKeyboard");
                        var virtualGamepad = InputSystem.AddDevice<Gamepad>("VirtualGamepad");

                        Debug.Log($"[InputSystemRuntime] Virtual devices added: Mouse={virtualMouse.deviceId}, Keyboard={virtualKeyboard.deviceId}, Gamepad={virtualGamepad.deviceId}");

                        // Configurar dispositivos virtuais para simulação
                        virtualMouse.MakeCurrent();
                        virtualKeyboard.MakeCurrent();
                        virtualGamepad.MakeCurrent();
                    }
                    catch (Exception e)
                    {
                        Debug.LogError($"[InputSystemRuntime] Failed to enable device simulation: {e.Message}");
                    }
#endif
                }
                else
                {
#if UNITY_EDITOR
                    try
                    {
                        // Remover dispositivos virtuais
                        var devicesToRemove = InputSystem.devices.Where(d => d.name.Contains("Virtual")).ToList();
                        foreach (var device in devicesToRemove)
                        {
                            InputSystem.RemoveDevice(device);
                            Debug.Log($"[InputSystemRuntime] Removed virtual device: {device.name}");
                        }
                    }
                    catch (Exception e)
                    {
                        Debug.LogError($"[InputSystemRuntime] Failed to disable device simulation: {e.Message}");
                    }
#endif
                }

                // Configurar dispositivos remotos
                if (enableRemoteDevices)
                {
                    var remoteSettings = @params["remote_settings"] as JObject;
                    if (remoteSettings != null)
                    {
                        bool startRemoteInput = remoteSettings["start_remote"]?.ToObject<bool>() ?? false;
                        if (startRemoteInput)
                        {
#if UNITY_EDITOR
                            UnityEditor.EditorApplication.ExecuteMenuItem("Window/Analysis/Input Debugger");
#endif
                        }
                    }
                }

                // Configurar backends nativos
                var backendSettings = @params["backend_settings"] as JObject;
                if (backendSettings != null)
                {
                    var disabledBackends = backendSettings["disabled_backends"] as JArray;
                    if (disabledBackends != null)
                    {
                        foreach (var backend in disabledBackends)
                        {
                            string backendName = backend.ToString();
                            // Note: Disabling specific backends requires platform-specific code
                            Debug.Log($"Backend '{backendName}' marked for disabling (platform-specific implementation required)");
                        }
                    }
                }

                // Configurar polling frequency
                float pollingFrequency = @params["polling_frequency"]?.ToObject<float>() ?? 60f;
                InputSystem.pollingFrequency = pollingFrequency;

                // Configurar update mode
                string updateMode = @params["update_mode"]?.ToString()?.ToLower() ?? "dynamic";
                switch (updateMode)
                {
                    case "fixed":
                        InputSystem.settings.updateMode = InputSettings.UpdateMode.ProcessEventsInFixedUpdate;
                        break;
                    case "dynamic":
                        InputSystem.settings.updateMode = InputSettings.UpdateMode.ProcessEventsInDynamicUpdate;
                        break;
                    case "manual":
                        InputSystem.settings.updateMode = InputSettings.UpdateMode.ProcessEventsManually;
                        break;
                }

                return Response.Success("Gerenciamento de dispositivos configurado com sucesso.", new
                {
                    deviceSimulation = enableDeviceSimulation,
                    remoteDevices = enableRemoteDevices,
                    nativeBackends = enableNativeBackends,
                    pollingFrequency = InputSystem.pollingFrequency,
                    updateMode = InputSystem.settings.updateMode.ToString(),
                    connectedDevices = InputSystem.devices.Count
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao configurar gerenciamento de dispositivos: {e.Message}");
            }
        }

        private static object AddInputDevice(JObject @params)
        {
            try
            {
                string deviceType = @params["device_type"]?.ToString();
                string deviceName = @params["device_name"]?.ToString();
                
                if (string.IsNullOrEmpty(deviceType))
                {
                    return Response.Error("Device type is required.");
                }

                InputDevice device = null;
                
                // Criar dispositivo baseado no tipo
                switch (deviceType.ToLower())
                {
                    case "keyboard":
                        device = InputSystem.AddDevice<Keyboard>(deviceName);
                        break;
                    case "mouse":
                        device = InputSystem.AddDevice<Mouse>(deviceName);
                        break;
                    case "gamepad":
                        device = InputSystem.AddDevice<Gamepad>(deviceName);
                        break;
                    case "touchscreen":
                        device = InputSystem.AddDevice<Touchscreen>(deviceName);
                        break;
                    case "joystick":
                        device = InputSystem.AddDevice<Joystick>(deviceName);
                        break;
                    default:
                        // Tentar criar dispositivo por layout
                        try
                        {
                            device = InputSystem.AddDevice(deviceType, deviceName);
                        }
                        catch
                        {
                            return Response.Error($"Unknown device type: '{deviceType}'. Supported types: keyboard, mouse, gamepad, touchscreen, joystick, or custom layout name.");
                        }
                        break;
                }

                if (device == null)
                {
                    return Response.Error($"Failed to create device of type '{deviceType}'.");
                }

                // Configurar propriedades do dispositivo se fornecidas
                var deviceProperties = @params["properties"] as JObject;
                if (deviceProperties != null)
                {
                    // Configurar descrição
                    string description = deviceProperties["description"]?.ToString();
                    if (!string.IsNullOrEmpty(description))
                    {
                        // Note: Device description is read-only after creation
                        Debug.Log($"Device description would be set to: {description}");
                    }

                    // Configurar enabled state
                    bool enabled = deviceProperties["enabled"]?.ToObject<bool>() ?? true;
                    if (enabled)
                    {
                        InputSystem.EnableDevice(device);
                    }
                    else
                    {
                        InputSystem.DisableDevice(device);
                    }
                }

                return Response.Success($"Dispositivo '{deviceType}' adicionado com sucesso.", new
                {
                    deviceId = device.deviceId,
                    deviceName = device.name,
                    deviceType = device.layout,
                    enabled = device.enabled,
                    description = device.description.ToString(),
                    canRunInBackground = GetDeviceBackgroundCapability(device),
                    supportsHaptics = SupportsHapticFeedback(device),
                    unity62Features = GetUnity62DeviceFeatures(device)
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao adicionar dispositivo: {e.Message}");
            }
        }

        /// <summary>
        /// Unity 6.2 Enhanced device capability detection
        /// </summary>
        private static bool GetDeviceBackgroundCapability(InputDevice device)
        {
            try
            {
                // Unity 6.2 enhanced background capability detection
                return device.description.capabilities.Contains("\"canRunInBackground\":true") ||
                       device is Gamepad || device is Keyboard || device is Mouse;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Unity 6.2 Enhanced haptic support detection
        /// </summary>
        private static bool SupportsHapticFeedback(InputDevice device)
        {
            try
            {
                if (device is Gamepad gamepad)
                {
                    // Unity 6.2 enhanced haptic detection - check if gamepad implements IDualMotorRumble
                    return gamepad is UnityEngine.InputSystem.Haptics.IDualMotorRumble;
                }
                
                // Unity 6.2 supports haptics on more device types
                return device.description.capabilities.Contains("haptic") ||
                       device.description.capabilities.Contains("vibration") ||
                       device.description.capabilities.Contains("motor");
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Unity 6.2 specific device features
        /// </summary>
        private static object GetUnity62DeviceFeatures(InputDevice device)
        {
            try
            {
                var features = new
                {
                    supportsEnhancedTouch = IsEnhancedTouchDevice(device),
                    supportsAdvancedGamepad = IsAdvancedGamepadDevice(device),
                    supportsVR = IsVRDevice(device),
                    supportsAR = IsARDevice(device),
                    hasAccelerometer = HasAccelerometer(device),
                    hasGyroscope = HasGyroscope(device),
                    hasCompass = HasCompass(device),
                    batteryLevel = GetBatteryLevel(device),
                    connectionType = GetConnectionType(device)
                };
                return features;
            }
            catch
            {
                return new { error = "Could not determine Unity 6.2 features" };
            }
        }

        private static bool IsEnhancedTouchDevice(InputDevice device)
        {
#if UNITY_2023_2_OR_NEWER
            return device is Touchscreen;
#else
            return device is Touchscreen;
#endif
        }

        private static bool IsAdvancedGamepadDevice(InputDevice device)
        {
            if (device is Gamepad gamepad)
            {
                // Unity 6.2 enhanced gamepad detection
                var capabilities = device.description.capabilities;
                return capabilities.Contains("\"leftTrigger\"") && 
                       capabilities.Contains("\"rightTrigger\"") &&
                       capabilities.Contains("\"leftStick\"") && 
                       capabilities.Contains("\"rightStick\"");
            }
            return false;
        }

        private static bool IsVRDevice(InputDevice device)
        {
            return device.layout.Contains("VR") || 
                   device.layout.Contains("XR") ||
                   device.description.deviceClass.Contains("VR") ||
                   device.description.deviceClass.Contains("XR");
        }

        private static bool IsARDevice(InputDevice device)
        {
            return device.layout.Contains("AR") || 
                   device.description.deviceClass.Contains("AR");
        }

        private static bool HasAccelerometer(InputDevice device)
        {
            return device.TryGetChildControl("acceleration") != null ||
                   device.description.capabilities.Contains("acceleration");
        }

        private static bool HasGyroscope(InputDevice device)
        {
            return device.TryGetChildControl("angularVelocity") != null ||
                   device.description.capabilities.Contains("gyroscope");
        }

        private static bool HasCompass(InputDevice device)
        {
            return device.TryGetChildControl("heading") != null ||
                   device.description.capabilities.Contains("compass");
        }

        private static float GetBatteryLevel(InputDevice device)
        {
            try
            {
                // Unity 6.2 battery level detection for supported devices
                var batteryControl = device.TryGetChildControl("batteryLevel");
                return batteryControl?.ReadValueAsObject() as float? ?? -1f;
            }
            catch
            {
                return -1f; // Unknown battery level
            }
        }

        private static string GetConnectionType(InputDevice device)
        {
            try
            {
                var interfaceName = device.description.interfaceName;
                if (interfaceName.Contains("Bluetooth")) return "Bluetooth";
                if (interfaceName.Contains("USB")) return "USB";
                if (interfaceName.Contains("Wireless")) return "Wireless";
                if (interfaceName.Contains("XInput")) return "XInput";
                if (interfaceName.Contains("DirectInput")) return "DirectInput";
                return "Unknown";
            }
            catch
            {
                return "Unknown";
            }
        }

        private static object RemoveInputDevice(JObject @params)
        {
            try
            {
                string deviceName = @params["device_name"]?.ToString();
                int deviceId = @params["device_id"]?.ToObject<int>() ?? -1;
                
                InputDevice device = null;
                
                if (!string.IsNullOrEmpty(deviceName))
                {
                    device = InputSystem.devices.FirstOrDefault(d => d.name == deviceName);
                }
                else if (deviceId >= 0)
                {
                    device = InputSystem.devices.FirstOrDefault(d => d.deviceId == deviceId);
                }
                else
                {
                    return Response.Error("Device name or device ID is required.");
                }

                if (device == null)
                {
                    return Response.Error($"Device not found with name '{deviceName}' or ID '{deviceId}'.");
                }

                string removedDeviceName = device.name;
                string removedDeviceType = device.layout;
                int removedDeviceId = device.deviceId;

                InputSystem.RemoveDevice(device);

                return Response.Success($"Dispositivo '{removedDeviceName}' removido com sucesso.", new
                {
                    deviceId = removedDeviceId,
                    deviceName = removedDeviceName,
                    deviceType = removedDeviceType
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao remover dispositivo: {e.Message}");
            }
        }

        private static object ConfigureInputDevice(JObject @params)
        {
            try
            {
                string deviceName = @params["device_name"]?.ToString();
                int deviceId = @params["device_id"]?.ToObject<int>() ?? -1;
                
                InputDevice device = null;
                
                if (!string.IsNullOrEmpty(deviceName))
                {
                    device = InputSystem.devices.FirstOrDefault(d => d.name == deviceName);
                }
                else if (deviceId >= 0)
                {
                    device = InputSystem.devices.FirstOrDefault(d => d.deviceId == deviceId);
                }
                else
                {
                    return Response.Error("Device name or device ID is required.");
                }

                if (device == null)
                {
                    return Response.Error($"Device not found with name '{deviceName}' or ID '{deviceId}'.");
                }

                var configuration = @params["configuration"] as JObject;
                if (configuration == null)
                {
                    return Response.Error("Configuration object is required.");
                }

                // Configurar enabled state
                if (configuration.ContainsKey("enabled"))
                {
                    bool enabled = configuration["enabled"].ToObject<bool>();
                    if (enabled)
                    {
                        InputSystem.EnableDevice(device);
                    }
                    else
                    {
                        InputSystem.DisableDevice(device);
                    }
                }

                // Configurar noise filtering
                if (configuration.ContainsKey("noise_filter") && device is UnityEngine.InputSystem.Pointer pointer)
                {
                    bool enableNoiseFilter = configuration["noise_filter"].ToObject<bool>();
                    // Note: Noise filtering configuration is device-specific
                    Debug.Log($"Noise filtering for {device.name} would be set to: {enableNoiseFilter}");
                }

                // Configurar dead zones para gamepads
                if (configuration.ContainsKey("deadzone") && device is Gamepad gamepad)
                {
                    float deadzone = configuration["deadzone"].ToObject<float>();
                    // Note: Deadzone configuration typically done through processors
                    Debug.Log($"Deadzone for {device.name} would be set to: {deadzone}");
                }

                // Configurar sensibilidade para mouse
                if (configuration.ContainsKey("sensitivity") && device is Mouse mouse)
                {
                    float sensitivity = configuration["sensitivity"].ToObject<float>();
                    // Note: Sensitivity configuration typically done through processors
                    Debug.Log($"Sensitivity for {device.name} would be set to: {sensitivity}");
                }

                return Response.Success($"Dispositivo '{device.name}' configurado com sucesso.", new
                {
                    deviceId = device.deviceId,
                    deviceName = device.name,
                    deviceType = device.layout,
                    enabled = device.enabled,
                    configuration = configuration
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao configurar dispositivo: {e.Message}");
            }
        }

        private static object ListInputDevices(JObject @params)
        {
            try
            {
                string deviceType = @params["device_type"]?.ToString();
                bool includeDisabled = @params["include_disabled"]?.ToObject<bool>() ?? true;
                bool detailedInfo = @params["detailed_info"]?.ToObject<bool>() ?? false;

                var devices = InputSystem.devices.AsEnumerable();

                // Filtrar por tipo se especificado
                if (!string.IsNullOrEmpty(deviceType))
                {
                    devices = devices.Where(d => d.layout.ToLower().Contains(deviceType.ToLower()) || 
                                                d.GetType().Name.ToLower().Contains(deviceType.ToLower()));
                }

                // Filtrar dispositivos desabilitados se necessário
                if (!includeDisabled)
                {
                    devices = devices.Where(d => d.enabled);
                }

                var deviceList = devices.Select(device => 
                {
                    if (detailedInfo)
                    {
                        return (object)new
                        {
                            deviceId = device.deviceId,
                            name = device.name,
                            displayName = device.displayName,
                            layout = device.layout,
                            enabled = device.enabled,
                            canRunInBackground = device.canRunInBackground,
                            added = device.added,
                            description = device.description.ToString(),
                            controls = device.allControls.Select(c => new
                            {
                                name = c.name,
                                path = c.path,
                                displayName = c.displayName,
                                valueType = c.valueType.Name
                            }).ToArray(),
                            usages = device.usages.Select(u => u.ToString()).ToArray()
                        };
                    }
                    else
                    {
                        return (object)new
                        {
                            deviceId = device.deviceId,
                            name = device.name,
                            displayName = device.displayName,
                            layout = device.layout,
                            enabled = device.enabled,
                            canRunInBackground = device.canRunInBackground,
                            added = device.added
                        };
                    }
                }).ToArray();

                return Response.Success($"Dispositivos listados com sucesso. Total: {deviceList.Length}", new
                {
                    totalDevices = deviceList.Length,
                    devices = deviceList,
                    systemInfo = new
                    {
                        pollingFrequency = InputSystem.pollingFrequency,
                        updateMode = InputSystem.settings.updateMode.ToString(),
                        totalSystemDevices = InputSystem.devices.Count
                    }
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao listar dispositivos: {e.Message}");
             }
         }

        /// <summary>
        /// Cria e gerencia UI de rebinding de input.
        /// </summary>
        public static object HandleCreateInputRebindingUI(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidRebindingActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidRebindingActions);
                return Response.Error($"Unknown action: '{action}'. Valid actions are: {validActionsList}");
            }

            try
            {
                switch (action)
                {
                    case "create_ui":
                        return CreateRebindingUI(@params);
                    case "start_rebinding":
                        return StartRebinding(@params);
                    case "cancel_rebinding":
                        return CancelRebinding(@params);
                    case "save_bindings":
                        return SaveBindings(@params);
                    case "load_bindings":
                        return LoadBindings(@params);
                    default:
                        return Response.Error($"Unknown action: '{action}'");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[InputSystemRuntime] Rebinding UI action '{action}' failed: {e}");
                return Response.Error($"Internal error processing rebinding UI action '{action}': {e.Message}");
            }
        }

        private static object CreateRebindingUI(JObject @params)
        {
            try
            {
                string actionMapName = @params["action_map"]?.ToString();
                string actionName = @params["action_name"]?.ToString();
                string uiType = @params["ui_type"]?.ToString()?.ToLower() ?? "button";
                
                if (string.IsNullOrEmpty(actionMapName) || string.IsNullOrEmpty(actionName))
                {
                    return Response.Error("Action map name and action name are required.");
                }

                // Encontrar o action map
                var actionMaps = Resources.FindObjectsOfTypeAll<InputActionAsset>();
                InputActionMap targetMap = null;
                InputAction targetAction = null;

                foreach (var asset in actionMaps)
                {
                    targetMap = asset.FindActionMap(actionMapName);
                    if (targetMap != null)
                    {
                        targetAction = targetMap.FindAction(actionName);
                        if (targetAction != null) break;
                    }
                }

                if (targetAction == null)
                {
                    return Response.Error($"Action '{actionName}' not found in action map '{actionMapName}'.");
                }

                // Criar GameObject para UI
                GameObject uiObject = new GameObject($"RebindingUI_{actionMapName}_{actionName}");
                
                // Adicionar Canvas se necessário
                Canvas canvas = uiObject.GetComponent<Canvas>();
                if (canvas == null)
                {
                    canvas = uiObject.AddComponent<Canvas>();
                    canvas.renderMode = RenderMode.ScreenSpaceOverlay;
                    uiObject.AddComponent<CanvasScaler>();
                    uiObject.AddComponent<GraphicRaycaster>();
                }

                // Criar UI baseado no tipo
                GameObject uiElement = null;
                switch (uiType)
                {
                    case "button":
                        uiElement = CreateRebindingButton(uiObject, targetAction, @params);
                        break;
                    case "text":
                        uiElement = CreateRebindingText(uiObject, targetAction, @params);
                        break;
                    case "panel":
                        uiElement = CreateRebindingPanel(uiObject, targetAction, @params);
                        break;
                    default:
                        return Response.Error($"Unknown UI type: '{uiType}'. Supported types: button, text, panel.");
                }

                if (uiElement == null)
                {
                    UnityEngine.Object.DestroyImmediate(uiObject);
                    return Response.Error($"Failed to create UI element of type '{uiType}'.");
                }

                // Configurar posição
                var position = @params["position"] as JObject;
                if (position != null)
                {
                    RectTransform rectTransform = uiElement.GetComponent<RectTransform>();
                    if (rectTransform != null)
                    {
                        float x = position["x"]?.ToObject<float>() ?? 0f;
                        float y = position["y"]?.ToObject<float>() ?? 0f;
                        rectTransform.anchoredPosition = new Vector2(x, y);
                        
                        float width = position["width"]?.ToObject<float>() ?? 200f;
                        float height = position["height"]?.ToObject<float>() ?? 50f;
                        rectTransform.sizeDelta = new Vector2(width, height);
                    }
                }

                return Response.Success($"UI de rebinding criada com sucesso para ação '{actionName}'.", new
                {
                    uiObjectId = uiObject.GetInstanceID(),
                    uiElementId = uiElement.GetInstanceID(),
                    actionMap = actionMapName,
                    actionName = actionName,
                    uiType = uiType,
                    currentBinding = targetAction.bindings.Count > 0 ? targetAction.bindings[0].effectivePath : "None"
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao criar UI de rebinding: {e.Message}");
            }
        }

        private static GameObject CreateRebindingButton(GameObject parent, InputAction action, JObject @params)
        {
            GameObject buttonObj = new GameObject("RebindButton");
            buttonObj.transform.SetParent(parent.transform);
            
            // Adicionar componentes de UI
            RectTransform rectTransform = buttonObj.AddComponent<RectTransform>();
            Image image = buttonObj.AddComponent<Image>();
            Button button = buttonObj.AddComponent<Button>();
            
            // Configurar aparência
            image.color = Color.white;
            
            // Criar texto do botão
            GameObject textObj = new GameObject("Text");
            textObj.transform.SetParent(buttonObj.transform);
            
            RectTransform textRect = textObj.AddComponent<RectTransform>();
            textRect.anchorMin = Vector2.zero;
            textRect.anchorMax = Vector2.one;
            textRect.offsetMin = Vector2.zero;
            textRect.offsetMax = Vector2.zero;
            
            Text text = textObj.AddComponent<Text>();
            text.text = @params["button_text"]?.ToString() ?? $"Rebind {action.name}";
            text.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
            text.fontSize = 14;
            text.alignment = TextAnchor.MiddleCenter;
            text.color = Color.black;
            
            // Configurar funcionalidade de rebinding
            button.onClick.AddListener(() => {
                StartInteractiveRebind(action, text);
            });
            
            return buttonObj;
        }

        private static GameObject CreateRebindingText(GameObject parent, InputAction action, JObject @params)
        {
            GameObject textObj = new GameObject("RebindText");
            textObj.transform.SetParent(parent.transform);
            
            RectTransform rectTransform = textObj.AddComponent<RectTransform>();
            Text text = textObj.AddComponent<Text>();
            
            text.text = @params["text_content"]?.ToString() ?? $"{action.name}: {(action.bindings.Count > 0 ? action.bindings[0].effectivePath : "None")}";
            text.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
            text.fontSize = @params["font_size"]?.ToObject<int>() ?? 16;
            text.color = Color.white;
            
            return textObj;
        }

        private static GameObject CreateRebindingPanel(GameObject parent, InputAction action, JObject @params)
        {
            GameObject panelObj = new GameObject("RebindPanel");
            panelObj.transform.SetParent(parent.transform);
            
            RectTransform rectTransform = panelObj.AddComponent<RectTransform>();
            Image image = panelObj.AddComponent<Image>();
            
            // Configurar aparência do painel
            image.color = new Color(0.2f, 0.2f, 0.2f, 0.8f);
            
            // Criar título
            GameObject titleObj = new GameObject("Title");
            titleObj.transform.SetParent(panelObj.transform);
            
            RectTransform titleRect = titleObj.AddComponent<RectTransform>();
            titleRect.anchorMin = new Vector2(0, 0.8f);
            titleRect.anchorMax = new Vector2(1, 1);
            titleRect.offsetMin = Vector2.zero;
            titleRect.offsetMax = Vector2.zero;
            
            Text titleText = titleObj.AddComponent<Text>();
            titleText.text = @params["panel_title"]?.ToString() ?? $"Rebind {action.name}";
            titleText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
            titleText.fontSize = 18;
            titleText.alignment = TextAnchor.MiddleCenter;
            titleText.color = Color.white;
            
            // Criar botão de rebinding
            GameObject buttonObj = CreateRebindingButton(panelObj, action, @params);
            RectTransform buttonRect = buttonObj.GetComponent<RectTransform>();
            buttonRect.anchorMin = new Vector2(0.1f, 0.4f);
            buttonRect.anchorMax = new Vector2(0.9f, 0.7f);
            buttonRect.offsetMin = Vector2.zero;
            buttonRect.offsetMax = Vector2.zero;
            
            return panelObj;
        }

        private static void StartInteractiveRebind(InputAction action, Text displayText)
        {
            action.Disable();
            
            var rebindOperation = action.PerformInteractiveRebinding()
                .WithControlsExcluding("Mouse")
                .OnMatchWaitForAnother(0.1f)
                .OnComplete(operation => {
                    displayText.text = $"{action.name}: {action.bindings[0].effectivePath}";
                    operation.Dispose();
                    action.Enable();
                })
                .OnCancel(operation => {
                    displayText.text = $"{action.name}: Cancelled";
                    operation.Dispose();
                    action.Enable();
                })
                .Start();
        }

        private static object StartRebinding(JObject @params)
        {
            try
            {
                string actionMapName = @params["action_map"]?.ToString();
                string actionName = @params["action_name"]?.ToString();
                
                if (string.IsNullOrEmpty(actionMapName) || string.IsNullOrEmpty(actionName))
                {
                    return Response.Error("Action map name and action name are required.");
                }

                // Encontrar a ação
                var actionMaps = Resources.FindObjectsOfTypeAll<InputActionAsset>();
                InputAction targetAction = null;

                foreach (var asset in actionMaps)
                {
                    var map = asset.FindActionMap(actionMapName);
                    if (map != null)
                    {
                        targetAction = map.FindAction(actionName);
                        if (targetAction != null) break;
                    }
                }

                if (targetAction == null)
                {
                    return Response.Error($"Action '{actionName}' not found in action map '{actionMapName}'.");
                }

                // Configurar opções de rebinding
                var options = @params["options"] as JObject;
                bool excludeMouse = options?["exclude_mouse"]?.ToObject<bool>() ?? true;
                bool excludeKeyboard = options?["exclude_keyboard"]?.ToObject<bool>() ?? false;
                float timeout = options?["timeout"]?.ToObject<float>() ?? 5f;
                
                targetAction.Disable();
                
                var rebindOperation = targetAction.PerformInteractiveRebinding();
                
                if (excludeMouse)
                    rebindOperation.WithControlsExcluding("Mouse");
                if (excludeKeyboard)
                    rebindOperation.WithControlsExcluding("Keyboard");
                    
                rebindOperation
                    .WithTimeout(timeout)
                    .OnComplete(operation => {
                        Debug.Log($"Rebinding completed for {actionName}: {targetAction.bindings[0].effectivePath}");
                        operation.Dispose();
                        targetAction.Enable();
                    })
                    .OnCancel(operation => {
                        Debug.Log($"Rebinding cancelled for {actionName}");
                        operation.Dispose();
                        targetAction.Enable();
                    })
                    .Start();

                return Response.Success($"Rebinding iniciado para ação '{actionName}'.", new
                {
                    actionMap = actionMapName,
                    actionName = actionName,
                    timeout = timeout,
                    excludeMouse = excludeMouse,
                    excludeKeyboard = excludeKeyboard
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao iniciar rebinding: {e.Message}");
            }
        }

        private static object CancelRebinding(JObject @params)
        {
            try
            {
                string actionMapName = @params["action_map"]?.ToString();
                string actionName = @params["action_name"]?.ToString();
                
                if (string.IsNullOrEmpty(actionMapName) || string.IsNullOrEmpty(actionName))
                {
                    return Response.Error("Action map name and action name are required.");
                }

                // Note: Para cancelar rebinding ativo, precisaríamos manter referência da operação
                // Por simplicidade, vamos apenas reabilitar a ação
                var actionMaps = Resources.FindObjectsOfTypeAll<InputActionAsset>();
                InputAction targetAction = null;

                foreach (var asset in actionMaps)
                {
                    var map = asset.FindActionMap(actionMapName);
                    if (map != null)
                    {
                        targetAction = map.FindAction(actionName);
                        if (targetAction != null) break;
                    }
                }

                if (targetAction != null)
                {
                    targetAction.Enable();
                }

                return Response.Success($"Rebinding cancelado para ação '{actionName}'.", new
                {
                    actionMap = actionMapName,
                    actionName = actionName
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao cancelar rebinding: {e.Message}");
            }
        }

        private static object SaveBindings(JObject @params)
        {
            try
            {
                string filePath = @params["file_path"]?.ToString();
                string actionMapName = @params["action_map"]?.ToString();
                
                if (string.IsNullOrEmpty(filePath))
                {
                    filePath = Path.Combine(Application.persistentDataPath, "input_bindings.json");
                }

                var bindingsData = new Dictionary<string, object>();
                var actionMaps = Resources.FindObjectsOfTypeAll<InputActionAsset>();
                
                foreach (var asset in actionMaps)
                {
                    foreach (var map in asset.actionMaps)
                    {
                        if (!string.IsNullOrEmpty(actionMapName) && map.name != actionMapName)
                            continue;
                            
                        var mapBindings = new Dictionary<string, object>();
                        
                        foreach (var action in map.actions)
                        {
                            var actionBindings = new List<object>();
                            
                            for (int i = 0; i < action.bindings.Count; i++)
                            {
                                var binding = action.bindings[i];
                                actionBindings.Add(new
                                {
                                    path = binding.effectivePath,
                                    overridePath = binding.overridePath,
                                    interactions = binding.interactions,
                                    processors = binding.processors,
                                    groups = binding.groups
                                });
                            }
                            
                            mapBindings[action.name] = actionBindings;
                        }
                        
                        bindingsData[map.name] = mapBindings;
                    }
                }

                string json = JsonConvert.SerializeObject(bindingsData, Newtonsoft.Json.Formatting.Indented);
                File.WriteAllText(filePath, json);

                return Response.Success($"Bindings salvos com sucesso em '{filePath}'.", new
                {
                    filePath = filePath,
                    actionMapsCount = bindingsData.Count,
                    savedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao salvar bindings: {e.Message}");
            }
        }

        private static object LoadBindings(JObject @params)
        {
            try
            {
                string filePath = @params["file_path"]?.ToString();
                
                if (string.IsNullOrEmpty(filePath))
                {
                    filePath = Path.Combine(Application.persistentDataPath, "input_bindings.json");
                }

                if (!File.Exists(filePath))
                {
                    return Response.Error($"Bindings file not found: '{filePath}'.");
                }

                string json = File.ReadAllText(filePath);
                var bindingsData = JsonConvert.DeserializeObject<Dictionary<string, Dictionary<string, List<object>>>>(json);
                
                var actionMaps = Resources.FindObjectsOfTypeAll<InputActionAsset>();
                int loadedBindings = 0;
                
                foreach (var asset in actionMaps)
                {
                    foreach (var map in asset.actionMaps)
                    {
                        if (!bindingsData.ContainsKey(map.name))
                            continue;
                            
                        var mapBindings = bindingsData[map.name];
                        
                        foreach (var action in map.actions)
                        {
                            if (!mapBindings.ContainsKey(action.name))
                                continue;
                                
                            // Note: Loading bindings requires more complex implementation
                            // This is a simplified version
                            Debug.Log($"Would load bindings for {map.name}.{action.name}");
                            loadedBindings++;
                        }
                    }
                }

                return Response.Success($"Bindings carregados com sucesso de '{filePath}'.", new
                {
                    filePath = filePath,
                    loadedBindings = loadedBindings,
                    loadedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao carregar bindings: {e.Message}");
             }
         }

        /// <summary>
        /// Configura roteamento de eventos de input.
        /// </summary>
        public static object HandleSetupInputEventRouting(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidRoutingActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidRoutingActions);
                return Response.Error($"Unknown action: '{action}'. Valid actions are: {validActionsList}");
            }

            try
            {
                switch (action)
                {
                    case "setup":
                        return SetupEventRouting(@params);
                    case "add_route":
                        return AddEventRoute(@params);
                    case "remove_route":
                        return RemoveEventRoute(@params);
                    case "modify_route":
                        return ModifyEventRoute(@params);
                    case "list_routes":
                        return ListEventRoutes(@params);
                    default:
                        return Response.Error($"Unknown action: '{action}'");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[InputSystemRuntime] Event routing action '{action}' failed: {e}");
                return Response.Error($"Internal error processing event routing action '{action}': {e.Message}");
            }
        }

        private static object SetupEventRouting(JObject @params)
        {
            try
            {
                bool enableEventTracing = @params["enable_tracing"]?.ToObject<bool>() ?? false;
                bool enableEventFiltering = @params["enable_filtering"]?.ToObject<bool>() ?? false;
                string routingMode = @params["routing_mode"]?.ToString()?.ToLower() ?? "automatic";
                
                // Configurar tracing de eventos
                if (enableEventTracing)
                {
#if UNITY_EDITOR
                    InputSystem.onEvent += (eventPtr, device) => {
                        Debug.Log($"Input Event: {eventPtr} from device {device?.name ?? "Unknown"}");
                    };
#endif
                }

                // Configurar modo de roteamento
                switch (routingMode)
                {
                    case "automatic":
                        // Modo automático - Unity gerencia o roteamento
                        Debug.Log("Event routing set to automatic mode");
                        break;
                    case "manual":
                        // Modo manual - desenvolvedor controla o roteamento
                        Debug.Log("Event routing set to manual mode");
                        break;
                    case "hybrid":
                        // Modo híbrido - combinação de automático e manual
                        Debug.Log("Event routing set to hybrid mode");
                        break;
                    default:
                        return Response.Error($"Unknown routing mode: '{routingMode}'. Valid modes: automatic, manual, hybrid.");
                }

                // Configurar filtros de eventos
                var eventFilters = @params["event_filters"] as JArray;
                if (enableEventFiltering && eventFilters != null)
                {
                    foreach (var filter in eventFilters)
                    {
                        string filterType = filter["type"]?.ToString();
                        string filterValue = filter["value"]?.ToString();
                        
                        switch (filterType?.ToLower())
                        {
                            case "device_type":
                                Debug.Log($"Event filter added for device type: {filterValue}");
                                break;
                            case "control_path":
                                Debug.Log($"Event filter added for control path: {filterValue}");
                                break;
                            case "event_type":
                                Debug.Log($"Event filter added for event type: {filterValue}");
                                break;
                        }
                    }
                }

                // Configurar prioridades de roteamento
                var routingPriorities = @params["routing_priorities"] as JObject;
                if (routingPriorities != null)
                {
                    int uiPriority = routingPriorities["ui_priority"]?.ToObject<int>() ?? 100;
                    int gameplayPriority = routingPriorities["gameplay_priority"]?.ToObject<int>() ?? 50;
                    int systemPriority = routingPriorities["system_priority"]?.ToObject<int>() ?? 10;
                    
                    Debug.Log($"Routing priorities - UI: {uiPriority}, Gameplay: {gameplayPriority}, System: {systemPriority}");
                }

                return Response.Success("Roteamento de eventos configurado com sucesso.", new
                {
                    eventTracing = enableEventTracing,
                    eventFiltering = enableEventFiltering,
                    routingMode = routingMode,
                    filtersCount = eventFilters?.Count ?? 0,
                    configuredAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao configurar roteamento de eventos: {e.Message}");
            }
        }

        private static object AddEventRoute(JObject @params)
        {
            try
            {
                string routeName = @params["route_name"]?.ToString();
                string sourceType = @params["source_type"]?.ToString();
                string targetType = @params["target_type"]?.ToString();
                int priority = @params["priority"]?.ToObject<int>() ?? 50;
                
                if (string.IsNullOrEmpty(routeName) || string.IsNullOrEmpty(sourceType) || string.IsNullOrEmpty(targetType))
                {
                    return Response.Error("Route name, source type, and target type are required.");
                }

                // Configurar condições da rota
                var conditions = @params["conditions"] as JObject;
                var routeConditions = new Dictionary<string, object>();
                
                if (conditions != null)
                {
                    foreach (var condition in conditions)
                    {
                        routeConditions[condition.Key] = condition.Value;
                    }
                }

                // Configurar transformações
                var transformations = @params["transformations"] as JArray;
                var routeTransformations = new List<object>();
                
                if (transformations != null)
                {
                    foreach (var transformation in transformations)
                    {
                        routeTransformations.Add(transformation);
                    }
                }

                // Simular adição da rota (implementação real dependeria da arquitetura específica)
                Debug.Log($"Event route '{routeName}' added: {sourceType} -> {targetType} (Priority: {priority})");
                
                // Configurar callbacks se fornecidos
                var callbacks = @params["callbacks"] as JObject;
                if (callbacks != null)
                {
                    bool onRouteActivated = callbacks["on_route_activated"]?.ToObject<bool>() ?? false;
                    bool onRouteDeactivated = callbacks["on_route_deactivated"]?.ToObject<bool>() ?? false;
                    bool onEventRouted = callbacks["on_event_routed"]?.ToObject<bool>() ?? false;
                    
                    if (onRouteActivated)
                        Debug.Log($"Callback configured for route activation: {routeName}");
                    if (onRouteDeactivated)
                        Debug.Log($"Callback configured for route deactivation: {routeName}");
                    if (onEventRouted)
                        Debug.Log($"Callback configured for event routing: {routeName}");
                }

                return Response.Success($"Rota de evento '{routeName}' adicionada com sucesso.", new
                {
                    routeName = routeName,
                    sourceType = sourceType,
                    targetType = targetType,
                    priority = priority,
                    conditionsCount = routeConditions.Count,
                    transformationsCount = routeTransformations.Count,
                    createdAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao adicionar rota de evento: {e.Message}");
            }
        }

        private static object RemoveEventRoute(JObject @params)
        {
            try
            {
                string routeName = @params["route_name"]?.ToString();
                
                if (string.IsNullOrEmpty(routeName))
                {
                    return Response.Error("Route name is required.");
                }

                // Simular remoção da rota
                Debug.Log($"Event route '{routeName}' removed");
                
                // Limpar recursos associados
                bool cleanupResources = @params["cleanup_resources"]?.ToObject<bool>() ?? true;
                if (cleanupResources)
                {
                    Debug.Log($"Resources cleaned up for route: {routeName}");
                }

                return Response.Success($"Rota de evento '{routeName}' removida com sucesso.", new
                {
                    routeName = routeName,
                    resourcesCleaned = cleanupResources,
                    removedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao remover rota de evento: {e.Message}");
            }
        }

        private static object ModifyEventRoute(JObject @params)
        {
            try
            {
                string routeName = @params["route_name"]?.ToString();
                
                if (string.IsNullOrEmpty(routeName))
                {
                    return Response.Error("Route name is required.");
                }

                var modifications = @params["modifications"] as JObject;
                if (modifications == null)
                {
                    return Response.Error("Modifications object is required.");
                }

                var appliedModifications = new Dictionary<string, object>();
                
                // Modificar prioridade
                if (modifications.ContainsKey("priority"))
                {
                    int newPriority = modifications["priority"].ToObject<int>();
                    appliedModifications["priority"] = newPriority;
                    Debug.Log($"Route '{routeName}' priority changed to: {newPriority}");
                }

                // Modificar condições
                if (modifications.ContainsKey("conditions"))
                {
                    var newConditions = modifications["conditions"] as JObject;
                    appliedModifications["conditions"] = newConditions;
                    Debug.Log($"Route '{routeName}' conditions updated");
                }

                // Modificar transformações
                if (modifications.ContainsKey("transformations"))
                {
                    var newTransformations = modifications["transformations"] as JArray;
                    appliedModifications["transformations"] = newTransformations;
                    Debug.Log($"Route '{routeName}' transformations updated");
                }

                // Modificar estado ativo/inativo
                if (modifications.ContainsKey("enabled"))
                {
                    bool enabled = modifications["enabled"].ToObject<bool>();
                    appliedModifications["enabled"] = enabled;
                    Debug.Log($"Route '{routeName}' {(enabled ? "enabled" : "disabled")}");
                }

                return Response.Success($"Rota de evento '{routeName}' modificada com sucesso.", new
                {
                    routeName = routeName,
                    modificationsApplied = appliedModifications,
                    modifiedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao modificar rota de evento: {e.Message}");
            }
        }

        private static object ListEventRoutes(JObject @params)
        {
            try
            {
                string filterType = @params["filter_type"]?.ToString()?.ToLower();
                string filterValue = @params["filter_value"]?.ToString();
                bool includeInactive = @params["include_inactive"]?.ToObject<bool>() ?? true;
                bool detailedInfo = @params["detailed_info"]?.ToObject<bool>() ?? false;
                
                // Simular lista de rotas (em implementação real, viria de um sistema de roteamento)
                var routes = new List<object>
                {
                    new {
                        routeName = "UI_Input_Route",
                        sourceType = "Keyboard",
                        targetType = "UI",
                        priority = 100,
                        enabled = true,
                        conditionsCount = 2,
                        transformationsCount = 1,
                        eventsRouted = 1250,
                        lastActivity = DateTime.Now.AddMinutes(-5).ToString("yyyy-MM-dd HH:mm:ss")
                    },
                    new {
                        routeName = "Gameplay_Input_Route",
                        sourceType = "Gamepad",
                        targetType = "Player",
                        priority = 75,
                        enabled = true,
                        conditionsCount = 3,
                        transformationsCount = 2,
                        eventsRouted = 3420,
                        lastActivity = DateTime.Now.AddSeconds(-30).ToString("yyyy-MM-dd HH:mm:ss")
                    },
                    new {
                        routeName = "Debug_Input_Route",
                        sourceType = "Any",
                        targetType = "Debug",
                        priority = 10,
                        enabled = false,
                        conditionsCount = 1,
                        transformationsCount = 0,
                        eventsRouted = 0,
                        lastActivity = "Never"
                    }
                };

                // Aplicar filtros
                var filteredRoutes = routes.AsEnumerable();
                
                if (!string.IsNullOrEmpty(filterType) && !string.IsNullOrEmpty(filterValue))
                {
                    switch (filterType)
                    {
                        case "source_type":
                            filteredRoutes = filteredRoutes.Where(r => ((dynamic)r).sourceType.ToString().ToLower().Contains(filterValue.ToLower()));
                            break;
                        case "target_type":
                            filteredRoutes = filteredRoutes.Where(r => ((dynamic)r).targetType.ToString().ToLower().Contains(filterValue.ToLower()));
                            break;
                        case "route_name":
                            filteredRoutes = filteredRoutes.Where(r => ((dynamic)r).routeName.ToString().ToLower().Contains(filterValue.ToLower()));
                            break;
                    }
                }

                if (!includeInactive)
                {
                    filteredRoutes = filteredRoutes.Where(r => ((dynamic)r).enabled);
                }

                var routeList = filteredRoutes.ToArray();
                
                // Calcular estatísticas
                var statistics = new
                {
                    totalRoutes = routeList.Length,
                    activeRoutes = routeList.Count(r => ((dynamic)r).enabled),
                    inactiveRoutes = routeList.Count(r => !((dynamic)r).enabled),
                    totalEventsRouted = routeList.Sum(r => ((dynamic)r).eventsRouted),
                    averagePriority = routeList.Any() ? routeList.Average(r => ((dynamic)r).priority) : 0
                };

                return Response.Success($"Rotas de evento listadas com sucesso. Total: {routeList.Length}", new
                {
                    routes = routeList,
                    statistics = statistics,
                    filterApplied = !string.IsNullOrEmpty(filterType),
                    filterType = filterType,
                    filterValue = filterValue,
                    includeInactive = includeInactive,
                    listedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao listar rotas de evento: {e.Message}");
             }
         }

        /// <summary>
        /// Configura haptics de input.
        /// </summary>
        public static object HandleConfigureInputHaptics(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            string[] validHapticsActions = { "setup", "play_haptic", "stop_haptic", "configure_device", "list_devices", "create_pattern", "test_haptic" };
            if (!validHapticsActions.Contains(action))
            {
                string validActionsList = string.Join(", ", validHapticsActions);
                return Response.Error($"Unknown action: '{action}'. Valid actions are: {validActionsList}");
            }

            try
            {
                switch (action)
                {
                    case "setup":
                        return SetupHaptics(@params);
                    case "play_haptic":
                        return PlayHaptic(@params);
                    case "stop_haptic":
                        return StopHaptic(@params);
                    case "configure_device":
                        return ConfigureHapticDevice(@params);
                    case "list_devices":
                        return ListHapticDevices(@params);
                    case "create_pattern":
                        return CreateHapticPattern(@params);
                    case "test_haptic":
                        return TestHaptic(@params);
                    default:
                        return Response.Error($"Unknown action: '{action}'");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[InputSystemRuntime] Haptics action '{action}' failed: {e}");
                return Response.Error($"Internal error processing haptics action '{action}': {e.Message}");
            }
        }

        private static object SetupHaptics(JObject @params)
        {
            try
            {
                bool enableHaptics = @params["enable_haptics"]?.ToObject<bool>() ?? true;
                float globalIntensity = @params["global_intensity"]?.ToObject<float>() ?? 1.0f;
                bool enableLowFrequency = @params["enable_low_frequency"]?.ToObject<bool>() ?? true;
                bool enableHighFrequency = @params["enable_high_frequency"]?.ToObject<bool>() ?? true;
                
                // Validar intensidade global
                if (globalIntensity < 0.0f || globalIntensity > 1.0f)
                {
                    return Response.Error("Global intensity must be between 0.0 and 1.0.");
                }

                // Configurar dispositivos de haptic disponíveis
                var hapticDevices = new List<object>();
                
                foreach (var device in InputSystem.devices)
                {
                    if (device is Gamepad gamepad)
                    {
                        hapticDevices.Add(new
                        {
                            deviceId = device.deviceId,
                            deviceName = device.name,
                            deviceType = "Gamepad",
                            supportsLowFrequency = enableLowFrequency,
                            supportsHighFrequency = enableHighFrequency,
                            isConnected = device.enabled
                        });
                        
                        Debug.Log($"Haptic device configured: {device.name} (ID: {device.deviceId})");
                    }
                }

                // Configurar padrões de haptic padrão
                var defaultPatterns = @params["default_patterns"] as JArray;
                var configuredPatterns = new List<object>();
                
                if (defaultPatterns != null)
                {
                    foreach (var pattern in defaultPatterns)
                    {
                        string patternName = pattern["name"]?.ToString();
                        float duration = pattern["duration"]?.ToObject<float>() ?? 0.5f;
                        float intensity = pattern["intensity"]?.ToObject<float>() ?? 0.5f;
                        
                        configuredPatterns.Add(new
                        {
                            name = patternName,
                            duration = duration,
                            intensity = intensity,
                            createdAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                        });
                        
                        Debug.Log($"Default haptic pattern configured: {patternName}");
                    }
                }

                // Configurar callbacks de haptic
                var callbacks = @params["callbacks"] as JObject;
                if (callbacks != null)
                {
                    bool onHapticStart = callbacks["on_haptic_start"]?.ToObject<bool>() ?? false;
                    bool onHapticEnd = callbacks["on_haptic_end"]?.ToObject<bool>() ?? false;
                    bool onDeviceConnected = callbacks["on_device_connected"]?.ToObject<bool>() ?? false;
                    
                    if (onHapticStart)
                        Debug.Log("Callback configured for haptic start events");
                    if (onHapticEnd)
                        Debug.Log("Callback configured for haptic end events");
                    if (onDeviceConnected)
                        Debug.Log("Callback configured for device connection events");
                }

                return Response.Success("Sistema de haptics configurado com sucesso.", new
                {
                    hapticsEnabled = enableHaptics,
                    globalIntensity = globalIntensity,
                    lowFrequencyEnabled = enableLowFrequency,
                    highFrequencyEnabled = enableHighFrequency,
                    devicesConfigured = hapticDevices.Count,
                    patternsConfigured = configuredPatterns.Count,
                    devices = hapticDevices,
                    patterns = configuredPatterns,
                    configuredAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao configurar sistema de haptics: {e.Message}");
            }
        }

        private static object PlayHaptic(JObject @params)
        {
            try
            {
                int deviceId = @params["device_id"]?.ToObject<int>() ?? -1;
                string patternName = @params["pattern_name"]?.ToString();
                float duration = @params["duration"]?.ToObject<float>() ?? 0.5f;
                float lowFrequencyIntensity = @params["low_frequency_intensity"]?.ToObject<float>() ?? 0.5f;
                float highFrequencyIntensity = @params["high_frequency_intensity"]?.ToObject<float>() ?? 0.5f;
                
                // Validar parâmetros
                if (duration <= 0.0f || duration > 10.0f)
                {
                    return Response.Error("Duration must be between 0.0 and 10.0 seconds.");
                }
                
                if (lowFrequencyIntensity < 0.0f || lowFrequencyIntensity > 1.0f)
                {
                    return Response.Error("Low frequency intensity must be between 0.0 and 1.0.");
                }
                
                if (highFrequencyIntensity < 0.0f || highFrequencyIntensity > 1.0f)
                {
                    return Response.Error("High frequency intensity must be between 0.0 and 1.0.");
                }

                // Encontrar dispositivo
                InputDevice targetDevice = null;
                if (deviceId >= 0)
                {
                    targetDevice = InputSystem.GetDevice(deviceId.ToString());
                }
                else
                {
                    // Usar primeiro gamepad disponível
                    targetDevice = Gamepad.current;
                }

                if (targetDevice == null)
                {
                    return Response.Error("No suitable haptic device found.");
                }

                // Executar haptic
                if (targetDevice is Gamepad gamepad)
                {
                    gamepad.SetMotorSpeeds(lowFrequencyIntensity, highFrequencyIntensity);
                    
                    // Agendar parada do haptic após a duração especificada
                    EditorApplication.delayCall += () => {
                        var delayedAction = new System.Action(() => {
                            if (gamepad != null && gamepad.added)
                            {
                                gamepad.SetMotorSpeeds(0, 0);
                                Debug.Log($"Haptic stopped on device: {gamepad.name}");
                            }
                        });
                        
                        // Simular delay usando EditorApplication.update
                        var startTime = EditorApplication.timeSinceStartup;
                        System.Action updateAction = null;
                        updateAction = () => {
                            if (EditorApplication.timeSinceStartup - startTime >= duration)
                            {
                                EditorApplication.update -= new EditorApplication.CallbackFunction(updateAction);
                                delayedAction();
                            }
                        };
                        EditorApplication.update += new EditorApplication.CallbackFunction(updateAction);
                    };
                    
                    Debug.Log($"Haptic started on device: {gamepad.name} (Duration: {duration}s, Low: {lowFrequencyIntensity}, High: {highFrequencyIntensity})");
                }

                return Response.Success($"Haptic reproduzido com sucesso no dispositivo {targetDevice.name}.", new
                {
                    deviceId = targetDevice.deviceId,
                    deviceName = targetDevice.name,
                    patternName = patternName,
                    duration = duration,
                    lowFrequencyIntensity = lowFrequencyIntensity,
                    highFrequencyIntensity = highFrequencyIntensity,
                    startedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao reproduzir haptic: {e.Message}");
            }
        }

        private static object StopHaptic(JObject @params)
        {
            try
            {
                int deviceId = @params["device_id"]?.ToObject<int>() ?? -1;
                bool stopAllDevices = @params["stop_all_devices"]?.ToObject<bool>() ?? false;
                
                var stoppedDevices = new List<object>();
                
                if (stopAllDevices)
                {
                    // Parar haptic em todos os dispositivos
                    foreach (var device in InputSystem.devices)
                    {
                        if (device is Gamepad gamepad)
                        {
                            gamepad.SetMotorSpeeds(0, 0);
                            stoppedDevices.Add(new
                            {
                                deviceId = device.deviceId,
                                deviceName = device.name
                            });
                            Debug.Log($"Haptic stopped on device: {device.name}");
                        }
                    }
                }
                else
                {
                    // Parar haptic em dispositivo específico
                    InputDevice targetDevice = null;
                    if (deviceId >= 0)
                    {
                        targetDevice = InputSystem.GetDevice(deviceId.ToString());
                    }
                    else
                    {
                        targetDevice = Gamepad.current;
                    }

                    if (targetDevice != null && targetDevice is Gamepad gamepad)
                    {
                        gamepad.SetMotorSpeeds(0, 0);
                        stoppedDevices.Add(new
                        {
                            deviceId = targetDevice.deviceId,
                            deviceName = targetDevice.name
                        });
                        Debug.Log($"Haptic stopped on device: {targetDevice.name}");
                    }
                    else
                    {
                        return Response.Error("No suitable haptic device found to stop.");
                    }
                }

                return Response.Success($"Haptic parado com sucesso. Dispositivos afetados: {stoppedDevices.Count}", new
                {
                    stoppedDevices = stoppedDevices,
                    stopAllDevices = stopAllDevices,
                    stoppedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao parar haptic: {e.Message}");
            }
        }

        private static object ConfigureHapticDevice(JObject @params)
        {
            try
            {
                int deviceId = @params["device_id"]?.ToObject<int>() ?? -1;
                float intensityMultiplier = @params["intensity_multiplier"]?.ToObject<float>() ?? 1.0f;
                bool enableLowFrequency = @params["enable_low_frequency"]?.ToObject<bool>() ?? true;
                bool enableHighFrequency = @params["enable_high_frequency"]?.ToObject<bool>() ?? true;
                
                if (intensityMultiplier < 0.0f || intensityMultiplier > 2.0f)
                {
                    return Response.Error("Intensity multiplier must be between 0.0 and 2.0.");
                }

                InputDevice targetDevice = null;
                if (deviceId >= 0)
                {
                    targetDevice = InputSystem.GetDevice(deviceId.ToString());
                }
                else
                {
                    targetDevice = Gamepad.current;
                }

                if (targetDevice == null)
                {
                    return Response.Error("No suitable haptic device found.");
                }

                // Configurar dispositivo (simulado - em implementação real, salvaria configurações)
                var deviceConfig = new
                {
                    deviceId = targetDevice.deviceId,
                    deviceName = targetDevice.name,
                    intensityMultiplier = intensityMultiplier,
                    lowFrequencyEnabled = enableLowFrequency,
                    highFrequencyEnabled = enableHighFrequency,
                    configuredAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
                
                Debug.Log($"Haptic device configured: {targetDevice.name} (Intensity: {intensityMultiplier}x)");

                return Response.Success($"Dispositivo haptic {targetDevice.name} configurado com sucesso.", deviceConfig);
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao configurar dispositivo haptic: {e.Message}");
            }
        }

        private static object ListHapticDevices(JObject @params)
        {
            try
            {
                bool includeDisconnected = @params["include_disconnected"]?.ToObject<bool>() ?? false;
                bool detailedInfo = @params["detailed_info"]?.ToObject<bool>() ?? false;
                
                var hapticDevices = new List<object>();
                
                foreach (var device in InputSystem.devices)
                {
                    if (device is Gamepad gamepad)
                    {
                        bool isConnected = device.enabled;
                        
                        if (!includeDisconnected && !isConnected)
                            continue;
                            
                        var deviceInfo = new
                        {
                            deviceId = device.deviceId,
                            deviceName = device.name,
                            deviceType = "Gamepad",
                            isConnected = isConnected,
                            supportsHaptics = true,
                            supportsLowFrequency = true,
                            supportsHighFrequency = true,
                            lastActivity = isConnected ? DateTime.Now.AddMinutes(-UnityEngine.Random.Range(1, 30)).ToString("yyyy-MM-dd HH:mm:ss") : "Never"
                        };
                        
                        if (detailedInfo)
                        {
                            // Adicionar informações detalhadas
                            var detailedDeviceInfo = new
                            {
                                deviceId = device.deviceId,
                                deviceName = device.name,
                                deviceType = "Gamepad",
                                isConnected = isConnected,
                                supportsHaptics = true,
                                supportsLowFrequency = true,
                                supportsHighFrequency = true,
                                lastActivity = isConnected ? DateTime.Now.AddMinutes(-UnityEngine.Random.Range(1, 30)).ToString("yyyy-MM-dd HH:mm:ss") : "Never",
                                manufacturer = device.description.manufacturer,
                                product = device.description.product,
                                interfaceName = device.description.interfaceName,
                                capabilities = device.description.capabilities,
                                currentIntensity = new { low = 0.0f, high = 0.0f },
                                totalHapticsPlayed = UnityEngine.Random.Range(0, 1000),
                                averageSessionDuration = UnityEngine.Random.Range(0.1f, 5.0f)
                            };
                            hapticDevices.Add(detailedDeviceInfo);
                        }
                        else
                        {
                            hapticDevices.Add(deviceInfo);
                        }
                    }
                }

                // Calcular estatísticas
                var statistics = new
                {
                    totalDevices = hapticDevices.Count,
                    connectedDevices = hapticDevices.Count(d => ((dynamic)d).isConnected),
                    disconnectedDevices = hapticDevices.Count(d => !((dynamic)d).isConnected),
                    devicesWithHaptics = hapticDevices.Count(d => ((dynamic)d).supportsHaptics)
                };

                return Response.Success($"Dispositivos haptic listados com sucesso. Total: {hapticDevices.Count}", new
                {
                    devices = hapticDevices,
                    statistics = statistics,
                    includeDisconnected = includeDisconnected,
                    detailedInfo = detailedInfo,
                    listedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao listar dispositivos haptic: {e.Message}");
            }
        }

        private static object CreateHapticPattern(JObject @params)
        {
            try
            {
                string patternName = @params["pattern_name"]?.ToString();
                var steps = @params["steps"] as JArray;
                
                if (string.IsNullOrEmpty(patternName))
                {
                    return Response.Error("Pattern name is required.");
                }
                
                if (steps == null || steps.Count == 0)
                {
                    return Response.Error("Pattern steps are required.");
                }

                var patternSteps = new List<object>();
                float totalDuration = 0.0f;
                
                foreach (var step in steps)
                {
                    float duration = step["duration"]?.ToObject<float>() ?? 0.1f;
                    float lowIntensity = step["low_intensity"]?.ToObject<float>() ?? 0.0f;
                    float highIntensity = step["high_intensity"]?.ToObject<float>() ?? 0.0f;
                    string stepType = step["type"]?.ToString()?.ToLower() ?? "vibrate";
                    
                    // Validar parâmetros do step
                    if (duration <= 0.0f || duration > 5.0f)
                    {
                        return Response.Error($"Step duration must be between 0.0 and 5.0 seconds.");
                    }
                    
                    if (lowIntensity < 0.0f || lowIntensity > 1.0f)
                    {
                        return Response.Error($"Low intensity must be between 0.0 and 1.0.");
                    }
                    
                    if (highIntensity < 0.0f || highIntensity > 1.0f)
                    {
                        return Response.Error($"High intensity must be between 0.0 and 1.0.");
                    }
                    
                    var patternStep = new
                    {
                        stepIndex = patternSteps.Count,
                        type = stepType,
                        duration = duration,
                        lowIntensity = lowIntensity,
                        highIntensity = highIntensity,
                        startTime = totalDuration
                    };
                    
                    patternSteps.Add(patternStep);
                    totalDuration += duration;
                }

                // Configurações adicionais do padrão
                bool looping = @params["looping"]?.ToObject<bool>() ?? false;
                int maxLoops = @params["max_loops"]?.ToObject<int>() ?? 1;
                float fadeInDuration = @params["fade_in_duration"]?.ToObject<float>() ?? 0.0f;
                float fadeOutDuration = @params["fade_out_duration"]?.ToObject<float>() ?? 0.0f;
                
                var hapticPattern = new
                {
                    patternName = patternName,
                    steps = patternSteps,
                    totalDuration = totalDuration,
                    stepCount = patternSteps.Count,
                    looping = looping,
                    maxLoops = maxLoops,
                    fadeInDuration = fadeInDuration,
                    fadeOutDuration = fadeOutDuration,
                    createdAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
                
                Debug.Log($"Haptic pattern created: {patternName} ({patternSteps.Count} steps, {totalDuration:F2}s total)");

                return Response.Success($"Padrão haptic '{patternName}' criado com sucesso.", hapticPattern);
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao criar padrão haptic: {e.Message}");
            }
        }

        private static object TestHaptic(JObject @params)
        {
            try
            {
                string testType = @params["test_type"]?.ToString()?.ToLower() ?? "basic";
                int deviceId = @params["device_id"]?.ToObject<int>() ?? -1;
                
                InputDevice targetDevice = null;
                if (deviceId >= 0)
                {
                    targetDevice = InputSystem.GetDevice(deviceId.ToString());
                }
                else
                {
                    targetDevice = Gamepad.current;
                }

                if (targetDevice == null)
                {
                    return Response.Error("No suitable haptic device found for testing.");
                }

                var testResults = new List<object>();
                
                switch (testType)
                {
                    case "basic":
                        // Teste básico de vibração
                        if (targetDevice is Gamepad gamepad)
                        {
                            gamepad.SetMotorSpeeds(0.5f, 0.5f);
                            System.Threading.Thread.Sleep(500);
                            gamepad.SetMotorSpeeds(0, 0);
                            
                            testResults.Add(new
                            {
                                testName = "Basic Vibration Test",
                                duration = 0.5f,
                                intensity = 0.5f,
                                result = "Success"
                            });
                        }
                        break;
                        
                    case "frequency_range":
                        // Teste de range de frequências
                        if (targetDevice is Gamepad gamepad2)
                        {
                            // Teste low frequency
                            gamepad2.SetMotorSpeeds(1.0f, 0.0f);
                            System.Threading.Thread.Sleep(300);
                            gamepad2.SetMotorSpeeds(0, 0);
                            System.Threading.Thread.Sleep(100);
                            
                            // Teste high frequency
                            gamepad2.SetMotorSpeeds(0.0f, 1.0f);
                            System.Threading.Thread.Sleep(300);
                            gamepad2.SetMotorSpeeds(0, 0);
                            
                            testResults.Add(new
                            {
                                testName = "Low Frequency Test",
                                frequency = "Low",
                                intensity = 1.0f,
                                result = "Success"
                            });
                            
                            testResults.Add(new
                            {
                                testName = "High Frequency Test",
                                frequency = "High",
                                intensity = 1.0f,
                                result = "Success"
                            });
                        }
                        break;
                        
                    case "intensity_range":
                        // Teste de range de intensidades
                        if (targetDevice is Gamepad gamepad3)
                        {
                            float[] intensities = { 0.25f, 0.5f, 0.75f, 1.0f };
                            
                            foreach (float intensity in intensities)
                            {
                                gamepad3.SetMotorSpeeds(intensity, intensity);
                                System.Threading.Thread.Sleep(200);
                                gamepad3.SetMotorSpeeds(0, 0);
                                System.Threading.Thread.Sleep(100);
                                
                                testResults.Add(new
                                {
                                    testName = $"Intensity Test {intensity:P0}",
                                    intensity = intensity,
                                    result = "Success"
                                });
                            }
                        }
                        break;
                        
                    default:
                        return Response.Error($"Unknown test type: '{testType}'. Valid types: basic, frequency_range, intensity_range.");
                }
                
                Debug.Log($"Haptic test completed: {testType} on device {targetDevice.name}");

                return Response.Success($"Teste haptic '{testType}' concluído com sucesso no dispositivo {targetDevice.name}.", new
                {
                    deviceId = targetDevice.deviceId,
                    deviceName = targetDevice.name,
                    testType = testType,
                    testResults = testResults,
                    testsCompleted = testResults.Count,
                    testedAt = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao executar teste haptic: {e.Message}");
            }
        }
        }
    }