using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.Timeline;
using UnityEngine.Playables;
using UnityMcpBridge.Editor.Helpers;
using Unity.Cinemachine;
using Unity.Cinemachine.TargetTracking;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using System.IO;

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// Handles operations for automated cinematics generation.
    /// </summary>
    public static class AutomatedCinematics
    {
        private static readonly List<string> ValidActions = new List<string>
        {
            "create",
            "modify",
            "delete",
            "list",
            "get_info"
        };

        public static object HandleCommand(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidActions);
                return Response.Error(
                    $"Unknown action: '{action}'. Valid actions are: {validActionsList}"
                );
            }

            try
            {
                switch (action)
                {
                    case "create":
                        return CreateCinematicElement(@params);
                    case "modify":
                        return ModifyCinematicElement(@params);
                    case "delete":
                        return DeleteCinematicElement(@params);
                    case "list":
                        return ListCinematicElements(@params);
                    case "get_info":
                        return GetCinematicInfo(@params);
                    default:
                        string validActionsListDefault = string.Join(", ", ValidActions);
                        return Response.Error(
                            $"Unknown action: '{action}'. Valid actions are: {validActionsListDefault}"
                        );
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[AutomatedCinematics] Action '{action}' failed: {e}");
                return Response.Error(
                    $"Internal error processing action '{action}': {e.Message}"
                );
            }
        }

        private static object CreateCinematicElement(JObject @params)
        {
            try
            {
                string elementType = @params["element_type"]?.ToString();
                
                switch (elementType)
                {
                    case "timeline":
                        return CreateTimelineFromEvents(@params);
                    case "camera":
                        return CreateCinemachineCamera(@params);
                    case "shot_list":
                        return CreateDynamicShotList(@params);
                    case "animation_sequence":
                        return CreateAnimationSequence(@params);
                    case "audio_track":
                        return CreateAudioTrack(@params);
                    case "trigger":
                        return CreateCinematicTrigger(@params);
                    case "post_processing":
                        return CreatePostProcessingControl(@params);
                    default:
                        return Response.Error($"Unknown element type: {elementType}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error creating cinematic element: {e.Message}");
            }
        }

        private static object CreateTimelineFromEvents(JObject @params)
        {
            string eventType = @params["event_type"]?.ToString() ?? "generic";
            string timelineName = @params["timeline_name"]?.ToString() ?? $"Timeline_{eventType}_{DateTime.Now:yyyyMMdd_HHmmss}";
            float duration = @params["duration"]?.ToObject<float>() ?? 10.0f;
            bool autoKeyframes = @params["auto_keyframes"]?.ToObject<bool>() ?? true;
            
            // Create Timeline Asset
            string assetPath = $"Assets/Timelines/{timelineName}.playable";
            EnsureDirectoryExists(Path.GetDirectoryName(assetPath));
            
            TimelineAsset timelineAsset = ScriptableObject.CreateInstance<TimelineAsset>();
            AssetDatabase.CreateAsset(timelineAsset, assetPath);
            
            // Set timeline duration
            timelineAsset.fixedDuration = duration;
            
            // Create tracks based on event type
            CreateTracksForEventType(timelineAsset, eventType, @params);
            
            // Auto-generate keyframes if requested
            if (autoKeyframes)
            {
                GenerateAutoKeyframes(timelineAsset, eventType, @params);
            }
            
            EditorUtility.SetDirty(timelineAsset);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            
            return Response.Success("Timeline created successfully.", new
            {
                timelineAsset = assetPath,
                duration = duration,
                eventType = eventType,
                trackCount = timelineAsset.GetRootTracks().Count()
            });
        }

        private static void CreateTracksForEventType(TimelineAsset timeline, string eventType, JObject @params)
        {
            switch (eventType.ToLower())
            {
                case "boss_battle":
                    CreateBossBattleTracks(timeline, @params);
                    break;
                case "cutscene":
                    CreateCutsceneTracks(timeline, @params);
                    break;
                case "dialogue":
                    CreateDialogueTracks(timeline, @params);
                    break;
                case "action_sequence":
                    CreateActionSequenceTracks(timeline, @params);
                    break;
                default:
                    CreateGenericTracks(timeline, @params);
                    break;
            }
        }

        private static void CreateBossBattleTracks(TimelineAsset timeline, JObject @params)
        {
            // Create Cinemachine track for dynamic camera work
            var cinemachineTrack = timeline.CreateTrack<CinemachineTrack>(null, "Boss Battle Cameras");
            
            // Create Animation track for boss animations
            var animationTrack = timeline.CreateTrack<AnimationTrack>(null, "Boss Animations");
            
            // Create Audio track for battle music and effects
            var audioTrack = timeline.CreateTrack<AudioTrack>(null, "Battle Audio");
            
            // Create Activation track for special effects
            var activationTrack = timeline.CreateTrack<ActivationTrack>(null, "Battle Effects");
        }

        private static void CreateCutsceneTracks(TimelineAsset timeline, JObject @params)
        {
            // Create multiple camera tracks for different shots
            var mainCameraTrack = timeline.CreateTrack<CinemachineTrack>(null, "Main Camera");
            var closeupTrack = timeline.CreateTrack<CinemachineTrack>(null, "Closeup Shots");
            
            // Create character animation tracks
            var characterAnimTrack = timeline.CreateTrack<AnimationTrack>(null, "Character Animations");
            
            // Create dialogue audio track
            var dialogueTrack = timeline.CreateTrack<AudioTrack>(null, "Dialogue");
            
            // Create ambient audio track
            var ambientTrack = timeline.CreateTrack<AudioTrack>(null, "Ambient Audio");
        }

        private static void CreateDialogueTracks(TimelineAsset timeline, JObject @params)
        {
            // Create camera track for dialogue shots
            var dialogueCameraTrack = timeline.CreateTrack<CinemachineTrack>(null, "Dialogue Cameras");
            
            // Create voice audio track
            var voiceTrack = timeline.CreateTrack<AudioTrack>(null, "Voice Audio");
            
            // Create subtitle track (using Playable track)
            var subtitleTrack = timeline.CreateTrack<PlayableTrack>(null, "Subtitles");
            
            // Create character expression animations
            var expressionTrack = timeline.CreateTrack<AnimationTrack>(null, "Character Expressions");
        }

        private static void CreateActionSequenceTracks(TimelineAsset timeline, JObject @params)
        {
            // Create dynamic camera track
            var actionCameraTrack = timeline.CreateTrack<CinemachineTrack>(null, "Action Cameras");
            
            // Create character action animations
            var actionAnimTrack = timeline.CreateTrack<AnimationTrack>(null, "Action Animations");
            
            // Create sound effects track
            var sfxTrack = timeline.CreateTrack<AudioTrack>(null, "Sound Effects");
            
            // Create particle effects track
            var vfxTrack = timeline.CreateTrack<ActivationTrack>(null, "Visual Effects");
            
            // Create music track
            var musicTrack = timeline.CreateTrack<AudioTrack>(null, "Action Music");
        }

        private static void CreateGenericTracks(TimelineAsset timeline, JObject @params)
        {
            // Create advanced tracks for cinematic events using Unity 6.2 Timeline
            var cameraTrack = timeline.CreateTrack<CinemachineTrack>(null, "Camera");
            var animationTrack = timeline.CreateTrack<AnimationTrack>(null, "Animation");
            var audioTrack = timeline.CreateTrack<AudioTrack>(null, "Audio");
        }

        private static void GenerateAutoKeyframes(TimelineAsset timeline, string eventType, JObject @params)
        {
            float duration = (float)timeline.fixedDuration;
            
            // Generate keyframes based on event type and duration
            switch (eventType.ToLower())
            {
                case "boss_battle":
                    GenerateBossBattleKeyframes(timeline, duration);
                    break;
                case "cutscene":
                    GenerateCutsceneKeyframes(timeline, duration);
                    break;
                case "dialogue":
                    GenerateDialogueKeyframes(timeline, duration);
                    break;
                case "action_sequence":
                    GenerateActionKeyframes(timeline, duration);
                    break;
            }
        }

        private static void GenerateBossBattleKeyframes(TimelineAsset timeline, float duration)
        {
            // Generate keyframes for boss battle: intro, battle phases, climax, resolution
            float introDuration = duration * 0.1f;
            float battleDuration = duration * 0.7f;
            float climaxDuration = duration * 0.15f;
            float resolutionDuration = duration * 0.05f;
            
            // Add markers for different phases
            timeline.CreateMarkerTrack();
            MarkerTrack markerTrack = timeline.GetOutputTrack(0) as MarkerTrack;
            if (markerTrack == null)
            {
                markerTrack = timeline.CreateTrack<MarkerTrack>();
            }
            SignalEmitter marker1 = markerTrack.CreateMarker<SignalEmitter>(0);
            marker1.asset = CreateSignalAsset("BossBattle_Start");
            SignalEmitter marker2 = markerTrack.CreateMarker<SignalEmitter>(introDuration);
            marker2.asset = CreateSignalAsset("BossBattle_Phase1");
            SignalEmitter marker3 = markerTrack.CreateMarker<SignalEmitter>(introDuration + battleDuration * 0.5f);
            marker3.asset = CreateSignalAsset("BossBattle_Phase2");
            SignalEmitter marker4 = markerTrack.CreateMarker<SignalEmitter>(introDuration + battleDuration);
            marker4.asset = CreateSignalAsset("BossBattle_Climax");
            SignalEmitter marker5 = markerTrack.CreateMarker<SignalEmitter>(duration - resolutionDuration);
            marker5.asset = CreateSignalAsset("BossBattle_End");
        }

        private static void GenerateCutsceneKeyframes(TimelineAsset timeline, float duration)
        {
            // Generate keyframes for cutscene: establishing shot, character focus, dialogue, conclusion
            float shotDuration = duration / 4.0f;
            
            timeline.CreateMarkerTrack();
            MarkerTrack markerTrack = timeline.GetOutputTrack(0) as MarkerTrack;
            if (markerTrack == null)
            {
                markerTrack = timeline.CreateTrack<MarkerTrack>();
            }
            SignalEmitter marker1 = markerTrack.CreateMarker<SignalEmitter>(0);
            marker1.asset = CreateSignalAsset("Cutscene_EstablishingShot");
            SignalEmitter marker2 = markerTrack.CreateMarker<SignalEmitter>(shotDuration);
            marker2.asset = CreateSignalAsset("Cutscene_CharacterFocus");
            SignalEmitter marker3 = markerTrack.CreateMarker<SignalEmitter>(shotDuration * 2);
            marker3.asset = CreateSignalAsset("Cutscene_Dialogue");
            SignalEmitter marker4 = markerTrack.CreateMarker<SignalEmitter>(shotDuration * 3);
            marker4.asset = CreateSignalAsset("Cutscene_Conclusion");
        }

        private static void GenerateDialogueKeyframes(TimelineAsset timeline, float duration)
        {
            // Generate keyframes for dialogue: speaker changes, emotional beats
            int speakerChanges = Mathf.Max(2, Mathf.RoundToInt(duration / 3.0f));
            float intervalDuration = duration / speakerChanges;
            
            timeline.CreateMarkerTrack();
            MarkerTrack markerTrack = timeline.GetOutputTrack(0) as MarkerTrack;
            if (markerTrack == null)
            {
                markerTrack = timeline.CreateTrack<MarkerTrack>();
            }
            for (int i = 0; i < speakerChanges; i++)
            {
                float time = i * intervalDuration;
                SignalEmitter marker = markerTrack.CreateMarker<SignalEmitter>(time);
                marker.asset = CreateSignalAsset($"Dialogue_Speaker{i + 1}");
            }
        }

        private static void GenerateActionKeyframes(TimelineAsset timeline, float duration)
        {
            // Generate keyframes for action: buildup, action beats, climax, resolution
            float buildupDuration = duration * 0.2f;
            float actionDuration = duration * 0.6f;
            float climaxDuration = duration * 0.15f;
            float resolutionDuration = duration * 0.05f;
            
            timeline.CreateMarkerTrack();
            MarkerTrack markerTrack = timeline.GetOutputTrack(0) as MarkerTrack;
            if (markerTrack == null)
            {
                markerTrack = timeline.CreateTrack<MarkerTrack>();
            }
            SignalEmitter marker1 = markerTrack.CreateMarker<SignalEmitter>(0);
            marker1.asset = CreateSignalAsset("Action_Buildup");
            SignalEmitter marker2 = markerTrack.CreateMarker<SignalEmitter>(buildupDuration);
            marker2.asset = CreateSignalAsset("Action_Start");
            SignalEmitter marker3 = markerTrack.CreateMarker<SignalEmitter>(buildupDuration + actionDuration * 0.5f);
            marker3.asset = CreateSignalAsset("Action_Peak");
            SignalEmitter marker4 = markerTrack.CreateMarker<SignalEmitter>(buildupDuration + actionDuration);
            marker4.asset = CreateSignalAsset("Action_Climax");
            SignalEmitter marker5 = markerTrack.CreateMarker<SignalEmitter>(duration - resolutionDuration);
            marker5.asset = CreateSignalAsset("Action_Resolution");
        }

        private static SignalAsset CreateSignalAsset(string signalName)
        {
            string assetPath = $"Assets/Signals/{signalName}.asset";
            EnsureDirectoryExists(Path.GetDirectoryName(assetPath));
            
            SignalAsset signal = ScriptableObject.CreateInstance<SignalAsset>();
            signal.name = signalName;
            AssetDatabase.CreateAsset(signal, assetPath);
            return signal;
        }

        private static object CreateCinemachineCamera(JObject @params)
        {
            string cameraName = @params["camera_name"]?.ToString() ?? "VirtualCamera";
            string targetObjectName = @params["target_object"]?.ToString();
            string cameraType = @params["camera_type"]?.ToString() ?? "freelook";
            int priority = @params["priority"]?.ToObject<int>() ?? 10;
            
            GameObject cameraGO = new GameObject(cameraName);
            
            CinemachineCamera virtualCamera = null;
            
            switch (cameraType.ToLower())
            {
                case "freelook":
                    virtualCamera = cameraGO.AddComponent<CinemachineCamera>();
                    SetupFreeLookCamera(virtualCamera, @params);
                    break;
                case "dolly":
                    virtualCamera = cameraGO.AddComponent<CinemachineCamera>();
                    SetupDollyCamera(virtualCamera, @params);
                    break;
                case "virtual":
                    virtualCamera = cameraGO.AddComponent<CinemachineCamera>();
                    SetupVirtualCamera(virtualCamera, @params);
                    break;
                case "blend_list":
                    // Create a blend list camera setup
                    return CreateBlendListCamera(@params);
                default:
                    virtualCamera = cameraGO.AddComponent<CinemachineCamera>();
                    SetupVirtualCamera(virtualCamera, @params);
                    break;
            }
            
            virtualCamera.Priority = priority;
            
            // Set target if specified
            if (!string.IsNullOrEmpty(targetObjectName))
            {
                GameObject target = GameObject.Find(targetObjectName);
                if (target != null)
                {
                    virtualCamera.Follow = target.transform;
                    virtualCamera.LookAt = target.transform;
                }
            }
            
            // Apply procedural rules if specified
            ApplyProceduralRules(virtualCamera, @params["procedural_rules"] as JObject);
            
            return Response.Success("Cinemachine camera created successfully.", new
            {
                cameraName = cameraName,
                cameraType = cameraType,
                priority = priority,
                instanceId = cameraGO.GetInstanceID()
            });
        }

        private static void SetupFreeLookCamera(CinemachineCamera freeLook, JObject @params)
        {
            // Configure camera settings (FreeLook functionality is now handled differently in Cinemachine 3.x)
            // Add orbital follow component for FreeLook-like behavior
            var orbitalFollow = freeLook.gameObject.AddComponent<CinemachineOrbitalFollow>();
            
            // Setup follow settings
            var followSettings = @params["follow_settings"] as JObject;
            if (followSettings != null)
            {
                if (followSettings["damping"] != null)
                {
                    float damping = followSettings["damping"].ToObject<float>();
                    orbitalFollow.TrackerSettings.PositionDamping = new Vector3(damping, damping, damping);
                }
            }
        }

        private static void SetupDollyCamera(CinemachineCamera vcam, JObject @params)
        {
            // Add spline dolly component
            var dollyCart = vcam.gameObject.AddComponent<CinemachineSplineDolly>();
            
            // Configure dolly settings
            dollyCart.AutomaticDolly.Enabled = true;
            // Note: Unity.Splines may not be available in this context
            // dollyCart.PositionUnits = Unity.Splines.PathIndexUnit.Distance;
        }

        private static void SetupVirtualCamera(CinemachineCamera vcam, JObject @params)
        {
            // Add basic follow and rotation composer
            var follow = vcam.gameObject.AddComponent<CinemachineFollow>();
            var composer = vcam.gameObject.AddComponent<CinemachineRotationComposer>();
            
            // Configure follow settings
            var followSettings = @params["follow_settings"] as JObject;
            if (followSettings != null)
            {
                if (followSettings["offset"] is JArray offsetArray && offsetArray.Count == 3)
                {
                    follow.FollowOffset = new Vector3(
                        offsetArray[0].ToObject<float>(),
                        offsetArray[1].ToObject<float>(),
                        offsetArray[2].ToObject<float>()
                    );
                }
                
                if (followSettings["damping"] != null)
                {
                    float damping = followSettings["damping"].ToObject<float>();
                    follow.TrackerSettings.PositionDamping = new Vector3(damping, damping, damping);
                }
            }
            
            // Configure look at settings
            var lookAtSettings = @params["look_at_settings"] as JObject;
            if (lookAtSettings != null)
            {
                if (lookAtSettings["composition"] != null)
                {
                    string composition = lookAtSettings["composition"].ToString();
                    switch (composition.ToLower())
                    {
                        case "center":
                            composer.TargetOffset = Vector3.zero;
                            break;
                        case "rule_of_thirds":
                            composer.TargetOffset = new Vector3(0.33f, 0.33f, 0);
                            break;
                    }
                }
            }
        }

        private static object CreateBlendListCamera(JObject @params)
        {
            string cameraName = @params["camera_name"]?.ToString() ?? "BlendListCamera";
            
            GameObject blendListGO = new GameObject(cameraName);
            var blendListCamera = blendListGO.AddComponent<CinemachineSequencerCamera>();
            
            // Create child virtual cameras for the blend list
            var cameraShots = @params["camera_shots"] as JArray;
            if (cameraShots != null)
            {
                for (int i = 0; i < cameraShots.Count; i++)
                {
                    string shotType = cameraShots[i].ToString();
                    GameObject childCameraGO = new GameObject($"Shot_{shotType}_{i}");
                    childCameraGO.transform.SetParent(blendListGO.transform);
                    
                    var childVCam = childCameraGO.AddComponent<CinemachineCamera>();
                    ConfigureCameraForShotType(childVCam, shotType);
                    
                    // Add to sequencer camera (renamed from blend list in Cinemachine 3.x)
                    var instruction = new CinemachineSequencerCamera.Instruction
                    {
                        Camera = childVCam,
                        Hold = 3.0f,
                        Blend = new CinemachineBlendDefinition(CinemachineBlendDefinition.Styles.EaseInOut, 1.0f)
                    };
                    
                    var instructionsList = new List<CinemachineSequencerCamera.Instruction>(blendListCamera.Instructions);
                    instructionsList.Add(instruction);
                    blendListCamera.Instructions = instructionsList;
                }
            }
            
            return Response.Success("Blend list camera created successfully.", new
            {
                cameraName = cameraName,
                shotCount = cameraShots?.Count ?? 0,
                instanceId = blendListGO.GetInstanceID()
            });
        }

        private static void ConfigureCameraForShotType(CinemachineCamera vcam, string shotType)
        {
            var follow = vcam.gameObject.AddComponent<CinemachineFollow>();
            var composer = vcam.gameObject.AddComponent<CinemachineRotationComposer>();
            
            switch (shotType.ToLower())
            {
                case "close_up":
                    follow.FollowOffset = new Vector3(0, 1.5f, 2f);
                    composer.TargetOffset = new Vector3(0, 0.5f, 0);
                    break;
                case "medium_shot":
                    follow.FollowOffset = new Vector3(0, 2f, 4f);
                    composer.TargetOffset = Vector3.zero;
                    break;
                case "wide_shot":
                    follow.FollowOffset = new Vector3(0, 3f, 8f);
                    composer.TargetOffset = new Vector3(0, -0.5f, 0);
                    break;
                case "extreme_close_up":
                    follow.FollowOffset = new Vector3(0, 1.8f, 1f);
                    composer.TargetOffset = new Vector3(0, 0.2f, 0);
                    break;
            }
        }

        private static void ApplyProceduralRules(CinemachineCamera camera, JObject proceduralRules)
        {
            if (proceduralRules == null) return;
            
            // Apply automatic framing rules
            if (proceduralRules["auto_framing"]?.ToObject<bool>() == true)
            {
                // Add auto-framing behavior
                var autoFraming = camera.gameObject.AddComponent<CinemachineAutoFraming>();
                if (proceduralRules["frame_padding"] != null)
                {
                    autoFraming.framePadding = proceduralRules["frame_padding"].ToObject<float>();
                }
            }
            
            // Apply dynamic follow rules
            if (proceduralRules["dynamic_follow"]?.ToObject<bool>() == true)
            {
                // Add dynamic follow behavior
                var dynamicFollow = camera.gameObject.AddComponent<CinemachineDynamicFollow>();
            }
        }

        // Custom components for procedural camera behavior
        public class CinemachineAutoFraming : MonoBehaviour
        {
            public float framePadding = 0.1f;
            
            void Update()
            {
                // Implement auto-framing logic
                // This would adjust camera position/zoom to keep targets in frame
            }
        }
        
        public class CinemachineDynamicFollow : MonoBehaviour
        {
            void Update()
            {
                // Implement dynamic follow logic
                // This would adjust follow behavior based on target movement
            }
        }

        private static object CreateDynamicShotList(JObject @params)
        {
            string sceneType = @params["scene_type"]?.ToString() ?? "generic";
            string shotTemplate = @params["shot_template"]?.ToString() ?? "standard";
            int shotCount = @params["shot_count"]?.ToObject<int>() ?? 5;
            var shotTypes = @params["shot_types"] as JArray;
            float durationPerShot = @params["duration_per_shot"]?.ToObject<float>() ?? 3.0f;
            
            List<object> generatedShots = new List<object>();
            
            // Generate shots based on scene type and template
            for (int i = 0; i < shotCount; i++)
            {
                string shotType = GetShotTypeForIndex(i, shotTypes, sceneType);
                string cameraAngle = GetCameraAngleForShot(shotType, sceneType);
                string transitionType = GetTransitionTypeForShot(i, shotCount, @params);
                
                var shot = new
                {
                    index = i,
                    shotType = shotType,
                    cameraAngle = cameraAngle,
                    duration = durationPerShot,
                    startTime = i * durationPerShot,
                    endTime = (i + 1) * durationPerShot,
                    transitionIn = i > 0 ? transitionType : "cut",
                    transitionOut = i < shotCount - 1 ? transitionType : "cut"
                };
                
                generatedShots.Add(shot);
            }
            
            return Response.Success("Dynamic shot list created successfully.", new
            {
                sceneType = sceneType,
                shotTemplate = shotTemplate,
                totalShots = shotCount,
                totalDuration = shotCount * durationPerShot,
                shots = generatedShots
            });
        }

        private static string GetShotTypeForIndex(int index, JArray shotTypes, string sceneType)
        {
            if (shotTypes != null && shotTypes.Count > 0)
            {
                return shotTypes[index % shotTypes.Count].ToString();
            }
            
            // Generate shot types based on scene type
            switch (sceneType.ToLower())
            {
                case "action":
                    string[] actionShots = { "wide_shot", "medium_shot", "close_up", "extreme_close_up", "wide_shot" };
                    return actionShots[index % actionShots.Length];
                case "dialogue":
                    string[] dialogueShots = { "medium_shot", "close_up", "medium_shot", "close_up" };
                    return dialogueShots[index % dialogueShots.Length];
                case "dramatic":
                    string[] dramaticShots = { "wide_shot", "medium_shot", "close_up", "extreme_close_up" };
                    return dramaticShots[index % dramaticShots.Length];
                default:
                    string[] genericShots = { "wide_shot", "medium_shot", "close_up" };
                    return genericShots[index % genericShots.Length];
            }
        }

        private static string GetCameraAngleForShot(string shotType, string sceneType)
        {
            switch (shotType.ToLower())
            {
                case "close_up":
                case "extreme_close_up":
                    return sceneType == "dramatic" ? "low_angle" : "eye_level";
                case "wide_shot":
                    return "high_angle";
                case "medium_shot":
                default:
                    return "eye_level";
            }
        }

        private static string GetTransitionTypeForShot(int index, int totalShots, JObject @params)
        {
            var transitionTypes = @params["transition_types"] as JArray;
            if (transitionTypes != null && transitionTypes.Count > 0)
            {
                return transitionTypes[index % transitionTypes.Count].ToString();
            }
            
            // Default transition pattern
            string[] transitions = { "cut", "dissolve", "cut", "fade" };
            return transitions[index % transitions.Length];
        }

        private static object CreateAnimationSequence(JObject @params)
        {
            string timelineAssetPath = @params["timeline_asset"]?.ToString();
            var animationClips = @params["animation_clips"] as JArray;
            var targetObjects = @params["target_objects"] as JArray;
            string sequencingMode = @params["sequencing_mode"]?.ToString() ?? "sequential";
            
            if (string.IsNullOrEmpty(timelineAssetPath))
            {
                return Response.Error("Timeline asset path is required.");
            }
            
            TimelineAsset timeline = AssetDatabase.LoadAssetAtPath<TimelineAsset>(timelineAssetPath);
            if (timeline == null)
            {
                return Response.Error($"Timeline asset not found at path: {timelineAssetPath}");
            }
            
            // Create animation tracks for each target object
            List<object> createdTracks = new List<object>();
            
            if (targetObjects != null)
            {
                for (int i = 0; i < targetObjects.Count; i++)
                {
                    string targetName = targetObjects[i].ToString();
                    var animTrack = timeline.CreateTrack<AnimationTrack>(null, $"Animation_{targetName}");
                    
                    // Sequence animations based on mode
                    if (animationClips != null)
                    {
                        SequenceAnimationsOnTrack(animTrack, animationClips, sequencingMode, @params);
                    }
                    
                    createdTracks.Add(new
                    {
                        trackName = animTrack.name,
                        targetObject = targetName,
                        clipCount = animTrack.GetClips().Count()
                    });
                }
            }
            
            EditorUtility.SetDirty(timeline);
            AssetDatabase.SaveAssets();
            
            return Response.Success("Animation sequence created successfully.", new
            {
                timelineAsset = timelineAssetPath,
                sequencingMode = sequencingMode,
                tracksCreated = createdTracks.Count,
                tracks = createdTracks
            });
        }

        private static void SequenceAnimationsOnTrack(AnimationTrack track, JArray animationClips, string sequencingMode, JObject @params)
        {
            float currentTime = 0f;
            var blendSettings = @params["blend_settings"] as JObject;
            float blendDuration = blendSettings?["duration"]?.ToObject<float>() ?? 0.5f;
            
            for (int i = 0; i < animationClips.Count; i++)
            {
                string clipPath = animationClips[i].ToString();
                AnimationClip clip = AssetDatabase.LoadAssetAtPath<AnimationClip>(clipPath);
                
                if (clip != null)
                {
                    var timelineClip = track.CreateClip(clip);
                    
                    switch (sequencingMode.ToLower())
                    {
                        case "sequential":
                            timelineClip.start = currentTime;
                            currentTime += (float)timelineClip.duration;
                            break;
                        case "parallel":
                            timelineClip.start = 0;
                            break;
                        case "custom":
                            // Apply custom timing from parameters
                            var timingAdjustments = @params["timing_adjustments"] as JObject;
                            if (timingAdjustments?[i.ToString()] != null)
                            {
                                timelineClip.start = timingAdjustments[i.ToString()]["start"].ToObject<double>();
                            }
                            break;
                    }
                    
                    // Apply blend settings
                    if (i > 0 && sequencingMode == "sequential")
                    {
                        timelineClip.blendInDuration = blendDuration;
                        var previousClip = track.GetClips().ElementAt(i - 1);
                        previousClip.blendOutDuration = blendDuration;
                    }
                }
            }
        }

        private static object CreateAudioTrack(JObject @params)
        {
            string timelineAssetPath = @params["timeline_asset"]?.ToString();
            var audioClips = @params["audio_clips"] as JArray;
            var audioTypes = @params["audio_types"] as JArray;
            var trackNames = @params["track_names"] as JArray;
            bool syncToAnimation = @params["sync_to_animation"]?.ToObject<bool>() ?? false;
            
            if (string.IsNullOrEmpty(timelineAssetPath))
            {
                return Response.Error("Timeline asset path is required.");
            }
            
            TimelineAsset timeline = AssetDatabase.LoadAssetAtPath<TimelineAsset>(timelineAssetPath);
            if (timeline == null)
            {
                return Response.Error($"Timeline asset not found at path: {timelineAssetPath}");
            }
            
            List<object> createdTracks = new List<object>();
            
            // Create audio tracks based on types
            if (audioTypes != null)
            {
                for (int i = 0; i < audioTypes.Count; i++)
                {
                    string audioType = audioTypes[i].ToString();
                    string trackName = trackNames?[i]?.ToString() ?? $"Audio_{audioType}";
                    
                    var audioTrack = timeline.CreateTrack<AudioTrack>(null, trackName);
                    
                    // Configure track based on audio type
                    ConfigureAudioTrackForType(audioTrack, audioType, @params);
                    
                    // Add audio clips if provided
                    if (audioClips != null && i < audioClips.Count)
                    {
                        string clipPath = audioClips[i].ToString();
                        AudioClip clip = AssetDatabase.LoadAssetAtPath<AudioClip>(clipPath);
                        
                        if (clip != null)
                        {
                            var timelineClip = audioTrack.CreateClip(clip);
                            
                            // Sync to animation if requested
                            if (syncToAnimation)
                            {
                                SyncAudioToAnimation(timelineClip, timeline);
                            }
                        }
                    }
                    
                    createdTracks.Add(new
                    {
                        trackName = trackName,
                        audioType = audioType,
                        clipCount = audioTrack.GetClips().Count()
                    });
                }
            }
            
            EditorUtility.SetDirty(timeline);
            AssetDatabase.SaveAssets();
            
            return Response.Success("Audio tracks created successfully.", new
            {
                timelineAsset = timelineAssetPath,
                tracksCreated = createdTracks.Count,
                syncToAnimation = syncToAnimation,
                tracks = createdTracks
            });
        }

        private static void ConfigureAudioTrackForType(AudioTrack track, string audioType, JObject @params)
        {
            var volumeSettings = @params["volume_settings"] as JObject;
            var spatialSettings = @params["spatial_settings"] as JObject;
            
            // Configure volume based on audio type
            switch (audioType.ToLower())
            {
                case "music":
                    // Music typically at lower volume to not overpower dialogue
                    if (volumeSettings?["music_volume"] != null)
                    {
                        // Volume would be applied to individual clips
                    }
                    break;
                case "sfx":
                    // Sound effects at medium volume
                    break;
                case "voice":
                    // Voice at higher priority and volume
                    break;
                case "ambient":
                    // Ambient at low volume
                    break;
            }
            
            // Apply spatial settings if provided
            if (spatialSettings != null)
            {
                bool is3D = spatialSettings["is_3d"]?.ToObject<bool>() ?? false;
                if (is3D)
                {
                    // Configure 3D audio settings
                    // This would require custom audio source configuration
                }
            }
        }

        private static void SyncAudioToAnimation(TimelineClip audioClip, TimelineAsset timeline)
        {
            // Find animation tracks and sync audio timing
            var animationTracks = timeline.GetRootTracks().OfType<AnimationTrack>();
            
            if (animationTracks.Any())
            {
                var firstAnimTrack = animationTracks.First();
                var animClips = firstAnimTrack.GetClips();
                
                if (animClips.Any())
                {
                    var firstAnimClip = animClips.First();
                    audioClip.start = firstAnimClip.start;
                    audioClip.duration = Math.Min(audioClip.duration, firstAnimClip.duration);
                }
            }
        }

        private static object CreateCinematicTrigger(JObject @params)
        {
            string triggerType = @params["trigger_type"]?.ToString() ?? "collision";
            var triggerConditions = @params["trigger_conditions"] as JObject;
            string cinematicAssetPath = @params["cinematic_asset"]?.ToString();
            bool gameplayPause = @params["gameplay_pause"]?.ToObject<bool>() ?? true;
            
            // Create trigger GameObject
            GameObject triggerGO = new GameObject($"CinematicTrigger_{triggerType}");
            
            // Add appropriate trigger component based on type
            switch (triggerType.ToLower())
            {
                case "collision":
                    SetupCollisionTrigger(triggerGO, triggerConditions, cinematicAssetPath, gameplayPause, @params);
                    break;
                case "proximity":
                    SetupProximityTrigger(triggerGO, triggerConditions, cinematicAssetPath, gameplayPause, @params);
                    break;
                case "event":
                    SetupEventTrigger(triggerGO, triggerConditions, cinematicAssetPath, gameplayPause, @params);
                    break;
                case "timer":
                    SetupTimerTrigger(triggerGO, triggerConditions, cinematicAssetPath, gameplayPause, @params);
                    break;
                default:
                    return Response.Error($"Unknown trigger type: {triggerType}");
            }
            
            return Response.Success("Cinematic trigger created successfully.", new
            {
                triggerType = triggerType,
                cinematicAsset = cinematicAssetPath,
                gameplayPause = gameplayPause,
                instanceId = triggerGO.GetInstanceID()
            });
        }

        private static void SetupCollisionTrigger(GameObject triggerGO, JObject conditions, string cinematicAsset, bool gameplayPause, JObject @params)
        {
            // Add collider for trigger
            var collider = triggerGO.AddComponent<BoxCollider>();
            collider.isTrigger = true;
            
            // Set trigger size from conditions
            if (conditions?["size"] is JArray sizeArray && sizeArray.Count == 3)
            {
                collider.size = new Vector3(
                    sizeArray[0].ToObject<float>(),
                    sizeArray[1].ToObject<float>(),
                    sizeArray[2].ToObject<float>()
                );
            }
            
            // Add custom trigger script
            var triggerScript = triggerGO.AddComponent<CinematicCollisionTrigger>();
            triggerScript.cinematicAssetPath = cinematicAsset;
            triggerScript.pauseGameplay = gameplayPause;
            
            // Configure trigger conditions
            if (conditions?["player_tag"] != null)
            {
                triggerScript.requiredTag = conditions["player_tag"].ToString();
            }
        }

        private static void SetupProximityTrigger(GameObject triggerGO, JObject conditions, string cinematicAsset, bool gameplayPause, JObject @params)
        {
            // Add sphere collider for proximity detection
            var collider = triggerGO.AddComponent<SphereCollider>();
            collider.isTrigger = true;
            
            // Set proximity radius
            if (conditions?["radius"] != null)
            {
                collider.radius = conditions["radius"].ToObject<float>();
            }
            
            // Add custom proximity script
            var proximityScript = triggerGO.AddComponent<CinematicProximityTrigger>();
            proximityScript.cinematicAssetPath = cinematicAsset;
            proximityScript.pauseGameplay = gameplayPause;
        }

        private static void SetupEventTrigger(GameObject triggerGO, JObject conditions, string cinematicAsset, bool gameplayPause, JObject @params)
        {
            // Add event-based trigger script
            var eventScript = triggerGO.AddComponent<CinematicEventTrigger>();
            eventScript.cinematicAssetPath = cinematicAsset;
            eventScript.pauseGameplay = gameplayPause;
            
            // Configure event conditions
            if (conditions?["event_name"] != null)
            {
                eventScript.eventName = conditions["event_name"].ToString();
            }
        }

        private static void SetupTimerTrigger(GameObject triggerGO, JObject conditions, string cinematicAsset, bool gameplayPause, JObject @params)
        {
            // Add timer-based trigger script
            var timerScript = triggerGO.AddComponent<CinematicTimerTrigger>();
            timerScript.cinematicAssetPath = cinematicAsset;
            timerScript.pauseGameplay = gameplayPause;
            
            // Configure timer
            if (conditions?["delay"] != null)
            {
                timerScript.triggerDelay = conditions["delay"].ToObject<float>();
            }
            
            if (conditions?["auto_start"]?.ToObject<bool>() == true)
            {
                timerScript.autoStart = true;
            }
        }

        // Custom trigger scripts
        public class CinematicCollisionTrigger : MonoBehaviour
        {
            public string cinematicAssetPath;
            public bool pauseGameplay = true;
            public string requiredTag = "Player";
            
            void OnTriggerEnter(Collider other)
            {
                if (other.CompareTag(requiredTag))
                {
                    TriggerCinematic();
                }
            }
            
            void TriggerCinematic()
            {
                // Load and play cinematic
                if (!string.IsNullOrEmpty(cinematicAssetPath))
                {
                    TimelineAsset timeline = AssetDatabase.LoadAssetAtPath<TimelineAsset>(cinematicAssetPath);
                    if (timeline != null)
                    {
                        PlayCinematic(timeline);
                    }
                }
            }
            
            void PlayCinematic(TimelineAsset timeline)
            {
                // Find or create PlayableDirector
                var director = FindFirstObjectByType<PlayableDirector>();
                if (director == null)
                {
                    GameObject directorGO = new GameObject("CinematicDirector");
                    director = directorGO.AddComponent<PlayableDirector>();
                }
                
                director.playableAsset = timeline;
                director.Play();
                
                if (pauseGameplay)
                {
                    Time.timeScale = 0f;
                    // Resume gameplay when cinematic ends
                    StartCoroutine(ResumeGameplayAfterCinematic(director));
                }
            }
            
            System.Collections.IEnumerator ResumeGameplayAfterCinematic(PlayableDirector director)
            {
                while (director.state == PlayState.Playing)
                {
                    yield return null;
                }
                Time.timeScale = 1f;
            }
        }
        
        public class CinematicProximityTrigger : CinematicCollisionTrigger
        {
            // Inherits from collision trigger with same functionality
        }
        
        public class CinematicEventTrigger : MonoBehaviour
        {
            public string cinematicAssetPath;
            public bool pauseGameplay = true;
            public string eventName;
            
            void Start()
            {
                // Subscribe to event system
                // This would integrate with your game's event system
            }
        }
        
        public class CinematicTimerTrigger : MonoBehaviour
        {
            public string cinematicAssetPath;
            public bool pauseGameplay = true;
            public float triggerDelay = 5f;
            public bool autoStart = false;
            
            void Start()
            {
                if (autoStart)
                {
                    StartCoroutine(TriggerAfterDelay());
                }
            }
            
            System.Collections.IEnumerator TriggerAfterDelay()
            {
                yield return new WaitForSeconds(triggerDelay);
                // Trigger cinematic logic here
            }
        }

        private static object CreatePostProcessingControl(JObject @params)
        {
            string timelineAssetPath = @params["timeline_asset"]?.ToString();
            string volumeProfilePath = @params["volume_profile"]?.ToString();
            var effectTypes = @params["effect_types"] as JArray;
            bool weatherSync = @params["weather_sync"]?.ToObject<bool>() ?? false;
            
            if (string.IsNullOrEmpty(timelineAssetPath))
            {
                return Response.Error("Timeline asset path is required.");
            }
            
            TimelineAsset timeline = AssetDatabase.LoadAssetAtPath<TimelineAsset>(timelineAssetPath);
            if (timeline == null)
            {
                return Response.Error($"Timeline asset not found at path: {timelineAssetPath}");
            }
            
            // Create post-processing track
            var postProcessTrack = timeline.CreateTrack<PlayableTrack>(null, "Post Processing Control");
            
            // Create custom post-processing playable
            // Note: PostProcessingPlayableAsset may not be available in Unity 6.2
            // var postProcessPlayable = CreatePostProcessingPlayable(@params);
            // var timelineClip = postProcessTrack.CreateClip<PostProcessingPlayableAsset>();
            // timelineClip.asset = postProcessPlayable;
            // timelineClip.duration = timeline.fixedDuration;
            
            // Configure keyframes for effects
            if (effectTypes != null)
            {
                // ConfigurePostProcessingKeyframes(timelineClip, effectTypes, @params);
            }
            
            // Setup weather synchronization if requested
            if (weatherSync)
            {
                SetupWeatherSync(timeline, @params);
            }
            
            EditorUtility.SetDirty(timeline);
            AssetDatabase.SaveAssets();
            
            return Response.Success("Post-processing control created successfully.", new
            {
                timelineAsset = timelineAssetPath,
                volumeProfile = volumeProfilePath,
                effectCount = effectTypes?.Count ?? 0,
                weatherSync = weatherSync
            });
        }

        private static ScriptableObject CreatePostProcessingPlayable(JObject @params)
        {
            // Create a custom ScriptableObject for post-processing control
            // This would be a custom playable asset for controlling post-processing
            var playableAsset = ScriptableObject.CreateInstance<PostProcessingControlAsset>();
            
            string assetPath = "Assets/PostProcessing/PostProcessingControl.asset";
            EnsureDirectoryExists(Path.GetDirectoryName(assetPath));
            AssetDatabase.CreateAsset(playableAsset, assetPath);
            
            return playableAsset;
        }

        private static void ConfigurePostProcessingKeyframes(TimelineClip clip, JArray effectTypes, JObject @params)
        {
            var keyframeData = @params["keyframe_data"] as JObject;
            var blendCurves = @params["blend_curves"] as JObject;
            
            // Configure animation curves for each effect type
            foreach (var effectType in effectTypes)
            {
                string effect = effectType.ToString();
                
                switch (effect.ToLower())
                {
                    case "bloom":
                        ConfigureBloomKeyframes(clip, keyframeData, blendCurves);
                        break;
                    case "color_grading":
                        ConfigureColorGradingKeyframes(clip, keyframeData, blendCurves);
                        break;
                    case "vignette":
                        ConfigureVignetteKeyframes(clip, keyframeData, blendCurves);
                        break;
                    case "chromatic_aberration":
                        ConfigureChromaticAberrationKeyframes(clip, keyframeData, blendCurves);
                        break;
                }
            }
        }

        private static void ConfigureBloomKeyframes(TimelineClip clip, JObject keyframeData, JObject blendCurves)
        {
            // Configure bloom intensity keyframes
            if (keyframeData?["bloom"] is JObject bloomData)
            {
                var intensityKeys = bloomData["intensity"] as JArray;
                if (intensityKeys != null)
                {
                    // Create animation curve for bloom intensity
                    AnimationCurve bloomCurve = new AnimationCurve();
                    
                    foreach (var key in intensityKeys)
                    {
                        if (key is JObject keyObj)
                        {
                            float time = keyObj["time"].ToObject<float>();
                            float value = keyObj["value"].ToObject<float>();
                            bloomCurve.AddKey(time, value);
                        }
                    }
                    
                    // Apply curve to clip (this would require custom playable implementation)
                }
            }
        }

        private static void ConfigureColorGradingKeyframes(TimelineClip clip, JObject keyframeData, JObject blendCurves)
        {
            // Configure color grading keyframes
            if (keyframeData?["color_grading"] is JObject colorData)
            {
                // Configure temperature, tint, saturation, etc.
                ConfigureColorParameter(clip, colorData, "temperature");
                ConfigureColorParameter(clip, colorData, "tint");
                ConfigureColorParameter(clip, colorData, "saturation");
                ConfigureColorParameter(clip, colorData, "contrast");
            }
        }

        private static void ConfigureColorParameter(TimelineClip clip, JObject colorData, string parameter)
        {
            var paramKeys = colorData[parameter] as JArray;
            if (paramKeys != null)
            {
                AnimationCurve curve = new AnimationCurve();
                
                foreach (var key in paramKeys)
                {
                    if (key is JObject keyObj)
                    {
                        float time = keyObj["time"].ToObject<float>();
                        float value = keyObj["value"].ToObject<float>();
                        curve.AddKey(time, value);
                    }
                }
                
                // Apply curve to clip
            }
        }

        private static void ConfigureVignetteKeyframes(TimelineClip clip, JObject keyframeData, JObject blendCurves)
        {
            // Configure vignette intensity and smoothness keyframes
            if (keyframeData?["vignette"] is JObject vignetteData)
            {
                ConfigureColorParameter(clip, vignetteData, "intensity");
                ConfigureColorParameter(clip, vignetteData, "smoothness");
            }
        }

        private static void ConfigureChromaticAberrationKeyframes(TimelineClip clip, JObject keyframeData, JObject blendCurves)
        {
            // Configure chromatic aberration intensity keyframes
            if (keyframeData?["chromatic_aberration"] is JObject chromaticData)
            {
                ConfigureColorParameter(clip, chromaticData, "intensity");
            }
        }

        private static void SetupWeatherSync(TimelineAsset timeline, JObject @params)
        {
            var moodSettings = @params["mood_settings"] as JObject;
            
            // Create weather synchronization track
            var weatherTrack = timeline.CreateTrack<PlayableTrack>(null, "Weather Sync");
            
            // Configure mood-based post-processing
            if (moodSettings != null)
            {
                string mood = moodSettings["mood"]?.ToString() ?? "neutral";
                
                switch (mood.ToLower())
                {
                    case "dramatic":
                        // High contrast, desaturated colors, strong vignette
                        break;
                    case "romantic":
                        // Warm colors, soft bloom, light vignette
                        break;
                    case "horror":
                        // Desaturated, high contrast, chromatic aberration
                        break;
                    case "action":
                        // High saturation, sharp contrast, minimal bloom
                        break;
                }
            }
        }

        // Custom ScriptableObject for post-processing control
        public class PostProcessingControlAsset : ScriptableObject
        {
            public VolumeProfile volumeProfile;
            public AnimationCurve bloomIntensity = AnimationCurve.Linear(0, 0, 1, 1);
            public AnimationCurve colorTemperature = AnimationCurve.Linear(0, 0, 1, 0);
            public AnimationCurve vignetteIntensity = AnimationCurve.Linear(0, 0, 1, 0);
        }

        private static object ModifyCinematicElement(JObject @params)
        {
            // Implementation for modifying existing cinematic elements
            return Response.Success("Cinematic element modified successfully.");
        }

        private static object DeleteCinematicElement(JObject @params)
        {
            // Implementation for deleting cinematic elements
            return Response.Success("Cinematic element deleted successfully.");
        }

        private static object ListCinematicElements(JObject @params)
        {
            // Implementation for listing cinematic elements
            var elements = new List<object>();
            
            // Find all Timeline assets
            string[] timelineGuids = AssetDatabase.FindAssets("t:TimelineAsset");
            foreach (string guid in timelineGuids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                TimelineAsset timeline = AssetDatabase.LoadAssetAtPath<TimelineAsset>(path);
                
                if (timeline != null)
                {
                    elements.Add(new
                    {
                        type = "timeline",
                        name = timeline.name,
                        path = path,
                        duration = timeline.fixedDuration,
                        trackCount = timeline.GetRootTracks().Count()
                    });
                }
            }
            
            // Find all Cinemachine cameras
            var virtualCameras = UnityEngine.Object.FindObjectsByType<CinemachineVirtualCameraBase>(FindObjectsSortMode.None);
            foreach (var camera in virtualCameras)
            {
                elements.Add(new
                {
                    type = "camera",
                    name = camera.name,
                    priority = camera.Priority,
                    isActive = camera.gameObject.activeInHierarchy
                });
            }
            
            return Response.Success("Cinematic elements listed successfully.", elements);
        }

        private static object GetCinematicInfo(JObject @params)
        {
            string elementType = @params["element_type"]?.ToString();
            string elementName = @params["element_name"]?.ToString();
            string assetPath = @params["asset_path"]?.ToString();
            
            if (string.IsNullOrEmpty(elementType))
            {
                return Response.Error("Element type is required.");
            }
            
            try
            {
                switch (elementType.ToLower())
                {
                    case "timeline":
                        return GetTimelineInfo(assetPath, elementName);
                    case "camera":
                        return GetCameraInfo(elementName);
                    case "post_processing":
                        return GetPostProcessingInfo(assetPath);
                    default:
                        return Response.Error($"Unknown element type: {elementType}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Error getting cinematic info: {e.Message}");
            }
        }

        private static object GetTimelineInfo(string assetPath, string timelineName)
        {
            TimelineAsset timeline = null;
            
            if (!string.IsNullOrEmpty(assetPath))
            {
                timeline = AssetDatabase.LoadAssetAtPath<TimelineAsset>(assetPath);
            }
            else if (!string.IsNullOrEmpty(timelineName))
            {
                string[] guids = AssetDatabase.FindAssets($"{timelineName} t:TimelineAsset");
                if (guids.Length > 0)
                {
                    assetPath = AssetDatabase.GUIDToAssetPath(guids[0]);
                    timeline = AssetDatabase.LoadAssetAtPath<TimelineAsset>(assetPath);
                }
            }
            
            if (timeline == null)
            {
                return Response.Error("Timeline not found.");
            }
            
            var tracks = timeline.GetRootTracks().ToList();
            var trackInfo = tracks.Select(track => new
            {
                name = track.name,
                type = track.GetType().Name,
                clipCount = track.GetClips().Count(),
                muted = track.muted,
                locked = track.locked
            }).ToList();
            
            return Response.Success("Timeline info retrieved successfully.", new
            {
                name = timeline.name,
                assetPath = assetPath,
                duration = timeline.fixedDuration,
                frameRate = timeline.editorSettings.frameRate,
                trackCount = tracks.Count,
                tracks = trackInfo
            });
        }

        private static object GetCameraInfo(string cameraName)
        {
            var cameras = UnityEngine.Object.FindObjectsByType<CinemachineVirtualCameraBase>(FindObjectsSortMode.None);
            var camera = cameras.FirstOrDefault(c => c.name == cameraName);
            
            if (camera == null)
            {
                return Response.Error($"Camera '{cameraName}' not found.");
            }
            
            var cameraInfo = new
            {
                name = camera.name,
                type = camera.GetType().Name,
                priority = camera.Priority,
                isActive = camera.gameObject.activeInHierarchy,
                position = camera.transform.position,
                rotation = camera.transform.rotation.eulerAngles
            };
            
            // Add specific info based on camera type
            if (camera is CinemachineCamera vcam)
            {
                var follow = vcam.GetCinemachineComponent(CinemachineCore.Stage.Body) as CinemachineFollow;
                var composer = vcam.GetCinemachineComponent(CinemachineCore.Stage.Aim) as CinemachineRotationComposer;
                
                return Response.Success("Camera info retrieved successfully.", new
                {
                    cameraInfo,
                    followTarget = vcam.Follow?.name,
                    lookAtTarget = vcam.LookAt?.name,
                    followOffset = follow?.FollowOffset,
                    damping = follow?.TrackerSettings.PositionDamping,
                    composition = new
                    {
                        targetOffset = composer?.TargetOffset
                    }
                });
            }
            
            return Response.Success("Camera info retrieved successfully.", cameraInfo);
        }

        private static object GetPostProcessingInfo(string assetPath)
        {
            if (string.IsNullOrEmpty(assetPath))
            {
                return Response.Error("Asset path is required for post-processing info.");
            }
            
            VolumeProfile profile = AssetDatabase.LoadAssetAtPath<VolumeProfile>(assetPath);
            if (profile == null)
            {
                return Response.Error($"Volume profile not found at path: {assetPath}");
            }
            
            var components = profile.components.Select(component => new
            {
                type = component.GetType().Name,
                active = component.active,
                parameters = GetVolumeComponentParameters(component)
            }).ToList();
            
            return Response.Success("Post-processing info retrieved successfully.", new
            {
                name = profile.name,
                assetPath = assetPath,
                componentCount = components.Count,
                components = components
            });
        }

        private static object GetVolumeComponentParameters(VolumeComponent component)
        {
            var parameters = new Dictionary<string, object>();
            
            var fields = component.GetType().GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
            foreach (var field in fields)
            {
                if (field.FieldType.IsSubclassOf(typeof(VolumeParameter)))
                {
                    var parameter = field.GetValue(component) as VolumeParameter;
                    if (parameter != null)
                    {
                        parameters[field.Name] = new
                        {
                            value = parameter.GetValue<object>(),
                            overrideState = parameter.overrideState
                        };
                    }
                }
            }
            
            return parameters;
        }

        private static void EnsureDirectoryExists(string directoryPath)
        {
            if (!string.IsNullOrEmpty(directoryPath) && !Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }
        }
    }
}