%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &1
MonoBehaviour:
  m_ObjectHideFlags: 53
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f2009558327c478886451e9bd1d60f8b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.AI.Material::Unity.AI.Material.Services.SessionPersistence.MaterialGeneratorSettings
  m_Session:
    settings:
      lastSelectedModels:
        serializedData:
        - key: 0
          value:
            rid: 1175402816129466940
        - key: 1
          value:
            rid: 1175402816129466941
        - key: 2
          value:
            rid: 1175402816129466942
      lastMaterialMappings:
        serializedData:
        - key: HDRP/Lit
          value:
            rid: 1175402816129466784
      previewSettings:
        sizeFactor: 1
  references:
    version: 2
    RefIds:
    - rid: 1175402816129466784
      type: {class: 'SerializableDictionary`2[[Unity.AI.Material.Services.Stores.States.MapType, Unity.AI.Material],[System.String, mscorlib]]', ns: Unity.AI.Generators.Redux.Toolkit, asm: Unity.AI.Generators.Redux}
      data:
        serializedData:
        - key: 1
          value: _HeightMap
        - key: 2
          value: _NormalMap
        - key: 3
          value: _EmissiveColorMap
        - key: 4
          value: None
        - key: 5
          value: None
        - key: 6
          value: _BaseColorMap
        - key: 7
          value: None
        - key: 8
          value: None
        - key: 9
          value: None
        - key: 10
          value: None
        - key: 11
          value: _MaskMap
    - rid: 1175402816129466940
      type: {class: ModelSelection, ns: Unity.AI.Material.Services.Stores.States, asm: Unity.AI.Material}
      data:
        modelID: 575ecf67-4425-4939-97b9-5e138b8ee234
    - rid: 1175402816129466941
      type: {class: ModelSelection, ns: Unity.AI.Material.Services.Stores.States, asm: Unity.AI.Material}
      data:
        modelID: 
    - rid: 1175402816129466942
      type: {class: ModelSelection, ns: Unity.AI.Material.Services.Stores.States, asm: Unity.AI.Material}
      data:
        modelID: 
