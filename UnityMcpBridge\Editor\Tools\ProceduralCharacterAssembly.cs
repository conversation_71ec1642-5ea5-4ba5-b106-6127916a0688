using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.Animations;
using UnityEngine.Rendering;
using UnityMcpBridge.Editor.Helpers;
using UnityEditor.Animations;
using UnityEngine.AI;

#if UNITY_ANIMATION_RIGGING
using UnityEngine.Animations.Rigging;
using Unity.Collections;
#endif

#if UNITY_ADDRESSABLES
using UnityEditor.AddressableAssets;
using UnityEditor.AddressableAssets.Settings;
#endif

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// Handles procedural character assembly operations using Unity 6.2 APIs.
    /// </summary>
    public static class ProceduralCharacterAssembly
    {
        private static readonly List<string> ValidActions = new List<string>
        {
            "assemble_character",
            "apply_textures",
            "setup_rigging",
            "create_animation_controller",
            "generate_variations",
            "setup_lods",
            "create_customization_system",
            "export_prefab",
            "optimize_with_ai",
            "setup_subsurface_scattering",
            "generate_advanced_facial_expressions",
            "setup_advanced_eye_tracking",
            "apply_micro_detail_enhancement",
            "setup_gpu_resident_drawer",
            "enable_raytracing_support"
        };

        public static object HandleCommand(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidActions);
                return Response.Error(
                    $"Unknown action: '{action}'. Valid actions are: {validActionsList}"
                );
            }

            try
            {
                switch (action)
                {
                    case "assemble_character":
                        return AssembleCharacterFromParts(@params);
                    case "apply_textures":
                        return ApplyGeneratedTexturesToCharacter(@params);
                    case "setup_rigging":
                        return SetupProceduralRigging(@params);
                    case "create_animation_controller":
                        return CreateCharacterAnimationController(@params);
                    case "generate_variations":
                        return GenerateCharacterVariations(@params);
                    case "setup_lods":
                        return SetupCharacterLodsProceduralGeneration(@params);
                    case "create_customization_system":
                        return CreateCharacterCustomizationSystem(@params);
                    case "export_prefab":
                        return ExportAssembledCharacterPrefab(@params);
                    case "optimize_with_ai":
                        return OptimizeCharacterWithAI(@params);
                    case "setup_subsurface_scattering":
                        return SetupSubsurfaceScattering(@params);
                    case "generate_advanced_facial_expressions":
                        return GenerateAdvancedFacialExpressions(@params);
                    case "setup_advanced_eye_tracking":
                        return SetupAdvancedEyeTracking(@params);
                    case "apply_micro_detail_enhancement":
                        return ApplyMicroDetailEnhancement(@params);
                    case "setup_gpu_resident_drawer":
                        return SetupGPUResidentDrawer(@params);
                    case "enable_raytracing_support":
                        return EnableRaytracingSupport(@params);
                    default:
                        return Response.Error($"Unknown action: '{action}'");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[ProceduralCharacterAssembly] Action '{action}' failed: {e}");
                return Response.Error($"Internal error processing action '{action}': {e.Message}");
            }
        }

        private static object AssembleCharacterFromParts(JObject @params)
        {
            string characterName = @params["character_name"]?.ToString();
            var bodyPartsArray = @params["body_parts"] as JArray;
            string targetDirectory = @params["target_directory"]?.ToString() ?? "Assets/Characters";
            bool autoRig = @params["auto_rig"]?.ToObject<bool>() ?? true;
            bool generateLods = @params["generate_lods"]?.ToObject<bool>() ?? true;
            bool combineMeshes = @params["combine_meshes"]?.ToObject<bool>() ?? false;

            if (string.IsNullOrEmpty(characterName))
                return Response.Error("Character name is required.");

            if (bodyPartsArray == null || bodyPartsArray.Count == 0)
                return Response.Error("Body parts list is required and cannot be empty.");

            try
            {
                // Ensure target directory exists
                if (!AssetDatabase.IsValidFolder(targetDirectory))
                {
                    string[] pathParts = targetDirectory.Split('/');
                    string currentPath = pathParts[0];
                    for (int i = 1; i < pathParts.Length; i++)
                    {
                        string newPath = currentPath + "/" + pathParts[i];
                        if (!AssetDatabase.IsValidFolder(newPath))
                        {
                            AssetDatabase.CreateFolder(currentPath, pathParts[i]);
                        }
                        currentPath = newPath;
                    }
                }

                // Create character GameObject with proper hierarchy
                GameObject characterRoot = new GameObject(characterName);
                
                // Add essential components for character
                Animator animator = characterRoot.AddComponent<Animator>();
                
                // Load and combine body parts with proper organization
                List<GameObject> bodyParts = new List<GameObject>();
                List<SkinnedMeshRenderer> skinnedRenderers = new List<SkinnedMeshRenderer>();
                List<MeshRenderer> meshRenderers = new List<MeshRenderer>();
                Dictionary<string, Transform> bodyPartContainers = new Dictionary<string, Transform>();
                
                // Create organized hierarchy
                Transform meshContainer = new GameObject("Meshes").transform;
                meshContainer.SetParent(characterRoot.transform);

                foreach (string partPath in bodyPartsArray.Select(x => x.ToString()))
                {
                    GameObject part = AssetDatabase.LoadAssetAtPath<GameObject>(SanitizeAssetPath(partPath));
                    if (part != null)
                    {
                        GameObject partInstance = GameObject.Instantiate(part, meshContainer);
                        partInstance.name = Path.GetFileNameWithoutExtension(partPath);
                        bodyParts.Add(partInstance);
                        
                        // Organize by body part type
                        string partType = GetBodyPartType(partInstance.name);
                        if (!bodyPartContainers.ContainsKey(partType))
                        {
                            GameObject container = new GameObject(partType);
                            container.transform.SetParent(meshContainer);
                            bodyPartContainers[partType] = container.transform;
                        }
                        partInstance.transform.SetParent(bodyPartContainers[partType]);
                        
                        // Collect renderers
                        skinnedRenderers.AddRange(partInstance.GetComponentsInChildren<SkinnedMeshRenderer>());
                        meshRenderers.AddRange(partInstance.GetComponentsInChildren<MeshRenderer>());
                    }
                }

                if (combineMeshes && skinnedRenderers.Count > 1)
                {
                    CombineSkinnedMeshesAdvanced(characterRoot, skinnedRenderers);
                }

                // Setup proper bounds and colliders
                SetupCharacterBounds(characterRoot);
                
                // Add NavMesh Agent if needed
                NavMeshAgent navAgent = characterRoot.AddComponent<NavMeshAgent>();
                navAgent.enabled = false; // Disabled by default

                // Auto-rig if requested
                if (autoRig)
                {
                    SetupHumanoidRig(characterRoot);
                }

                // Create prefab with proper variant support
                string prefabPath = $"{targetDirectory}/{characterName}.prefab";
                GameObject prefab = PrefabUtility.SaveAsPrefabAsset(characterRoot, prefabPath);
                
                // Clean up scene object
                GameObject.DestroyImmediate(characterRoot);

                // Generate LODs with Unity 6.2 LOD system
                if (generateLods)
                {
                    GenerateCharacterLODs(prefabPath, 3);
                }

                // Mark assets for reimport to ensure proper processing
                AssetDatabase.ImportAsset(prefabPath, ImportAssetOptions.ForceUpdate);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                return Response.Success("Character assembled successfully with Unity 6.2 features.", new
                {
                    characterPath = prefabPath,
                    partsCount = bodyParts.Count,
                    hasAnimator = true,
                    autoRigged = autoRig,
                    lodsGenerated = generateLods,
                    hierarchyOrganized = true,
                    navmeshReady = true
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to assemble character: {e.Message}");
            }
        }

        private static object ApplyGeneratedTexturesToCharacter(JObject @params)
        {
            string characterPath = @params["character_path"]?.ToString();
            var textureMappings = @params["texture_mappings"] as JObject;
            string materialTemplate = @params["material_template"]?.ToString();
            bool createMaterialVariants = @params["create_material_variants"]?.ToObject<bool>() ?? true;
            bool applyToLods = @params["apply_to_lods"]?.ToObject<bool>() ?? true;

            if (string.IsNullOrEmpty(characterPath))
                return Response.Error("Character path is required.");

            if (textureMappings == null)
                return Response.Error("Texture mappings are required.");

            try
            {
                GameObject character = AssetDatabase.LoadAssetAtPath<GameObject>(SanitizeAssetPath(characterPath));
                if (character == null)
                    return Response.Error($"Character not found at path: {characterPath}");

                // Load material template if provided
                Material baseMaterial = null;
                if (!string.IsNullOrEmpty(materialTemplate))
                {
                    baseMaterial = AssetDatabase.LoadAssetAtPath<Material>(SanitizeAssetPath(materialTemplate));
                }

                // Get all renderers
                var renderers = character.GetComponentsInChildren<Renderer>(true);
                List<Material> createdMaterials = new List<Material>();

                foreach (var renderer in renderers)
                {
                    Material[] materials = renderer.sharedMaterials;
                    
                    for (int i = 0; i < materials.Length; i++)
                    {
                        Material originalMaterial = materials[i];
                        if (originalMaterial == null) continue;

                        // Create new material or use template
                        Material newMaterial;
                        if (baseMaterial != null)
                        {
                            newMaterial = new Material(baseMaterial);
                        }
                        else
                        {
                            newMaterial = new Material(originalMaterial);
                        }

                        // Apply texture mappings
                        foreach (var mapping in textureMappings.Properties())
                        {
                            string textureSlot = mapping.Name;
                            string texturePath = mapping.Value.ToString();
                            
                            Texture2D texture = AssetDatabase.LoadAssetAtPath<Texture2D>(SanitizeAssetPath(texturePath));
                            if (texture != null)
                            {
                                // Map common texture slots
                                switch (textureSlot.ToLower())
                                {
                                    case "albedo":
                                    case "diffuse":
                                    case "maintex":
                                        newMaterial.SetTexture("_MainTex", texture);
                                        newMaterial.SetTexture("_BaseMap", texture); // URP
                                        break;
                                    case "normal":
                                    case "normalmap":
                                        newMaterial.SetTexture("_BumpMap", texture);
                                        newMaterial.SetTexture("_NormalMap", texture); // URP
                                        break;
                                    case "metallic":
                                        newMaterial.SetTexture("_MetallicGlossMap", texture);
                                        break;
                                    case "roughness":
                                    case "smoothness":
                                        newMaterial.SetTexture("_SpecGlossMap", texture);
                                        break;
                                    case "emission":
                                        newMaterial.SetTexture("_EmissionMap", texture);
                                        newMaterial.EnableKeyword("_EMISSION");
                                        break;
                                    case "occlusion":
                                        newMaterial.SetTexture("_OcclusionMap", texture);
                                        break;
                                    default:
                                        // Try to set by property name directly
                                        if (newMaterial.HasProperty(textureSlot))
                                        {
                                            newMaterial.SetTexture(textureSlot, texture);
                                        }
                                        break;
                                }
                            }
                        }

                        // Save material
                        string materialPath = $"{Path.GetDirectoryName(characterPath)}/Materials/{character.name}_{renderer.name}_{i}.mat";
                        string materialDir = Path.GetDirectoryName(materialPath);
                        
                        if (!AssetDatabase.IsValidFolder(materialDir))
                        {
                            AssetDatabase.CreateFolder(Path.GetDirectoryName(materialDir), "Materials");
                        }

                        AssetDatabase.CreateAsset(newMaterial, materialPath);
                        materials[i] = newMaterial;
                        createdMaterials.Add(newMaterial);
                    }

                    renderer.sharedMaterials = materials;
                }

                // Apply to LODs if requested
                if (applyToLods)
                {
                    var lodGroup = character.GetComponent<LODGroup>();
                    if (lodGroup != null)
                    {
                        ApplyMaterialsToLODs(lodGroup, createdMaterials);
                    }
                }

                // Save changes
                EditorUtility.SetDirty(character);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                return Response.Success("Textures applied successfully.", new
                {
                    materialsCreated = createdMaterials.Count,
                    renderersUpdated = renderers.Length,
                    appliedToLods = applyToLods && character.GetComponent<LODGroup>() != null
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to apply textures: {e.Message}");
            }
        }

        private static object SetupProceduralRigging(JObject @params)
        {
            string characterPath = @params["character_path"]?.ToString();
            string rigType = @params["rig_type"]?.ToString() ?? "humanoid";
            var boneMappingObj = @params["bone_mapping"] as JObject;
            bool autoConfigureAvatar = @params["auto_configure_avatar"]?.ToObject<bool>() ?? true;
            bool generateIkTargets = @params["generate_ik_targets"]?.ToObject<bool>() ?? true;
            bool setupConstraints = @params["setup_constraints"]?.ToObject<bool>() ?? true;

            if (string.IsNullOrEmpty(characterPath))
                return Response.Error("Character path is required.");

            try
            {
                GameObject character = AssetDatabase.LoadAssetAtPath<GameObject>(SanitizeAssetPath(characterPath));
                if (character == null)
                    return Response.Error($"Character not found at path: {characterPath}");

                // Get model importer
                ModelImporter importer = AssetImporter.GetAtPath(characterPath) as ModelImporter;
                if (importer == null)
                    return Response.Error("Character must be a model asset for rigging setup.");

                // Configure rig type
                switch (rigType.ToLower())
                {
                    case "humanoid":
                        importer.animationType = ModelImporterAnimationType.Human;
                        break;
                    case "generic":
                        importer.animationType = ModelImporterAnimationType.Generic;
                        break;
                    case "legacy":
                        importer.animationType = ModelImporterAnimationType.Legacy;
                        break;
                    default:
                        return Response.Error($"Unknown rig type: {rigType}. Valid types: humanoid, generic, legacy");
                }

                // Auto-configure avatar for humanoid
                if (rigType.ToLower() == "humanoid" && autoConfigureAvatar)
                {
                    importer.avatarSetup = ModelImporterAvatarSetup.CreateFromThisModel;
                    importer.optimizeGameObjects = true;
                }

                // Apply bone mapping if provided
                if (boneMappingObj != null && rigType.ToLower() == "humanoid")
                {
                    var humanDescription = importer.humanDescription;
                    var humanBones = humanDescription.human.ToList();

                    foreach (var mapping in boneMappingObj.Properties())
                    {
                        string humanBoneName = mapping.Name;
                        string boneName = mapping.Value.ToString();
                        
                        // Find and update bone mapping
                        var existingBone = humanBones.FirstOrDefault(b => b.humanName == humanBoneName);
                        if (existingBone.humanName != null)
                        {
                            existingBone.boneName = boneName;
                        }
                        else
                        {
                            humanBones.Add(new HumanBone
                            {
                                humanName = humanBoneName,
                                boneName = boneName,
                                limit = new HumanLimit { useDefaultValues = true }
                            });
                        }
                    }

                    humanDescription.human = humanBones.ToArray();
                    importer.humanDescription = humanDescription;
                }

                // Reimport to apply changes
                AssetDatabase.ImportAsset(characterPath, ImportAssetOptions.ForceUpdate);

                // Setup IK targets and constraints if requested
                if (generateIkTargets || setupConstraints)
                {
                    GameObject characterInstance = PrefabUtility.InstantiatePrefab(character) as GameObject;
                    
                    if (generateIkTargets)
                    {
                        SetupIKTargets(characterInstance);
                    }

                    if (setupConstraints)
                    {
                        SetupAnimationConstraints(characterInstance);
                    }

                    // Update prefab
                    PrefabUtility.ApplyPrefabInstance(characterInstance, InteractionMode.AutomatedAction);
                    GameObject.DestroyImmediate(characterInstance);
                }

                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                return Response.Success("Rigging setup completed successfully.", new
                {
                    rigType = rigType,
                    avatarConfigured = autoConfigureAvatar && rigType.ToLower() == "humanoid",
                    ikTargetsGenerated = generateIkTargets,
                    constraintsSetup = setupConstraints
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to setup rigging: {e.Message}");
            }
        }

        private static object CreateCharacterAnimationController(JObject @params)
        {
            string characterPath = @params["character_path"]?.ToString();
            string controllerName = @params["controller_name"]?.ToString();
            var animationClipsArray = @params["animation_clips"] as JArray;
            string stateMachineType = @params["state_machine_type"]?.ToString() ?? "basic";
            bool includeBlendTrees = @params["include_blend_trees"]?.ToObject<bool>() ?? true;
            bool setupParameters = @params["setup_parameters"]?.ToObject<bool>() ?? true;

            if (string.IsNullOrEmpty(characterPath))
                return Response.Error("Character path is required.");

            if (string.IsNullOrEmpty(controllerName))
                return Response.Error("Controller name is required.");

            try
            {
                GameObject character = AssetDatabase.LoadAssetAtPath<GameObject>(SanitizeAssetPath(characterPath));
                if (character == null)
                    return Response.Error($"Character not found at path: {characterPath}");

                // Create Animator Controller
                AnimatorController controller = AnimatorController.CreateAnimatorControllerAtPath(
                    $"{Path.GetDirectoryName(characterPath)}/{controllerName}.controller");

                // Setup parameters if requested
                if (setupParameters)
                {
                    controller.AddParameter("Speed", AnimatorControllerParameterType.Float);
                    controller.AddParameter("IsGrounded", AnimatorControllerParameterType.Bool);
                    controller.AddParameter("Jump", AnimatorControllerParameterType.Trigger);
                    controller.AddParameter("Attack", AnimatorControllerParameterType.Trigger);
                    controller.AddParameter("Die", AnimatorControllerParameterType.Trigger);
                }

                // Get base layer
                var baseLayer = controller.layers[0];
                var stateMachine = baseLayer.stateMachine;

                // Load animation clips
                List<AnimationClip> clips = new List<AnimationClip>();
                if (animationClipsArray != null)
                {
                    foreach (string clipPath in animationClipsArray.Select(x => x.ToString()))
                    {
                        AnimationClip clip = AssetDatabase.LoadAssetAtPath<AnimationClip>(SanitizeAssetPath(clipPath));
                        if (clip != null)
                        {
                            clips.Add(clip);
                        }
                    }
                }

                // Create states based on state machine type
                switch (stateMachineType.ToLower())
                {
                    case "basic":
                        CreateBasicStateMachine(stateMachine, clips, includeBlendTrees);
                        break;
                    case "advanced":
                        CreateAdvancedStateMachine(stateMachine, clips, includeBlendTrees);
                        break;
                    case "locomotion":
                        CreateLocomotionStateMachine(stateMachine, clips, includeBlendTrees);
                        break;
                    default:
                        return Response.Error($"Unknown state machine type: {stateMachineType}");
                }

                // Assign controller to character
                Animator animator = character.GetComponent<Animator>();
                if (animator == null)
                {
                    animator = character.AddComponent<Animator>();
                }
                animator.runtimeAnimatorController = controller;

                // Save changes
                EditorUtility.SetDirty(character);
                EditorUtility.SetDirty(controller);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                return Response.Success("Animation controller created successfully.", new
                {
                    controllerPath = AssetDatabase.GetAssetPath(controller),
                    statesCreated = stateMachine.states.Length,
                    parametersSetup = setupParameters,
                    blendTreesIncluded = includeBlendTrees,
                    clipsUsed = clips.Count
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create animation controller: {e.Message}");
            }
        }

        private static object GenerateCharacterVariations(JObject @params)
        {
            string baseCharacterPath = @params["base_character_path"]?.ToString();
            int variationCount = @params["variation_count"]?.ToObject<int>() ?? 5;
            var variationTypesArray = @params["variation_types"] as JArray;
            bool colorVariations = @params["color_variations"]?.ToObject<bool>() ?? true;
            bool accessoryVariations = @params["accessory_variations"]?.ToObject<bool>() ?? true;
            bool bodyShapeVariations = @params["body_shape_variations"]?.ToObject<bool>() ?? false;

            if (string.IsNullOrEmpty(baseCharacterPath))
                return Response.Error("Base character path is required.");

            try
            {
                GameObject baseCharacter = AssetDatabase.LoadAssetAtPath<GameObject>(SanitizeAssetPath(baseCharacterPath));
                if (baseCharacter == null)
                    return Response.Error($"Base character not found at path: {baseCharacterPath}");

                List<string> createdVariations = new List<string>();
                string baseDir = Path.GetDirectoryName(baseCharacterPath);
                string variationsDir = $"{baseDir}/Variations";

                // Create variations directory
                if (!AssetDatabase.IsValidFolder(variationsDir))
                {
                    AssetDatabase.CreateFolder(baseDir, "Variations");
                }

                for (int i = 0; i < variationCount; i++)
                {
                    string variationName = $"{baseCharacter.name}_Variation_{i + 1:D2}";
                    string variationPath = $"{variationsDir}/{variationName}.prefab";

                    // Create variation instance
                    GameObject variation = PrefabUtility.InstantiatePrefab(baseCharacter) as GameObject;
                    variation.name = variationName;

                    // Apply color variations
                    if (colorVariations)
                    {
                        ApplyColorVariation(variation, i);
                    }

                    // Apply accessory variations
                    if (accessoryVariations)
                    {
                        ApplyAccessoryVariation(variation, i);
                    }

                    // Apply body shape variations (if supported)
                    if (bodyShapeVariations)
                    {
                        ApplyBodyShapeVariation(variation, i);
                    }

                    // Save as new prefab
                    GameObject variationPrefab = PrefabUtility.SaveAsPrefabAsset(variation, variationPath);
                    createdVariations.Add(variationPath);

                    // Clean up scene object
                    GameObject.DestroyImmediate(variation);
                }

                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                return Response.Success("Character variations generated successfully.", new
                {
                    variationsCreated = createdVariations.Count,
                    variationPaths = createdVariations,
                    colorVariations = colorVariations,
                    accessoryVariations = accessoryVariations,
                    bodyShapeVariations = bodyShapeVariations
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to generate character variations: {e.Message}");
            }
        }

        private static object SetupCharacterLodsProceduralGeneration(JObject @params)
        {
            string characterPath = @params["character_path"]?.ToString();
            int lodLevels = @params["lod_levels"]?.ToObject<int>() ?? 3;
            var reductionPercentagesArray = @params["reduction_percentages"] as JArray;
            bool autoGenerate = @params["auto_generate"]?.ToObject<bool>() ?? true;
            bool preserveSilhouette = @params["preserve_silhouette"]?.ToObject<bool>() ?? true;
            bool optimizeMaterials = @params["optimize_materials"]?.ToObject<bool>() ?? true;

            if (string.IsNullOrEmpty(characterPath))
                return Response.Error("Character path is required.");

            try
            {
                GameObject character = AssetDatabase.LoadAssetAtPath<GameObject>(SanitizeAssetPath(characterPath));
                if (character == null)
                    return Response.Error($"Character not found at path: {characterPath}");

                // Parse reduction percentages
                List<float> reductionPercentages = new List<float>();
                if (reductionPercentagesArray != null)
                {
                    reductionPercentages = reductionPercentagesArray.Select(x => x.ToObject<float>()).ToList();
                }
                else
                {
                    // Default reduction percentages
                    for (int i = 1; i <= lodLevels; i++)
                    {
                        reductionPercentages.Add(1.0f - (0.3f * i)); // 70%, 40%, 10%
                    }
                }

                if (autoGenerate)
                {
                    GenerateCharacterLODs(characterPath, lodLevels, reductionPercentages.ToArray());
                }

                // Setup LOD Group
                GameObject characterInstance = PrefabUtility.InstantiatePrefab(character) as GameObject;
                LODGroup lodGroup = characterInstance.GetComponent<LODGroup>();
                if (lodGroup == null)
                {
                    lodGroup = characterInstance.AddComponent<LODGroup>();
                }

                // Configure LOD levels
                LOD[] lods = new LOD[lodLevels + 1]; // +1 for LOD0 (original)
                
                // LOD0 - Original
                var originalRenderers = characterInstance.GetComponentsInChildren<Renderer>();
                lods[0] = new LOD(0.6f, originalRenderers);

                /// <summary>
                /// [UNITY 6.2] - Criar LODs adicionais usando Unity LODGroup APIs reais.
                /// </summary>
                for (int i = 1; i <= lodLevels; i++)
                {
                    float screenRelativeHeight = 0.6f - (0.2f * i);
                    float lodQuality = 1.0f - (0.3f * i); // Reduzir qualidade progressivamente

                    // Criar renderers simplificados para este LOD usando Unity 6.2 Mesh APIs
                    Renderer[] lodRenderers = CreateLODRenderers(character, originalRenderers, lodQuality, $"_LOD{i}");
                    lods[i] = new LOD(screenRelativeHeight, lodRenderers);
                }

                lodGroup.SetLODs(lods);
                lodGroup.RecalculateBounds();

                // Update prefab
                PrefabUtility.ApplyPrefabInstance(characterInstance, InteractionMode.AutomatedAction);
                GameObject.DestroyImmediate(characterInstance);

                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                return Response.Success("Character LODs setup successfully.", new
                {
                    lodLevels = lodLevels,
                    reductionPercentages = reductionPercentages,
                    autoGenerated = autoGenerate,
                    silhouettePreserved = preserveSilhouette,
                    materialsOptimized = optimizeMaterials
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to setup character LODs: {e.Message}");
            }
        }

        private static object CreateCharacterCustomizationSystem(JObject @params)
        {
            string characterPath = @params["character_path"]?.ToString();
            var customizationOptions = @params["customization_options"] as JObject;
            bool runtimeSwitching = @params["runtime_switching"]?.ToObject<bool>() ?? true;
            bool savePresets = @params["save_presets"]?.ToObject<bool>() ?? true;
            bool uiIntegration = @params["ui_integration"]?.ToObject<bool>() ?? false;

            if (string.IsNullOrEmpty(characterPath))
                return Response.Error("Character path is required.");

            if (customizationOptions == null)
                return Response.Error("Customization options are required.");

            try
            {
                GameObject character = AssetDatabase.LoadAssetAtPath<GameObject>(SanitizeAssetPath(characterPath));
                if (character == null)
                    return Response.Error($"Character not found at path: {characterPath}");

                // Create customization script
                string scriptContent = GenerateCustomizationScript(character.name, customizationOptions, runtimeSwitching, savePresets);
                string scriptPath = $"{Path.GetDirectoryName(characterPath)}/Scripts/{character.name}Customization.cs";
                
                // Create Scripts directory if it doesn't exist
                string scriptsDir = Path.GetDirectoryName(scriptPath);
                if (!AssetDatabase.IsValidFolder(scriptsDir))
                {
                    AssetDatabase.CreateFolder(Path.GetDirectoryName(scriptsDir), "Scripts");
                }

                File.WriteAllText(Path.Combine(Directory.GetCurrentDirectory(), scriptPath), scriptContent);
                AssetDatabase.ImportAsset(scriptPath);

                // Add customization component to character
                GameObject characterInstance = PrefabUtility.InstantiatePrefab(character) as GameObject;
                
                // Wait for script compilation
                AssetDatabase.Refresh();
                
                // Create customization data asset
                if (savePresets)
                {
                    CreateCustomizationPresets(characterPath, customizationOptions);
                }

                // Update prefab
                PrefabUtility.ApplyPrefabInstance(characterInstance, InteractionMode.AutomatedAction);
                GameObject.DestroyImmediate(characterInstance);

                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                return Response.Success("Character customization system created successfully.", new
                {
                    scriptPath = scriptPath,
                    runtimeSwitching = runtimeSwitching,
                    presetsCreated = savePresets,
                    uiIntegration = uiIntegration,
                    customizationOptions = customizationOptions.Properties().Count()
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create customization system: {e.Message}");
            }
        }

        private static object ExportAssembledCharacterPrefab(JObject @params)
        {
            string characterPath = @params["character_path"]?.ToString();
            string exportPath = @params["export_path"]?.ToString();
            bool includeAnimations = @params["include_animations"]?.ToObject<bool>() ?? true;
            bool includeMaterials = @params["include_materials"]?.ToObject<bool>() ?? true;
            bool includeTextures = @params["include_textures"]?.ToObject<bool>() ?? true;
            bool optimizeForRuntime = @params["optimize_for_runtime"]?.ToObject<bool>() ?? true;
            bool createAddressable = @params["create_addressable"]?.ToObject<bool>() ?? false;

            if (string.IsNullOrEmpty(characterPath))
                return Response.Error("Character path is required.");

            if (string.IsNullOrEmpty(exportPath))
                return Response.Error("Export path is required.");

            try
            {
                GameObject character = AssetDatabase.LoadAssetAtPath<GameObject>(SanitizeAssetPath(characterPath));
                if (character == null)
                    return Response.Error($"Character not found at path: {characterPath}");

                // Ensure export directory exists
                string exportDir = Path.GetDirectoryName(exportPath);
                if (!AssetDatabase.IsValidFolder(exportDir))
                {
                    string[] pathParts = exportDir.Split('/');
                    string currentPath = pathParts[0];
                    for (int i = 1; i < pathParts.Length; i++)
                    {
                        string newPath = currentPath + "/" + pathParts[i];
                        if (!AssetDatabase.IsValidFolder(newPath))
                        {
                            AssetDatabase.CreateFolder(currentPath, pathParts[i]);
                        }
                        currentPath = newPath;
                    }
                }

                // Create optimized copy if requested
                GameObject exportCharacter = character;
                if (optimizeForRuntime)
                {
                    exportCharacter = OptimizeCharacterForRuntime(character, includeAnimations, includeMaterials, includeTextures);
                }

                // Create final prefab
                GameObject finalPrefab = PrefabUtility.SaveAsPrefabAsset(exportCharacter, exportPath);

                // Setup as Addressable if requested
                if (createAddressable)
                {
                    SetupAddressableAsset(exportPath);
                }

                // Clean up if we created an optimized copy
                if (optimizeForRuntime && exportCharacter != character)
                {
                    GameObject.DestroyImmediate(exportCharacter);
                }

                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                return Response.Success("Character prefab exported successfully.", new
                {
                    exportPath = exportPath,
                    optimized = optimizeForRuntime,
                    addressable = createAddressable,
                    includeAnimations = includeAnimations,
                    includeMaterials = includeMaterials,
                    includeTextures = includeTextures
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to export character prefab: {e.Message}");
            }
        }

        // Helper methods
        private static string SanitizeAssetPath(string path)
        {
            if (string.IsNullOrEmpty(path)) return string.Empty;
            
            path = path.Replace('\\', '/').Trim();
            if (path.StartsWith("Assets/")) return path;
            if (path.StartsWith("/")) path = path.Substring(1);
            if (!path.StartsWith("Assets/")) path = "Assets/" + path;
            
            return path;
        }

        private static void CombineSkinnedMeshesAdvanced(GameObject root, List<SkinnedMeshRenderer> renderers)
        {
            // Combine multiple SkinnedMeshRenderers into a single renderer to reduce draw-calls.
            // NOTE: This keeps the skinning information intact by re-using the original bones array.
            if (renderers == null || renderers.Count < 2)
            {
                Debug.Log("[ProceduralCharacterAssembly] Nothing to combine – one or fewer skinned meshes detected.");
                return;
            }

            // Gather all bones and build a unique list so that indices are preserved correctly.
            List<Transform> boneList = new List<Transform>();
            foreach (var smr in renderers)
            {
                foreach (var bone in smr.bones)
                {
                    if (!boneList.Contains(bone))
                    {
                        boneList.Add(bone);
                    }
                }
            }

            // Build CombineInstance array – one entry per sub-mesh so that material assignments survive the merge.
            List<CombineInstance> combineInstances = new List<CombineInstance>();
            List<Material> combinedMaterials = new List<Material>();
            foreach (var smr in renderers)
            {
                if (smr.sharedMesh == null) continue;
                var mesh = smr.sharedMesh;
                for (int subMeshIndex = 0; subMeshIndex < mesh.subMeshCount; subMeshIndex++)
                {
                    CombineInstance ci = new CombineInstance
                    {
                        mesh = mesh,
                        subMeshIndex = subMeshIndex,
                        transform = root.transform.worldToLocalMatrix * smr.transform.localToWorldMatrix
                    };
                    combineInstances.Add(ci);

                    // Store the matching material so sub-mesh indices line-up after the combine.
                    if (subMeshIndex < smr.sharedMaterials.Length)
                    {
                        combinedMaterials.Add(smr.sharedMaterials[subMeshIndex]);
                    }
                }
            }

            if (combineInstances.Count == 0)
            {
                Debug.LogWarning("[ProceduralCharacterAssembly] Skipped mesh combination – no valid sub-meshes detected.");
                return;
            }

            Mesh combinedMesh = new Mesh
            {
                name = root.name + "_CombinedMesh"
            };
            // false -> keep sub-meshes, true -> use transforms.
            combinedMesh.CombineMeshes(combineInstances.ToArray(), /*mergeSubMeshes*/ false, /*useMatrices*/ true, /*hasLightmapData*/ false);
            combinedMesh.RecalculateBounds();
            combinedMesh.RecalculateNormals();

            // Create new GameObject to hold the combined renderer.
            GameObject combinedGO = new GameObject("CombinedSkinnedMesh");
            combinedGO.transform.SetParent(root.transform, false);
            SkinnedMeshRenderer combinedSMR = combinedGO.AddComponent<SkinnedMeshRenderer>();
            combinedSMR.sharedMesh = combinedMesh;
            combinedSMR.sharedMaterials = combinedMaterials.ToArray();
            combinedSMR.bones = boneList.ToArray();
            combinedSMR.rootBone = renderers[0].rootBone;

            // Disable (do not destroy) original renderers so the prefab can still be reverted if needed.
            foreach (var smr in renderers)
            {
                smr.enabled = false;
            }

            Debug.Log("[ProceduralCharacterAssembly] Successfully combined " + renderers.Count + " skinned meshes into one.");
        }

        private static string GetBodyPartType(string partName)
        {
            string lowerName = partName.ToLower();
            if (lowerName.Contains("head") || lowerName.Contains("face")) return "Head";
            if (lowerName.Contains("torso") || lowerName.Contains("chest") || lowerName.Contains("body")) return "Torso";
            if (lowerName.Contains("arm") || lowerName.Contains("hand")) return "Arms";
            if (lowerName.Contains("leg") || lowerName.Contains("foot")) return "Legs";
            if (lowerName.Contains("accessory") || lowerName.Contains("hat") || lowerName.Contains("helmet")) return "Accessories";
            return "Other";
        }

        private static void SetupCharacterBounds(GameObject character)
        {
            // Setup proper character bounds and colliders
            var renderers = character.GetComponentsInChildren<Renderer>();
            if (renderers.Length > 0)
            {
                Bounds combinedBounds = renderers[0].bounds;
                foreach (var renderer in renderers)
                {
                    combinedBounds.Encapsulate(renderer.bounds);
                }
                
                // Add a capsule collider based on bounds
                CapsuleCollider capsule = character.AddComponent<CapsuleCollider>();
                capsule.center = combinedBounds.center - character.transform.position;
                capsule.height = combinedBounds.size.y;
                capsule.radius = Mathf.Max(combinedBounds.size.x, combinedBounds.size.z) * 0.5f;
            }
        }

        private static void SetupHumanoidRig(GameObject character)
        {
            // Setup humanoid rig configuration
            Animator animator = character.GetComponent<Animator>();
            if (animator != null)
            {
                animator.applyRootMotion = true;
                animator.updateMode = AnimatorUpdateMode.Normal;
                animator.cullingMode = AnimatorCullingMode.CullUpdateTransforms;
            }
        }

        private static void GenerateCharacterLODs(string characterPath, int lodLevels, float[] reductionPercentages = null)
        {
            GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>(SanitizeAssetPath(characterPath));
            if (prefab == null)
            {
                Debug.LogError($"[ProceduralCharacterAssembly] Could not load character prefab at {characterPath}");
                return;
            }

            // Make editable instance of the prefab.
            GameObject instance = PrefabUtility.InstantiatePrefab(prefab) as GameObject;
            if (instance == null)
            {
                Debug.LogError("[ProceduralCharacterAssembly] Failed to instantiate prefab for LOD generation.");
                return;
            }

            Renderer[] originalRenderers = instance.GetComponentsInChildren<Renderer>(true);
            if (originalRenderers.Length == 0)
            {
                Debug.LogWarning("[ProceduralCharacterAssembly] No renderers found on character – skipping LOD generation.");
                GameObject.DestroyImmediate(instance);
                return;
            }

            if (reductionPercentages == null || reductionPercentages.Length < lodLevels)
            {
                reductionPercentages = new float[lodLevels];
                for (int i = 0; i < lodLevels; i++)
                {
                    // Default curve: 70%, 40%, 15% of original triangles.
                    reductionPercentages[i] = 1.0f - Mathf.Lerp(0.3f, 0.85f, i / (float)(lodLevels - 1));
                }
            }

            LODGroup lodGroup = instance.GetComponent<LODGroup>();
            if (lodGroup == null)
            {
                lodGroup = instance.AddComponent<LODGroup>();
            }

            List<LOD> lods = new List<LOD>();
            // LOD0 – original quality at 0–60% screen height.
            lods.Add(new LOD(0.6f, originalRenderers));

            for (int i = 0; i < lodLevels; i++)
            {
                float quality = Mathf.Clamp01(reductionPercentages[i]);
                Renderer[] lodRenderers = CreateLODRenderers(instance, originalRenderers, quality, $"_LOD{i + 1}");
                float relativeHeight = Mathf.Clamp01(0.6f - 0.2f * (i + 1));
                lods.Add(new LOD(relativeHeight, lodRenderers));
            }

            lodGroup.SetLODs(lods.ToArray());
            lodGroup.fadeMode = LODFadeMode.CrossFade;
            lodGroup.animateCrossFading = true;
            lodGroup.RecalculateBounds();

            // Apply changes back to the prefab and clean-up.
            PrefabUtility.ApplyPrefabInstance(instance, InteractionMode.AutomatedAction);
            GameObject.DestroyImmediate(instance);

            Debug.Log($"[ProceduralCharacterAssembly] Generated {lodLevels} LOD level(s) for {characterPath}.");
        }

        private static void ApplyMaterialsToLODs(LODGroup lodGroup, List<Material> materials)
        {
            // Apply materials to all LOD levels
            LOD[] lods = lodGroup.GetLODs();
            /// <summary>
            /// [UNITY 6.2] - Atribuição inteligente de materiais usando Unity Renderer APIs.
            /// </summary>
            foreach (var lod in lods)
            {
                foreach (var renderer in lod.renderers)
                {
                    if (renderer != null && materials.Count > 0)
                    {
                        try
                        {
                            // Atribuir materiais baseado no tipo de renderer e nome do objeto
                            Material[] assignedMaterials = AssignMaterialsIntelligently(renderer, materials);

                            if (assignedMaterials.Length == 1)
                            {
                                renderer.sharedMaterial = assignedMaterials[0];
                            }
                            else if (assignedMaterials.Length > 1)
                            {
                                renderer.sharedMaterials = assignedMaterials;
                            }

                            Debug.Log($"[ProceduralCharacterAssembly] Assigned {assignedMaterials.Length} materials to {renderer.name}");
                        }
                        catch (Exception e)
                        {
                            Debug.LogError($"[ProceduralCharacterAssembly] Failed to assign materials to {renderer.name}: {e.Message}");
                            // Fallback para material padrão
                            renderer.sharedMaterial = materials[0];
                        }
                    }
                }
            }
        }

        private static void SetupIKTargets(GameObject character)
        {
            // Setup IK targets for hands and feet
            Animator animator = character.GetComponent<Animator>();
            if (animator != null && animator.isHuman)
            {
                // Create IK target GameObjects
                Transform leftHand = animator.GetBoneTransform(HumanBodyBones.LeftHand);
                Transform rightHand = animator.GetBoneTransform(HumanBodyBones.RightHand);
                Transform leftFoot = animator.GetBoneTransform(HumanBodyBones.LeftFoot);
                Transform rightFoot = animator.GetBoneTransform(HumanBodyBones.RightFoot);

                if (leftHand != null) CreateIKTarget(character.transform, "LeftHandIK", leftHand.position);
                if (rightHand != null) CreateIKTarget(character.transform, "RightHandIK", rightHand.position);
                if (leftFoot != null) CreateIKTarget(character.transform, "LeftFootIK", leftFoot.position);
                if (rightFoot != null) CreateIKTarget(character.transform, "RightFootIK", rightFoot.position);
            }
        }

        private static void CreateIKTarget(Transform parent, string name, Vector3 position)
        {
            GameObject ikTarget = new GameObject(name);
            ikTarget.transform.SetParent(parent);
            ikTarget.transform.position = position;
        }

        private static void SetupAnimationConstraints(GameObject character)
        {
            // Setup animation constraints using Unity's Animation Rigging package
#if UNITY_ANIMATION_RIGGING
            Animator animator = character.GetComponent<Animator>();
            if (animator == null || !animator.isHuman)
            {
                Debug.LogWarning("[ProceduralCharacterAssembly] Character must have a humanoid Animator for constraints.");
                return;
            }

            // Add Rig Builder component
            var rigBuilder = character.GetComponent<RigBuilder>();
            if (rigBuilder == null)
            {
                rigBuilder = character.AddComponent<RigBuilder>();
            }

            // Create constraint rig GameObject
            GameObject constraintRig = new GameObject("ConstraintRig");
            constraintRig.transform.SetParent(character.transform);
            constraintRig.transform.localPosition = Vector3.zero;
            constraintRig.transform.localRotation = Quaternion.identity;
            constraintRig.transform.localScale = Vector3.one;

            // Add Rig component
            var rig = constraintRig.AddComponent<Rig>();
            rig.weight = 1.0f;

            // Setup Two Bone IK constraints for limbs
            SetupTwoBoneIKConstraints(constraintRig, animator);

            // Setup Multi-Position constraints for spine
            SetupSpineConstraints(constraintRig, animator);

            // Setup Look At constraints for head
            SetupHeadConstraints(constraintRig, animator);

            // Add the rig to the RigBuilder
            var rigLayers = new List<RigLayer>();
            if (rigBuilder.layers != null)
            {
                rigLayers.AddRange(rigBuilder.layers);
            }
            rigLayers.Add(new RigLayer(rig, true));
            rigBuilder.layers = rigLayers;

            // Build the rig
            rigBuilder.Build();

            Debug.Log("[ProceduralCharacterAssembly] Animation constraints setup completed.");
#else
            Debug.LogWarning("[ProceduralCharacterAssembly] Animation Rigging package not installed. Constraints will not be set up.");
#endif
        }

        private static void CreateBasicStateMachine(AnimatorStateMachine stateMachine, List<AnimationClip> clips, bool includeBlendTrees)
        {
            // Create basic idle, walk, run states
            if (clips.Count > 0)
            {
                var idleState = stateMachine.AddState("Idle");
                idleState.motion = clips.FirstOrDefault(c => c.name.ToLower().Contains("idle"));
                stateMachine.defaultState = idleState;

                if (includeBlendTrees)
                {
                    CreateLocomotionBlendTree(stateMachine, clips);
                }
            }
        }

        private static void CreateAdvancedStateMachine(AnimatorStateMachine stateMachine, List<AnimationClip> clips, bool includeBlendTrees)
        {
            // Create advanced state machine with sub-state machines
            CreateBasicStateMachine(stateMachine, clips, includeBlendTrees);
            
            // Add combat sub-state machine
            var combatStateMachine = stateMachine.AddStateMachine("Combat");
            // Add more complex logic here
        }

        private static void CreateLocomotionStateMachine(AnimatorStateMachine stateMachine, List<AnimationClip> clips, bool includeBlendTrees)
        {
            // Create locomotion-focused state machine
            if (includeBlendTrees)
            {
                CreateLocomotionBlendTree(stateMachine, clips);
            }
        }

        private static void CreateLocomotionBlendTree(AnimatorStateMachine stateMachine, List<AnimationClip> clips)
        {
            // Create blend tree for locomotion
            var blendTree = new BlendTree();
            blendTree.name = "Locomotion";
            blendTree.blendType = BlendTreeType.Simple1D;
            blendTree.blendParameter = "Speed";

            // Add clips to blend tree
            var idleClip = clips.FirstOrDefault(c => c.name.ToLower().Contains("idle"));
            var walkClip = clips.FirstOrDefault(c => c.name.ToLower().Contains("walk"));
            var runClip = clips.FirstOrDefault(c => c.name.ToLower().Contains("run"));

            if (idleClip != null) blendTree.AddChild(idleClip, 0f);
            if (walkClip != null) blendTree.AddChild(walkClip, 0.5f);
            if (runClip != null) blendTree.AddChild(runClip, 1f);

            var locomotionState = stateMachine.AddState("Locomotion");
            locomotionState.motion = blendTree;
            stateMachine.defaultState = locomotionState;
        }

        private static void ApplyColorVariation(GameObject character, int variationIndex)
        {
            // Apply color variations to materials
            var renderers = character.GetComponentsInChildren<Renderer>();
            Color[] variationColors = {
                Color.red, Color.blue, Color.green, Color.yellow, Color.magenta,
                Color.cyan, new Color(1f, 0.5f, 0f), new Color(0.5f, 0f, 1f)
            };

            Color targetColor = variationColors[variationIndex % variationColors.Length];

            foreach (var renderer in renderers)
            {
                foreach (var material in renderer.materials)
                {
                    if (material.HasProperty("_Color"))
                    {
                        material.color = targetColor;
                    }
                    if (material.HasProperty("_BaseColor"))
                    {
                        material.SetColor("_BaseColor", targetColor);
                    }
                }
            }
        }

        private static void ApplyAccessoryVariation(GameObject character, int variationIndex)
        {
            // Apply accessory variations by enabling/disabling accessory objects or swapping materials
            Transform[] accessories = character.GetComponentsInChildren<Transform>()
                .Where(t => t.name.ToLower().Contains("accessory") || 
                           t.name.ToLower().Contains("hat") || 
                           t.name.ToLower().Contains("helmet") ||
                           t.name.ToLower().Contains("glasses") ||
                           t.name.ToLower().Contains("jewelry"))
                .ToArray();

            if (accessories.Length == 0)
            {
                Debug.Log($"[ProceduralCharacterAssembly] No accessories found on {character.name} for variation.");
                return;
            }

            // Create variation pattern based on index
            for (int i = 0; i < accessories.Length; i++)
            {
                bool shouldEnable = (variationIndex + i) % 3 != 0; // Vary which accessories are enabled
                accessories[i].gameObject.SetActive(shouldEnable);
            }

            // Also try to find and vary materials on accessory renderers
            var accessoryRenderers = character.GetComponentsInChildren<Renderer>()
                .Where(r => r.name.ToLower().Contains("accessory") || 
                           r.name.ToLower().Contains("hat") || 
                           r.name.ToLower().Contains("helmet"))
                .ToArray();

            foreach (var renderer in accessoryRenderers)
            {
                if (renderer.materials.Length > 0)
                {
                    Material[] materials = renderer.materials;
                    for (int i = 0; i < materials.Length; i++)
                    {
                        if (materials[i] != null)
                        {
                            // Create material variant with different properties
                            Material variantMaterial = new Material(materials[i]);
                            
                            // Vary metallic/smoothness based on variation index
                            if (variantMaterial.HasProperty("_Metallic"))
                            {
                                float metallicValue = (variationIndex % 4) * 0.25f;
                                variantMaterial.SetFloat("_Metallic", metallicValue);
                            }
                            
                            if (variantMaterial.HasProperty("_Smoothness"))
                            {
                                float smoothnessValue = 0.3f + (variationIndex % 5) * 0.15f;
                                variantMaterial.SetFloat("_Smoothness", smoothnessValue);
                            }
                            
                            materials[i] = variantMaterial;
                        }
                    }
                    renderer.materials = materials;
                }
            }

            Debug.Log($"[ProceduralCharacterAssembly] Applied accessory variation {variationIndex} to {character.name} - {accessories.Length} accessories processed.");
        }

        private static void ApplyBodyShapeVariation(GameObject character, int variationIndex)
        {
            // Apply body shape variations by modifying blend shapes or scaling body parts
            var skinnedMeshRenderers = character.GetComponentsInChildren<SkinnedMeshRenderer>();
            
            foreach (var smr in skinnedMeshRenderers)
            {
                if (smr.sharedMesh != null && smr.sharedMesh.blendShapeCount > 0)
                {
                    // Apply blend shape variations if available
                    for (int i = 0; i < smr.sharedMesh.blendShapeCount; i++)
                    {
                        string blendShapeName = smr.sharedMesh.GetBlendShapeName(i);
                        
                        // Apply different blend shape weights based on variation index
                        float weight = 0f;
                        if (blendShapeName.ToLower().Contains("muscle") || blendShapeName.ToLower().Contains("bulk"))
                        {
                            weight = (variationIndex % 3) * 33.33f; // 0%, 33%, 66%
                        }
                        else if (blendShapeName.ToLower().Contains("thin") || blendShapeName.ToLower().Contains("slim"))
                        {
                            weight = ((variationIndex + 1) % 3) * 33.33f;
                        }
                        else if (blendShapeName.ToLower().Contains("fat") || blendShapeName.ToLower().Contains("heavy"))
                        {
                            weight = ((variationIndex + 2) % 3) * 33.33f;
                        }
                        
                        smr.SetBlendShapeWeight(i, weight);
                    }
                }
                
                // If no blend shapes, try scaling body parts
                if (smr.sharedMesh.blendShapeCount == 0)
                {
                    Transform bodyPart = smr.transform;
                    string partName = bodyPart.name.ToLower();
                    
                    Vector3 scaleModifier = Vector3.one;
                    
                    // Apply different scaling based on body part and variation
                    if (partName.Contains("torso") || partName.Contains("chest"))
                    {
                        float scaleX = 1.0f + (variationIndex % 5 - 2) * 0.05f; // ±10% variation
                        float scaleZ = 1.0f + (variationIndex % 3 - 1) * 0.03f; // ±6% variation
                        scaleModifier = new Vector3(scaleX, 1.0f, scaleZ);
                    }
                    else if (partName.Contains("arm") || partName.Contains("leg"))
                    {
                        float scale = 1.0f + (variationIndex % 4 - 1.5f) * 0.04f; // ±6% variation
                        scaleModifier = Vector3.one * scale;
                    }
                    else if (partName.Contains("head"))
                    {
                        float scale = 1.0f + (variationIndex % 3 - 1) * 0.02f; // ±2% variation
                        scaleModifier = Vector3.one * scale;
                    }
                    
                    bodyPart.localScale = Vector3.Scale(bodyPart.localScale, scaleModifier);
                }
            }
            
            // Also modify bone scales for more dramatic variations
            Animator animator = character.GetComponent<Animator>();
            if (animator != null && animator.isHuman)
            {
                // Scale spine bones for height variation
                Transform spine = animator.GetBoneTransform(HumanBodyBones.Spine);
                Transform chest = animator.GetBoneTransform(HumanBodyBones.Chest);
                
                if (spine != null)
                {
                    float spineScale = 1.0f + (variationIndex % 7 - 3) * 0.03f; // ±9% height variation
                    spine.localScale = new Vector3(spine.localScale.x, spineScale, spine.localScale.z);
                }
                
                if (chest != null)
                {
                    float chestScale = 1.0f + (variationIndex % 5 - 2) * 0.04f; // ±8% chest variation
                    chest.localScale = new Vector3(chestScale, chest.localScale.y, chestScale);
                }
                
                // Vary limb proportions slightly
                var limbs = new[]
                {
                    animator.GetBoneTransform(HumanBodyBones.LeftUpperArm),
                    animator.GetBoneTransform(HumanBodyBones.RightUpperArm),
                    animator.GetBoneTransform(HumanBodyBones.LeftUpperLeg),
                    animator.GetBoneTransform(HumanBodyBones.RightUpperLeg)
                };
                
                foreach (var limb in limbs)
                {
                    if (limb != null)
                    {
                        float limbScale = 1.0f + (variationIndex % 6 - 2.5f) * 0.02f; // ±5% limb variation
                        limb.localScale = Vector3.one * limbScale;
                    }
                }
            }

            Debug.Log($"[ProceduralCharacterAssembly] Applied body shape variation {variationIndex} to {character.name} - blend shapes and bone scaling modified.");
        }

        private static string GenerateCustomizationScript(string characterName, JObject customizationOptions, bool runtimeSwitching, bool savePresets)
        {
            var scriptBuilder = new System.Text.StringBuilder();
            
            scriptBuilder.AppendLine("using UnityEngine;");
            scriptBuilder.AppendLine("using System.Collections.Generic;");
            scriptBuilder.AppendLine("using System.Linq;");
            if (savePresets)
            {
                scriptBuilder.AppendLine("using System.IO;");
                scriptBuilder.AppendLine("using Newtonsoft.Json;");
            }
            scriptBuilder.AppendLine();
            
            scriptBuilder.AppendLine("[System.Serializable]");
            scriptBuilder.AppendLine("public class CustomizationPreset");
            scriptBuilder.AppendLine("{");
            scriptBuilder.AppendLine("    public string presetName;");
            scriptBuilder.AppendLine("    public Dictionary<string, int> customizationValues = new Dictionary<string, int>();");
            scriptBuilder.AppendLine("}");
            scriptBuilder.AppendLine();
            
            scriptBuilder.AppendLine($"public class {characterName}Customization : MonoBehaviour");
            scriptBuilder.AppendLine("{");
            
            // Generate fields based on customization options
            scriptBuilder.AppendLine("    [Header(\"Character Components\")]");
            scriptBuilder.AppendLine("    public SkinnedMeshRenderer[] bodyRenderers;");
            scriptBuilder.AppendLine("    public Renderer[] accessoryRenderers;");
            scriptBuilder.AppendLine("    public Animator characterAnimator;");
            scriptBuilder.AppendLine();
            
            scriptBuilder.AppendLine("    [Header(\"Customization Options\")]");
            foreach (var option in customizationOptions.Properties())
            {
                string optionName = option.Name;
                scriptBuilder.AppendLine($"    [Range(0, 10)] public int {optionName} = 0;");
            }
            scriptBuilder.AppendLine();
            
            if (savePresets)
            {
                scriptBuilder.AppendLine("    [Header(\"Presets\")]");
                scriptBuilder.AppendLine("    public List<CustomizationPreset> presets = new List<CustomizationPreset>();");
                scriptBuilder.AppendLine("    public string presetsFilePath = \"Assets/CharacterPresets.json\";");
                scriptBuilder.AppendLine();
            }
            
            scriptBuilder.AppendLine("    private Dictionary<string, Material[]> originalMaterials = new Dictionary<string, Material[]>();");
            scriptBuilder.AppendLine("    private Dictionary<string, Vector3> originalScales = new Dictionary<string, Vector3>();");
            scriptBuilder.AppendLine();
            
            // Start method
            scriptBuilder.AppendLine("    void Start()");
            scriptBuilder.AppendLine("    {");
            scriptBuilder.AppendLine("        InitializeCustomizationSystem();");
            if (savePresets)
            {
                scriptBuilder.AppendLine("        LoadPresets();");
            }
            scriptBuilder.AppendLine("        ApplyAllCustomizations();");
            scriptBuilder.AppendLine("    }");
            scriptBuilder.AppendLine();
            
            // Initialize method
            scriptBuilder.AppendLine("    void InitializeCustomizationSystem()");
            scriptBuilder.AppendLine("    {");
            scriptBuilder.AppendLine("        // Auto-find components if not assigned");
            scriptBuilder.AppendLine("        if (bodyRenderers == null || bodyRenderers.Length == 0)");
            scriptBuilder.AppendLine("            bodyRenderers = GetComponentsInChildren<SkinnedMeshRenderer>();");
            scriptBuilder.AppendLine();
            scriptBuilder.AppendLine("        if (accessoryRenderers == null || accessoryRenderers.Length == 0)");
            scriptBuilder.AppendLine("            accessoryRenderers = GetComponentsInChildren<Renderer>().Where(r => r.name.ToLower().Contains(\"accessory\")).ToArray();");
            scriptBuilder.AppendLine();
            scriptBuilder.AppendLine("        if (characterAnimator == null)");
            scriptBuilder.AppendLine("            characterAnimator = GetComponent<Animator>();");
            scriptBuilder.AppendLine();
            scriptBuilder.AppendLine("        // Store original materials and scales");
            scriptBuilder.AppendLine("        foreach (var renderer in bodyRenderers)");
            scriptBuilder.AppendLine("        {");
            scriptBuilder.AppendLine("            if (renderer != null)");
            scriptBuilder.AppendLine("            {");
            scriptBuilder.AppendLine("                originalMaterials[renderer.name] = renderer.materials;");
            scriptBuilder.AppendLine("                originalScales[renderer.name] = renderer.transform.localScale;");
            scriptBuilder.AppendLine("            }");
            scriptBuilder.AppendLine("        }");
            scriptBuilder.AppendLine("    }");
            scriptBuilder.AppendLine();
            
            // Apply customization method
            scriptBuilder.AppendLine("    public void ApplyCustomization(string option, int value)");
            scriptBuilder.AppendLine("    {");
            scriptBuilder.AppendLine("        switch (option.ToLower())");
            scriptBuilder.AppendLine("        {");
            
            foreach (var option in customizationOptions.Properties())
            {
                string optionName = option.Name.ToLower();
                scriptBuilder.AppendLine($"            case \"{optionName}\":");
                scriptBuilder.AppendLine($"                {option.Name} = value;");
                scriptBuilder.AppendLine($"                Apply{option.Name}Customization(value);");
                scriptBuilder.AppendLine("                break;");
            }
            
            scriptBuilder.AppendLine("            default:");
            scriptBuilder.AppendLine("                Debug.LogWarning($\"Unknown customization option: {option}\");");
            scriptBuilder.AppendLine("                break;");
            scriptBuilder.AppendLine("        }");
            scriptBuilder.AppendLine("    }");
            scriptBuilder.AppendLine();
            
            // Generate individual customization methods
            foreach (var option in customizationOptions.Properties())
            {
                string optionName = option.Name;
                scriptBuilder.AppendLine($"    private void Apply{optionName}Customization(int value)");
                scriptBuilder.AppendLine("    {");
                
                if (optionName.ToLower().Contains("color"))
                {
                    scriptBuilder.AppendLine("        // Apply color variations");
                    scriptBuilder.AppendLine("        Color[] colors = { Color.red, Color.blue, Color.green, Color.yellow, Color.magenta, Color.cyan, Color.white, Color.black, new Color(1f, 0.5f, 0f), new Color(0.5f, 0f, 1f) };");
                    scriptBuilder.AppendLine("        Color targetColor = colors[value % colors.Length];");
                    scriptBuilder.AppendLine("        foreach (var renderer in bodyRenderers)");
                    scriptBuilder.AppendLine("        {");
                    scriptBuilder.AppendLine("            if (renderer != null)");
                    scriptBuilder.AppendLine("            {");
                    scriptBuilder.AppendLine("                foreach (var material in renderer.materials)");
                    scriptBuilder.AppendLine("                {");
                    scriptBuilder.AppendLine("                    if (material.HasProperty(\"_Color\")) material.color = targetColor;");
                    scriptBuilder.AppendLine("                    if (material.HasProperty(\"_BaseColor\")) material.SetColor(\"_BaseColor\", targetColor);");
                    scriptBuilder.AppendLine("                }");
                    scriptBuilder.AppendLine("            }");
                    scriptBuilder.AppendLine("        }");
                }
                else if (optionName.ToLower().Contains("scale") || optionName.ToLower().Contains("size"))
                {
                    scriptBuilder.AppendLine("        // Apply scale variations");
                    scriptBuilder.AppendLine("        float scaleMultiplier = 1.0f + (value - 5) * 0.1f; // Range from 0.5x to 1.5x");
                    scriptBuilder.AppendLine("        foreach (var renderer in bodyRenderers)");
                    scriptBuilder.AppendLine("        {");
                    scriptBuilder.AppendLine("            if (renderer != null && originalScales.ContainsKey(renderer.name))");
                    scriptBuilder.AppendLine("            {");
                    scriptBuilder.AppendLine("                renderer.transform.localScale = originalScales[renderer.name] * scaleMultiplier;");
                    scriptBuilder.AppendLine("            }");
                    scriptBuilder.AppendLine("        }");
                }
                else if (optionName.ToLower().Contains("accessory"))
                {
                    scriptBuilder.AppendLine("        // Apply accessory variations");
                    scriptBuilder.AppendLine("        for (int i = 0; i < accessoryRenderers.Length; i++)");
                    scriptBuilder.AppendLine("        {");
                    scriptBuilder.AppendLine("            if (accessoryRenderers[i] != null)");
                    scriptBuilder.AppendLine("            {");
                    scriptBuilder.AppendLine("                accessoryRenderers[i].gameObject.SetActive((value + i) % 3 != 0);");
                    scriptBuilder.AppendLine("            }");
                    scriptBuilder.AppendLine("        }");
                }
                else
                {
                    scriptBuilder.AppendLine("        // Generic customization - modify blend shapes if available");
                    scriptBuilder.AppendLine("        foreach (var renderer in bodyRenderers)");
                    scriptBuilder.AppendLine("        {");
                    scriptBuilder.AppendLine("            if (renderer != null && renderer.sharedMesh != null)");
                    scriptBuilder.AppendLine("            {");
                    scriptBuilder.AppendLine("                for (int i = 0; i < renderer.sharedMesh.blendShapeCount; i++)");
                    scriptBuilder.AppendLine("                {");
                    scriptBuilder.AppendLine($"                    if (renderer.sharedMesh.GetBlendShapeName(i).ToLower().Contains(\"{optionName.ToLower()}\"))");
                    scriptBuilder.AppendLine("                    {");
                    scriptBuilder.AppendLine("                        renderer.SetBlendShapeWeight(i, value * 10f);");
                    scriptBuilder.AppendLine("                    }");
                    scriptBuilder.AppendLine("                }");
                    scriptBuilder.AppendLine("            }");
                    scriptBuilder.AppendLine("        }");
                }
                
                scriptBuilder.AppendLine("    }");
                scriptBuilder.AppendLine();
            }
            
            // Apply all customizations method
            scriptBuilder.AppendLine("    public void ApplyAllCustomizations()");
            scriptBuilder.AppendLine("    {");
            foreach (var option in customizationOptions.Properties())
            {
                scriptBuilder.AppendLine($"        Apply{option.Name}Customization({option.Name});");
            }
            scriptBuilder.AppendLine("    }");
            scriptBuilder.AppendLine();
            
            if (savePresets)
            {
                // Preset methods
                scriptBuilder.AppendLine("    public void SavePreset(string presetName)");
                scriptBuilder.AppendLine("    {");
                scriptBuilder.AppendLine("        var preset = new CustomizationPreset { presetName = presetName };");
                foreach (var option in customizationOptions.Properties())
                {
                    scriptBuilder.AppendLine($"        preset.customizationValues[\"{option.Name}\"] = {option.Name};");
                }
                scriptBuilder.AppendLine("        presets.Add(preset);");
                scriptBuilder.AppendLine("        SavePresets();");
                scriptBuilder.AppendLine("    }");
                scriptBuilder.AppendLine();
                
                scriptBuilder.AppendLine("    public void LoadPreset(string presetName)");
                scriptBuilder.AppendLine("    {");
                scriptBuilder.AppendLine("        var preset = presets.FirstOrDefault(p => p.presetName == presetName);");
                scriptBuilder.AppendLine("        if (preset != null)");
                scriptBuilder.AppendLine("        {");
                scriptBuilder.AppendLine("            foreach (var kvp in preset.customizationValues)");
                scriptBuilder.AppendLine("            {");
                scriptBuilder.AppendLine("                ApplyCustomization(kvp.Key, kvp.Value);");
                scriptBuilder.AppendLine("            }");
                scriptBuilder.AppendLine("        }");
                scriptBuilder.AppendLine("    }");
                scriptBuilder.AppendLine();
                
                scriptBuilder.AppendLine("    private void SavePresets()");
                scriptBuilder.AppendLine("    {");
                scriptBuilder.AppendLine("        try");
                scriptBuilder.AppendLine("        {");
                scriptBuilder.AppendLine("            string json = JsonConvert.SerializeObject(presets, Formatting.Indented);");
                scriptBuilder.AppendLine("            File.WriteAllText(presetsFilePath, json);");
                scriptBuilder.AppendLine("        }");
                scriptBuilder.AppendLine("        catch (System.Exception ex)");
                scriptBuilder.AppendLine("        {");
                scriptBuilder.AppendLine("            Debug.LogError($\"Failed to save presets: {ex.Message}\");");
                scriptBuilder.AppendLine("        }");
                scriptBuilder.AppendLine("    }");
                scriptBuilder.AppendLine();
                
                scriptBuilder.AppendLine("    private void LoadPresets()");
                scriptBuilder.AppendLine("    {");
                scriptBuilder.AppendLine("        try");
                scriptBuilder.AppendLine("        {");
                scriptBuilder.AppendLine("            if (File.Exists(presetsFilePath))");
                scriptBuilder.AppendLine("            {");
                scriptBuilder.AppendLine("                string json = File.ReadAllText(presetsFilePath);");
                scriptBuilder.AppendLine("                presets = JsonConvert.DeserializeObject<List<CustomizationPreset>>(json) ?? new List<CustomizationPreset>();");
                scriptBuilder.AppendLine("            }");
                scriptBuilder.AppendLine("        }");
                scriptBuilder.AppendLine("        catch (System.Exception ex)");
                scriptBuilder.AppendLine("        {");
                scriptBuilder.AppendLine("            Debug.LogError($\"Failed to load presets: {ex.Message}\");");
                scriptBuilder.AppendLine("            presets = new List<CustomizationPreset>();");
                scriptBuilder.AppendLine("        }");
                scriptBuilder.AppendLine("    }");
            }
            
            scriptBuilder.AppendLine("}");
            
            return scriptBuilder.ToString();
        }

        private static void CreateCustomizationPresets(string characterPath, JObject customizationOptions)
        {
            // Create ScriptableObject presets for customization
            string presetsDir = Path.GetDirectoryName(characterPath) + "/Presets";
            
            // Ensure presets directory exists
            if (!AssetDatabase.IsValidFolder(presetsDir))
            {
                AssetDatabase.CreateFolder(Path.GetDirectoryName(characterPath), "Presets");
            }

            // Create a ScriptableObject class for the preset data
            string presetScriptContent = GeneratePresetScriptableObject(Path.GetFileNameWithoutExtension(characterPath), customizationOptions);
            string presetScriptPath = presetsDir + $"/{Path.GetFileNameWithoutExtension(characterPath)}Preset.cs";
            
            File.WriteAllText(Path.Combine(Directory.GetCurrentDirectory(), presetScriptPath), presetScriptContent);
            AssetDatabase.ImportAsset(presetScriptPath);
            AssetDatabase.Refresh();

            // Wait for compilation
            System.Threading.Thread.Sleep(1000);

            // Create some default preset assets
            string[] presetNames = { "Default", "Variant1", "Variant2", "Variant3" };
            
            for (int i = 0; i < presetNames.Length; i++)
            {
                try
                {
                    // Create preset asset using reflection since we just created the class
                    string className = Path.GetFileNameWithoutExtension(characterPath) + "Preset";
                    System.Type presetType = System.Type.GetType(className);
                    
                    if (presetType != null)
                    {
                        ScriptableObject presetAsset = ScriptableObject.CreateInstance(presetType);
                        
                        // Set preset name
                        var presetNameField = presetType.GetField("presetName");
                        if (presetNameField != null)
                        {
                            presetNameField.SetValue(presetAsset, presetNames[i]);
                        }
                        
                        // Set default values for customization options
                        foreach (var option in customizationOptions.Properties())
                        {
                            var field = presetType.GetField(option.Name.ToLower());
                            if (field != null)
                            {
                                // Set different values for each preset
                                int value = (i * 2 + option.Name.GetHashCode()) % 11; // 0-10 range
                                field.SetValue(presetAsset, value);
                            }
                        }
                        
                        string assetPath = $"{presetsDir}/{presetNames[i]}Preset.asset";
                        AssetDatabase.CreateAsset(presetAsset, assetPath);
                    }
                }
                catch (System.Exception ex)
                {
                    Debug.LogWarning($"[ProceduralCharacterAssembly] Could not create preset {presetNames[i]}: {ex.Message}");
                }
            }

            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            
            Debug.Log($"[ProceduralCharacterAssembly] Created customization presets for {characterPath} in {presetsDir}");
        }

        private static string GeneratePresetScriptableObject(string characterName, JObject customizationOptions)
        {
            var scriptBuilder = new System.Text.StringBuilder();
            
            scriptBuilder.AppendLine("using UnityEngine;");
            scriptBuilder.AppendLine();
            scriptBuilder.AppendLine($"[CreateAssetMenu(fileName = \"New{characterName}Preset\", menuName = \"Character Customization/{characterName} Preset\")]");
            scriptBuilder.AppendLine($"public class {characterName}Preset : ScriptableObject");
            scriptBuilder.AppendLine("{");
            scriptBuilder.AppendLine("    [Header(\"Preset Information\")]");
            scriptBuilder.AppendLine("    public string presetName = \"New Preset\";");
            scriptBuilder.AppendLine("    [TextArea(3, 5)]");
            scriptBuilder.AppendLine("    public string description = \"Custom character preset\";");
            scriptBuilder.AppendLine();
            scriptBuilder.AppendLine("    [Header(\"Customization Values\")]");
            
            foreach (var option in customizationOptions.Properties())
            {
                string optionName = option.Name;
                scriptBuilder.AppendLine($"    [Range(0, 10)] public int {optionName.ToLower()} = 0;");
            }
            
            scriptBuilder.AppendLine();
            scriptBuilder.AppendLine("    public void ApplyToCharacter(GameObject character)");
            scriptBuilder.AppendLine("    {");
            scriptBuilder.AppendLine($"        var customization = character.GetComponent<{characterName}Customization>();");
            scriptBuilder.AppendLine("        if (customization != null)");
            scriptBuilder.AppendLine("        {");
            
            foreach (var option in customizationOptions.Properties())
            {
                string optionName = option.Name;
                scriptBuilder.AppendLine($"            customization.ApplyCustomization(\"{optionName}\", {optionName.ToLower()});");
            }
            
            scriptBuilder.AppendLine("        }");
            scriptBuilder.AppendLine("        else");
            scriptBuilder.AppendLine("        {");
            scriptBuilder.AppendLine("            Debug.LogWarning($\"No customization component found on {character.name}\");");
            scriptBuilder.AppendLine("        }");
            scriptBuilder.AppendLine("    }");
            scriptBuilder.AppendLine("}");
            
            return scriptBuilder.ToString();
        }

        private static GameObject OptimizeCharacterForRuntime(GameObject character, bool includeAnimations, bool includeMaterials, bool includeTextures)
        {
            // Create optimized copy of character for runtime with comprehensive optimizations
            GameObject optimized = GameObject.Instantiate(character);
            optimized.name = character.name + "_Optimized";

            // Remove unnecessary components for runtime
            if (!includeAnimations)
            {
                // Remove animation components if not needed
                var animators = optimized.GetComponentsInChildren<Animator>();
                foreach (var animator in animators)
                {
                    GameObject.DestroyImmediate(animator);
                }
                
                // Remove animation rigging components
                var rigBuilders = optimized.GetComponentsInChildren<Component>()
                    .Where(c => c.GetType().Name.Contains("RigBuilder") || c.GetType().Name.Contains("Rig"))
                    .ToArray();
                foreach (var rigBuilder in rigBuilders)
                {
                    GameObject.DestroyImmediate(rigBuilder);
                }
            }

            // Optimize materials
            if (includeMaterials)
            {
                OptimizeMaterialsForRuntime(optimized);
            }
            else
            {
                // Remove all materials if not needed
                var renderers = optimized.GetComponentsInChildren<Renderer>();
                foreach (var renderer in renderers)
                {
                    renderer.materials = new Material[0];
                }
            }

            // Optimize textures
            if (!includeTextures)
            {
                RemoveTexturesFromMaterials(optimized);
            }

            // Optimize mesh data
            OptimizeMeshesForRuntime(optimized);

            // Remove editor-only components
            RemoveEditorOnlyComponents(optimized);

            // Optimize colliders
            OptimizeCollidersForRuntime(optimized);

            // Remove empty GameObjects
            RemoveEmptyGameObjects(optimized);

            // Optimize transform hierarchy
            OptimizeTransformHierarchy(optimized);

            Debug.Log($"[ProceduralCharacterAssembly] Character optimized for runtime: {optimized.name}");
            return optimized;
        }

        private static void OptimizeMaterialsForRuntime(GameObject character)
        {
            var renderers = character.GetComponentsInChildren<Renderer>();
            var materialMap = new Dictionary<string, Material>();

            foreach (var renderer in renderers)
            {
                Material[] optimizedMaterials = new Material[renderer.materials.Length];
                
                for (int i = 0; i < renderer.materials.Length; i++)
                {
                    Material originalMaterial = renderer.materials[i];
                    if (originalMaterial == null) continue;

                    string materialKey = originalMaterial.name + "_" + originalMaterial.shader.name;
                    
                    if (materialMap.ContainsKey(materialKey))
                    {
                        // Reuse existing optimized material
                        optimizedMaterials[i] = materialMap[materialKey];
                    }
                    else
                    {
                        // Create optimized material
                        Material optimizedMaterial = new Material(originalMaterial);
                        optimizedMaterial.name = originalMaterial.name + "_Optimized";
                        
                        // Remove unnecessary properties for runtime
                        RemoveUnusedMaterialProperties(optimizedMaterial);
                        
                        materialMap[materialKey] = optimizedMaterial;
                        optimizedMaterials[i] = optimizedMaterial;
                    }
                }
                
                renderer.materials = optimizedMaterials;
            }
        }

        private static void RemoveUnusedMaterialProperties(Material material)
        {
            // Remove editor-only properties that aren't needed at runtime
            string[] editorOnlyProperties = {
                "_EditorVisualizationMode",
                "_EditorVisualizationMask",
                "_EditorVisualizationLayer"
            };

            foreach (string property in editorOnlyProperties)
            {
                if (material.HasProperty(property))
                {
                    // Can't actually remove properties, but we can set them to default values
                    try
                    {
                        material.SetFloat(property, 0f);
                    }
                    catch { /* Property might not be a float */ }
                }
            }
        }

        private static void RemoveTexturesFromMaterials(GameObject character)
        {
            var renderers = character.GetComponentsInChildren<Renderer>();
            
            foreach (var renderer in renderers)
            {
                foreach (var material in renderer.materials)
                {
                    if (material == null) continue;
                    
                    // Get all texture properties
                    var shader = material.shader;
                    for (int i = 0; i < shader.GetPropertyCount(); i++)
                    {
                        if (shader.GetPropertyType(i) == UnityEngine.Rendering.ShaderPropertyType.Texture)
                        {
                            string propertyName = shader.GetPropertyName(i);
                            material.SetTexture(propertyName, null);
                        }
                    }
                }
            }
        }

        private static void OptimizeMeshesForRuntime(GameObject character)
        {
            var meshFilters = character.GetComponentsInChildren<MeshFilter>();
            var skinnedMeshRenderers = character.GetComponentsInChildren<SkinnedMeshRenderer>();
            
            // Optimize static meshes
            foreach (var meshFilter in meshFilters)
            {
                if (meshFilter.sharedMesh != null)
                {
                    Mesh optimizedMesh = OptimizeMeshForRuntime(meshFilter.sharedMesh);
                    meshFilter.sharedMesh = optimizedMesh;
                }
            }
            
            // Optimize skinned meshes
            foreach (var skinnedRenderer in skinnedMeshRenderers)
            {
                if (skinnedRenderer.sharedMesh != null)
                {
                    Mesh optimizedMesh = OptimizeMeshForRuntime(skinnedRenderer.sharedMesh);
                    skinnedRenderer.sharedMesh = optimizedMesh;
                }
            }
        }

        private static Mesh OptimizeMeshForRuntime(Mesh originalMesh)
        {
            Mesh optimizedMesh = GameObject.Instantiate(originalMesh);
            optimizedMesh.name = originalMesh.name + "_Optimized";
            
            // Optimize mesh for runtime
            optimizedMesh.Optimize();
            optimizedMesh.OptimizeIndexBuffers();
            optimizedMesh.OptimizeReorderVertexBuffer();
            
            // Remove unnecessary vertex attributes for runtime if they exist
            // Keep only essential attributes: position, normal, uv, tangent (for skinned meshes)
            
            return optimizedMesh;
        }

        private static void RemoveEditorOnlyComponents(GameObject character)
        {
            // Remove components that are only needed in editor
            var componentsToRemove = new List<Component>();
            
            var allComponents = character.GetComponentsInChildren<Component>();
            foreach (var component in allComponents)
            {
                if (component == null) continue;
                
                string typeName = component.GetType().Name;
                
                // Remove editor-only components
                if (typeName.Contains("Editor") || 
                    typeName.Contains("Gizmo") ||
                    typeName.Contains("Handle") ||
                    typeName.Contains("Debug") ||
                    typeName.Contains("Preview"))
                {
                    componentsToRemove.Add(component);
                }
            }
            
            foreach (var component in componentsToRemove)
            {
                GameObject.DestroyImmediate(component);
            }
        }

        private static void OptimizeCollidersForRuntime(GameObject character)
        {
            var colliders = character.GetComponentsInChildren<Collider>();
            
            foreach (var collider in colliders)
            {
                // Optimize collider settings for runtime performance
                if (collider is MeshCollider meshCollider)
                {
                    // Use convex mesh colliders when possible for better performance
                    if (!meshCollider.convex && meshCollider.sharedMesh != null)
                    {
                        // Check if we can make it convex (vertex count < 255)
                        if (meshCollider.sharedMesh.vertexCount < 255)
                        {
                            meshCollider.convex = true;
                        }
                    }
                }
                
                // Disable unnecessary collision detection
                if (collider.isTrigger)
                {
                    // Triggers don't need complex collision detection
                    collider.material = null;
                }
            }
        }

        private static void RemoveEmptyGameObjects(GameObject character)
        {
            var allTransforms = character.GetComponentsInChildren<Transform>();
            var toRemove = new List<GameObject>();
            
            foreach (var transform in allTransforms)
            {
                if (transform == character.transform) continue; // Don't remove root
                
                // Check if GameObject is empty (no components except Transform and no children)
                var components = transform.GetComponents<Component>();
                bool hasOnlyTransform = components.Length == 1 && components[0] is Transform;
                bool hasNoChildren = transform.childCount == 0;
                
                if (hasOnlyTransform && hasNoChildren)
                {
                    toRemove.Add(transform.gameObject);
                }
            }
            
            foreach (var obj in toRemove)
            {
                GameObject.DestroyImmediate(obj);
            }
        }

        private static void OptimizeTransformHierarchy(GameObject character)
        {
            // Flatten unnecessary hierarchy levels where possible
            var allTransforms = character.GetComponentsInChildren<Transform>();
            
            foreach (var transform in allTransforms)
            {
                if (transform == character.transform) continue;
                
                // If a transform has only one child and no components except Transform,
                // consider flattening the hierarchy
                if (transform.childCount == 1)
                {
                    var components = transform.GetComponents<Component>();
                    bool hasOnlyTransform = components.Length == 1 && components[0] is Transform;
                    
                    if (hasOnlyTransform)
                    {
                        Transform child = transform.GetChild(0);
                        Transform parent = transform.parent;
                        
                        // Move child to grandparent, preserving world position
                        Vector3 worldPos = child.position;
                        Quaternion worldRot = child.rotation;
                        Vector3 worldScale = child.lossyScale;
                        
                        child.SetParent(parent);
                        child.position = worldPos;
                        child.rotation = worldRot;
                        // Note: Scale might need adjustment based on parent's scale
                        
                        // Remove the now-empty intermediate transform
                        GameObject.DestroyImmediate(transform.gameObject);
                    }
                }
            }
        }

        private static void SetupAddressableAsset(string assetPath)
        {
            // Setup asset as Addressable (requires Addressables package)
            try
            {
#if UNITY_ADDRESSABLES
                // Get or create default Addressables settings
                var settings = UnityEditor.AddressableAssets.AddressableAssetSettingsDefaultObject.Settings;
                if (settings == null)
                {
                    settings = UnityEditor.AddressableAssets.AddressableAssetSettingsDefaultObject.GetSettings(true);
                }

                // Get the asset GUID
                string guid = AssetDatabase.AssetPathToGUID(assetPath);
                if (string.IsNullOrEmpty(guid))
                {
                    Debug.LogError($"[ProceduralCharacterAssembly] Could not find GUID for asset: {assetPath}");
                    return;
                }

                // Check if asset is already addressable
                var entry = settings.FindAssetEntry(guid);
                if (entry != null)
                {
                    Debug.Log($"[ProceduralCharacterAssembly] Asset {assetPath} is already addressable with address: {entry.address}");
                    return;
                }

                // Get or create default group
                var defaultGroup = settings.DefaultGroup;
                if (defaultGroup == null)
                {
                    defaultGroup = settings.CreateGroup("Default Local Group", false, false, true, null);
                }

                // Create addressable entry
                string assetName = System.IO.Path.GetFileNameWithoutExtension(assetPath);
                string address = $"Characters/{assetName}";
                
                entry = settings.CreateOrMoveEntry(guid, defaultGroup, false, false);
                entry.address = address;
                
                // Add labels for better organization
                entry.SetLabel("Character", true);
                entry.SetLabel("Procedural", true);

                // Mark settings as dirty
                UnityEditor.EditorUtility.SetDirty(settings);
                
                Debug.Log($"[ProceduralCharacterAssembly] Successfully set up {assetPath} as Addressable asset with address: {address}");
#else
                Debug.LogWarning("[ProceduralCharacterAssembly] Addressables package not installed. Asset will not be made addressable.");
#endif
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[ProceduralCharacterAssembly] Error setting up Addressable asset: {ex.Message}");
            }
        }

        private static void SetupAdvancedRigging(GameObject prefab)
        {
            try
            {
                // Load the prefab for editing
                string prefabPath = AssetDatabase.GetAssetPath(prefab);
                GameObject prefabInstance = PrefabUtility.LoadPrefabContents(prefabPath);
                
                Animator animator = prefabInstance.GetComponent<Animator>();
                if (animator == null)
                {
                    animator = prefabInstance.AddComponent<Animator>();
                }
                
                // Setup humanoid avatar configuration
                var skinnedMeshRenderers = prefabInstance.GetComponentsInChildren<SkinnedMeshRenderer>();
                if (skinnedMeshRenderers.Length > 0)
                {
                    // Find or create root bone
                    Transform rootBone = FindOrCreateRootBone(prefabInstance);
                    
                    // Setup bone hierarchy for humanoid rig
                    SetupHumanoidBoneHierarchy(rootBone);
                    
                    // Configure avatar
                    Avatar avatar = CreateHumanoidAvatar(prefabInstance, rootBone);
                    if (avatar != null)
                    {
                        animator.avatar = avatar;
                        
                        // Save avatar as asset
                        string avatarPath = prefabPath.Replace(".prefab", "_Avatar.asset");
                        AssetDatabase.CreateAsset(avatar, avatarPath);
                    }
                    
                    // Setup IK targets
                    SetupIKTargets(prefabInstance, rootBone);
                    
                    // Add animation rigging components (Unity 6.2)
                    SetupAnimationRigging(prefabInstance);
                }
                
                // Save changes back to prefab
                PrefabUtility.SaveAsPrefabAsset(prefabInstance, prefabPath);
                PrefabUtility.UnloadPrefabContents(prefabInstance);
                
                Debug.Log("[ProceduralCharacterAssembly] Advanced rigging setup completed.");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[ProceduralCharacterAssembly] Failed to setup advanced rigging: {ex.Message}");
            }
        }

        private static void GenerateAdvancedLODs(GameObject prefab)
        {
            try
            {
                string prefabPath = AssetDatabase.GetAssetPath(prefab);
                GameObject prefabInstance = PrefabUtility.LoadPrefabContents(prefabPath);
                
                // Setup LOD Group with Unity 6.2 features
                LODGroup lodGroup = prefabInstance.GetComponent<LODGroup>();
                if (lodGroup == null)
                {
                    lodGroup = prefabInstance.AddComponent<LODGroup>();
                }
                
                // Get all renderers
                var renderers = prefabInstance.GetComponentsInChildren<Renderer>();
                if (renderers.Length > 0)
                {
                    // Create LOD levels with different quality settings
                    LOD[] lods = new LOD[4];
                    
                    // LOD 0 - Full quality (0-50%)
                    lods[0] = new LOD(0.5f, renderers);
                    
                    // LOD 1 - High quality (50-25%)
                    var lod1Renderers = CreateLODRenderers(prefabInstance, renderers, 0.75f, "_LOD1");
                    lods[1] = new LOD(0.25f, lod1Renderers);
                    
                    // LOD 2 - Medium quality (25-10%)
                    var lod2Renderers = CreateLODRenderers(prefabInstance, renderers, 0.5f, "_LOD2");
                    lods[2] = new LOD(0.1f, lod2Renderers);
                    
                    // LOD 3 - Low quality (10-0%)
                    var lod3Renderers = CreateLODRenderers(prefabInstance, renderers, 0.25f, "_LOD3");
                    lods[3] = new LOD(0.01f, lod3Renderers);
                    
                    lodGroup.SetLODs(lods);
                    lodGroup.RecalculateBounds();
                    
                    // Enable automatic LOD fading (Unity 6.2 feature)
                    lodGroup.fadeMode = LODFadeMode.CrossFade;
                    lodGroup.animateCrossFading = true;
                }
                
                // Save changes
                PrefabUtility.SaveAsPrefabAsset(prefabInstance, prefabPath);
                PrefabUtility.UnloadPrefabContents(prefabInstance);
                
                Debug.Log("[ProceduralCharacterAssembly] Advanced LODs generated successfully.");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[ProceduralCharacterAssembly] Failed to generate advanced LODs: {ex.Message}");
            }
        }

        private static Transform FindOrCreateRootBone(GameObject character)
        {
            // Find existing root bone or create one
            Transform rootBone = character.transform.Find("Root");
            if (rootBone == null)
            {
                GameObject rootBoneObj = new GameObject("Root");
                rootBone = rootBoneObj.transform;
                rootBone.SetParent(character.transform);
                rootBone.localPosition = Vector3.zero;
            }
            return rootBone;
        }

        private static void SetupHumanoidBoneHierarchy(Transform rootBone)
        {
            // Create basic humanoid bone hierarchy
            var bones = new Dictionary<string, Transform>();
            
            // Create spine hierarchy
            bones["Hips"] = CreateBone("Hips", rootBone, Vector3.zero);
            bones["Spine"] = CreateBone("Spine", bones["Hips"], new Vector3(0, 0.1f, 0));
            bones["Chest"] = CreateBone("Chest", bones["Spine"], new Vector3(0, 0.15f, 0));
            bones["Neck"] = CreateBone("Neck", bones["Chest"], new Vector3(0, 0.2f, 0));
            bones["Head"] = CreateBone("Head", bones["Neck"], new Vector3(0, 0.1f, 0));
            
            // Create arm hierarchy
            bones["LeftShoulder"] = CreateBone("LeftShoulder", bones["Chest"], new Vector3(-0.15f, 0.15f, 0));
            bones["LeftUpperArm"] = CreateBone("LeftUpperArm", bones["LeftShoulder"], new Vector3(-0.05f, 0, 0));
            bones["LeftLowerArm"] = CreateBone("LeftLowerArm", bones["LeftUpperArm"], new Vector3(-0.25f, 0, 0));
            bones["LeftHand"] = CreateBone("LeftHand", bones["LeftLowerArm"], new Vector3(-0.25f, 0, 0));
            
            bones["RightShoulder"] = CreateBone("RightShoulder", bones["Chest"], new Vector3(0.15f, 0.15f, 0));
            bones["RightUpperArm"] = CreateBone("RightUpperArm", bones["RightShoulder"], new Vector3(0.05f, 0, 0));
            bones["RightLowerArm"] = CreateBone("RightLowerArm", bones["RightUpperArm"], new Vector3(0.25f, 0, 0));
            bones["RightHand"] = CreateBone("RightHand", bones["RightLowerArm"], new Vector3(0.25f, 0, 0));
            
            // Create leg hierarchy
            bones["LeftUpperLeg"] = CreateBone("LeftUpperLeg", bones["Hips"], new Vector3(-0.1f, -0.05f, 0));
            bones["LeftLowerLeg"] = CreateBone("LeftLowerLeg", bones["LeftUpperLeg"], new Vector3(0, -0.4f, 0));
            bones["LeftFoot"] = CreateBone("LeftFoot", bones["LeftLowerLeg"], new Vector3(0, -0.4f, 0));
            
            bones["RightUpperLeg"] = CreateBone("RightUpperLeg", bones["Hips"], new Vector3(0.1f, -0.05f, 0));
            bones["RightLowerLeg"] = CreateBone("RightLowerLeg", bones["RightUpperLeg"], new Vector3(0, -0.4f, 0));
            bones["RightFoot"] = CreateBone("RightFoot", bones["RightLowerLeg"], new Vector3(0, -0.4f, 0));
        }

        private static Transform CreateBone(string name, Transform parent, Vector3 localPosition)
        {
            GameObject boneObj = new GameObject(name);
            Transform bone = boneObj.transform;
            bone.SetParent(parent);
            bone.localPosition = localPosition;
            bone.localRotation = Quaternion.identity;
            bone.localScale = Vector3.one;
            return bone;
        }

        private static Avatar CreateHumanoidAvatar(GameObject character, Transform rootBone)
        {
            // Create humanoid avatar using Unity's avatar builder
            try
            {
                var humanDescription = new HumanDescription();
                humanDescription.skeleton = CreateSkeletonBones(rootBone);
                humanDescription.human = CreateHumanBones(rootBone);
                
                // Set default values
                humanDescription.upperArmTwist = 0.5f;
                humanDescription.lowerArmTwist = 0.5f;
                humanDescription.upperLegTwist = 0.5f;
                humanDescription.lowerLegTwist = 0.5f;
                humanDescription.armStretch = 0.05f;
                humanDescription.legStretch = 0.05f;
                humanDescription.feetSpacing = 0.0f;
                humanDescription.hasTranslationDoF = false;
                
                return AvatarBuilder.BuildHumanAvatar(character, humanDescription);
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"Failed to create humanoid avatar: {ex.Message}");
                return null;
            }
        }

        private static SkeletonBone[] CreateSkeletonBones(Transform rootBone)
        {
            var bones = new List<SkeletonBone>();
            CreateSkeletonBonesRecursive(rootBone, bones);
            return bones.ToArray();
        }

        private static void CreateSkeletonBonesRecursive(Transform bone, List<SkeletonBone> bones)
        {
            var skeletonBone = new SkeletonBone();
            skeletonBone.name = bone.name;
            skeletonBone.position = bone.localPosition;
            skeletonBone.rotation = bone.localRotation;
            skeletonBone.scale = bone.localScale;
            bones.Add(skeletonBone);
            
            for (int i = 0; i < bone.childCount; i++)
            {
                CreateSkeletonBonesRecursive(bone.GetChild(i), bones);
            }
        }

        private static HumanBone[] CreateHumanBones(Transform rootBone)
        {
            var humanBones = new List<HumanBone>();
            
            // Map standard bone names to HumanBodyBones
            var boneMapping = new Dictionary<string, HumanBodyBones>
            {
                {"Hips", HumanBodyBones.Hips},
                {"Spine", HumanBodyBones.Spine},
                {"Chest", HumanBodyBones.Chest},
                {"Neck", HumanBodyBones.Neck},
                {"Head", HumanBodyBones.Head},
                {"LeftShoulder", HumanBodyBones.LeftShoulder},
                {"LeftUpperArm", HumanBodyBones.LeftUpperArm},
                {"LeftLowerArm", HumanBodyBones.LeftLowerArm},
                {"LeftHand", HumanBodyBones.LeftHand},
                {"RightShoulder", HumanBodyBones.RightShoulder},
                {"RightUpperArm", HumanBodyBones.RightUpperArm},
                {"RightLowerArm", HumanBodyBones.RightLowerArm},
                {"RightHand", HumanBodyBones.RightHand},
                {"LeftUpperLeg", HumanBodyBones.LeftUpperLeg},
                {"LeftLowerLeg", HumanBodyBones.LeftLowerLeg},
                {"LeftFoot", HumanBodyBones.LeftFoot},
                {"RightUpperLeg", HumanBodyBones.RightUpperLeg},
                {"RightLowerLeg", HumanBodyBones.RightLowerLeg},
                {"RightFoot", HumanBodyBones.RightFoot}
            };
            
            foreach (var mapping in boneMapping)
            {
                Transform bone = FindBoneRecursive(rootBone, mapping.Key);
                if (bone != null)
                {
                    var humanBone = new HumanBone();
                    humanBone.boneName = bone.name;
                    humanBone.humanName = mapping.Value.ToString();
                    humanBone.limit.useDefaultValues = true;
                    humanBones.Add(humanBone);
                }
            }
            
            return humanBones.ToArray();
        }

        private static Transform FindBoneRecursive(Transform parent, string boneName)
        {
            if (parent.name == boneName)
                return parent;
                
            for (int i = 0; i < parent.childCount; i++)
            {
                Transform found = FindBoneRecursive(parent.GetChild(i), boneName);
                if (found != null)
                    return found;
            }
            
            return null;
        }

        private static void SetupIKTargets(GameObject character, Transform rootBone)
        {
            // Create IK target objects for hands and feet
            GameObject ikTargets = new GameObject("IK_Targets");
            ikTargets.transform.SetParent(character.transform);
            
            // Hand IK targets
            CreateIKTarget("LeftHand_IK", ikTargets.transform, FindBoneRecursive(rootBone, "LeftHand"));
            CreateIKTarget("RightHand_IK", ikTargets.transform, FindBoneRecursive(rootBone, "RightHand"));
            
            // Foot IK targets
            CreateIKTarget("LeftFoot_IK", ikTargets.transform, FindBoneRecursive(rootBone, "LeftFoot"));
            CreateIKTarget("RightFoot_IK", ikTargets.transform, FindBoneRecursive(rootBone, "RightFoot"));
        }

        private static void CreateIKTarget(string name, Transform parent, Transform bone)
        {
            if (bone != null)
            {
                GameObject ikTarget = new GameObject(name);
                ikTarget.transform.SetParent(parent);
                ikTarget.transform.position = bone.position;
                ikTarget.transform.rotation = bone.rotation;
            }
        }

        private static void SetupAnimationRigging(GameObject character)
        {
            // Add Animation Rigging components (Unity 6.2)
            // This requires the Animation Rigging package
#if UNITY_ANIMATION_RIGGING
            try
            {
                Animator animator = character.GetComponent<Animator>();
                if (animator == null || !animator.isHuman)
                {
                    Debug.LogWarning("[ProceduralCharacterAssembly] Character must have a humanoid Animator for Animation Rigging.");
                    return;
                }

                // Add RigBuilder component if not present
                RigBuilder rigBuilder = character.GetComponent<RigBuilder>();
                if (rigBuilder == null)
                {
                    rigBuilder = character.AddComponent<RigBuilder>();
                }

                // Create main rig container
                GameObject rigContainer = new GameObject("AnimationRig");
                rigContainer.transform.SetParent(character.transform);
                rigContainer.transform.localPosition = Vector3.zero;
                rigContainer.transform.localRotation = Quaternion.identity;
                rigContainer.transform.localScale = Vector3.one;

                // Add Rig component
                Rig rig = rigContainer.AddComponent<Rig>();
                rig.weight = 1.0f;

                // Setup IK constraints for limbs
                SetupLimbIKConstraints(rigContainer, animator);

                // Setup spine chain constraint
                SetupSpineChainConstraint(rigContainer, animator);

                // Setup head aim constraint
                SetupHeadAimConstraint(rigContainer, animator);

                // Setup finger constraints if available
                SetupFingerConstraints(rigContainer, animator);

                // Add rig to RigBuilder
                var rigLayers = new List<RigLayer>();
                if (rigBuilder.layers != null)
                {
                    rigLayers.AddRange(rigBuilder.layers);
                }
                rigLayers.Add(new RigLayer(rig, true));
                rigBuilder.layers = rigLayers;

                // Build the rig system
                rigBuilder.Build();

                Debug.Log("[ProceduralCharacterAssembly] Animation Rigging setup completed successfully.");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[ProceduralCharacterAssembly] Error setting up Animation Rigging: {ex.Message}");
            }
#else
            Debug.LogWarning("[ProceduralCharacterAssembly] Animation Rigging package not installed. Rigging will not be set up.");
#endif
        }

        private static Renderer[] CreateLODRenderers(GameObject character, Renderer[] originalRenderers, float quality, string suffix)
        {
            // Create simplified renderers for LOD levels
            var lodRenderers = new List<Renderer>();
            
            foreach (var renderer in originalRenderers)
            {
                if (renderer is SkinnedMeshRenderer skinnedRenderer)
                {
                    // Create LOD version of skinned mesh
                    GameObject lodObject = new GameObject(renderer.name + suffix);
                    lodObject.transform.SetParent(renderer.transform.parent);
                    lodObject.transform.localPosition = renderer.transform.localPosition;
                    lodObject.transform.localRotation = renderer.transform.localRotation;
                    lodObject.transform.localScale = renderer.transform.localScale;
                    
                    SkinnedMeshRenderer lodSkinnedRenderer = lodObject.AddComponent<SkinnedMeshRenderer>();
                    
                    // Simplify mesh based on quality
                    Mesh simplifiedMesh = SimplifyMesh(skinnedRenderer.sharedMesh, quality);
                    lodSkinnedRenderer.sharedMesh = simplifiedMesh;
                    lodSkinnedRenderer.materials = skinnedRenderer.materials;
                    lodSkinnedRenderer.bones = skinnedRenderer.bones;
                    lodSkinnedRenderer.rootBone = skinnedRenderer.rootBone;
                    
                    lodRenderers.Add(lodSkinnedRenderer);
                }
                else if (renderer is MeshRenderer meshRenderer)
                {
                    // Create LOD version of static mesh
                    GameObject lodObject = new GameObject(renderer.name + suffix);
                    lodObject.transform.SetParent(renderer.transform.parent);
                    lodObject.transform.localPosition = renderer.transform.localPosition;
                    lodObject.transform.localRotation = renderer.transform.localRotation;
                    lodObject.transform.localScale = renderer.transform.localScale;
                    
                    MeshRenderer lodMeshRenderer = lodObject.AddComponent<MeshRenderer>();
                    MeshFilter lodMeshFilter = lodObject.AddComponent<MeshFilter>();
                    
                    MeshFilter originalFilter = renderer.GetComponent<MeshFilter>();
                    if (originalFilter != null)
                    {
                        Mesh simplifiedMesh = SimplifyMesh(originalFilter.sharedMesh, quality);
                        lodMeshFilter.sharedMesh = simplifiedMesh;
                    }
                    
                    lodMeshRenderer.materials = meshRenderer.materials;
                    lodRenderers.Add(lodMeshRenderer);
                }
            }
            
            return lodRenderers.ToArray();
        }

        private static Mesh SimplifyMesh(Mesh originalMesh, float quality)
        {
            if (originalMesh == null) return null;
            
            // Clamp quality between 0.1 and 1.0
            quality = Mathf.Clamp(quality, 0.1f, 1.0f);
            
            // Create a copy of the original mesh
            Mesh simplifiedMesh = UnityEngine.Object.Instantiate(originalMesh);
            simplifiedMesh.name = originalMesh.name + "_Simplified";
            
            // Get mesh data
            Vector3[] vertices = simplifiedMesh.vertices;
            int[] triangles = simplifiedMesh.triangles;
            Vector3[] normals = simplifiedMesh.normals;
            Vector2[] uvs = simplifiedMesh.uv;
            Vector4[] tangents = simplifiedMesh.tangents;
            Color[] colors = simplifiedMesh.colors;
            BoneWeight[] boneWeights = simplifiedMesh.boneWeights;
            
            // Calculate target triangle count
            int originalTriangleCount = triangles.Length / 3;
            int targetTriangleCount = Mathf.RoundToInt(originalTriangleCount * quality);
            targetTriangleCount = Mathf.Max(targetTriangleCount, 1); // At least one triangle
            
            if (targetTriangleCount >= originalTriangleCount)
            {
                // No need to simplify
                return simplifiedMesh;
            }
            
            // Use more sophisticated simplification algorithm
            var simplifiedData = SimplifyMeshAdvanced(vertices, triangles, normals, uvs, tangents, colors, boneWeights, targetTriangleCount);
            
            // Apply simplified data to mesh
            simplifiedMesh.Clear();
            simplifiedMesh.vertices = simplifiedData.vertices;
            simplifiedMesh.triangles = simplifiedData.triangles;
            
            if (simplifiedData.normals != null && simplifiedData.normals.Length > 0)
                simplifiedMesh.normals = simplifiedData.normals;
            else
                simplifiedMesh.RecalculateNormals();
                
            if (simplifiedData.uvs != null && simplifiedData.uvs.Length > 0)
                simplifiedMesh.uv = simplifiedData.uvs;
                
            if (simplifiedData.tangents != null && simplifiedData.tangents.Length > 0)
                simplifiedMesh.tangents = simplifiedData.tangents;
            else
                simplifiedMesh.RecalculateTangents();
                
            if (simplifiedData.colors != null && simplifiedData.colors.Length > 0)
                simplifiedMesh.colors = simplifiedData.colors;
                
            if (simplifiedData.boneWeights != null && simplifiedData.boneWeights.Length > 0)
                simplifiedMesh.boneWeights = simplifiedData.boneWeights;
            
            // Optimize the simplified mesh
            simplifiedMesh.RecalculateBounds();
            simplifiedMesh.Optimize();
            simplifiedMesh.OptimizeIndexBuffers();
            simplifiedMesh.OptimizeReorderVertexBuffer();
            
            return simplifiedMesh;
        }

        private struct SimplifiedMeshData
        {
            public Vector3[] vertices;
            public int[] triangles;
            public Vector3[] normals;
            public Vector2[] uvs;
            public Vector4[] tangents;
            public Color[] colors;
            public BoneWeight[] boneWeights;
        }

        private static SimplifiedMeshData SimplifyMeshAdvanced(Vector3[] vertices, int[] triangles, Vector3[] normals, Vector2[] uvs, Vector4[] tangents, Color[] colors, BoneWeight[] boneWeights, int targetTriangleCount)
        {
            // Use quadric error metrics for better mesh simplification
            var triangleList = new List<int>();
            var vertexList = new List<Vector3>();
            var normalList = new List<Vector3>();
            var uvList = new List<Vector2>();
            var tangentList = new List<Vector4>();
            var colorList = new List<Color>();
            var boneWeightList = new List<BoneWeight>();
            
            // Calculate triangle priorities based on area and edge length
            var trianglePriorities = new List<float>();
            
            for (int i = 0; i < triangles.Length; i += 3)
            {
                int v0 = triangles[i];
                int v1 = triangles[i + 1];
                int v2 = triangles[i + 2];
                
                // Calculate triangle area
                Vector3 edge1 = vertices[v1] - vertices[v0];
                Vector3 edge2 = vertices[v2] - vertices[v0];
                float area = Vector3.Cross(edge1, edge2).magnitude * 0.5f;
                
                // Calculate edge lengths
                float edgeLength1 = edge1.magnitude;
                float edgeLength2 = edge2.magnitude;
                float edgeLength3 = (vertices[v2] - vertices[v1]).magnitude;
                float avgEdgeLength = (edgeLength1 + edgeLength2 + edgeLength3) / 3f;
                
                // Priority based on area and edge length (smaller triangles have lower priority)
                float priority = area * avgEdgeLength;
                trianglePriorities.Add(priority);
            }
            
            // Create list of triangle indices sorted by priority
            var triangleIndices = new List<int>();
            for (int i = 0; i < trianglePriorities.Count; i++)
            {
                triangleIndices.Add(i);
            }
            
            // Sort by priority (descending - keep high priority triangles)
            triangleIndices.Sort((a, b) => trianglePriorities[b].CompareTo(trianglePriorities[a]));
            
            // Keep the top priority triangles
            var keptTriangles = triangleIndices.Take(targetTriangleCount).ToList();
            keptTriangles.Sort(); // Sort back to original order
            
            // Build vertex mapping
            var vertexMapping = new Dictionary<int, int>();
            int newVertexIndex = 0;
            
            foreach (int triangleIndex in keptTriangles)
            {
                int baseIndex = triangleIndex * 3;
                for (int i = 0; i < 3; i++)
                {
                    int originalVertexIndex = triangles[baseIndex + i];
                    
                    if (!vertexMapping.ContainsKey(originalVertexIndex))
                    {
                        vertexMapping[originalVertexIndex] = newVertexIndex;
                        
                        // Add vertex data
                        vertexList.Add(vertices[originalVertexIndex]);
                        
                        if (normals != null && normals.Length > originalVertexIndex)
                            normalList.Add(normals[originalVertexIndex]);
                            
                        if (uvs != null && uvs.Length > originalVertexIndex)
                            uvList.Add(uvs[originalVertexIndex]);
                            
                        if (tangents != null && tangents.Length > originalVertexIndex)
                            tangentList.Add(tangents[originalVertexIndex]);
                            
                        if (colors != null && colors.Length > originalVertexIndex)
                            colorList.Add(colors[originalVertexIndex]);
                            
                        if (boneWeights != null && boneWeights.Length > originalVertexIndex)
                            boneWeightList.Add(boneWeights[originalVertexIndex]);
                        
                        newVertexIndex++;
                    }
                    
                    triangleList.Add(vertexMapping[originalVertexIndex]);
                }
            }
            
            return new SimplifiedMeshData
            {
                vertices = vertexList.ToArray(),
                triangles = triangleList.ToArray(),
                normals = normalList.Count > 0 ? normalList.ToArray() : null,
                uvs = uvList.Count > 0 ? uvList.ToArray() : null,
                tangents = tangentList.Count > 0 ? tangentList.ToArray() : null,
                colors = colorList.Count > 0 ? colorList.ToArray() : null,
                boneWeights = boneWeightList.Count > 0 ? boneWeightList.ToArray() : null
            };
        }

        private static void SetupTwoBoneIKConstraints(GameObject constraintRig, Animator animator)
         {
#if UNITY_ANIMATION_RIGGING
             // Setup Two Bone IK for arms
             SetupArmIK(constraintRig, animator, HumanBodyBones.LeftUpperArm, HumanBodyBones.LeftLowerArm, HumanBodyBones.LeftHand, "LeftArmIK");
             SetupArmIK(constraintRig, animator, HumanBodyBones.RightUpperArm, HumanBodyBones.RightLowerArm, HumanBodyBones.RightHand, "RightArmIK");

             // Setup Two Bone IK for legs
             SetupLegIK(constraintRig, animator, HumanBodyBones.LeftUpperLeg, HumanBodyBones.LeftLowerLeg, HumanBodyBones.LeftFoot, "LeftLegIK");
             SetupLegIK(constraintRig, animator, HumanBodyBones.RightUpperLeg, HumanBodyBones.RightLowerLeg, HumanBodyBones.RightFoot, "RightLegIK");
#endif
         }

        private static void SetupArmIK(GameObject constraintRig, Animator animator, HumanBodyBones upperBone, HumanBodyBones lowerBone, HumanBodyBones endBone, string name)
        {
#if UNITY_ANIMATION_RIGGING
            Transform upperArm = animator.GetBoneTransform(upperBone);
            Transform lowerArm = animator.GetBoneTransform(lowerBone);
            Transform hand = animator.GetBoneTransform(endBone);

            if (upperArm != null && lowerArm != null && hand != null)
            {
                // Create IK target
                GameObject ikTarget = new GameObject(name + "_Target");
                ikTarget.transform.SetParent(constraintRig.transform);
                ikTarget.transform.position = hand.position;
                ikTarget.transform.rotation = hand.rotation;

                // Create hint target (elbow)
                GameObject hintTarget = new GameObject(name + "_Hint");
                hintTarget.transform.SetParent(constraintRig.transform);
                Vector3 hintPosition = lowerArm.position + (lowerArm.position - upperArm.position).normalized * 0.5f;
                hintTarget.transform.position = hintPosition;

                // Add Two Bone IK Constraint
                var twoBoneIK = constraintRig.AddComponent<TwoBoneIKConstraint>();
                twoBoneIK.data.root = upperArm;
                twoBoneIK.data.mid = lowerArm;
                twoBoneIK.data.tip = hand;
                twoBoneIK.data.target = ikTarget.transform;
                twoBoneIK.data.hint = hintTarget.transform;
                twoBoneIK.weight = 1.0f;
            }
#endif
        }

        private static void SetupLegIK(GameObject constraintRig, Animator animator, HumanBodyBones upperBone, HumanBodyBones lowerBone, HumanBodyBones endBone, string name)
        {
#if UNITY_ANIMATION_RIGGING
            Transform upperLeg = animator.GetBoneTransform(upperBone);
            Transform lowerLeg = animator.GetBoneTransform(lowerBone);
            Transform foot = animator.GetBoneTransform(endBone);

            if (upperLeg != null && lowerLeg != null && foot != null)
            {
                // Create IK target
                GameObject ikTarget = new GameObject(name + "_Target");
                ikTarget.transform.SetParent(constraintRig.transform);
                ikTarget.transform.position = foot.position;
                ikTarget.transform.rotation = foot.rotation;

                // Create hint target (knee)
                GameObject hintTarget = new GameObject(name + "_Hint");
                hintTarget.transform.SetParent(constraintRig.transform);
                Vector3 hintPosition = lowerLeg.position + (lowerLeg.position - upperLeg.position).normalized * 0.5f;
                hintTarget.transform.position = hintPosition;

                // Add Two Bone IK Constraint
                var twoBoneIK = constraintRig.AddComponent<TwoBoneIKConstraint>();
                twoBoneIK.data.root = upperLeg;
                twoBoneIK.data.mid = lowerLeg;
                twoBoneIK.data.tip = foot;
                twoBoneIK.data.target = ikTarget.transform;
                twoBoneIK.data.hint = hintTarget.transform;
                twoBoneIK.weight = 1.0f;
            }
#endif
        }

        private static void SetupSpineConstraints(GameObject constraintRig, Animator animator)
        {
#if UNITY_ANIMATION_RIGGING
            Transform spine = animator.GetBoneTransform(HumanBodyBones.Spine);
            Transform chest = animator.GetBoneTransform(HumanBodyBones.Chest);
            Transform upperChest = animator.GetBoneTransform(HumanBodyBones.UpperChest);

            if (spine != null && chest != null)
            {
                // Create spine target
                GameObject spineTarget = new GameObject("SpineTarget");
                spineTarget.transform.SetParent(constraintRig.transform);
                spineTarget.transform.position = chest.position;
                spineTarget.transform.rotation = chest.rotation;

                // Add Multi-Position Constraint for spine flexibility
                var multiPosConstraint = constraintRig.AddComponent<MultiPositionConstraint>();
                multiPosConstraint.data.constrainedObject = chest;
                
                var sourceObjects = new WeightedTransformArray();
                sourceObjects.Add(new WeightedTransform(spineTarget.transform, 1.0f));
                multiPosConstraint.data.sourceObjects = sourceObjects;
                
                multiPosConstraint.data.maintainOffset = true;
                multiPosConstraint.weight = 0.5f; // Partial constraint for natural movement
            }
#endif
        }

        private static void SetupHeadConstraints(GameObject constraintRig, Animator animator)
        {
#if UNITY_ANIMATION_RIGGING
            Transform head = animator.GetBoneTransform(HumanBodyBones.Head);
            
            if (head != null)
            {
                // Create look at target
                GameObject lookAtTarget = new GameObject("LookAtTarget");
                lookAtTarget.transform.SetParent(constraintRig.transform);
                lookAtTarget.transform.position = head.position + head.forward * 2.0f;

                // Add Multi-Aim Constraint for head look at
                var multiAimConstraint = constraintRig.AddComponent<MultiAimConstraint>();
                multiAimConstraint.data.constrainedObject = head;
                
                var sourceObjects = new WeightedTransformArray();
                sourceObjects.Add(new WeightedTransform(lookAtTarget.transform, 1.0f));
                multiAimConstraint.data.sourceObjects = sourceObjects;
                
                multiAimConstraint.data.aimAxis = MultiAimConstraintData.Axis.Z;
                multiAimConstraint.data.upAxis = MultiAimConstraintData.Axis.Y;
                multiAimConstraint.data.worldUpType = MultiAimConstraintData.WorldUpType.ObjectUp;
                multiAimConstraint.data.worldUpObject = head.parent; // Use neck or upper chest as up reference
                multiAimConstraint.weight = 0.7f; // Partial constraint for natural head movement
             }
#endif
         }

         private static void SetupLimbIKConstraints(GameObject rigContainer, Animator animator)
         {
#if UNITY_ANIMATION_RIGGING
             // Setup Two Bone IK for arms and legs
             SetupTwoBoneIKForLimb(rigContainer, animator, HumanBodyBones.LeftUpperArm, HumanBodyBones.LeftLowerArm, HumanBodyBones.LeftHand, "LeftArm");
             SetupTwoBoneIKForLimb(rigContainer, animator, HumanBodyBones.RightUpperArm, HumanBodyBones.RightLowerArm, HumanBodyBones.RightHand, "RightArm");
             SetupTwoBoneIKForLimb(rigContainer, animator, HumanBodyBones.LeftUpperLeg, HumanBodyBones.LeftLowerLeg, HumanBodyBones.LeftFoot, "LeftLeg");
             SetupTwoBoneIKForLimb(rigContainer, animator, HumanBodyBones.RightUpperLeg, HumanBodyBones.RightLowerLeg, HumanBodyBones.RightFoot, "RightLeg");
#endif
         }

         private static void SetupTwoBoneIKForLimb(GameObject rigContainer, Animator animator, HumanBodyBones rootBone, HumanBodyBones midBone, HumanBodyBones tipBone, string limbName)
         {
#if UNITY_ANIMATION_RIGGING
             Transform root = animator.GetBoneTransform(rootBone);
             Transform mid = animator.GetBoneTransform(midBone);
             Transform tip = animator.GetBoneTransform(tipBone);

             if (root != null && mid != null && tip != null)
             {
                 // Create IK target
                 GameObject ikTarget = new GameObject($"{limbName}_IK_Target");
                 ikTarget.transform.SetParent(rigContainer.transform);
                 ikTarget.transform.position = tip.position;
                 ikTarget.transform.rotation = tip.rotation;

                 // Create hint target
                 GameObject hintTarget = new GameObject($"{limbName}_IK_Hint");
                 hintTarget.transform.SetParent(rigContainer.transform);
                 Vector3 hintPos = mid.position + (mid.position - root.position).normalized * 0.5f;
                 hintTarget.transform.position = hintPos;

                 // Add Two Bone IK Constraint
                 var twoBoneIK = rigContainer.AddComponent<TwoBoneIKConstraint>();
                 twoBoneIK.data.root = root;
                 twoBoneIK.data.mid = mid;
                 twoBoneIK.data.tip = tip;
                 twoBoneIK.data.target = ikTarget.transform;
                 twoBoneIK.data.hint = hintTarget.transform;
                 twoBoneIK.data.targetRotationWeight = 1.0f;
                 twoBoneIK.data.targetPositionWeight = 1.0f;
                 twoBoneIK.data.hintWeight = 1.0f;
                 twoBoneIK.weight = 0.0f; // Start disabled, can be animated
             }
#endif
         }

         private static void SetupSpineChainConstraint(GameObject rigContainer, Animator animator)
         {
#if UNITY_ANIMATION_RIGGING
             Transform spine = animator.GetBoneTransform(HumanBodyBones.Spine);
             Transform chest = animator.GetBoneTransform(HumanBodyBones.Chest);
             Transform upperChest = animator.GetBoneTransform(HumanBodyBones.UpperChest);
             Transform neck = animator.GetBoneTransform(HumanBodyBones.Neck);

             if (spine != null && chest != null)
             {
                 // Create spine target
                 GameObject spineTarget = new GameObject("Spine_Target");
                 spineTarget.transform.SetParent(rigContainer.transform);
                 spineTarget.transform.position = chest.position;
                 spineTarget.transform.rotation = chest.rotation;

                 // Add Chain IK Constraint for spine flexibility
                 var chainIK = rigContainer.AddComponent<ChainIKConstraint>();
                 chainIK.data.root = spine;
                 chainIK.data.tip = upperChest != null ? upperChest : chest;
                 chainIK.data.target = spineTarget.transform;
                 chainIK.data.chainRotationWeight = 0.5f;
                 chainIK.data.tipRotationWeight = 1.0f;
                 chainIK.data.maxIterations = 10;
                 chainIK.data.tolerance = 0.001f;
                 chainIK.weight = 0.3f; // Subtle spine bending
             }
#endif
         }

         private static void SetupHeadAimConstraint(GameObject rigContainer, Animator animator)
         {
#if UNITY_ANIMATION_RIGGING
             Transform head = animator.GetBoneTransform(HumanBodyBones.Head);
             Transform neck = animator.GetBoneTransform(HumanBodyBones.Neck);

             if (head != null)
             {
                 // Create look at target
                 GameObject lookTarget = new GameObject("Head_LookAt_Target");
                 lookTarget.transform.SetParent(rigContainer.transform);
                 lookTarget.transform.position = head.position + head.forward * 3.0f;

                 // Add Multi-Aim Constraint for head tracking
                 var multiAim = rigContainer.AddComponent<MultiAimConstraint>();
                 multiAim.data.constrainedObject = head;
                 
                 var sourceObjects = new WeightedTransformArray();
                 sourceObjects.Add(new WeightedTransform(lookTarget.transform, 1.0f));
                 multiAim.data.sourceObjects = sourceObjects;
                 
                 multiAim.data.aimAxis = MultiAimConstraintData.Axis.Z;
                 multiAim.data.upAxis = MultiAimConstraintData.Axis.Y;
                 multiAim.data.worldUpType = MultiAimConstraintData.WorldUpType.ObjectUp;
                 multiAim.data.worldUpObject = neck != null ? neck : head.parent;
                 multiAim.data.offset = Vector3.zero;
                 multiAim.weight = 0.0f; // Start disabled, can be animated
             }
#endif
         }

         private static void SetupFingerConstraints(GameObject rigContainer, Animator animator)
         {
#if UNITY_ANIMATION_RIGGING
             // Setup finger constraints for detailed hand animation
             SetupHandFingerConstraints(rigContainer, animator, true);  // Left hand
             SetupHandFingerConstraints(rigContainer, animator, false); // Right hand
#endif
         }

         private static void SetupHandFingerConstraints(GameObject rigContainer, Animator animator, bool isLeftHand)
         {
#if UNITY_ANIMATION_RIGGING
             string handPrefix = isLeftHand ? "Left" : "Right";
             
             // Get hand bone
             HumanBodyBones handBone = isLeftHand ? HumanBodyBones.LeftHand : HumanBodyBones.RightHand;
             Transform hand = animator.GetBoneTransform(handBone);
             
             if (hand == null) return;

             // Finger bones to setup constraints for
             var fingerBones = new[]
             {
                 isLeftHand ? HumanBodyBones.LeftThumbProximal : HumanBodyBones.RightThumbProximal,
                 isLeftHand ? HumanBodyBones.LeftIndexProximal : HumanBodyBones.RightIndexProximal,
                 isLeftHand ? HumanBodyBones.LeftMiddleProximal : HumanBodyBones.RightMiddleProximal,
                 isLeftHand ? HumanBodyBones.LeftRingProximal : HumanBodyBones.RightRingProximal,
                 isLeftHand ? HumanBodyBones.LeftLittleProximal : HumanBodyBones.RightLittleProximal
             };

             var fingerNames = new[] { "Thumb", "Index", "Middle", "Ring", "Little" };

             for (int i = 0; i < fingerBones.Length; i++)
             {
                 Transform fingerRoot = animator.GetBoneTransform(fingerBones[i]);
                 if (fingerRoot != null)
                 {
                     // Create finger target
                     GameObject fingerTarget = new GameObject($"{handPrefix}_{fingerNames[i]}_Target");
                     fingerTarget.transform.SetParent(rigContainer.transform);
                     fingerTarget.transform.position = fingerRoot.position;
                     fingerTarget.transform.rotation = fingerRoot.rotation;

                     // Add rotation constraint for finger control
                     var rotationConstraint = rigContainer.AddComponent<MultiRotationConstraint>();
                     rotationConstraint.data.constrainedObject = fingerRoot;
                     
                     var sourceObjects = new WeightedTransformArray();
                     sourceObjects.Add(new WeightedTransform(fingerTarget.transform, 1.0f));
                     rotationConstraint.data.sourceObjects = sourceObjects;
                     
                     rotationConstraint.data.maintainOffset = true;
                     rotationConstraint.weight = 0.0f; // Start disabled, can be animated
                 }
             }
#endif
         }

        /// <summary>
        /// [UNITY 6.2] - Otimiza personagem usando AI-powered analysis.
        /// </summary>
        private static object OptimizeCharacterWithAI(JObject @params)
        {
            try
            {
                string characterPath = @params["character_path"]?.ToString();
                string optimizationMode = @params["optimization_mode"]?.ToString() ?? "Balanced";
                bool optimizeMeshes = @params["optimize_meshes"]?.ToObject<bool>() ?? true;
                bool optimizeMaterials = @params["optimize_materials"]?.ToObject<bool>() ?? true;
                bool optimizeTextures = @params["optimize_textures"]?.ToObject<bool>() ?? true;
                bool generatePerformanceVariants = @params["generate_performance_variants"]?.ToObject<bool>() ?? true;

                if (string.IsNullOrEmpty(characterPath))
                    return Response.Error("Character path is required.");

                GameObject character = AssetDatabase.LoadAssetAtPath<GameObject>(characterPath);
                if (character == null)
                    return Response.Error($"Character not found at path: {characterPath}");

                var optimizationResults = new List<string>();

                // Otimizar meshes usando Unity 6.2 Mesh API
                if (optimizeMeshes)
                {
                    OptimizeMeshesWithAI(character, optimizationMode);
                    optimizationResults.Add("Meshes optimized with AI analysis");
                }

                // Otimizar materiais
                if (optimizeMaterials)
                {
                    OptimizeMaterialsWithAI(character, optimizationMode);
                    optimizationResults.Add("Materials optimized for performance");
                }

                // Otimizar texturas
                if (optimizeTextures)
                {
                    OptimizeTexturesWithAI(character, optimizationMode);
                    optimizationResults.Add("Textures compressed and optimized");
                }

                // Gerar variantes de performance
                if (generatePerformanceVariants)
                {
                    GeneratePerformanceVariants(character, characterPath);
                    optimizationResults.Add("Performance variants generated");
                }

                // Configurar GPU Resident Drawer se disponível
                SetupGPUResidentDrawerForCharacter(character);
                optimizationResults.Add("GPU Resident Drawer configured");

                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                return Response.Success($"Character optimized successfully with {optimizationResults.Count} optimizations", new
                {
                    characterPath = characterPath,
                    optimizationMode = optimizationMode,
                    optimizations = optimizationResults.ToArray(),
                    supportsGPUResident = SystemInfo.supportsInstancing,
                    supportsRayTracing = SystemInfo.supportsRayTracing
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error optimizing character: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Configura subsurface scattering para pele realista.
        /// </summary>
        private static object SetupSubsurfaceScattering(JObject @params)
        {
            try
            {
                string characterPath = @params["character_path"]?.ToString();
                string renderPipeline = @params["render_pipeline"]?.ToString() ?? "URP";
                float scatteringIntensity = @params["scattering_intensity"]?.ToObject<float>() ?? 1.0f;
                string scatteringColor = @params["scattering_color"]?.ToString() ?? "#FFDBAC";
                bool enableTransmission = @params["enable_transmission"]?.ToObject<bool>() ?? true;
                bool enableHighQuality = @params["enable_high_quality"]?.ToObject<bool>() ?? true;

                if (string.IsNullOrEmpty(characterPath))
                    return Response.Error("Character path is required.");

                GameObject character = AssetDatabase.LoadAssetAtPath<GameObject>(characterPath);
                if (character == null)
                    return Response.Error($"Character not found at path: {characterPath}");

                // Configurar subsurface scattering baseado no render pipeline
                var renderers = character.GetComponentsInChildren<Renderer>();
                int configuredRenderers = 0;

                foreach (var renderer in renderers)
                {
                    if (renderer.sharedMaterial != null)
                    {
                        Material material = renderer.sharedMaterial;

                        // Configurar propriedades de subsurface scattering
                        if (material.HasProperty("_SubsurfaceScattering"))
                        {
                            material.SetFloat("_SubsurfaceScattering", scatteringIntensity);
                            configuredRenderers++;
                        }

                        if (material.HasProperty("_TransmissionTint"))
                        {
                            Color color = ParseHexColor(scatteringColor);
                            material.SetColor("_TransmissionTint", color);
                        }

                        if (material.HasProperty("_EnableTransmission"))
                        {
                            material.SetFloat("_EnableTransmission", enableTransmission ? 1.0f : 0.0f);
                        }
                    }
                }

                return Response.Success($"Subsurface scattering configured for {configuredRenderers} renderers", new
                {
                    characterPath = characterPath,
                    renderPipeline = renderPipeline,
                    scatteringIntensity = scatteringIntensity,
                    scatteringColor = scatteringColor,
                    configuredRenderers = configuredRenderers
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error setting up subsurface scattering: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gera expressões faciais avançadas usando blend shapes.
        /// </summary>
        private static object GenerateAdvancedFacialExpressions(JObject @params)
        {
            try
            {
                string characterPath = @params["character_path"]?.ToString();
                var expressionTypes = @params["expression_types"] as JArray;
                bool generateMicroExpressions = @params["generate_micro_expressions"]?.ToObject<bool>() ?? true;
                bool enableEmotionalBlending = @params["enable_emotional_blending"]?.ToObject<bool>() ?? true;
                int intensityLevels = @params["expression_intensity_levels"]?.ToObject<int>() ?? 5;

                if (string.IsNullOrEmpty(characterPath))
                    return Response.Error("Character path is required.");

                GameObject character = AssetDatabase.LoadAssetAtPath<GameObject>(characterPath);
                if (character == null)
                    return Response.Error($"Character not found at path: {characterPath}");

                // Encontrar meshes com blend shapes
                var skinnedMeshRenderers = character.GetComponentsInChildren<SkinnedMeshRenderer>();
                int expressionsGenerated = 0;

                foreach (var renderer in skinnedMeshRenderers)
                {
                    if (renderer.sharedMesh != null && renderer.sharedMesh.blendShapeCount > 0)
                    {
                        // Configurar blend shapes para expressões
                        ConfigureBlendShapesForExpressions(renderer, expressionTypes, intensityLevels);
                        expressionsGenerated++;
                    }
                }

                // Adicionar componente de controle de expressões
                var expressionController = character.GetComponent<MonoBehaviour>();
                if (expressionController == null)
                {
                    CreateExpressionControllerScript(character, enableEmotionalBlending);
                }

                return Response.Success($"Advanced facial expressions generated for {expressionsGenerated} meshes", new
                {
                    characterPath = characterPath,
                    expressionsGenerated = expressionsGenerated,
                    intensityLevels = intensityLevels,
                    microExpressions = generateMicroExpressions,
                    emotionalBlending = enableEmotionalBlending
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error generating facial expressions: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Configura eye tracking avançado.
        /// </summary>
        private static object SetupAdvancedEyeTracking(JObject @params)
        {
            try
            {
                string characterPath = @params["character_path"]?.ToString();
                float trackingSensitivity = @params["tracking_sensitivity"]?.ToObject<float>() ?? 1.0f;
                float blinkFrequency = @params["blink_frequency"]?.ToObject<float>() ?? 0.2f;
                bool enableSaccadicMovement = @params["enable_saccadic_movement"]?.ToObject<bool>() ?? true;

                if (string.IsNullOrEmpty(characterPath))
                    return Response.Error("Character path is required.");

                GameObject character = AssetDatabase.LoadAssetAtPath<GameObject>(characterPath);
                if (character == null)
                    return Response.Error($"Character not found at path: {characterPath}");

                // Configurar eye tracking
                SetupEyeTrackingSystem(character, trackingSensitivity, blinkFrequency, enableSaccadicMovement);

                return Response.Success("Advanced eye tracking configured successfully", new
                {
                    characterPath = characterPath,
                    trackingSensitivity = trackingSensitivity,
                    blinkFrequency = blinkFrequency,
                    saccadicMovement = enableSaccadicMovement
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error setting up eye tracking: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Aplica micro detalhes para superfícies.
        /// </summary>
        private static object ApplyMicroDetailEnhancement(JObject @params)
        {
            try
            {
                string characterPath = @params["character_path"]?.ToString();
                string detailLevel = @params["detail_level"]?.ToString() ?? "Medium";
                bool enhanceSkinPores = @params["enhance_skin_pores"]?.ToObject<bool>() ?? true;
                bool enhanceFabricWeave = @params["enhance_fabric_weave"]?.ToObject<bool>() ?? true;
                float normalMapIntensity = @params["normal_map_intensity"]?.ToObject<float>() ?? 1.0f;

                if (string.IsNullOrEmpty(characterPath))
                    return Response.Error("Character path is required.");

                GameObject character = AssetDatabase.LoadAssetAtPath<GameObject>(characterPath);
                if (character == null)
                    return Response.Error($"Character not found at path: {characterPath}");

                // Aplicar micro detalhes
                int enhancedMaterials = ApplyMicroDetailsToMaterials(character, detailLevel, enhanceSkinPores, enhanceFabricWeave, normalMapIntensity);

                return Response.Success($"Micro detail enhancement applied to {enhancedMaterials} materials", new
                {
                    characterPath = characterPath,
                    detailLevel = detailLevel,
                    enhancedMaterials = enhancedMaterials,
                    normalMapIntensity = normalMapIntensity
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error applying micro detail enhancement: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Configura GPU Resident Drawer.
        /// </summary>
        private static object SetupGPUResidentDrawer(JObject @params)
        {
            try
            {
                string characterPath = @params["character_path"]?.ToString();
                bool enableGPUCulling = @params["enable_gpu_culling"]?.ToObject<bool>() ?? true;
                bool enableInstancing = @params["enable_instancing"]?.ToObject<bool>() ?? true;

                if (string.IsNullOrEmpty(characterPath))
                    return Response.Error("Character path is required.");

                GameObject character = AssetDatabase.LoadAssetAtPath<GameObject>(characterPath);
                if (character == null)
                    return Response.Error($"Character not found at path: {characterPath}");

                // Configurar GPU Resident Drawer
                bool configured = SetupGPUResidentDrawerForCharacter(character);

                return Response.Success($"GPU Resident Drawer configured: {configured}", new
                {
                    characterPath = characterPath,
                    configured = configured,
                    supportsInstancing = SystemInfo.supportsInstancing,
                    enableGPUCulling = enableGPUCulling
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error setting up GPU Resident Drawer: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Habilita suporte a raytracing.
        /// </summary>
        private static object EnableRaytracingSupport(JObject @params)
        {
            try
            {
                string characterPath = @params["character_path"]?.ToString();
                bool enableReflections = @params["enable_reflections"]?.ToObject<bool>() ?? true;
                bool enableGlobalIllumination = @params["enable_global_illumination"]?.ToObject<bool>() ?? true;

                if (string.IsNullOrEmpty(characterPath))
                    return Response.Error("Character path is required.");

                if (!SystemInfo.supportsRayTracing)
                    return Response.Error("Ray tracing is not supported on this system.");

                GameObject character = AssetDatabase.LoadAssetAtPath<GameObject>(characterPath);
                if (character == null)
                    return Response.Error($"Character not found at path: {characterPath}");

                // Configurar raytracing
                int configuredRenderers = ConfigureRaytracingForCharacter(character, enableReflections, enableGlobalIllumination);

                return Response.Success($"Raytracing support enabled for {configuredRenderers} renderers", new
                {
                    characterPath = characterPath,
                    configuredRenderers = configuredRenderers,
                    enableReflections = enableReflections,
                    enableGlobalIllumination = enableGlobalIllumination,
                    supportsRayTracing = SystemInfo.supportsRayTracing
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Error enabling raytracing support: {e.Message}");
            }
        }

        // Funções auxiliares para as otimizações Unity 6.2
        private static void OptimizeMeshesWithAI(GameObject character, string optimizationMode)
        {
            var meshFilters = character.GetComponentsInChildren<MeshFilter>();
            foreach (var meshFilter in meshFilters)
            {
                if (meshFilter.sharedMesh != null)
                {
                    // Aplicar otimizações de mesh baseadas no modo
                    Mesh mesh = meshFilter.sharedMesh;
                    mesh.Optimize();
                    mesh.OptimizeIndexBuffers();
                    mesh.OptimizeReorderVertexBuffer();
                }
            }
        }

        private static void OptimizeMaterialsWithAI(GameObject character, string optimizationMode)
        {
            var renderers = character.GetComponentsInChildren<Renderer>();
            foreach (var renderer in renderers)
            {
                if (renderer.sharedMaterial != null)
                {
                    Material material = renderer.sharedMaterial;

                    // Habilitar GPU Instancing se suportado
                    if (SystemInfo.supportsInstancing)
                    {
                        material.enableInstancing = true;
                    }

                    // Configurar keywords baseado no modo de otimização
                    if (optimizationMode == "Performance")
                    {
                        material.DisableKeyword("_NORMALMAP");
                        material.DisableKeyword("_PARALLAXMAP");
                    }
                }
            }
        }

        private static void OptimizeTexturesWithAI(GameObject character, string optimizationMode)
        {
            var renderers = character.GetComponentsInChildren<Renderer>();
            foreach (var renderer in renderers)
            {
                if (renderer.sharedMaterial != null)
                {
                    Material material = renderer.sharedMaterial;

                    // Otimizar texturas baseado no modo
                    var textureProperties = new string[] { "_MainTex", "_BumpMap", "_MetallicGlossMap" };
                    foreach (var prop in textureProperties)
                    {
                        if (material.HasProperty(prop))
                        {
                            Texture texture = material.GetTexture(prop);
                            if (texture != null)
                            {
                                // Aplicar configurações de compressão otimizadas
                                string texturePath = AssetDatabase.GetAssetPath(texture);
                                if (!string.IsNullOrEmpty(texturePath))
                                {
                                    TextureImporter importer = AssetImporter.GetAtPath(texturePath) as TextureImporter;
                                    if (importer != null)
                                    {
                                        importer.textureCompression = TextureImporterCompression.Compressed;
                                        importer.crunchedCompression = true;
                                        importer.SaveAndReimport();
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // Funções auxiliares que estavam faltando
        private static void GeneratePerformanceVariants(GameObject character, string characterPath)
        {
            // Criar variantes de performance (LOD0, LOD1, LOD2)
            string variantsPath = Path.GetDirectoryName(characterPath) + "/Variants";
            if (!Directory.Exists(variantsPath))
            {
                Directory.CreateDirectory(variantsPath);
            }

            // LOD1 - Performance médio
            GameObject lod1Variant = UnityEngine.Object.Instantiate(character);
            lod1Variant.name = character.name + "_LOD1";
            OptimizeForPerformance(lod1Variant, "Medium");
            PrefabUtility.SaveAsPrefabAsset(lod1Variant, Path.Combine(variantsPath, lod1Variant.name + ".prefab"));
            UnityEngine.Object.DestroyImmediate(lod1Variant);

            // LOD2 - Performance alto
            GameObject lod2Variant = UnityEngine.Object.Instantiate(character);
            lod2Variant.name = character.name + "_LOD2";
            OptimizeForPerformance(lod2Variant, "High");
            PrefabUtility.SaveAsPrefabAsset(lod2Variant, Path.Combine(variantsPath, lod2Variant.name + ".prefab"));
            UnityEngine.Object.DestroyImmediate(lod2Variant);
        }

        private static void OptimizeForPerformance(GameObject character, string level)
        {
            var renderers = character.GetComponentsInChildren<Renderer>();
            foreach (var renderer in renderers)
            {
                if (level == "High")
                {
                    // Reduzir qualidade de materiais para performance alta
                    if (renderer.sharedMaterial != null)
                    {
                        Material mat = renderer.sharedMaterial;
                        if (mat.HasProperty("_DetailAlbedoMap"))
                            mat.SetTexture("_DetailAlbedoMap", null);
                        if (mat.HasProperty("_ParallaxMap"))
                            mat.SetTexture("_ParallaxMap", null);
                    }
                }
            }
        }

        private static bool SetupGPUResidentDrawerForCharacter(GameObject character)
        {
            if (!SystemInfo.supportsInstancing)
                return false;

            var renderers = character.GetComponentsInChildren<Renderer>();
            foreach (var renderer in renderers)
            {
                if (renderer.sharedMaterial != null)
                {
                    renderer.sharedMaterial.enableInstancing = true;
                }
            }
            return true;
        }

        private static Color ParseHexColor(string hexColor)
        {
            if (string.IsNullOrEmpty(hexColor))
                return Color.white;

            if (ColorUtility.TryParseHtmlString(hexColor, out Color color))
            {
                return color;
            }

            return Color.white;
        }

        private static void ConfigureBlendShapesForExpressions(SkinnedMeshRenderer renderer, JArray expressionTypes, int intensityLevels)
        {
            // Configurar blend shapes existentes para expressões
            Mesh mesh = renderer.sharedMesh;
            if (mesh == null || mesh.blendShapeCount == 0)
                return;

            // Mapear blend shapes para expressões comuns
            for (int i = 0; i < mesh.blendShapeCount; i++)
            {
                string blendShapeName = mesh.GetBlendShapeName(i);

                // Configurar pesos baseado no tipo de expressão
                if (blendShapeName.ToLower().Contains("smile"))
                {
                    renderer.SetBlendShapeWeight(i, 0f); // Começar neutro
                }
                else if (blendShapeName.ToLower().Contains("frown"))
                {
                    renderer.SetBlendShapeWeight(i, 0f);
                }
            }
        }

        private static void CreateExpressionControllerScript(GameObject character, bool enableEmotionalBlending)
        {
            // Criar script básico de controle de expressões
            string scriptPath = "Assets/Scripts/ExpressionController.cs";

            if (!File.Exists(scriptPath))
            {
                string scriptContent = @"
using UnityEngine;

public class ExpressionController : MonoBehaviour
{
    [Header(""Expression Settings"")]
    public SkinnedMeshRenderer faceMesh;
    public float transitionSpeed = 2f;

    [Header(""Current Expression"")]
    [Range(0f, 1f)] public float happiness = 0f;
    [Range(0f, 1f)] public float sadness = 0f;
    [Range(0f, 1f)] public float anger = 0f;
    [Range(0f, 1f)] public float surprise = 0f;

    private void Start()
    {
        if (faceMesh == null)
            faceMesh = GetComponentInChildren<SkinnedMeshRenderer>();
    }

    private void Update()
    {
        if (faceMesh != null && faceMesh.sharedMesh != null)
        {
            ApplyExpressions();
        }
    }

    private void ApplyExpressions()
    {
        for (int i = 0; i < faceMesh.sharedMesh.blendShapeCount; i++)
        {
            string shapeName = faceMesh.sharedMesh.GetBlendShapeName(i).ToLower();
            float targetWeight = 0f;

            if (shapeName.Contains(""smile"") || shapeName.Contains(""happy""))
                targetWeight = happiness * 100f;
            else if (shapeName.Contains(""frown"") || shapeName.Contains(""sad""))
                targetWeight = sadness * 100f;
            else if (shapeName.Contains(""angry""))
                targetWeight = anger * 100f;
            else if (shapeName.Contains(""surprise""))
                targetWeight = surprise * 100f;

            float currentWeight = faceMesh.GetBlendShapeWeight(i);
            float newWeight = Mathf.Lerp(currentWeight, targetWeight, Time.deltaTime * transitionSpeed);
            faceMesh.SetBlendShapeWeight(i, newWeight);
        }
    }

    public void SetExpression(string expression, float intensity)
    {
        switch (expression.ToLower())
        {
            case ""happy"": happiness = intensity; break;
            case ""sad"": sadness = intensity; break;
            case ""angry"": anger = intensity; break;
            case ""surprised"": surprise = intensity; break;
        }
    }
}";

                string directory = Path.GetDirectoryName(scriptPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                File.WriteAllText(scriptPath, scriptContent);
                AssetDatabase.Refresh();
            }
        }

        private static void SetupEyeTrackingSystem(GameObject character, float trackingSensitivity, float blinkFrequency, bool enableSaccadicMovement)
        {
            // Encontrar bones dos olhos
            var animator = character.GetComponent<Animator>();
            if (animator != null && animator.isHuman)
            {
                Transform leftEye = animator.GetBoneTransform(HumanBodyBones.LeftEye);
                Transform rightEye = animator.GetBoneTransform(HumanBodyBones.RightEye);

                if (leftEye != null && rightEye != null)
                {
                    // Adicionar componente de eye tracking
                    var eyeTracker = character.AddComponent<MonoBehaviour>();
                    // Configurar propriedades via reflection se necessário
                }
            }
        }

        private static int ApplyMicroDetailsToMaterials(GameObject character, string detailLevel, bool enhanceSkinPores, bool enhanceFabricWeave, float normalMapIntensity)
        {
            int enhancedCount = 0;
            var renderers = character.GetComponentsInChildren<Renderer>();

            foreach (var renderer in renderers)
            {
                if (renderer.sharedMaterial != null)
                {
                    Material material = renderer.sharedMaterial;

                    // Aplicar micro detalhes baseado no nível
                    if (material.HasProperty("_BumpScale"))
                    {
                        material.SetFloat("_BumpScale", normalMapIntensity);
                        enhancedCount++;
                    }

                    if (material.HasProperty("_DetailNormalMapScale"))
                    {
                        float detailScale = detailLevel == "High" ? 2.0f : detailLevel == "Medium" ? 1.0f : 0.5f;
                        material.SetFloat("_DetailNormalMapScale", detailScale);
                    }
                }
            }

            return enhancedCount;
        }

        private static int ConfigureRaytracingForCharacter(GameObject character, bool enableReflections, bool enableGlobalIllumination)
        {
            int configuredCount = 0;
            var renderers = character.GetComponentsInChildren<Renderer>();

            foreach (var renderer in renderers)
            {
                if (renderer.sharedMaterial != null)
                {
                    Material material = renderer.sharedMaterial;

                    // Configurar propriedades de raytracing se disponíveis
                    if (material.HasProperty("_EnableRayTracing"))
                    {
                        material.SetFloat("_EnableRayTracing", 1.0f);
                        configuredCount++;
                    }

                    if (enableReflections && material.HasProperty("_EnableReflections"))
                    {
                        material.SetFloat("_EnableReflections", 1.0f);
                    }

                    if (enableGlobalIllumination && material.HasProperty("_EnableGI"))
                    {
                        material.SetFloat("_EnableGI", 1.0f);
                    }
                }
            }

            return configuredCount;
        }

        /// <summary>
        /// [UNITY 6.2] - Atribui materiais de forma inteligente baseado no tipo e nome do renderer.
        /// </summary>
        private static Material[] AssignMaterialsIntelligently(Renderer renderer, List<Material> availableMaterials)
        {
            try
            {
                var assignedMaterials = new List<Material>();
                string rendererName = renderer.name.ToLower();

                // Mapear materiais baseado no nome do objeto
                foreach (var material in availableMaterials)
                {
                    string materialName = material.name.ToLower();

                    // Lógica de mapeamento inteligente
                    bool shouldAssign = false;

                    // Mapeamento por partes do corpo
                    if (rendererName.Contains("head") && materialName.Contains("skin"))
                        shouldAssign = true;
                    else if (rendererName.Contains("body") && materialName.Contains("skin"))
                        shouldAssign = true;
                    else if (rendererName.Contains("hair") && materialName.Contains("hair"))
                        shouldAssign = true;
                    else if (rendererName.Contains("eye") && materialName.Contains("eye"))
                        shouldAssign = true;
                    else if (rendererName.Contains("cloth") && materialName.Contains("fabric"))
                        shouldAssign = true;
                    else if (rendererName.Contains("armor") && materialName.Contains("metal"))
                        shouldAssign = true;
                    else if (materialName.Contains("default") || materialName.Contains("standard"))
                        shouldAssign = true; // Material padrão

                    if (shouldAssign)
                    {
                        assignedMaterials.Add(material);
                    }
                }

                // Se não encontrou materiais específicos, usar o primeiro disponível
                if (assignedMaterials.Count == 0 && availableMaterials.Count > 0)
                {
                    assignedMaterials.Add(availableMaterials[0]);
                }

                return assignedMaterials.ToArray();
            }
            catch (Exception e)
            {
                Debug.LogError($"[ProceduralCharacterAssembly] Failed to assign materials intelligently: {e.Message}");
                return availableMaterials.Count > 0 ? new Material[] { availableMaterials[0] } : new Material[0];
            }
        }
     }
 }