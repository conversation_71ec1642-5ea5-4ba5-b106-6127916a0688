from mcp.server.fastmcp import FastMC<PERSON>, Context
from typing import Dict, Any, List, Optional
from unity_connection import get_unity_connection

def register_behavior_ai_generation_tools(mcp: FastMCP):
    """Register Unity Behavior AI Generation tools with the MCP server."""

    @mcp.tool()
    def manage_behavior_graph(
        ctx: Context,
        action: str,
        graph_path: Optional[str] = None,
        placeholders: Optional[List[Dict[str, Any]]] = None,
        auto_connect: Optional[bool] = None,
        validation_rules: Optional[List[str]] = None,
        optimization_settings: Optional[Dict[str, Any]] = None,
        node_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Gerenciamento avançado de grafos de comportamento com placeholders e validação.

        Funcionalidades implementadas:
        - add_placeholders_to_graph: Adicionar placeholders ao grafo
        - calculate_tree_depth: Calcular profundidade da árvore
        - validate_behavior_graph: Validar grafo de comportamento
        - optimize_graph_structure: Otimizar estrutura do grafo
        - analyze_graph_complexity: Analisar complexidade do grafo

        Args:
            action: Ação a executar
            graph_path: Caminho do grafo de comportamento
            placeholders: Lista de placeholders para adicionar
            auto_connect: Conectar automaticamente os nós
            validation_rules: Regras de validação
            optimization_settings: Configurações de otimização
            node_config: Configuração dos nós

        Returns:
            Dictionary com resultados da operação
        """
        try:
            params = {
                "action": action,
                "graph_path": graph_path,
                "placeholders": placeholders,
                "auto_connect": auto_connect,
                "validation_rules": validation_rules,
                "optimization_settings": optimization_settings,
                "node_config": node_config
            }
            params = {k: v for k, v in params.items() if v is not None}

            response = get_unity_connection().send_command("behavior_ai_generation", params)

            if response.get("success"):
                return {
                    "success": True,
                    "message": response.get("message", "Behavior graph operation completed successfully."),
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False,
                    "message": response.get("error", "Failed to execute behavior graph operation.")
                }

        except Exception as e:
            return {"success": False, "message": f"Python error in behavior graph management: {str(e)}"}

    @mcp.tool()
    def generate_behavior_nodes_with_ai(
        ctx: Context,
        description: str,
        node_type: str = "action",
        behavior_tree_path: str = None,
        ai_model_settings: Dict[str, Any] = None,
        generation_parameters: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Geração de nós de comportamento usando IA generativa.

        Args:
            description: Descrição do comportamento desejado para o nó
            node_type: Tipo do nó (action, condition, composite, decorator)
            behavior_tree_path: Caminho para o behavior tree onde adicionar o nó
            ai_model_settings: Configurações do modelo de IA (temperatura, max_tokens, etc.)
            generation_parameters: Parâmetros específicos de geração (complexity, style, etc.)

        Returns:
            Dictionary com resultados ('success', 'message', 'data')
        """
        try:
            params = {
                "action": "generate_nodes",
                "description": description,
                "node_type": node_type,
                "behavior_tree_path": behavior_tree_path,
                "ai_model_settings": ai_model_settings,
                "generation_parameters": generation_parameters
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("behavior_ai_generation", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Nós de comportamento gerados com sucesso."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Erro desconhecido.")
                }

        except Exception as e:
            return {"success": False, "message": f"Erro Python: {str(e)}"}

    @mcp.tool()
    def create_ai_behavior_branches(
        ctx: Context,
        branch_description: str,
        parent_node_id: str = None,
        behavior_tree_path: str = None,
        branch_complexity: str = "medium",
        conditional_logic: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Criação de branches de comportamento com IA.

        Args:
            branch_description: Descrição do comportamento da branch
            parent_node_id: ID do nó pai onde conectar a branch
            behavior_tree_path: Caminho para o behavior tree
            branch_complexity: Complexidade da branch (simple, medium, complex)
            conditional_logic: Lógica condicional para a branch

        Returns:
            Dictionary com resultados ('success', 'message', 'data')
        """
        try:
            params = {
                "action": "create_branches",
                "branch_description": branch_description,
                "parent_node_id": parent_node_id,
                "behavior_tree_path": behavior_tree_path,
                "branch_complexity": branch_complexity,
                "conditional_logic": conditional_logic
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("behavior_ai_generation", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Branches de comportamento criadas com sucesso."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Erro desconhecido.")
                }

        except Exception as e:
            return {"success": False, "message": f"Erro Python: {str(e)}"}

    @mcp.tool()
    def setup_generative_ai_behavior_graph(
        ctx: Context,
        graph_name: str,
        graph_description: str,
        target_entity_type: str = "npc",
        behavior_complexity: str = "medium",
        ai_generation_settings: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Configuração de grafos de comportamento gerados por IA.

        Args:
            graph_name: Nome do grafo de comportamento
            graph_description: Descrição do comportamento geral do grafo
            target_entity_type: Tipo de entidade alvo (npc, enemy, ally, neutral)
            behavior_complexity: Complexidade do comportamento (simple, medium, complex, advanced)
            ai_generation_settings: Configurações específicas de geração de IA

        Returns:
            Dictionary com resultados ('success', 'message', 'data')
        """
        try:
            params = {
                "action": "setup_graph",
                "graph_name": graph_name,
                "graph_description": graph_description,
                "target_entity_type": target_entity_type,
                "behavior_complexity": behavior_complexity,
                "ai_generation_settings": ai_generation_settings
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("behavior_ai_generation", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Grafo de comportamento configurado com sucesso."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Erro desconhecido.")
                }

        except Exception as e:
            return {"success": False, "message": f"Erro Python: {str(e)}"}

    @mcp.tool()
    def generate_action_nodes_from_description(
        ctx: Context,
        action_description: str,
        target_object_type: str = None,
        action_parameters: Dict[str, Any] = None,
        code_generation_style: str = "standard",
        include_error_handling: bool = True
    ) -> Dict[str, Any]:
        """Geração de action nodes a partir de descrições.

        Args:
            action_description: Descrição detalhada da ação desejada
            target_object_type: Tipo do objeto alvo da ação
            action_parameters: Parâmetros específicos da ação
            code_generation_style: Estilo de geração de código (standard, optimized, verbose)
            include_error_handling: Se deve incluir tratamento de erros

        Returns:
            Dictionary com resultados ('success', 'message', 'data')
        """
        try:
            params = {
                "action": "generate_action_nodes",
                "action_description": action_description,
                "target_object_type": target_object_type,
                "action_parameters": action_parameters,
                "code_generation_style": code_generation_style,
                "include_error_handling": include_error_handling
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("behavior_ai_generation", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Action nodes gerados com sucesso."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Erro desconhecido.")
                }

        except Exception as e:
            return {"success": False, "message": f"Erro Python: {str(e)}"}

    @mcp.tool()
    def create_placeholder_behavior_nodes(
        ctx: Context,
        node_count: int,
        node_types: List[str] = None,
        behavior_tree_path: str = None,
        placeholder_naming_pattern: str = "Node_{index}",
        auto_connect: bool = False
    ) -> Dict[str, Any]:
        """Criação de nós placeholder para comportamentos.

        Args:
            node_count: Número de nós placeholder a criar
            node_types: Lista de tipos de nós a criar
            behavior_tree_path: Caminho para o behavior tree
            placeholder_naming_pattern: Padrão de nomenclatura dos placeholders
            auto_connect: Se deve conectar automaticamente os nós

        Returns:
            Dictionary com resultados ('success', 'message', 'data')
        """
        try:
            params = {
                "action": "create_placeholders",
                "node_count": node_count,
                "node_types": node_types,
                "behavior_tree_path": behavior_tree_path,
                "placeholder_naming_pattern": placeholder_naming_pattern,
                "auto_connect": auto_connect
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("behavior_ai_generation", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Nós placeholder criados com sucesso."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Erro desconhecido.")
                }

        except Exception as e:
            return {"success": False, "message": f"Erro Python: {str(e)}"}

    @mcp.tool()
    def optimize_behavior_tree_with_ai(
        ctx: Context,
        behavior_tree_path: str,
        optimization_goals: List[str] = None,
        performance_targets: Dict[str, Any] = None,
        preserve_functionality: bool = True,
        optimization_level: str = "balanced"
    ) -> Dict[str, Any]:
        """Otimização de behavior trees usando IA.

        Args:
            behavior_tree_path: Caminho para o behavior tree a otimizar
            optimization_goals: Lista de objetivos de otimização (performance, readability, maintainability)
            performance_targets: Alvos específicos de performance
            preserve_functionality: Se deve preservar a funcionalidade original
            optimization_level: Nível de otimização (conservative, balanced, aggressive)

        Returns:
            Dictionary com resultados ('success', 'message', 'data')
        """
        try:
            params = {
                "action": "optimize_tree",
                "behavior_tree_path": behavior_tree_path,
                "optimization_goals": optimization_goals,
                "performance_targets": performance_targets,
                "preserve_functionality": preserve_functionality,
                "optimization_level": optimization_level
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("behavior_ai_generation", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Behavior tree otimizado com sucesso."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Erro desconhecido.")
                }

        except Exception as e:
            return {"success": False, "message": f"Erro Python: {str(e)}"}

    @mcp.tool()
    def validate_generated_behavior_code(
        ctx: Context,
        behavior_code_path: str = None,
        behavior_tree_path: str = None,
        validation_rules: List[str] = None,
        auto_fix_issues: bool = False,
        generate_report: bool = True
    ) -> Dict[str, Any]:
        """Validação de código de comportamento gerado por IA.

        Args:
            behavior_code_path: Caminho para o código de comportamento a validar
            behavior_tree_path: Caminho para o behavior tree a validar
            validation_rules: Lista de regras de validação específicas
            auto_fix_issues: Se deve tentar corrigir automaticamente os problemas
            generate_report: Se deve gerar um relatório detalhado

        Returns:
            Dictionary com resultados ('success', 'message', 'data')
        """
        try:
            params = {
                "action": "validate_code",
                "behavior_code_path": behavior_code_path,
                "behavior_tree_path": behavior_tree_path,
                "validation_rules": validation_rules,
                "auto_fix_issues": auto_fix_issues,
                "generate_report": generate_report
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("behavior_ai_generation", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Validação de código concluída com sucesso."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Erro desconhecido.")
                }

        except Exception as e:
            return {"success": False, "message": f"Erro Python: {str(e)}"}