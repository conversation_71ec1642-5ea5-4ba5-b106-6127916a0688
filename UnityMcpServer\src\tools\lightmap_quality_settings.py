from mcp.server.fastmcp import FastMC<PERSON>, Context
from typing import Dict, Any, List, Optional
from unity_connection import get_unity_connection

def register_lightmap_quality_settings_tools(mcp: FastMCP):
    """Register Lightmap Quality Settings tools with the MCP server."""

    @mcp.tool()
    def lightmap_quality_settings(
        ctx: Context,
        action: str,
        quality_preset: Optional[str] = None,
        lightmap_resolution: Optional[int] = None,
        lightmap_padding: Optional[int] = None,
        compress_lightmaps: Optional[bool] = None,
        ao_max_distance: Optional[float] = None,
        indirect_output_scale: Optional[float] = None,
        albedo_boost: Optional[float] = None,
        lightmap_parameters: Optional[Dict[str, Any]] = None,
        baking_backend: Optional[str] = None
    ) -> Dict[str, Any]:
        """Configurações de Qualidade de Lightmaps usando Unity 6.2 Lightmapping APIs.

        Funcionalidades:
        - set_quality_preset: Definir preset de qualidade
        - configure_lightmap_settings: Configurar configurações de lightmap
        - optimize_for_platform: Otimizar para plataforma específica
        - batch_apply_settings: Aplicar configurações em lote
        - validate_settings: Validar configurações
        - export_quality_profile: Exportar perfil de qualidade

        Args:
            action: Operação a executar
            quality_preset: Preset (draft, medium, high, production)
            lightmap_resolution: Resolução do lightmap
            lightmap_padding: Padding entre UV islands
            compress_lightmaps: Comprimir lightmaps
            ao_max_distance: Distância máxima para AO
            indirect_output_scale: Escala de saída indireta
            albedo_boost: Boost de albedo
            lightmap_parameters: Parâmetros customizados
            baking_backend: Backend de baking (CPU, GPU, Progressive)

        Returns:
            Dictionary com resultados ('success', 'message', 'data')
        """
        try:
            params = {
                "action": action,
                "quality_preset": quality_preset,
                "lightmap_resolution": lightmap_resolution,
                "lightmap_padding": lightmap_padding,
                "compress_lightmaps": compress_lightmaps,
                "ao_max_distance": ao_max_distance,
                "indirect_output_scale": indirect_output_scale,
                "albedo_boost": albedo_boost,
                "lightmap_parameters": lightmap_parameters,
                "baking_backend": baking_backend
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("lightmap_quality_settings", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Lightmap quality settings applied successfully."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Failed to apply lightmap quality settings.")
                }

        except Exception as e:
            return {"success": False, "message": f"Python error in lightmap quality settings: {str(e)}"}
