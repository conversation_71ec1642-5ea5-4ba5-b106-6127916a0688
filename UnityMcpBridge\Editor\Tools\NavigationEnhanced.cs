using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.AI;
using Unity.AI.Navigation;
using UnityMcpBridge.Editor.Helpers;

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// Handles enhanced navigation and pathfinding operations for NavMesh systems.
    /// </summary>
    public static class NavigationEnhanced
    {
        private static readonly List<string> ValidSetupDynamicNavmeshAreasActions = new List<string>
        {
            "create", "modify", "delete", "list", "get_info"
        };

        private static readonly List<string> ValidConfigureNavmeshCostsActions = new List<string>
        {
            "set", "get", "reset", "create_profile", "apply_profile"
        };

        private static readonly List<string> ValidSetupRuntimeObstacleAvoidanceActions = new List<string>
        {
            "setup", "modify", "remove", "get_info", "list"
        };

        private static readonly List<string> ValidCreateDynamicPathModifiersActions = new List<string>
        {
            "create", "modify", "delete", "enable", "disable", "list"
        };

        private static readonly List<string> ValidSetupNavmeshLinksActions = new List<string>
        {
            "create", "modify", "delete", "enable", "disable", "list"
        };

        private static readonly List<string> ValidConfigureAreaMasksActions = new List<string>
        {
            "set", "get", "create_mask", "apply_mask", "list_masks"
        };

        public static object HandleSetupDynamicNavmeshAreas(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidSetupDynamicNavmeshAreasActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidSetupDynamicNavmeshAreasActions);
                return Response.Error($"Unknown action: '{action}'. Valid actions are: {validActionsList}");
            }

            try
            {
                switch (action)
                {
                    case "create":
                        return CreateNavMeshArea(@params);
                    case "modify":
                        return ModifyNavMeshArea(@params);
                    case "delete":
                        return DeleteNavMeshArea(@params);
                    case "list":
                        return ListNavMeshAreas();
                    case "get_info":
                        return GetNavMeshAreaInfo(@params);
                    default:
                        return Response.Error($"Unknown action: '{action}'");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[NavigationEnhanced] SetupDynamicNavmeshAreas action '{action}' failed: {e}");
                return Response.Error($"Internal error processing action '{action}': {e.Message}");
            }
        }

        public static object HandleConfigureNavmeshCosts(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidConfigureNavmeshCostsActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidConfigureNavmeshCostsActions);
                return Response.Error($"Unknown action: '{action}'. Valid actions are: {validActionsList}");
            }

            try
            {
                switch (action)
                {
                    case "set":
                        return SetNavMeshCosts(@params);
                    case "get":
                        return GetNavMeshCosts(@params);
                    case "reset":
                        return ResetNavMeshCosts(@params);
                    case "create_profile":
                        return CreateCostProfile(@params);
                    case "apply_profile":
                        return ApplyCostProfile(@params);
                    default:
                        return Response.Error($"Unknown action: '{action}'");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[NavigationEnhanced] ConfigureNavmeshCosts action '{action}' failed: {e}");
                return Response.Error($"Internal error processing action '{action}': {e.Message}");
            }
        }

        public static object HandleSetupRuntimeObstacleAvoidance(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidSetupRuntimeObstacleAvoidanceActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidSetupRuntimeObstacleAvoidanceActions);
                return Response.Error($"Unknown action: '{action}'. Valid actions are: {validActionsList}");
            }

            try
            {
                switch (action)
                {
                    case "setup":
                        return SetupObstacleAvoidance(@params);
                    case "modify":
                        return ModifyObstacleAvoidance(@params);
                    case "remove":
                        return RemoveObstacleAvoidance(@params);
                    case "get_info":
                        return GetObstacleAvoidanceInfo(@params);
                    case "list":
                        return ListObstacleAvoidanceAgents();
                    default:
                        return Response.Error($"Unknown action: '{action}'");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[NavigationEnhanced] SetupRuntimeObstacleAvoidance action '{action}' failed: {e}");
                return Response.Error($"Internal error processing action '{action}': {e.Message}");
            }
        }

        public static object HandleCreateDynamicPathModifiers(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidCreateDynamicPathModifiersActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidCreateDynamicPathModifiersActions);
                return Response.Error($"Unknown action: '{action}'. Valid actions are: {validActionsList}");
            }

            try
            {
                switch (action)
                {
                    case "create":
                        return CreatePathModifier(@params);
                    case "modify":
                        return ModifyPathModifier(@params);
                    case "delete":
                        return DeletePathModifier(@params);
                    case "enable":
                        return EnablePathModifier(@params);
                    case "disable":
                        return DisablePathModifier(@params);
                    case "list":
                        return ListPathModifiers();
                    default:
                        return Response.Error($"Unknown action: '{action}'");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[NavigationEnhanced] CreateDynamicPathModifiers action '{action}' failed: {e}");
                return Response.Error($"Internal error processing action '{action}': {e.Message}");
            }
        }

        public static object HandleSetupNavmeshLinks(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidSetupNavmeshLinksActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidSetupNavmeshLinksActions);
                return Response.Error($"Unknown action: '{action}'. Valid actions are: {validActionsList}");
            }

            try
            {
                switch (action)
                {
                    case "create":
                        return CreateNavMeshLink(@params);
                    case "modify":
                        return ModifyNavMeshLink(@params);
                    case "delete":
                        return DeleteNavMeshLink(@params);
                    case "enable":
                        return EnableNavMeshLink(@params);
                    case "disable":
                        return DisableNavMeshLink(@params);
                    case "list":
                        return ListNavMeshLinks();
                    default:
                        return Response.Error($"Unknown action: '{action}'");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[NavigationEnhanced] SetupNavmeshLinks action '{action}' failed: {e}");
                return Response.Error($"Internal error processing action '{action}': {e.Message}");
            }
        }

        public static object HandleConfigureAreaMasks(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidConfigureAreaMasksActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidConfigureAreaMasksActions);
                return Response.Error($"Unknown action: '{action}'. Valid actions are: {validActionsList}");
            }

            try
            {
                switch (action)
                {
                    case "set":
                        return SetAreaMask(@params);
                    case "get":
                        return GetAreaMask(@params);
                    case "create_mask":
                        return CreateAreaMask(@params);
                    case "apply_mask":
                        return ApplyAreaMask(@params);
                    case "list_masks":
                        return ListAreaMasks();
                    default:
                        return Response.Error($"Unknown action: '{action}'");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[NavigationEnhanced] ConfigureAreaMasks action '{action}' failed: {e}");
                return Response.Error($"Internal error processing action '{action}': {e.Message}");
            }
        }

        #region NavMesh Areas Implementation

        private static object CreateNavMeshArea(JObject @params)
        {
            string areaName = @params["area_name"]?.ToString();
            if (string.IsNullOrEmpty(areaName))
            {
                return Response.Error("Area name is required.");
            }

            float areaCost = @params["area_cost"]?.ToObject<float>() ?? 1.0f;
            int areaId = @params["area_id"]?.ToObject<int>() ?? -1;

            // Get current NavMesh areas
            string[] areaNames = NavMesh.GetAreaNames();
            
            // Check if area already exists
            if (areaNames.Contains(areaName))
            {
                return Response.Error($"NavMesh area '{areaName}' already exists.");
            }

            // Find available area ID if not specified
            if (areaId == -1)
            {
                for (int i = 3; i < 32; i++) // Areas 0-2 are reserved
                {
                    if (string.IsNullOrEmpty(areaNames[i]))
                    {
                        areaId = i;
                        break;
                    }
                }
            }

            if (areaId == -1 || areaId < 3 || areaId > 31)
            {
                return Response.Error("No available area ID found or invalid area ID specified (must be 3-31).");
            }

            try
            {
                // In Unity 6.2, we need to use NavMeshBuildSettings and NavMeshBuilder approach
                // Since GameObjectUtility.SetNavMeshAreaNames is deprecated
                
                // Set the area cost - this is the main way to configure areas in Unity 6.2
                NavMesh.SetAreaCost(areaId, areaCost);
                
                // Store area name mapping in EditorPrefs for later retrieval
                // This is the recommended approach since GameObjectUtility methods are deprecated
                EditorPrefs.SetString($"NavMeshAreaName_{areaId}", areaName);
                
                // Mark as dirty to ensure changes are saved
                EditorUtility.SetDirty(Selection.activeGameObject);
                
                return Response.Success($"NavMesh area '{areaName}' created successfully with ID {areaId} and cost {areaCost}.", new
                {
                    areaName = areaName,
                    areaId = areaId,
                    areaCost = areaCost,
                    message = "Area created using Unity 6.2 compatible API"
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create NavMesh area: {e.Message}");
            }
        }

        private static object ModifyNavMeshArea(JObject @params)
        {
            string areaName = @params["area_name"]?.ToString();
            if (string.IsNullOrEmpty(areaName))
            {
                return Response.Error("Area name is required.");
            }

            // Since GameObjectUtility.GetAreaFromName is deprecated, we need to find the area ID manually
            int areaId = -1;
            
            // First check standard areas
            string[] standardAreas = { "Walkable", "Not Walkable", "Jump" };
            for (int i = 0; i < standardAreas.Length; i++)
            {
                if (standardAreas[i] == areaName)
                {
                    areaId = i;
                    break;
                }
            }
            
            // If not found in standard areas, check our custom stored areas
            if (areaId == -1)
            {
                for (int i = 3; i < 32; i++)
                {
                    string storedName = EditorPrefs.GetString($"NavMeshAreaName_{i}", "");
                    if (storedName == areaName)
                    {
                        areaId = i;
                        break;
                    }
                }
            }
            
            if (areaId == -1)
            {
                return Response.Error($"NavMesh area '{areaName}' not found.");
            }

            try
            {
                bool modified = false;
                
                if (@params["area_cost"] != null)
                {
                    float newCost = @params["area_cost"].ToObject<float>();
                    NavMesh.SetAreaCost(areaId, newCost);
                    modified = true;
                }
                
                if (@params["new_area_name"] != null)
                {
                    string newAreaName = @params["new_area_name"].ToString();
                    if (areaId >= 3) // Only custom areas can be renamed
                    {
                        EditorPrefs.SetString($"NavMeshAreaName_{areaId}", newAreaName);
                        modified = true;
                    }
                    else
                    {
                        return Response.Error("Cannot rename built-in NavMesh areas (Walkable, Not Walkable, Jump).");
                    }
                }

                if (!modified)
                {
                    return Response.Error("No modifications specified. Use 'area_cost' or 'new_area_name' parameters.");
                }

                return Response.Success($"NavMesh area '{areaName}' modified successfully.", new
                {
                    areaId = areaId,
                    currentCost = NavMesh.GetAreaCost(areaId)
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to modify NavMesh area: {e.Message}");
            }
        }

        private static object DeleteNavMeshArea(JObject @params)
        {
            string areaName = @params["area_name"]?.ToString();
            if (string.IsNullOrEmpty(areaName))
            {
                return Response.Error("Area name is required.");
            }

            // Find area ID using our custom lookup method
            int areaId = -1;
            
            // First check standard areas
            string[] standardAreas = { "Walkable", "Not Walkable", "Jump" };
            for (int i = 0; i < standardAreas.Length; i++)
            {
                if (standardAreas[i] == areaName)
                {
                    areaId = i;
                    break;
                }
            }
            
            // If not found in standard areas, check our custom stored areas
            if (areaId == -1)
            {
                for (int i = 3; i < 32; i++)
                {
                    string storedName = EditorPrefs.GetString($"NavMeshAreaName_{i}", "");
                    if (storedName == areaName)
                    {
                        areaId = i;
                        break;
                    }
                }
            }
            
            if (areaId == -1)
            {
                return Response.Error($"NavMesh area '{areaName}' not found.");
            }

            if (areaId < 3)
            {
                return Response.Error("Cannot delete built-in NavMesh areas (Walkable, Not Walkable, Jump).");
            }

            try
            {
                // Reset area cost to default
                NavMesh.SetAreaCost(areaId, 1.0f);
                
                // Remove the stored area name mapping
                EditorPrefs.DeleteKey($"NavMeshAreaName_{areaId}");
                
                // Check if any GameObjects are using this area and warn
                var buildSources = new List<NavMeshBuildSource>();
                var markups = new List<NavMeshBuildMarkup>();
                
                NavMeshBuilder.CollectSources(
                    null, // Include all root transforms
                    -1,   // Include all layers
                    NavMeshCollectGeometry.RenderMeshes | NavMeshCollectGeometry.PhysicsColliders,
                    0,    // Default area
                    markups,
                    buildSources
                );
                
                var objectsUsingArea = buildSources.Count(source => source.area == areaId);
                
                string warningMessage = objectsUsingArea > 0 
                    ? $"Warning: {objectsUsingArea} objects were using this area and will revert to default area."
                    : "";
                
                return Response.Success($"NavMesh area '{areaName}' deleted successfully. {warningMessage}", new
                {
                    deletedAreaId = areaId,
                    objectsAffected = objectsUsingArea
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to delete NavMesh area: {e.Message}");
            }
        }

        private static object ListNavMeshAreas()
        {
            try
            {
                var areas = new List<object>();
                
                // Add built-in areas
                string[] builtInAreas = { "Walkable", "Not Walkable", "Jump" };
                for (int i = 0; i < builtInAreas.Length; i++)
                {
                    areas.Add(new
                    {
                        id = i,
                        name = builtInAreas[i],
                        cost = NavMesh.GetAreaCost(i),
                        isBuiltIn = true,
                        mask = 1 << i,
                        description = GetAreaDescription(i)
                    });
                }
                
                // Add custom areas (stored in EditorPrefs)
                for (int i = 3; i < 32; i++)
                {
                    string customAreaName = EditorPrefs.GetString($"NavMeshAreaName_{i}", "");
                    if (!string.IsNullOrEmpty(customAreaName))
                    {
                        areas.Add(new
                        {
                            id = i,
                            name = customAreaName,
                            cost = NavMesh.GetAreaCost(i),
                            isBuiltIn = false,
                            mask = 1 << i,
                            description = "Custom area"
                        });
                    }
                }

                return Response.Success("NavMesh areas listed successfully.", new
                {
                    areas = areas,
                    totalAreas = areas.Count,
                    builtInAreas = 3,
                    customAreas = areas.Count - 3
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to list NavMesh areas: {e.Message}");
            }
        }
        
        private static string GetAreaDescription(int areaId)
        {
            return areaId switch
            {
                0 => "Default walkable area for navigation",
                1 => "Area that blocks navigation",
                2 => "Area that allows jumping actions",
                _ => "Custom navigation area"
            };
        }

        private static object GetNavMeshAreaInfo(JObject @params)
        {
            string areaName = @params["area_name"]?.ToString();
            if (string.IsNullOrEmpty(areaName))
            {
                return Response.Error("Area name is required.");
            }

            // Find area ID using our custom lookup method
            int areaId = -1;
            bool isBuiltIn = false;
            
            // First check standard areas
            string[] standardAreas = { "Walkable", "Not Walkable", "Jump" };
            for (int i = 0; i < standardAreas.Length; i++)
            {
                if (standardAreas[i] == areaName)
                {
                    areaId = i;
                    isBuiltIn = true;
                    break;
                }
            }
            
            // If not found in standard areas, check our custom stored areas
            if (areaId == -1)
            {
                for (int i = 3; i < 32; i++)
                {
                    string storedName = EditorPrefs.GetString($"NavMeshAreaName_{i}", "");
                    if (storedName == areaName)
                    {
                        areaId = i;
                        break;
                    }
                }
            }
            
            if (areaId == -1)
            {
                return Response.Error($"NavMesh area '{areaName}' not found.");
            }

            try
            {
                // Count objects using this area
                var buildSources = new List<NavMeshBuildSource>();
                var markups = new List<NavMeshBuildMarkup>();
                
                NavMeshBuilder.CollectSources(
                    null, // Include all root transforms
                    -1,   // Include all layers
                    NavMeshCollectGeometry.RenderMeshes | NavMeshCollectGeometry.PhysicsColliders,
                    0,    // Default area
                    markups,
                    buildSources
                );
                
                var objectsUsingArea = buildSources.Count(source => source.area == areaId);
                
                var areaInfo = new
                {
                    id = areaId,
                    name = areaName,
                    cost = NavMesh.GetAreaCost(areaId),
                    isBuiltIn = isBuiltIn,
                    mask = 1 << areaId,
                    description = GetAreaDescription(areaId),
                    objectsUsingArea = objectsUsingArea,
                    canBeDeleted = !isBuiltIn,
                    canBeRenamed = !isBuiltIn,
                    validMaskRange = "Use in areaMask calculations (bitwise operations)",
                    usageExample = $"agent.areaMask = {1 << areaId}; // Only allow this area"
                };

                return Response.Success($"NavMesh area '{areaName}' info retrieved successfully.", areaInfo);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get NavMesh area info: {e.Message}");
            }
        }

        #endregion

        #region NavMesh Costs Implementation

        private static object SetNavMeshCosts(JObject @params)
        {
            try
            {
                JObject areaCosts = @params["area_costs"] as JObject;
                float globalMultiplier = @params["global_cost_multiplier"]?.ToObject<float>() ?? 1.0f;
                bool applyToAll = @params["apply_to_all_agents"]?.ToObject<bool>() ?? true;
                
                var processedAreas = new List<object>();
                var errors = new List<string>();

                if (areaCosts != null)
                {
                    foreach (var kvp in areaCosts)
                    {
                        string areaName = kvp.Key;
                        float baseCost = kvp.Value.ToObject<float>();
                        float finalCost = baseCost * globalMultiplier;
                        
                        // Find area ID
                        int areaId = FindAreaIdByName(areaName);
                        
                        if (areaId != -1)
                        {
                            // In Unity 6.2, NavMesh.SetAreaCost applies to all agent types
                            // This is the correct and current way to set area costs
                            NavMesh.SetAreaCost(areaId, finalCost);
                            
                            processedAreas.Add(new
                            {
                                areaName = areaName,
                                areaId = areaId,
                                baseCost = baseCost,
                                finalCost = finalCost,
                                multiplier = globalMultiplier
                            });
                        }
                        else
                        {
                            errors.Add($"Area '{areaName}' not found");
                        }
                    }
                }
                
                // Apply global multiplier to all areas if specified
                if (@params["apply_to_all_areas"]?.ToObject<bool>() == true)
                {
                    // Apply to built-in areas
                    for (int i = 0; i < 3; i++)
                    {
                        float currentCost = NavMesh.GetAreaCost(i);
                        NavMesh.SetAreaCost(i, currentCost * globalMultiplier);
                    }
                    
                    // Apply to custom areas
                    for (int i = 3; i < 32; i++)
                    {
                        string areaName = EditorPrefs.GetString($"NavMeshAreaName_{i}", "");
                        if (!string.IsNullOrEmpty(areaName))
                        {
                            float currentCost = NavMesh.GetAreaCost(i);
                            NavMesh.SetAreaCost(i, currentCost * globalMultiplier);
                        }
                    }
                }

                var result = new
                {
                    processedAreas = processedAreas,
                    errors = errors,
                    globalMultiplier = globalMultiplier,
                    totalProcessed = processedAreas.Count,
                    totalErrors = errors.Count
                };

                return errors.Count == 0 
                    ? Response.Success("NavMesh costs set successfully.", result)
                    : Response.Success($"NavMesh costs set with {errors.Count} errors.", result);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to set NavMesh costs: {e.Message}");
            }
        }
        
        private static int FindAreaIdByName(string areaName)
        {
            // Check built-in areas first
            string[] builtInAreas = { "Walkable", "Not Walkable", "Jump" };
            for (int i = 0; i < builtInAreas.Length; i++)
            {
                if (builtInAreas[i] == areaName)
                {
                    return i;
                }
            }
            
            // Check custom areas
            for (int i = 3; i < 32; i++)
            {
                string storedName = EditorPrefs.GetString($"NavMeshAreaName_{i}", "");
                if (storedName == areaName)
                {
                    return i;
                }
            }
            
            return -1; // Not found
        }

        private static object GetNavMeshCosts(JObject @params)
        {
            try
            {
                var costs = new Dictionary<string, object>();
                var allAreaInfo = new List<object>();
                
                // Get built-in area costs
                string[] builtInAreas = { "Walkable", "Not Walkable", "Jump" };
                for (int i = 0; i < builtInAreas.Length; i++)
                {
                    float cost = NavMesh.GetAreaCost(i);
                    costs[builtInAreas[i]] = cost;
                    
                    allAreaInfo.Add(new
                    {
                        id = i,
                        name = builtInAreas[i],
                        cost = cost,
                        isBuiltIn = true,
                        mask = 1 << i
                    });
                }
                
                // Get custom area costs
                for (int i = 3; i < 32; i++)
                {
                    string areaName = EditorPrefs.GetString($"NavMeshAreaName_{i}", "");
                    if (!string.IsNullOrEmpty(areaName))
                    {
                        float cost = NavMesh.GetAreaCost(i);
                        costs[areaName] = cost;
                        
                        allAreaInfo.Add(new
                        {
                            id = i,
                            name = areaName,
                            cost = cost,
                            isBuiltIn = false,
                            mask = 1 << i
                        });
                    }
                }
                
                // Include agent type information if requested
                bool includeAgentInfo = @params["include_agent_info"]?.ToObject<bool>() ?? false;
                var agentTypes = new List<object>();
                
                if (includeAgentInfo)
                {
                    for (int i = 0; i < NavMesh.GetSettingsCount(); i++)
                    {
                        NavMeshBuildSettings settings = NavMesh.GetSettingsByIndex(i);
                        string agentName = NavMesh.GetSettingsNameFromID(settings.agentTypeID);
                        
                        agentTypes.Add(new
                        {
                            agentTypeID = settings.agentTypeID,
                            agentName = !string.IsNullOrEmpty(agentName) ? agentName : $"Agent Type {settings.agentTypeID}",
                            agentRadius = settings.agentRadius,
                            agentHeight = settings.agentHeight,
                            agentSlope = settings.agentSlope,
                            agentClimb = settings.agentClimb
                        });
                    }
                }

                var result = new
                {
                    costs = costs,
                    detailedAreaInfo = allAreaInfo,
                    totalAreas = costs.Count,
                    builtInAreas = 3,
                    customAreas = costs.Count - 3,
                    agentTypes = includeAgentInfo ? agentTypes : null,
                    note = "In Unity 6.2, area costs apply to all agent types globally"
                };

                return Response.Success("NavMesh costs retrieved successfully.", result);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get NavMesh costs: {e.Message}");
            }
        }

        private static object ResetNavMeshCosts(JObject @params)
        {
            try
            {
                bool resetCustomOnly = @params["reset_custom_only"]?.ToObject<bool>() ?? false;
                bool resetBuiltInOnly = @params["reset_builtin_only"]?.ToObject<bool>() ?? false;
                var resetAreas = new List<object>();
                
                if (!resetCustomOnly)
                {
                    // Reset built-in areas to default values
                    var builtInDefaults = new Dictionary<int, float>
                    {
                        { 0, 1.0f }, // Walkable
                        { 1, 1.0f }, // Not Walkable (though agents can't traverse it anyway)
                        { 2, 1.0f }  // Jump
                    };
                    
                    foreach (var kvp in builtInDefaults)
                    {
                        NavMesh.SetAreaCost(kvp.Key, kvp.Value);
                        resetAreas.Add(new
                        {
                            areaId = kvp.Key,
                            areaName = GetBuiltInAreaName(kvp.Key),
                            newCost = kvp.Value,
                            isBuiltIn = true
                        });
                    }
                }
                
                if (!resetBuiltInOnly)
                {
                    // Reset custom areas to default value (1.0)
                    for (int i = 3; i < 32; i++)
                    {
                        string areaName = EditorPrefs.GetString($"NavMeshAreaName_{i}", "");
                        if (!string.IsNullOrEmpty(areaName))
                        {
                            NavMesh.SetAreaCost(i, 1.0f);
                            resetAreas.Add(new
                            {
                                areaId = i,
                                areaName = areaName,
                                newCost = 1.0f,
                                isBuiltIn = false
                            });
                        }
                    }
                }
                
                // Apply specific area resets if provided
                if (@params["specific_areas"] != null)
                {
                    var specificAreas = @params["specific_areas"].ToObject<string[]>();
                    foreach (string areaName in specificAreas)
                    {
                        int areaId = FindAreaIdByName(areaName);
                        if (areaId != -1)
                        {
                            NavMesh.SetAreaCost(areaId, 1.0f);
                            resetAreas.Add(new
                            {
                                areaId = areaId,
                                areaName = areaName,
                                newCost = 1.0f,
                                isBuiltIn = areaId < 3
                            });
                        }
                    }
                }

                return Response.Success("NavMesh costs reset to default values successfully.", new
                {
                    resetAreas = resetAreas,
                    totalReset = resetAreas.Count,
                    defaultCost = 1.0f,
                    note = "All areas now have a pathfinding cost of 1.0 (default)"
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to reset NavMesh costs: {e.Message}");
            }
        }
        
        private static string GetBuiltInAreaName(int areaId)
        {
            return areaId switch
            {
                0 => "Walkable",
                1 => "Not Walkable",
                2 => "Jump",
                _ => "Unknown"
            };
        }

        private static object CreateCostProfile(JObject @params)
        {
            string profileName = @params["cost_profile_name"]?.ToString();
            if (string.IsNullOrEmpty(profileName))
            {
                return Response.Error("Cost profile name is required.");
            }

            try
            {
                var profile = new Dictionary<string, float>();
                var profileInfo = new List<object>();
                
                // Save built-in area costs
                string[] builtInAreas = { "Walkable", "Not Walkable", "Jump" };
                for (int i = 0; i < builtInAreas.Length; i++)
                {
                    float cost = NavMesh.GetAreaCost(i);
                    profile[builtInAreas[i]] = cost;
                    profileInfo.Add(new
                    {
                        areaId = i,
                        areaName = builtInAreas[i],
                        cost = cost,
                        isBuiltIn = true
                    });
                }
                
                // Save custom area costs
                for (int i = 3; i < 32; i++)
                {
                    string areaName = EditorPrefs.GetString($"NavMeshAreaName_{i}", "");
                    if (!string.IsNullOrEmpty(areaName))
                    {
                        float cost = NavMesh.GetAreaCost(i);
                        profile[areaName] = cost;
                        profileInfo.Add(new
                        {
                            areaId = i,
                            areaName = areaName,
                            cost = cost,
                            isBuiltIn = false
                        });
                    }
                }

                // Create a more robust serialization format
                var profileData = new
                {
                    profileName = profileName,
                    createdAt = System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    unityVersion = UnityEngine.Application.unityVersion,
                    costs = profile,
                    areaDetails = profileInfo,
                    totalAreas = profile.Count
                };
                
                string serializedData = JsonUtility.ToJson(new SerializableProfile
                {
                    profileName = profileData.profileName,
                    createdAt = profileData.createdAt,
                    unityVersion = profileData.unityVersion,
                    costs = profile.Select(kvp => new SerializableCost { areaName = kvp.Key, cost = kvp.Value }).ToArray(),
                    totalAreas = profileData.totalAreas
                });
                
                EditorPrefs.SetString($"NavMeshCostProfile_{profileName}", serializedData);

                // Also save a list of all profiles for easy retrieval
                string existingProfiles = EditorPrefs.GetString("NavMeshCostProfiles_List", "");
                var profileList = string.IsNullOrEmpty(existingProfiles) 
                    ? new List<string>() 
                    : existingProfiles.Split(',').ToList();
                
                if (!profileList.Contains(profileName))
                {
                    profileList.Add(profileName);
                    EditorPrefs.SetString("NavMeshCostProfiles_List", string.Join(",", profileList));
                }

                return Response.Success($"Cost profile '{profileName}' created successfully.", new
                {
                    profileName = profileName,
                    totalAreas = profile.Count,
                    areas = profileInfo,
                    createdAt = profileData.createdAt,
                    storageKey = $"NavMeshCostProfile_{profileName}"
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create cost profile: {e.Message}");
            }
        }

        private static object ApplyCostProfile(JObject @params)
        {
            string profileName = @params["cost_profile_name"]?.ToString();
            if (string.IsNullOrEmpty(profileName))
            {
                return Response.Error("Cost profile name is required.");
            }

            try
            {
                string profileData = EditorPrefs.GetString($"NavMeshCostProfile_{profileName}", "");
                if (string.IsNullOrEmpty(profileData))
                {
                    return Response.Error($"Cost profile '{profileName}' not found.");
                }

                SerializableProfile profile = JsonUtility.FromJson<SerializableProfile>(profileData);
                var appliedAreas = new List<object>();
                var errors = new List<string>();
                
                bool allowPartialApplication = @params["allow_partial_application"]?.ToObject<bool>() ?? true;
                
                foreach (var costEntry in profile.costs)
                {
                    int areaId = FindAreaIdByName(costEntry.areaName);
                    
                    if (areaId != -1)
                    {
                        float previousCost = NavMesh.GetAreaCost(areaId);
                        NavMesh.SetAreaCost(areaId, costEntry.cost);
                        
                        appliedAreas.Add(new
                        {
                            areaId = areaId,
                            areaName = costEntry.areaName,
                            previousCost = previousCost,
                            newCost = costEntry.cost,
                            isBuiltIn = areaId < 3
                        });
                    }
                    else
                    {
                        string errorMsg = $"Area '{costEntry.areaName}' from profile not found in current scene";
                        errors.Add(errorMsg);
                        
                        if (!allowPartialApplication)
                        {
                            return Response.Error($"Profile application failed: {errorMsg}. Use 'allow_partial_application': true to apply available areas only.");
                        }
                    }
                }
                
                var result = new
                {
                    profileName = profileName,
                    profileCreatedAt = profile.createdAt,
                    profileUnityVersion = profile.unityVersion,
                    appliedAreas = appliedAreas,
                    errors = errors,
                    totalApplied = appliedAreas.Count,
                    totalErrors = errors.Count,
                    profileTotalAreas = profile.totalAreas,
                    partialApplication = errors.Count > 0
                };

                if (errors.Count == 0)
                {
                    return Response.Success($"Cost profile '{profileName}' applied successfully.", result);
                }
                else if (appliedAreas.Count > 0)
                {
                    return Response.Success($"Cost profile '{profileName}' partially applied with {errors.Count} warnings.", result);
                }
                else
                {
                    return Response.Error($"Could not apply any areas from profile '{profileName}'.", result);
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to apply cost profile: {e.Message}");
            }
        }

        #endregion

        #region Obstacle Avoidance Implementation

        private static object SetupObstacleAvoidance(JObject @params)
        {
            string gameObjectName = @params["gameobject_name"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
            {
                return Response.Error("GameObject name is required.");
            }

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
            {
                return Response.Error($"GameObject '{gameObjectName}' not found.");
            }

            try
            {
                NavMeshAgent agent = go.GetComponent<NavMeshAgent>();
                if (agent == null)
                {
                    agent = go.AddComponent<NavMeshAgent>();
                }

                // Configure basic agent parameters
                if (@params["agent_radius"] != null)
                    agent.radius = @params["agent_radius"].ToObject<float>();
                
                if (@params["agent_height"] != null)
                    agent.height = @params["agent_height"].ToObject<float>();
                
                if (@params["agent_speed"] != null)
                    agent.speed = @params["agent_speed"].ToObject<float>();
                
                if (@params["agent_acceleration"] != null)
                    agent.acceleration = @params["agent_acceleration"].ToObject<float>();
                
                if (@params["agent_angular_speed"] != null)
                    agent.angularSpeed = @params["agent_angular_speed"].ToObject<float>();
                
                if (@params["stopping_distance"] != null)
                    agent.stoppingDistance = @params["stopping_distance"].ToObject<float>();
                
                // Configure obstacle avoidance specific parameters
                if (@params["avoidance_priority"] != null)
                {
                    int priority = @params["avoidance_priority"].ToObject<int>();
                    agent.avoidancePriority = Mathf.Clamp(priority, 0, 99);
                }
                
                // Set obstacle avoidance quality
                string avoidanceQuality = @params["avoidance_quality"]?.ToString()?.ToLower() ?? "high";
                agent.obstacleAvoidanceType = avoidanceQuality switch
                {
                    "none" => ObstacleAvoidanceType.NoObstacleAvoidance,
                    "low" => ObstacleAvoidanceType.LowQualityObstacleAvoidance,
                    "medium" => ObstacleAvoidanceType.MedQualityObstacleAvoidance,
                    "good" => ObstacleAvoidanceType.GoodQualityObstacleAvoidance,
                    "high" => ObstacleAvoidanceType.HighQualityObstacleAvoidance,
                    _ => ObstacleAvoidanceType.HighQualityObstacleAvoidance
                };
                
                // Configure area mask if provided
                if (@params["area_mask"] != null)
                {
                    agent.areaMask = @params["area_mask"].ToObject<int>();
                }
                else if (@params["allowed_areas"] != null)
                {
                    var allowedAreas = @params["allowed_areas"].ToObject<string[]>();
                    int areaMask = 0;
                    foreach (string areaName in allowedAreas)
                    {
                        int areaId = FindAreaIdByName(areaName);
                        if (areaId != -1)
                        {
                            areaMask |= (1 << areaId);
                        }
                    }
                    agent.areaMask = areaMask;
                }

                // Configure agent type if specified
                if (@params["agent_type_id"] != null)
                {
                    agent.agentTypeID = @params["agent_type_id"].ToObject<int>();
                }

                // Auto-path generation settings
                if (@params["auto_braking"] != null)
                    agent.autoBraking = @params["auto_braking"].ToObject<bool>();
                
                if (@params["auto_repath"] != null)
                    agent.autoRepath = @params["auto_repath"].ToObject<bool>();

                EditorUtility.SetDirty(go);

                return Response.Success($"Obstacle avoidance setup completed for '{gameObjectName}'.", new
                {
                    gameObjectName = gameObjectName,
                    agentConfiguration = new
                    {
                        avoidancePriority = agent.avoidancePriority,
                        radius = agent.radius,
                        height = agent.height,
                        speed = agent.speed,
                        acceleration = agent.acceleration,
                        angularSpeed = agent.angularSpeed,
                        stoppingDistance = agent.stoppingDistance,
                        obstacleAvoidanceType = agent.obstacleAvoidanceType.ToString(),
                        areaMask = agent.areaMask,
                        agentTypeID = agent.agentTypeID,
                        autoBraking = agent.autoBraking,
                        autoRepath = agent.autoRepath
                    },
                    tips = new
                    {
                        avoidancePriority = "Lower values = higher priority (0 = highest, 99 = lowest)",
                        obstacleAvoidanceType = "Higher quality = better avoidance but more performance cost",
                        areaMask = "Bitwise mask of areas this agent can traverse"
                    }
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to setup obstacle avoidance: {e.Message}");
            }
        }

        private static object ModifyObstacleAvoidance(JObject @params)
        {
            string gameObjectName = @params["gameobject_name"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
            {
                return Response.Error("GameObject name is required.");
            }

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
            {
                return Response.Error($"GameObject '{gameObjectName}' not found.");
            }

            NavMeshAgent agent = go.GetComponent<NavMeshAgent>();
            if (agent == null)
            {
                return Response.Error($"NavMeshAgent component not found on '{gameObjectName}'.");
            }

            try
            {
                var previousSettings = new
                {
                    avoidancePriority = agent.avoidancePriority,
                    radius = agent.radius,
                    height = agent.height,
                    speed = agent.speed,
                    acceleration = agent.acceleration,
                    angularSpeed = agent.angularSpeed,
                    stoppingDistance = agent.stoppingDistance,
                    obstacleAvoidanceType = agent.obstacleAvoidanceType.ToString(),
                    areaMask = agent.areaMask,
                    agentTypeID = agent.agentTypeID
                };

                var modifiedProperties = new List<string>();

                // Modify basic agent parameters
                if (@params["agent_radius"] != null)
                {
                    agent.radius = @params["agent_radius"].ToObject<float>();
                    modifiedProperties.Add("radius");
                }
                
                if (@params["agent_height"] != null)
                {
                    agent.height = @params["agent_height"].ToObject<float>();
                    modifiedProperties.Add("height");
                }
                
                if (@params["agent_speed"] != null)
                {
                    agent.speed = @params["agent_speed"].ToObject<float>();
                    modifiedProperties.Add("speed");
                }
                
                if (@params["agent_acceleration"] != null)
                {
                    agent.acceleration = @params["agent_acceleration"].ToObject<float>();
                    modifiedProperties.Add("acceleration");
                }
                
                if (@params["agent_angular_speed"] != null)
                {
                    agent.angularSpeed = @params["agent_angular_speed"].ToObject<float>();
                    modifiedProperties.Add("angularSpeed");
                }
                
                if (@params["stopping_distance"] != null)
                {
                    agent.stoppingDistance = @params["stopping_distance"].ToObject<float>();
                    modifiedProperties.Add("stoppingDistance");
                }

                // Modify obstacle avoidance specific parameters
                if (@params["avoidance_priority"] != null)
                {
                    int priority = @params["avoidance_priority"].ToObject<int>();
                    agent.avoidancePriority = Mathf.Clamp(priority, 0, 99);
                    modifiedProperties.Add("avoidancePriority");
                }
                
                if (@params["avoidance_quality"] != null)
                {
                    string avoidanceQuality = @params["avoidance_quality"].ToString().ToLower();
                    agent.obstacleAvoidanceType = avoidanceQuality switch
                    {
                        "none" => ObstacleAvoidanceType.NoObstacleAvoidance,
                        "low" => ObstacleAvoidanceType.LowQualityObstacleAvoidance,
                        "medium" => ObstacleAvoidanceType.MedQualityObstacleAvoidance,
                        "good" => ObstacleAvoidanceType.GoodQualityObstacleAvoidance,
                        "high" => ObstacleAvoidanceType.HighQualityObstacleAvoidance,
                        _ => agent.obstacleAvoidanceType
                    };
                    modifiedProperties.Add("obstacleAvoidanceType");
                }
                
                // Modify area mask
                if (@params["area_mask"] != null)
                {
                    agent.areaMask = @params["area_mask"].ToObject<int>();
                    modifiedProperties.Add("areaMask");
                }
                else if (@params["allowed_areas"] != null)
                {
                    var allowedAreas = @params["allowed_areas"].ToObject<string[]>();
                    int areaMask = 0;
                    foreach (string areaName in allowedAreas)
                    {
                        int areaId = FindAreaIdByName(areaName);
                        if (areaId != -1)
                        {
                            areaMask |= (1 << areaId);
                        }
                    }
                    agent.areaMask = areaMask;
                    modifiedProperties.Add("areaMask");
                }

                // Modify agent type
                if (@params["agent_type_id"] != null)
                {
                    agent.agentTypeID = @params["agent_type_id"].ToObject<int>();
                    modifiedProperties.Add("agentTypeID");
                }

                // Modify auto settings
                if (@params["auto_braking"] != null)
                {
                    agent.autoBraking = @params["auto_braking"].ToObject<bool>();
                    modifiedProperties.Add("autoBraking");
                }
                
                if (@params["auto_repath"] != null)
                {
                    agent.autoRepath = @params["auto_repath"].ToObject<bool>();
                    modifiedProperties.Add("autoRepath");
                }

                EditorUtility.SetDirty(go);

                return Response.Success($"Obstacle avoidance modified for '{gameObjectName}'.", new
                {
                    gameObjectName = gameObjectName,
                    modifiedProperties = modifiedProperties,
                    previousSettings = previousSettings,
                    currentSettings = new
                    {
                        avoidancePriority = agent.avoidancePriority,
                        radius = agent.radius,
                        height = agent.height,
                        speed = agent.speed,
                        acceleration = agent.acceleration,
                        angularSpeed = agent.angularSpeed,
                        stoppingDistance = agent.stoppingDistance,
                        obstacleAvoidanceType = agent.obstacleAvoidanceType.ToString(),
                        areaMask = agent.areaMask,
                        agentTypeID = agent.agentTypeID,
                        autoBraking = agent.autoBraking,
                        autoRepath = agent.autoRepath
                    }
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to modify obstacle avoidance: {e.Message}");
            }
        }

        private static object RemoveObstacleAvoidance(JObject @params)
        {
            string gameObjectName = @params["gameobject_name"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
            {
                return Response.Error("GameObject name is required.");
            }

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
            {
                return Response.Error($"GameObject '{gameObjectName}' not found.");
            }

            NavMeshAgent agent = go.GetComponent<NavMeshAgent>();
            if (agent == null)
            {
                return Response.Error($"NavMeshAgent component not found on '{gameObjectName}'.");
            }

            try
            {
                UnityEngine.Object.DestroyImmediate(agent);
                EditorUtility.SetDirty(go);

                return Response.Success($"Obstacle avoidance removed from '{gameObjectName}'.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to remove obstacle avoidance: {e.Message}");
            }
        }

        private static object GetObstacleAvoidanceInfo(JObject @params)
        {
            string gameObjectName = @params["gameobject_name"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
            {
                return Response.Error("GameObject name is required.");
            }

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
            {
                return Response.Error($"GameObject '{gameObjectName}' not found.");
            }

            NavMeshAgent agent = go.GetComponent<NavMeshAgent>();
            if (agent == null)
            {
                return Response.Error($"NavMeshAgent component not found on '{gameObjectName}'.");
            }

            try
            {
                // Get current path information if available
                var pathInfo = agent.hasPath ? new
                {
                    hasPath = true,
                    pathStatus = agent.pathStatus.ToString(),
                    pathPending = agent.pathPending,
                    remainingDistance = agent.remainingDistance,
                    pathLength = agent.path.corners.Length,
                    destination = new { x = agent.destination.x, y = agent.destination.y, z = agent.destination.z }
                } : new
                {
                    hasPath = false,
                    pathStatus = agent.pathStatus.ToString(),
                    pathPending = false,
                    remainingDistance = 0f,
                    pathLength = 0,
                    destination = new { x = 0f, y = 0f, z = 0f }
                };

                // Get allowed areas list
                var allowedAreas = new List<string>();
                for (int i = 0; i < 32; i++)
                {
                    if ((agent.areaMask & (1 << i)) != 0)
                    {
                        string areaName = i < 3 ? GetBuiltInAreaName(i) : EditorPrefs.GetString($"NavMeshAreaName_{i}", $"Area_{i}");
                        if (!string.IsNullOrEmpty(areaName))
                        {
                            allowedAreas.Add(areaName);
                        }
                    }
                }

                var info = new
                {
                    gameObjectName = gameObjectName,
                    transform = new
                    {
                        position = new { x = go.transform.position.x, y = go.transform.position.y, z = go.transform.position.z },
                        rotation = new { x = go.transform.rotation.x, y = go.transform.rotation.y, z = go.transform.rotation.z, w = go.transform.rotation.w }
                    },
                    agentConfiguration = new
                    {
                        avoidancePriority = agent.avoidancePriority,
                        radius = agent.radius,
                        height = agent.height,
                        speed = agent.speed,
                        maxSpeed = agent.speed,
                        acceleration = agent.acceleration,
                        angularSpeed = agent.angularSpeed,
                        stoppingDistance = agent.stoppingDistance,
                        obstacleAvoidanceType = agent.obstacleAvoidanceType.ToString(),
                        areaMask = agent.areaMask,
                        agentTypeID = agent.agentTypeID,
                        autoBraking = agent.autoBraking,
                        autoRepath = agent.autoRepath,
                        allowedAreas = allowedAreas
                    },
                    runtimeStatus = new
                    {
                        isActiveAndEnabled = agent.isActiveAndEnabled,
                        isOnNavMesh = agent.isOnNavMesh,
                        velocity = new { x = agent.velocity.x, y = agent.velocity.y, z = agent.velocity.z },
                        desiredVelocity = new { x = agent.desiredVelocity.x, y = agent.desiredVelocity.y, z = agent.desiredVelocity.z },
                        isStopped = agent.isStopped,
                        updatePosition = agent.updatePosition,
                        updateRotation = agent.updateRotation,
                        updateUpAxis = agent.updateUpAxis
                    },
                    pathInformation = pathInfo,
                    obstacleAvoidanceQuality = new
                    {
                        currentType = agent.obstacleAvoidanceType.ToString(),
                        description = GetObstacleAvoidanceDescription(agent.obstacleAvoidanceType),
                        performanceImpact = GetPerformanceImpact(agent.obstacleAvoidanceType)
                    }
                };

                return Response.Success($"Obstacle avoidance info retrieved for '{gameObjectName}'.", info);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get obstacle avoidance info: {e.Message}");
            }
        }

        private static string GetObstacleAvoidanceDescription(ObstacleAvoidanceType type)
        {
            return type switch
            {
                ObstacleAvoidanceType.NoObstacleAvoidance => "No obstacle avoidance - agent moves directly towards target",
                ObstacleAvoidanceType.LowQualityObstacleAvoidance => "Basic avoidance with minimal computational cost",
                ObstacleAvoidanceType.MedQualityObstacleAvoidance => "Medium quality avoidance with balanced performance",
                ObstacleAvoidanceType.GoodQualityObstacleAvoidance => "Good quality avoidance with higher computational cost",
                ObstacleAvoidanceType.HighQualityObstacleAvoidance => "Best quality avoidance with highest computational cost",
                _ => "Unknown avoidance type"
            };
        }

        private static string GetPerformanceImpact(ObstacleAvoidanceType type)
        {
            return type switch
            {
                ObstacleAvoidanceType.NoObstacleAvoidance => "None",
                ObstacleAvoidanceType.LowQualityObstacleAvoidance => "Very Low",
                ObstacleAvoidanceType.MedQualityObstacleAvoidance => "Low",
                ObstacleAvoidanceType.GoodQualityObstacleAvoidance => "Medium",
                ObstacleAvoidanceType.HighQualityObstacleAvoidance => "High",
                _ => "Unknown"
            };
        }

        private static object ListObstacleAvoidanceAgents()
        {
            try
            {
                NavMeshAgent[] agents = UnityEngine.Object.FindObjectsByType<NavMeshAgent>(FindObjectsSortMode.None);
                var agentList = new List<object>();
                var agentsByType = new Dictionary<ObstacleAvoidanceType, int>();
                var agentsByPriority = new Dictionary<int, int>();

                foreach (var agent in agents)
                {
                    // Count agents by type
                    if (!agentsByType.ContainsKey(agent.obstacleAvoidanceType))
                        agentsByType[agent.obstacleAvoidanceType] = 0;
                    agentsByType[agent.obstacleAvoidanceType]++;

                    // Count agents by priority
                    if (!agentsByPriority.ContainsKey(agent.avoidancePriority))
                        agentsByPriority[agent.avoidancePriority] = 0;
                    agentsByPriority[agent.avoidancePriority]++;

                    agentList.Add(new
                    {
                        gameObjectName = agent.gameObject.name,
                        transform = new
                        {
                            position = new { x = agent.transform.position.x, y = agent.transform.position.y, z = agent.transform.position.z }
                        },
                        agentConfiguration = new
                        {
                            avoidancePriority = agent.avoidancePriority,
                            radius = agent.radius,
                            height = agent.height,
                            speed = agent.speed,
                            obstacleAvoidanceType = agent.obstacleAvoidanceType.ToString(),
                            agentTypeID = agent.agentTypeID,
                            areaMask = agent.areaMask
                        },
                        runtimeStatus = new
                        {
                            isActiveAndEnabled = agent.isActiveAndEnabled,
                            isOnNavMesh = agent.isOnNavMesh,
                            hasPath = agent.hasPath,
                            pathStatus = agent.pathStatus.ToString(),
                            isStopped = agent.isStopped,
                            velocityMagnitude = agent.velocity.magnitude
                        },
                        performance = new
                        {
                            avoidanceQuality = agent.obstacleAvoidanceType.ToString(),
                            performanceImpact = GetPerformanceImpact(agent.obstacleAvoidanceType)
                        }
                    });
                }

                var summary = new
                {
                    totalAgents = agentList.Count,
                    activeAgents = agents.Count(a => a.isActiveAndEnabled),
                    agentsOnNavMesh = agents.Count(a => a.isOnNavMesh),
                    agentsWithPaths = agents.Count(a => a.hasPath),
                    agentsByAvoidanceType = agentsByType.ToDictionary(kvp => kvp.Key.ToString(), kvp => kvp.Value),
                    agentsByPriority = agentsByPriority.OrderBy(kvp => kvp.Key).ToDictionary(kvp => $"Priority_{kvp.Key}", kvp => kvp.Value)
                };

                return Response.Success("Obstacle avoidance agents listed successfully.", new
                {
                    agents = agentList,
                    summary = summary
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to list obstacle avoidance agents: {e.Message}");
            }
        }

        #endregion

        #region Path Modifiers Implementation

        private static object CreatePathModifier(JObject @params)
        {
            string modifierName = @params["modifier_name"]?.ToString();
            string gameObjectName = @params["gameobject_name"]?.ToString();
            string modifierType = @params["modifier_type"]?.ToString()?.ToLower();

            if (string.IsNullOrEmpty(modifierName))
            {
                return Response.Error("Modifier name is required.");
            }

            if (string.IsNullOrEmpty(gameObjectName))
            {
                return Response.Error("GameObject name is required.");
            }

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
            {
                return Response.Error($"GameObject '{gameObjectName}' not found.");
            }

            try
            {
                switch (modifierType)
                {
                    case "volume":
                        var volumeModifier = go.GetComponent<NavMeshModifierVolume>();
                        if (volumeModifier == null)
                        {
                            volumeModifier = go.AddComponent<NavMeshModifierVolume>();
                        }
                        
                        if (@params["modifier_area"] != null)
                        {
                            var area = @params["modifier_area"].ToObject<float[]>();
                            if (area.Length >= 6)
                            {
                                go.transform.position = new Vector3(area[0], area[1], area[2]);
                                volumeModifier.size = new Vector3(area[3], area[4], area[5]);
                            }
                        }
                        else
                        {
                            // Set default size if not specified
                            volumeModifier.size = new Vector3(5f, 3f, 5f);
                        }
                        
                        // Set area type for volume modifier
                        if (@params["area_type"] != null)
                        {
                            var areaTypeParam = @params["area_type"];
                            if (areaTypeParam.Type == JTokenType.String)
                            {
                                string areaName = areaTypeParam.ToString();
                                int areaId = FindAreaIdByName(areaName);
                                if (areaId != -1)
                                {
                                    volumeModifier.area = areaId;
                                }
                            }
                            else
                            {
                                volumeModifier.area = areaTypeParam.ToObject<int>();
                            }
                        }
                        
                        // Configure affected agents
                        if (@params["affected_agent_types"] != null)
                        {
                            var agentTypes = @params["affected_agent_types"].ToObject<int[]>();
                            // NavMeshModifierVolume affects all agent types by default in Unity 6.2
                            // This information is stored for reference but doesn't affect the component directly
                        }
                        break;

                    case "surface":
                        var surfaceModifier = go.GetComponent<NavMeshModifier>();
                        if (surfaceModifier == null)
                        {
                            surfaceModifier = go.AddComponent<NavMeshModifier>();
                        }
                        
                        // Configure area override
                        if (@params["area_type"] != null)
                        {
                            surfaceModifier.overrideArea = true;
                            var areaTypeParam = @params["area_type"];
                            if (areaTypeParam.Type == JTokenType.String)
                            {
                                string areaName = areaTypeParam.ToString();
                                int areaId = FindAreaIdByName(areaName);
                                if (areaId != -1)
                                {
                                    surfaceModifier.area = areaId;
                                }
                            }
                            else
                            {
                                surfaceModifier.area = areaTypeParam.ToObject<int>();
                            }
                        }
                        
                        // Configure ignore from build
                        if (@params["ignore_from_build"] != null)
                        {
                            surfaceModifier.ignoreFromBuild = @params["ignore_from_build"].ToObject<bool>();
                        }
                        
                        // Configure affected agent types
                        if (@params["affected_agent_types"] != null)
                        {
                            var agentTypes = @params["affected_agent_types"].ToObject<int[]>();
                            // Store this information in the component name or a custom script if needed
                            // NavMeshModifier doesn't have direct agent type filtering in Unity 6.2
                        }
                        break;

                    case "obstacle":
                        var obstacleModifier = go.GetComponent<NavMeshObstacle>();
                        if (obstacleModifier == null)
                        {
                            obstacleModifier = go.AddComponent<NavMeshObstacle>();
                        }
                        
                        // Configure obstacle shape
                        string obstacleShape = @params["obstacle_shape"]?.ToString()?.ToLower() ?? "box";
                        obstacleModifier.shape = obstacleShape switch
                        {
                            "box" => NavMeshObstacleShape.Box,
                            "capsule" => NavMeshObstacleShape.Capsule,
                            _ => NavMeshObstacleShape.Box
                        };
                        
                        // Configure obstacle size
                        if (@params["obstacle_size"] != null)
                        {
                            var size = @params["obstacle_size"].ToObject<float[]>();
                            if (size.Length >= 3)
                            {
                                obstacleModifier.size = new Vector3(size[0], size[1], size[2]);
                            }
                        }
                        else
                        {
                            obstacleModifier.size = new Vector3(1f, 2f, 1f); // Default size
                        }
                        
                        // Configure obstacle center
                        if (@params["obstacle_center"] != null)
                        {
                            var center = @params["obstacle_center"].ToObject<float[]>();
                            if (center.Length >= 3)
                            {
                                obstacleModifier.center = new Vector3(center[0], center[1], center[2]);
                            }
                        }
                        
                        // Configure carving
                        if (@params["carve_navigation"] != null)
                        {
                            obstacleModifier.carving = @params["carve_navigation"].ToObject<bool>();
                        }
                        else
                        {
                            obstacleModifier.carving = true; // Default to carving
                        }
                        
                        // Configure carve only stationary
                        if (@params["carve_only_stationary"] != null)
                        {
                            obstacleModifier.carveOnlyStationary = @params["carve_only_stationary"].ToObject<bool>();
                        }
                        
                        // Configure time to stationary
                        if (@params["time_to_stationary"] != null)
                        {
                            obstacleModifier.carvingTimeToStationary = @params["time_to_stationary"].ToObject<float>();
                        }
                        
                        // Configure move threshold
                        if (@params["carving_move_threshold"] != null)
                        {
                            obstacleModifier.carvingMoveThreshold = @params["carving_move_threshold"].ToObject<float>();
                        }
                        break;

                    default:
                        return Response.Error($"Unknown modifier type: '{modifierType}'. Valid types are: volume, surface, obstacle");
                }

                EditorUtility.SetDirty(go);

                // Get detailed information about the created modifier
                var modifierInfo = new Dictionary<string, object>
                {
                    ["modifierName"] = modifierName,
                    ["gameObjectName"] = gameObjectName,
                    ["modifierType"] = modifierType,
                    ["transform"] = new
                    {
                        position = new { x = go.transform.position.x, y = go.transform.position.y, z = go.transform.position.z },
                        rotation = new { x = go.transform.rotation.x, y = go.transform.rotation.y, z = go.transform.rotation.z, w = go.transform.rotation.w },
                        scale = new { x = go.transform.localScale.x, y = go.transform.localScale.y, z = go.transform.localScale.z }
                    }
                };

                // Add type-specific information
                switch (modifierType)
                {
                    case "volume":
                        var volumeModifier = go.GetComponent<NavMeshModifierVolume>();
                        modifierInfo["volumeSettings"] = new
                        {
                            size = new { x = volumeModifier.size.x, y = volumeModifier.size.y, z = volumeModifier.size.z },
                            area = volumeModifier.area,
                            areaName = volumeModifier.area < 3 ? GetBuiltInAreaName(volumeModifier.area) : EditorPrefs.GetString($"NavMeshAreaName_{volumeModifier.area}", $"Area_{volumeModifier.area}"),
                            center = new { x = volumeModifier.center.x, y = volumeModifier.center.y, z = volumeModifier.center.z }
                        };
                        break;
                        
                    case "surface":
                        var surfaceModifier = go.GetComponent<NavMeshModifier>();
                        modifierInfo["surfaceSettings"] = new
                        {
                            overrideArea = surfaceModifier.overrideArea,
                            area = surfaceModifier.area,
                            areaName = surfaceModifier.area < 3 ? GetBuiltInAreaName(surfaceModifier.area) : EditorPrefs.GetString($"NavMeshAreaName_{surfaceModifier.area}", $"Area_{surfaceModifier.area}"),
                            ignoreFromBuild = surfaceModifier.ignoreFromBuild
                        };
                        break;
                        
                    case "obstacle":
                        var obstacleModifier = go.GetComponent<NavMeshObstacle>();
                        modifierInfo["obstacleSettings"] = new
                        {
                            shape = obstacleModifier.shape.ToString(),
                            size = new { x = obstacleModifier.size.x, y = obstacleModifier.size.y, z = obstacleModifier.size.z },
                            center = new { x = obstacleModifier.center.x, y = obstacleModifier.center.y, z = obstacleModifier.center.z },
                            carving = obstacleModifier.carving,
                            carveOnlyStationary = obstacleModifier.carveOnlyStationary,
                            carvingTimeToStationary = obstacleModifier.carvingTimeToStationary,
                            carvingMoveThreshold = obstacleModifier.carvingMoveThreshold
                        };
                        break;
                }

                return Response.Success($"Path modifier '{modifierName}' created successfully on '{gameObjectName}'.", modifierInfo);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create path modifier: {e.Message}");
            }
        }

        private static object ModifyPathModifier(JObject @params)
        {
            string gameObjectName = @params["gameobject_name"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
            {
                return Response.Error("GameObject name is required.");
            }

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
            {
                return Response.Error($"GameObject '{gameObjectName}' not found.");
            }

            try
            {
                var volumeModifier = go.GetComponent<NavMeshModifierVolume>();
                var surfaceModifier = go.GetComponent<NavMeshModifier>();

                if (volumeModifier != null)
                {
                    if (@params["modifier_area"] != null)
                    {
                        var area = @params["modifier_area"].ToObject<float[]>();
                        if (area.Length >= 6)
                        {
                            go.transform.position = new Vector3(area[0], area[1], area[2]);
                            volumeModifier.size = new Vector3(area[3], area[4], area[5]);
                        }
                    }
                }

                if (surfaceModifier != null)
                {
                    if (@params["area_type"] != null)
                    {
                        surfaceModifier.area = @params["area_type"].ToObject<int>();
                    }
                }

                EditorUtility.SetDirty(go);

                return Response.Success($"Path modifier on '{gameObjectName}' modified successfully.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to modify path modifier: {e.Message}");
            }
        }

        private static object DeletePathModifier(JObject @params)
        {
            string gameObjectName = @params["gameobject_name"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
            {
                return Response.Error("GameObject name is required.");
            }

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
            {
                return Response.Error($"GameObject '{gameObjectName}' not found.");
            }

            try
            {
                var volumeModifier = go.GetComponent<NavMeshModifierVolume>();
                var surfaceModifier = go.GetComponent<NavMeshModifier>();

                if (volumeModifier != null)
                {
                    UnityEngine.Object.DestroyImmediate(volumeModifier);
                }

                if (surfaceModifier != null)
                {
                    UnityEngine.Object.DestroyImmediate(surfaceModifier);
                }

                EditorUtility.SetDirty(go);

                return Response.Success($"Path modifiers removed from '{gameObjectName}'.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to delete path modifier: {e.Message}");
            }
        }

        private static object EnablePathModifier(JObject @params)
        {
            string gameObjectName = @params["gameobject_name"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
            {
                return Response.Error("GameObject name is required.");
            }

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
            {
                return Response.Error($"GameObject '{gameObjectName}' not found.");
            }

            try
            {
                var volumeModifier = go.GetComponent<NavMeshModifierVolume>();
                var surfaceModifier = go.GetComponent<NavMeshModifier>();

                if (volumeModifier != null)
                {
                    volumeModifier.enabled = true;
                }

                if (surfaceModifier != null)
                {
                    surfaceModifier.enabled = true;
                }

                EditorUtility.SetDirty(go);

                return Response.Success($"Path modifiers enabled on '{gameObjectName}'.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to enable path modifier: {e.Message}");
            }
        }

        private static object DisablePathModifier(JObject @params)
        {
            string gameObjectName = @params["gameobject_name"]?.ToString();
            if (string.IsNullOrEmpty(gameObjectName))
            {
                return Response.Error("GameObject name is required.");
            }

            GameObject go = GameObject.Find(gameObjectName);
            if (go == null)
            {
                return Response.Error($"GameObject '{gameObjectName}' not found.");
            }

            try
            {
                var volumeModifier = go.GetComponent<NavMeshModifierVolume>();
                var surfaceModifier = go.GetComponent<NavMeshModifier>();

                if (volumeModifier != null)
                {
                    volumeModifier.enabled = false;
                }

                if (surfaceModifier != null)
                {
                    surfaceModifier.enabled = false;
                }

                EditorUtility.SetDirty(go);

                return Response.Success($"Path modifiers disabled on '{gameObjectName}'.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to disable path modifier: {e.Message}");
            }
        }

        private static object ListPathModifiers()
        {
            try
            {
                var volumeModifiers = UnityEngine.Object.FindObjectsByType<NavMeshModifierVolume>(FindObjectsSortMode.None);
                var surfaceModifiers = UnityEngine.Object.FindObjectsByType<NavMeshModifier>(FindObjectsSortMode.None);
                var modifierList = new List<object>();

                foreach (var modifier in volumeModifiers)
                {
                    modifierList.Add(new
                    {
                        gameObjectName = modifier.gameObject.name,
                        type = "volume",
                        enabled = modifier.enabled,
                        size = new { x = modifier.size.x, y = modifier.size.y, z = modifier.size.z },
                        area = modifier.area
                    });
                }

                foreach (var modifier in surfaceModifiers)
                {
                    modifierList.Add(new
                    {
                        gameObjectName = modifier.gameObject.name,
                        type = "surface",
                        enabled = modifier.enabled,
                        overrideArea = modifier.overrideArea,
                        area = modifier.area
                    });
                }

                return Response.Success("Path modifiers listed successfully.", modifierList);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to list path modifiers: {e.Message}");
            }
        }

        #endregion

        #region NavMesh Links Implementation

        private static object CreateNavMeshLink(JObject @params)
        {
            string linkName = @params["link_name"]?.ToString();
            if (string.IsNullOrEmpty(linkName))
            {
                return Response.Error("Link name is required.");
            }

            try
            {
                GameObject linkGO = new GameObject(linkName);
                NavMeshLink link = linkGO.AddComponent<NavMeshLink>();

                if (@params["start_position"] != null)
                {
                    var startPos = @params["start_position"].ToObject<float[]>();
                    if (startPos.Length >= 3)
                    {
                        link.startPoint = new Vector3(startPos[0], startPos[1], startPos[2]);
                    }
                }

                if (@params["end_position"] != null)
                {
                    var endPos = @params["end_position"].ToObject<float[]>();
                    if (endPos.Length >= 3)
                    {
                        link.endPoint = new Vector3(endPos[0], endPos[1], endPos[2]);
                    }
                }

                if (@params["link_width"] != null)
                {
                    link.width = @params["link_width"].ToObject<float>();
                }

                if (@params["cost_modifier"] != null)
                {
                    link.costModifier = @params["cost_modifier"].ToObject<float>();
                }

                if (@params["bidirectional"] != null)
                {
                    link.bidirectional = @params["bidirectional"].ToObject<bool>();
                }

                if (@params["auto_update_positions"] != null)
                {
                    link.autoUpdate = @params["auto_update_positions"].ToObject<bool>();
                }

                if (@params["area_type"] != null)
                {
                    link.area = @params["area_type"].ToObject<int>();
                }

                if (@params["agent_type_mask"] != null)
                {
                    link.agentTypeID = @params["agent_type_mask"].ToObject<int>();
                }

                EditorUtility.SetDirty(linkGO);

                return Response.Success($"NavMesh link '{linkName}' created successfully.", new
                {
                    linkName = linkName,
                    startPoint = new { x = link.startPoint.x, y = link.startPoint.y, z = link.startPoint.z },
                    endPoint = new { x = link.endPoint.x, y = link.endPoint.y, z = link.endPoint.z },
                    width = link.width,
                    costModifier = link.costModifier,
                    bidirectional = link.bidirectional,
                    autoUpdate = link.autoUpdate,
                    area = link.area,
                    agentTypeID = link.agentTypeID
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create NavMesh link: {e.Message}");
            }
        }

        private static object ModifyNavMeshLink(JObject @params)
        {
            string linkName = @params["link_name"]?.ToString();
            if (string.IsNullOrEmpty(linkName))
            {
                return Response.Error("Link name is required.");
            }

            GameObject linkGO = GameObject.Find(linkName);
            if (linkGO == null)
            {
                return Response.Error($"NavMesh link '{linkName}' not found.");
            }

            NavMeshLink link = linkGO.GetComponent<NavMeshLink>();
            if (link == null)
            {
                return Response.Error($"NavMeshLink component not found on '{linkName}'.");
            }

            try
            {
                if (@params["start_position"] != null)
                {
                    var startPos = @params["start_position"].ToObject<float[]>();
                    if (startPos.Length >= 3)
                    {
                        link.startPoint = new Vector3(startPos[0], startPos[1], startPos[2]);
                    }
                }

                if (@params["end_position"] != null)
                {
                    var endPos = @params["end_position"].ToObject<float[]>();
                    if (endPos.Length >= 3)
                    {
                        link.endPoint = new Vector3(endPos[0], endPos[1], endPos[2]);
                    }
                }

                if (@params["link_width"] != null)
                {
                    link.width = @params["link_width"].ToObject<float>();
                }

                if (@params["cost_modifier"] != null)
                {
                    link.costModifier = @params["cost_modifier"].ToObject<float>();
                }

                if (@params["bidirectional"] != null)
                {
                    link.bidirectional = @params["bidirectional"].ToObject<bool>();
                }

                EditorUtility.SetDirty(linkGO);

                return Response.Success($"NavMesh link '{linkName}' modified successfully.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to modify NavMesh link: {e.Message}");
            }
        }

        private static object DeleteNavMeshLink(JObject @params)
        {
            string linkName = @params["link_name"]?.ToString();
            if (string.IsNullOrEmpty(linkName))
            {
                return Response.Error("Link name is required.");
            }

            GameObject linkGO = GameObject.Find(linkName);
            if (linkGO == null)
            {
                return Response.Error($"NavMesh link '{linkName}' not found.");
            }

            try
            {
                UnityEngine.Object.DestroyImmediate(linkGO);
                return Response.Success($"NavMesh link '{linkName}' deleted successfully.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to delete NavMesh link: {e.Message}");
            }
        }

        private static object EnableNavMeshLink(JObject @params)
        {
            string linkName = @params["link_name"]?.ToString();
            if (string.IsNullOrEmpty(linkName))
            {
                return Response.Error("Link name is required.");
            }

            GameObject linkGO = GameObject.Find(linkName);
            if (linkGO == null)
            {
                return Response.Error($"NavMesh link '{linkName}' not found.");
            }

            NavMeshLink link = linkGO.GetComponent<NavMeshLink>();
            if (link == null)
            {
                return Response.Error($"NavMeshLink component not found on '{linkName}'.");
            }

            try
            {
                link.enabled = true;
                EditorUtility.SetDirty(linkGO);
                return Response.Success($"NavMesh link '{linkName}' enabled successfully.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to enable NavMesh link: {e.Message}");
            }
        }

        private static object DisableNavMeshLink(JObject @params)
        {
            string linkName = @params["link_name"]?.ToString();
            if (string.IsNullOrEmpty(linkName))
            {
                return Response.Error("Link name is required.");
            }

            GameObject linkGO = GameObject.Find(linkName);
            if (linkGO == null)
            {
                return Response.Error($"NavMesh link '{linkName}' not found.");
            }

            NavMeshLink link = linkGO.GetComponent<NavMeshLink>();
            if (link == null)
            {
                return Response.Error($"NavMeshLink component not found on '{linkName}'.");
            }

            try
            {
                link.enabled = false;
                EditorUtility.SetDirty(linkGO);
                return Response.Success($"NavMesh link '{linkName}' disabled successfully.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to disable NavMesh link: {e.Message}");
            }
        }

        private static object ListNavMeshLinks()
        {
            try
            {
                NavMeshLink[] links = UnityEngine.Object.FindObjectsByType<NavMeshLink>(FindObjectsSortMode.None);
                var linkList = new List<object>();

                foreach (var link in links)
                {
                    linkList.Add(new
                    {
                        linkName = link.gameObject.name,
                        enabled = link.enabled,
                        startPoint = new { x = link.startPoint.x, y = link.startPoint.y, z = link.startPoint.z },
                        endPoint = new { x = link.endPoint.x, y = link.endPoint.y, z = link.endPoint.z },
                        width = link.width,
                        costModifier = link.costModifier,
                        bidirectional = link.bidirectional,
                        autoUpdate = link.autoUpdate,
                        area = link.area,
                        agentTypeID = link.agentTypeID
                    });
                }

                return Response.Success("NavMesh links listed successfully.", linkList);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to list NavMesh links: {e.Message}");
            }
        }

        #endregion

        #region Area Masks Implementation

        private static object SetAreaMask(JObject @params)
        {
            string agentName = @params["agent_name"]?.ToString();
            
            try
            {
                int areaMask = 0;
                
                if (@params["area_mask"] != null)
                {
                    areaMask = @params["area_mask"].ToObject<int>();
                }
                else if (@params["allowed_areas"] != null)
                {
                    var allowedAreas = @params["allowed_areas"].ToObject<string[]>();
                    foreach (string areaName in allowedAreas)
                    {
                        int areaId = NavMesh.GetAreaFromName(areaName);
                        if (areaId != -1)
                        {
                            areaMask |= (1 << areaId);
                        }
                    }
                }
                else if (@params["forbidden_areas"] != null)
                {
                    // Start with all areas allowed
                    areaMask = -1;
                    var forbiddenAreas = @params["forbidden_areas"].ToObject<string[]>();
                    foreach (string areaName in forbiddenAreas)
                    {
                        int areaId = NavMesh.GetAreaFromName(areaName);
                        if (areaId != -1)
                        {
                            areaMask &= ~(1 << areaId);
                        }
                    }
                }

                if (!string.IsNullOrEmpty(agentName))
                {
                    // Apply to specific agent
                    GameObject go = GameObject.Find(agentName);
                    if (go == null)
                    {
                        return Response.Error($"GameObject '{agentName}' not found.");
                    }

                    NavMeshAgent agent = go.GetComponent<NavMeshAgent>();
                    if (agent == null)
                    {
                        return Response.Error($"NavMeshAgent component not found on '{agentName}'.");
                    }

                    agent.areaMask = areaMask;
                    EditorUtility.SetDirty(go);
                }
                else if (@params["apply_to_agent_type"] != null)
                {
                    // Apply to all agents of specific type
                    int agentTypeId = @params["apply_to_agent_type"].ToObject<int>();
                    NavMeshAgent[] agents = UnityEngine.Object.FindObjectsByType<NavMeshAgent>(FindObjectsSortMode.None);
                    
                    foreach (var agent in agents)
                    {
                        if (agent.agentTypeID == agentTypeId)
                        {
                            agent.areaMask = areaMask;
                            EditorUtility.SetDirty(agent.gameObject);
                        }
                    }
                }

                return Response.Success("Area mask set successfully.", new { areaMask = areaMask });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to set area mask: {e.Message}");
            }
        }

        private static object GetAreaMask(JObject @params)
        {
            string agentName = @params["agent_name"]?.ToString();
            if (string.IsNullOrEmpty(agentName))
            {
                return Response.Error("Agent name is required.");
            }

            GameObject go = GameObject.Find(agentName);
            if (go == null)
            {
                return Response.Error($"GameObject '{agentName}' not found.");
            }

            NavMeshAgent agent = go.GetComponent<NavMeshAgent>();
            if (agent == null)
            {
                return Response.Error($"NavMeshAgent component not found on '{agentName}'.");
            }

            try
            {
                int areaMask = agent.areaMask;
                var allowedAreas = new List<string>();
                string[] areaNames = NavMesh.GetAreaNames();

                for (int i = 0; i < areaNames.Length; i++)
                {
                    if (!string.IsNullOrEmpty(areaNames[i]) && (areaMask & (1 << i)) != 0)
                    {
                        allowedAreas.Add(areaNames[i]);
                    }
                }

                return Response.Success($"Area mask retrieved for '{agentName}'.", new
                {
                    agentName = agentName,
                    areaMask = areaMask,
                    allowedAreas = allowedAreas
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get area mask: {e.Message}");
            }
        }

        private static object CreateAreaMask(JObject @params)
        {
            string maskName = @params["mask_name"]?.ToString();
            if (string.IsNullOrEmpty(maskName))
            {
                return Response.Error("Mask name is required.");
            }

            try
            {
                int areaMask = 0;
                
                if (@params["allowed_areas"] != null)
                {
                    var allowedAreas = @params["allowed_areas"].ToObject<string[]>();
                    foreach (string areaName in allowedAreas)
                    {
                        int areaId = NavMesh.GetAreaFromName(areaName);
                        if (areaId != -1)
                        {
                            areaMask |= (1 << areaId);
                        }
                    }
                }

                // Save mask preset
                EditorPrefs.SetInt($"NavMeshAreaMask_{maskName}", areaMask);

                return Response.Success($"Area mask '{maskName}' created successfully.", new
                {
                    maskName = maskName,
                    areaMask = areaMask
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create area mask: {e.Message}");
            }
        }

        private static object ApplyAreaMask(JObject @params)
        {
            string maskName = @params["mask_name"]?.ToString();
            if (string.IsNullOrEmpty(maskName))
            {
                return Response.Error("Mask name is required.");
            }

            try
            {
                int areaMask = EditorPrefs.GetInt($"NavMeshAreaMask_{maskName}", -1);
                if (areaMask == -1)
                {
                    return Response.Error($"Area mask '{maskName}' not found.");
                }

                string targetAgent = @params["target_agent"]?.ToString();
                if (!string.IsNullOrEmpty(targetAgent))
                {
                    GameObject go = GameObject.Find(targetAgent);
                    if (go == null)
                    {
                        return Response.Error($"GameObject '{targetAgent}' not found.");
                    }

                    NavMeshAgent agent = go.GetComponent<NavMeshAgent>();
                    if (agent == null)
                    {
                        return Response.Error($"NavMeshAgent component not found on '{targetAgent}'.");
                    }

                    agent.areaMask = areaMask;
                    EditorUtility.SetDirty(go);
                }
                else
                {
                    // Apply to all agents
                    NavMeshAgent[] agents = UnityEngine.Object.FindObjectsByType<NavMeshAgent>(FindObjectsSortMode.None);
                    foreach (var agent in agents)
                    {
                        agent.areaMask = areaMask;
                        EditorUtility.SetDirty(agent.gameObject);
                    }
                }

                return Response.Success($"Area mask '{maskName}' applied successfully.");
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to apply area mask: {e.Message}");
            }
        }

        private static object ListAreaMasks()
        {
            try
            {
                var masks = new List<object>();
                string[] areaNames = NavMesh.GetAreaNames();

                // Get saved mask presets
                for (int i = 0; i < 32; i++)
                {
                    string maskKey = $"NavMeshAreaMask_Preset{i}";
                    if (EditorPrefs.HasKey(maskKey))
                    {
                        int areaMask = EditorPrefs.GetInt(maskKey);
                        var allowedAreas = new List<string>();

                        for (int j = 0; j < areaNames.Length; j++)
                        {
                            if (!string.IsNullOrEmpty(areaNames[j]) && (areaMask & (1 << j)) != 0)
                            {
                                allowedAreas.Add(areaNames[j]);
                            }
                        }

                        masks.Add(new
                        {
                            maskName = $"Preset{i}",
                            areaMask = areaMask,
                            allowedAreas = allowedAreas
                        });
                    }
                }

                return Response.Success("Area masks listed successfully.", masks);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to list area masks: {e.Message}");
            }
        }

        #endregion

        /// <summary>
        /// [UNITY 6.2] - Main command handler for NavigationEnhanced operations
        /// </summary>
        public static object HandleCommand(JObject args)
        {
            try
            {
                string operation = args["operation"]?.ToString();
                if (string.IsNullOrEmpty(operation))
                {
                    return Response.Error("Operation parameter is required");
                }

                switch (operation.ToLower())
                {
                    case "setup_dynamic_navmesh_areas":
                        return HandleSetupDynamicNavmeshAreas(args);
                    case "configure_navmesh_costs":
                        return HandleConfigureNavmeshCosts(args);
                    case "setup_runtime_obstacle_avoidance":
                        return HandleSetupRuntimeObstacleAvoidance(args);
                    case "create_dynamic_path_modifiers":
                        return HandleCreateDynamicPathModifiers(args);
                    case "setup_navmesh_links":
                        return HandleSetupNavmeshLinks(args);
                    case "configure_area_masks":
                        return HandleConfigureAreaMasks(args);
                    default:
                        return Response.Error($"Unknown navigation operation: {operation}");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[NavigationEnhanced] HandleCommand failed: {e}");
                return Response.Error($"Navigation command failed: {e.Message}");
            }
        }

        #region Helper Classes

        [System.Serializable]
        private class SerializableDictionary
        {
            public Dictionary<string, float> items = new Dictionary<string, float>();
        }

        [System.Serializable]
        private class SerializableProfile
        {
            public string profileName;
            public string createdAt;
            public string unityVersion;
            public SerializableCost[] costs;
            public int totalAreas;
        }

        [System.Serializable]
        private class SerializableCost
        {
            public string areaName;
            public float cost;
        }

        #endregion
    }
}