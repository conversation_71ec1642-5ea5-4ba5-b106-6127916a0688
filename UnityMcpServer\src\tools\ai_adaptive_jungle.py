from mcp.server.fastmcp import FastMC<PERSON>, Context
from typing import Dict, Any, List, Optional
from unity_connection import get_unity_connection

def register_ai_adaptive_jungle_tools(mcp: FastMCP):
    """Register AI Adaptive Jungle tools with the MCP server."""

    @mcp.tool()
    def ai_adaptive_jungle(
        ctx: Context,
        action: str,
        jungle_name: Optional[str] = None,
        adaptation_mode: Optional[str] = None,
        baseline_skill_level: Optional[float] = None,
        enable_ai_learning: Optional[bool] = None,
        max_monster_variants: Optional[int] = None,
        player_skill_data: Optional[Dict[str, Any]] = None,
        difficulty_adjustment: Optional[float] = None,
        monster_types: Optional[List[str]] = None,
        spawn_locations: Optional[List[Dict[str, float]]] = None,
        route_optimization_mode: Optional[str] = None,
        meta_prediction_timeframe: Optional[str] = None,
        analytics_period: Optional[str] = None
    ) -> Dict[str, Any]:
        """Sistema de Jungle Adaptativo com IA para MOBA AURACRON.

        Funcionalidades:
        - setup_adaptive_jungle: Configura jungle adaptativo
        - analyze_jungle_patterns: Analisa padrões de jungle
        - adjust_difficulty: Ajusta dificuldade dinamicamente
        - spawn_adaptive_monsters: Spawna monstros adaptativos
        - optimize_jungle_routes: Otimiza rotas de jungle
        - predict_jungle_meta: Prediz meta de jungle
        - get_jungle_analytics: Obtém analytics do jungle

        [MOBA FEATURES]:
        - IA que adapta spawns baseado no skill dos jogadores
        - Análise de padrões de movimento
        - Balanceamento dinâmico de recompensas
        - Predição de meta-game

        Args:
            action: Operação (setup_adaptive_jungle, analyze_jungle_patterns, adjust_difficulty, 
                   spawn_adaptive_monsters, optimize_jungle_routes, predict_jungle_meta, get_jungle_analytics)
            jungle_name: Nome do jungle adaptativo
            adaptation_mode: Modo de adaptação (dynamic, static, learning)
            baseline_skill_level: Nível de skill baseline (0-100)
            enable_ai_learning: Habilitar aprendizado de IA
            max_monster_variants: Máximo de variantes de monstros
            player_skill_data: Dados de skill dos jogadores
            difficulty_adjustment: Ajuste de dificuldade (-1.0 a 1.0)
            monster_types: Lista de tipos de monstros
            spawn_locations: Lista de localizações de spawn
            route_optimization_mode: Modo de otimização de rotas
            meta_prediction_timeframe: Período para predição de meta
            analytics_period: Período para analytics

        Returns:
            Dictionary com resultados ('success', 'message', 'data')
        """
        try:
            # Preparar parâmetros, removendo valores None
            params = {
                "action": action,
                "jungle_name": jungle_name,
                "adaptation_mode": adaptation_mode,
                "baseline_skill_level": baseline_skill_level,
                "enable_ai_learning": enable_ai_learning,
                "max_monster_variants": max_monster_variants,
                "player_skill_data": player_skill_data,
                "difficulty_adjustment": difficulty_adjustment,
                "monster_types": monster_types,
                "spawn_locations": spawn_locations,
                "route_optimization_mode": route_optimization_mode,
                "meta_prediction_timeframe": meta_prediction_timeframe,
                "analytics_period": analytics_period
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            # Enviar comando para Unity
            response = get_unity_connection().send_command("ai_adaptive_jungle", params)

            # Processar resposta
            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "AI Adaptive Jungle operation completed successfully."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Unknown error in AI Adaptive Jungle operation.")
                }

        except Exception as e:
            return {"success": False, "message": f"Python error in AI Adaptive Jungle: {str(e)}"}
