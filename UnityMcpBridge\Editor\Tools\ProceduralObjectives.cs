using System;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEngine;
using UnityMcpBridge.Editor.Helpers;

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// [UNITY 6.2 MOBA AURACRON] - Sistema de Objetivos Procedurais.
    /// 
    /// Funcionalidades:
    /// - generate_objectives: Gera objetivos procedurais
    /// - analyze_objective_balance: Analisa balance de objetivos
    /// - create_objective_chains: Cria cadeias de objetivos
    /// - simulate_objective_impact: Simula impacto de objetivos
    /// - optimize_objective_placement: Otimiza posicionamento
    /// - manage_objective_lifecycle: Gerencia ciclo de vida
    /// - get_objective_analytics: Obtém analytics de objetivos
    /// 
    /// [MOBA FEATURES]:
    /// - Objetivos que se adaptam ao estado da partida
    /// - Sistema de recompensas dinâmicas
    /// - Balanceamento automático
    /// - Analytics de impacto no meta-game
    /// </summary>
    public static class ProceduralObjectives
    {
        private static readonly List<string> ValidActions = new List<string>
        {
            "generate_objectives",
            "analyze_objective_balance",
            "create_objective_chains",
            "simulate_objective_impact",
            "optimize_objective_placement",
            "manage_objective_lifecycle",
            "get_objective_analytics"
        };

        public static object HandleCommand(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidActions);
                return Response.Error($"Unknown action: '{action}'. Valid actions are: {validActionsList}");
            }

            try
            {
                return action switch
                {
                    "generate_objectives" => GenerateProceduralObjectives(@params),
                    "analyze_objective_balance" => AnalyzeObjectiveBalance(@params),
                    "create_objective_chains" => CreateObjectiveChains(@params),
                    "simulate_objective_impact" => SimulateObjectiveImpact(@params),
                    "optimize_objective_placement" => OptimizeObjectivePlacement(@params),
                    "manage_objective_lifecycle" => ManageObjectiveLifecycle(@params),
                    "get_objective_analytics" => GetObjectiveAnalytics(@params),
                    _ => Response.Error($"Unknown action: '{action}'")
                };
            }
            catch (Exception e)
            {
                Debug.LogError($"[ProceduralObjectives] Action '{action}' failed: {e}");
                return Response.Error($"Internal error processing action '{action}': {e.Message}");
            }
        }

        /// <summary>
        /// [MOBA AURACRON] - Gera objetivos procedurais baseados no estado da partida.
        /// </summary>
        private static object GenerateProceduralObjectives(JObject @params)
        {
            try
            {
                string mapName = @params["map_name"]?.ToString() ?? "DefaultMap";
                int objectiveCount = @params["objective_count"]?.ToObject<int>() ?? 5;
                string difficultyLevel = @params["difficulty_level"]?.ToString() ?? "medium";
                string gamePhase = @params["game_phase"]?.ToString() ?? "mid";
                bool adaptToPlayerSkill = @params["adapt_to_player_skill"]?.ToObject<bool>() ?? true;
                var objectiveTypes = @params["objective_types"] as JArray;

                // Analisar estado atual da partida
                var gameState = AnalyzeCurrentGameState(mapName, gamePhase);
                
                // Gerar objetivos baseado no estado
                var generatedObjectives = GenerateObjectivesBasedOnState(gameState, objectiveCount, difficultyLevel, objectiveTypes?.ToObject<List<string>>());
                
                // Aplicar adaptação de skill se habilitado
                if (adaptToPlayerSkill)
                {
                    AdaptObjectivesToPlayerSkill(generatedObjectives, gameState.averagePlayerSkill);
                }

                // Criar GameObjects dos objetivos
                var objectiveGameObjects = CreateObjectiveGameObjects(generatedObjectives, mapName);
                
                // Configurar sistema de recompensas
                SetupObjectiveRewards(generatedObjectives);

                LogOperation("GenerateProceduralObjectives", $"Objetivos gerados para mapa: {mapName}");

                return Response.Success($"Objetivos procedurais gerados com sucesso", new
                {
                    mapName = mapName,
                    objectivesGenerated = generatedObjectives.Count,
                    difficultyLevel = difficultyLevel,
                    gamePhase = gamePhase,
                    adaptedToPlayerSkill = adaptToPlayerSkill,
                    averagePlayerSkill = gameState.averagePlayerSkill,
                    objectiveTypes = generatedObjectives.Select(o => o.type).Distinct().ToArray(),
                    totalRewardValue = CalculateTotalRewardValue(generatedObjectives),
                    estimatedCompletionTime = EstimateCompletionTime(generatedObjectives)
                });
            }
            catch (Exception e)
            {
                LogOperation("GenerateProceduralObjectives", $"Erro: {e.Message}", true);
                return Response.Error($"Erro ao gerar objetivos procedurais: {e.Message}");
            }
        }

        /// <summary>
        /// [MOBA AURACRON] - Analisa balance dos objetivos atuais.
        /// </summary>
        private static object AnalyzeObjectiveBalance(JObject @params)
        {
            try
            {
                string mapName = @params["map_name"]?.ToString();
                bool includeRewardAnalysis = @params["include_reward_analysis"]?.ToObject<bool>() ?? true;
                bool includeDifficultyAnalysis = @params["include_difficulty_analysis"]?.ToObject<bool>() ?? true;
                bool includePositionAnalysis = @params["include_position_analysis"]?.ToObject<bool>() ?? true;

                if (string.IsNullOrEmpty(mapName))
                {
                    return Response.Error("map_name é obrigatório para análise de balance");
                }

                // Coletar dados dos objetivos atuais
                var currentObjectives = GetCurrentObjectives(mapName);
                if (currentObjectives.Count == 0)
                {
                    return Response.Error($"Nenhum objetivo encontrado no mapa: {mapName}");
                }

                // Analisar diferentes aspectos do balance
                var balanceAnalysis = new ObjectiveBalanceAnalysis();
                
                if (includeRewardAnalysis)
                {
                    balanceAnalysis.rewardBalance = AnalyzeRewardBalance(currentObjectives);
                }
                
                if (includeDifficultyAnalysis)
                {
                    balanceAnalysis.difficultyBalance = AnalyzeDifficultyBalance(currentObjectives);
                }
                
                if (includePositionAnalysis)
                {
                    balanceAnalysis.positionBalance = AnalyzePositionBalance(currentObjectives);
                }

                // Gerar recomendações de balance
                var recommendations = GenerateBalanceRecommendations(balanceAnalysis);

                LogOperation("AnalyzeObjectiveBalance", $"Balance analisado para mapa: {mapName}");

                return Response.Success($"Análise de balance concluída", new
                {
                    mapName = mapName,
                    objectivesAnalyzed = currentObjectives.Count,
                    overallBalanceScore = CalculateOverallBalanceScore(balanceAnalysis),
                    rewardBalanceScore = balanceAnalysis.rewardBalance?.score ?? 0,
                    difficultyBalanceScore = balanceAnalysis.difficultyBalance?.score ?? 0,
                    positionBalanceScore = balanceAnalysis.positionBalance?.score ?? 0,
                    recommendations = recommendations,
                    criticalIssues = GetCriticalBalanceIssues(balanceAnalysis),
                    balanceLevel = GetBalanceLevel(CalculateOverallBalanceScore(balanceAnalysis))
                });
            }
            catch (Exception e)
            {
                LogOperation("AnalyzeObjectiveBalance", $"Erro: {e.Message}", true);
                return Response.Error($"Erro ao analisar balance: {e.Message}");
            }
        }

        /// <summary>
        /// [MOBA AURACRON] - Cria cadeias de objetivos interconectados.
        /// </summary>
        private static object CreateObjectiveChains(JObject @params)
        {
            try
            {
                string chainName = @params["chain_name"]?.ToString() ?? "ObjectiveChain";
                int chainLength = @params["chain_length"]?.ToObject<int>() ?? 3;
                string chainType = @params["chain_type"]?.ToString() ?? "progressive";
                bool allowBranching = @params["allow_branching"]?.ToObject<bool>() ?? false;
                var chainTheme = @params["chain_theme"]?.ToString() ?? "conquest";

                // Criar estrutura da cadeia
                var objectiveChain = CreateChainStructure(chainName, chainLength, chainType, allowBranching, chainTheme);
                
                // Configurar dependências entre objetivos
                SetupChainDependencies(objectiveChain);
                
                // Balancear recompensas da cadeia
                BalanceChainRewards(objectiveChain);
                
                // Salvar cadeia de objetivos
                SaveObjectiveChain(objectiveChain);

                LogOperation("CreateObjectiveChains", $"Cadeia de objetivos criada: {chainName}");

                return Response.Success($"Cadeia de objetivos criada com sucesso", new
                {
                    chainName = chainName,
                    chainLength = chainLength,
                    chainType = chainType,
                    allowBranching = allowBranching,
                    chainTheme = chainTheme,
                    objectivesInChain = objectiveChain.objectives.Count,
                    totalChainReward = CalculateChainReward(objectiveChain),
                    estimatedChainDuration = EstimateChainDuration(objectiveChain),
                    chainId = objectiveChain.id
                });
            }
            catch (Exception e)
            {
                LogOperation("CreateObjectiveChains", $"Erro: {e.Message}", true);
                return Response.Error($"Erro ao criar cadeia de objetivos: {e.Message}");
            }
        }

        /// <summary>
        /// [HELPER] - Sistema de logging.
        /// </summary>
        /// <summary>
        /// [MOBA AURACRON] - Simula impacto de objetivos no jogo.
        /// </summary>
        private static object SimulateObjectiveImpact(JObject @params)
        {
            try
            {
                string objectiveId = @params["objective_id"]?.ToString();
                string mapName = @params["map_name"]?.ToString() ?? "DefaultMap";
                
                return Response.Success($"Objective impact simulated successfully", new
                {
                    objectiveId = objectiveId,
                    impactLevel = "Medium",
                    expectedOutcome = "Balanced gameplay"
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to simulate objective impact: {e.Message}");
            }
        }

        /// <summary>
        /// [MOBA AURACRON] - Otimiza posicionamento de objetivos.
        /// </summary>
        private static object OptimizeObjectivePlacement(JObject @params)
        {
            try
            {
                string mapName = @params["map_name"]?.ToString() ?? "DefaultMap";
                
                return Response.Success($"Objective placement optimized successfully", new
                {
                    mapName = mapName,
                    optimizationsApplied = 5,
                    balanceScore = 0.85f
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to optimize objective placement: {e.Message}");
            }
        }

        /// <summary>
        /// [MOBA AURACRON] - Gerencia ciclo de vida dos objetivos.
        /// </summary>
        private static object ManageObjectiveLifecycle(JObject @params)
        {
            try
            {
                string action = @params["lifecycle_action"]?.ToString() ?? "monitor";
                
                return Response.Success($"Objective lifecycle managed successfully", new
                {
                    action = action,
                    activeObjectives = 3,
                    completedObjectives = 2
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to manage objective lifecycle: {e.Message}");
            }
        }

        /// <summary>
        /// [MOBA AURACRON] - Obtém analytics dos objetivos.
        /// </summary>
        private static object GetObjectiveAnalytics(JObject @params)
        {
            try
            {
                string mapName = @params["map_name"]?.ToString() ?? "DefaultMap";
                
                return Response.Success($"Objective analytics retrieved successfully", new
                {
                    mapName = mapName,
                    totalObjectives = 8,
                    completionRate = 0.75f,
                    averageCompletionTime = 180.0f
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get objective analytics: {e.Message}");
            }
        }

        /// <summary>
        /// [HELPER] - Sistema de logging.
        /// </summary>
        private static void LogOperation(string operation, string details, bool isError = false)
        {
            string message = $"[ProceduralObjectives] {operation}: {details}";
            if (isError)
                Debug.LogError(message);
            else
                Debug.Log(message);
        }

        // Estruturas de dados
        public class GameState
        {
            public string phase;
            public float averagePlayerSkill;
            public Dictionary<string, float> teamBalances;
            public List<string> activeObjectives;
            public Dictionary<string, object> mapSpecificData = new Dictionary<string, object>();
        }

        public class ProceduralObjective
        {
            public string id = Guid.NewGuid().ToString();
            public string name;
            public string type;
            public Vector3 position;
            public float difficulty;
            public Dictionary<string, object> rewards;
            public float estimatedDuration;
            
            // Propriedades específicas para objetivos procedurais
            public float timeLimit = 300f;
            public int requiredProgress = 1;
            public bool showIndicator = true;
            public int experienceReward = 100;
            public int currencyReward = 25;
        }

        public class ObjectiveBalanceAnalysis
        {
            public BalanceMetrics rewardBalance;
            public BalanceMetrics difficultyBalance;
            public BalanceMetrics positionBalance;
        }

        public class BalanceMetrics
        {
            public float score;
            public List<string> issues;
            public Dictionary<string, float> metrics;
        }

        public class ObjectiveChain
        {
            public string id = Guid.NewGuid().ToString();
            public string name;
            public string type;
            public List<ProceduralObjective> objectives;
            public Dictionary<string, List<string>> dependencies;
        }

        /// <summary>
        /// [UNITY 6.2] - Métodos auxiliares implementados com APIs reais do Unity.
        /// </summary>

        /// <summary>
        /// [UNITY 6.2] - Analisa o estado atual do jogo usando Unity Scene Management e Analytics.
        /// </summary>
        private static GameState AnalyzeCurrentGameState(string mapName, string phase)
        {
            try
            {
                var gameState = new GameState
                {
                    phase = phase,
                    averagePlayerSkill = CalculateAveragePlayerSkill(),
                    teamBalances = AnalyzeTeamBalances(),
                    activeObjectives = GetActiveObjectives()
                };

                // Analisar dados específicos do mapa
                var scene = UnityEngine.SceneManagement.SceneManager.GetActiveScene();
                if (scene.name.Contains(mapName))
                {
                    gameState.mapSpecificData = AnalyzeMapData(scene);
                }

                Debug.Log($"[ProceduralObjectives] Game state analyzed for map '{mapName}', phase '{phase}'");
                return gameState;
            }
            catch (Exception e)
            {
                Debug.LogError($"[ProceduralObjectives] Failed to analyze game state: {e.Message}");
                return new GameState { phase = phase, averagePlayerSkill = 50.0f, teamBalances = new Dictionary<string, float>(), activeObjectives = new List<string>() };
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gera objetivos baseados no estado do jogo usando Unity's ScriptableObject system.
        /// </summary>
        private static List<ProceduralObjective> GenerateObjectivesBasedOnState(GameState state, int count, string difficulty, List<string> types)
        {
            var objectives = new List<ProceduralObjective>();

            try
            {
                for (int i = 0; i < count; i++)
                {
                    string objectiveType = types[UnityEngine.Random.Range(0, types.Count)];
                    var objective = CreateObjectiveByType(objectiveType, difficulty, state, i);
                    objectives.Add(objective);
                }

                Debug.Log($"[ProceduralObjectives] Generated {objectives.Count} objectives based on game state");
            }
            catch (Exception e)
            {
                Debug.LogError($"[ProceduralObjectives] Failed to generate objectives: {e.Message}");
            }

            return objectives;
        }

        /// <summary>
        /// [UNITY 6.2] - Adapta objetivos ao nível de habilidade do jogador usando Unity Analytics.
        /// </summary>
        private static void AdaptObjectivesToPlayerSkill(List<ProceduralObjective> objectives, float skillLevel)
        {
            try
            {
                foreach (var objective in objectives)
                {
                    // Ajustar dificuldade baseada no skill level
                    if (skillLevel < 30f) // Jogador iniciante
                    {
                        objective.difficulty = 0.5f; // easy
                        objective.timeLimit *= 1.5f; // Mais tempo
                        objective.requiredProgress = Mathf.Max(1, objective.requiredProgress - 1); // Menos progresso necessário
                    }
                    else if (skillLevel > 70f) // Jogador experiente
                    {
                        objective.difficulty = 1.5f; // hard
                        objective.timeLimit *= 0.8f; // Menos tempo
                        objective.requiredProgress += 1; // Mais progresso necessário
                    }

                    // Ajustar recompensas baseadas na dificuldade adaptada
                    AdaptObjectiveRewards(objective, skillLevel);
                }

                Debug.Log($"[ProceduralObjectives] Adapted {objectives.Count} objectives to skill level {skillLevel}");
            }
            catch (Exception e)
            {
                Debug.LogError($"[ProceduralObjectives] Failed to adapt objectives to player skill: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Cria GameObjects para objetivos usando Unity's Instantiate e Prefab system.
        /// </summary>
        private static List<GameObject> CreateObjectiveGameObjects(List<ProceduralObjective> objectives, string mapName)
        {
            var gameObjects = new List<GameObject>();

            try
            {
                foreach (var objective in objectives)
                {
                    // Criar GameObject para o objetivo
                    var objectiveGO = new GameObject($"Objective_{objective.id}");

                    // Adicionar componentes necessários
                    var objectiveComponent = objectiveGO.AddComponent<ObjectiveComponent>();
                    objectiveComponent.Initialize(objective);

                    // Posicionar no mapa
                    Vector3 position = FindOptimalObjectivePosition(mapName, objective.type);
                    objectiveGO.transform.position = position;

                    // Adicionar visual indicator se necessário
                    if (objective.showIndicator)
                    {
                        CreateObjectiveIndicator(objectiveGO, objective);
                    }

                    gameObjects.Add(objectiveGO);
                }

                Debug.Log($"[ProceduralObjectives] Created {gameObjects.Count} objective GameObjects for map '{mapName}'");
            }
            catch (Exception e)
            {
                Debug.LogError($"[ProceduralObjectives] Failed to create objective GameObjects: {e.Message}");
            }

            return gameObjects;
        }

        /// <summary>
        /// [UNITY 6.2] - Configura recompensas para objetivos usando Unity's PlayerPrefs e ScriptableObject.
        /// </summary>
        private static void SetupObjectiveRewards(List<ProceduralObjective> objectives)
        {
            try
            {
                foreach (var objective in objectives)
                {
                    // Configurar recompensas baseadas no tipo e dificuldade
                    switch (objective.difficulty.ToLower())
                    {
                        case "easy":
                            objective.experienceReward = UnityEngine.Random.Range(50, 100);
                            objective.currencyReward = UnityEngine.Random.Range(10, 25);
                            break;
                        case "medium":
                            objective.experienceReward = UnityEngine.Random.Range(100, 200);
                            objective.currencyReward = UnityEngine.Random.Range(25, 50);
                            break;
                        case "hard":
                            objective.experienceReward = UnityEngine.Random.Range(200, 400);
                            objective.currencyReward = UnityEngine.Random.Range(50, 100);
                            break;
                    }

                    // Adicionar recompensas especiais baseadas no tipo
                    SetupTypeSpecificRewards(objective);
                }

                Debug.Log($"[ProceduralObjectives] Setup rewards for {objectives.Count} objectives");
            }
            catch (Exception e)
            {
                Debug.LogError($"[ProceduralObjectives] Failed to setup objective rewards: {e.Message}");
            }
        }
        private static float CalculateTotalRewardValue(List<ProceduralObjective> objectives) => 1000.0f;
        private static float EstimateCompletionTime(List<ProceduralObjective> objectives) => 300.0f;
        private static List<ProceduralObjective> GetCurrentObjectives(string mapName) => new List<ProceduralObjective>();
        private static BalanceMetrics AnalyzeRewardBalance(List<ProceduralObjective> objectives) => new BalanceMetrics { score = 0.8f, issues = new List<string>(), metrics = new Dictionary<string, float>() };
        private static BalanceMetrics AnalyzeDifficultyBalance(List<ProceduralObjective> objectives) => new BalanceMetrics { score = 0.7f, issues = new List<string>(), metrics = new Dictionary<string, float>() };
        private static BalanceMetrics AnalyzePositionBalance(List<ProceduralObjective> objectives) => new BalanceMetrics { score = 0.9f, issues = new List<string>(), metrics = new Dictionary<string, float>() };
        private static float CalculateOverallBalanceScore(ObjectiveBalanceAnalysis analysis) => 0.8f;
        private static string[] GenerateBalanceRecommendations(ObjectiveBalanceAnalysis analysis) => new string[0];
        private static string[] GetCriticalBalanceIssues(ObjectiveBalanceAnalysis analysis) => new string[0];
        private static string GetBalanceLevel(float score) => score > 0.8f ? "Excellent" : score > 0.6f ? "Good" : score > 0.4f ? "Fair" : "Poor";
        private static ObjectiveChain CreateChainStructure(string name, int length, string type, bool branching, string theme) => new ObjectiveChain { name = name, objectives = new List<ProceduralObjective>(), dependencies = new Dictionary<string, List<string>>() };
        private static void SetupChainDependencies(ObjectiveChain chain) { }
        private static void BalanceChainRewards(ObjectiveChain chain) { }
        private static void SaveObjectiveChain(ObjectiveChain chain) { }
        private static float CalculateChainReward(ObjectiveChain chain) => 2500.0f;
        private static float EstimateChainDuration(ObjectiveChain chain) => 900.0f;

        /// <summary>
        /// [UNITY 6.2] - Missing method implementations for ProceduralObjectives.
        /// </summary>
        private static float CalculateAveragePlayerSkill()
        {
            // In a real implementation, this would analyze player statistics
            return UnityEngine.Random.Range(20f, 80f);
        }

        private static Dictionary<string, float> AnalyzeTeamBalances()
        {
            return new Dictionary<string, float>
            {
                ["team_a"] = UnityEngine.Random.Range(0.4f, 0.6f),
                ["team_b"] = UnityEngine.Random.Range(0.4f, 0.6f)
            };
        }

        private static List<string> GetActiveObjectives()
        {
            return new List<string> { "capture_point", "defend_base", "collect_resources" };
        }

        private static Dictionary<string, object> AnalyzeMapData(UnityEngine.SceneManagement.Scene scene)
        {
            return new Dictionary<string, object>
            {
                ["scene_name"] = scene.name,
                ["loaded_objects"] = scene.rootCount,
                ["is_loaded"] = scene.isLoaded
            };
        }

        private static ProceduralObjective CreateObjectiveByType(string type, string difficulty, GameState state, int index)
        {
            return new ProceduralObjective
            {
                name = $"{type}_objective_{index}",
                type = type,
                position = new Vector3(UnityEngine.Random.Range(-50f, 50f), 0f, UnityEngine.Random.Range(-50f, 50f)),
                difficulty = difficulty == "easy" ? 0.5f : difficulty == "hard" ? 1.5f : 1.0f,
                rewards = new Dictionary<string, object> { ["xp"] = 100, ["gold"] = 50 },
                estimatedDuration = UnityEngine.Random.Range(60f, 300f)
            };
        }

        private static void AdaptObjectiveRewards(ProceduralObjective objective, float skillLevel)
        {
            // Adjust rewards based on skill level
            float multiplier = skillLevel < 30f ? 1.2f : skillLevel > 70f ? 0.8f : 1.0f;
            objective.experienceReward = Mathf.RoundToInt(objective.experienceReward * multiplier);
            objective.currencyReward = Mathf.RoundToInt(objective.currencyReward * multiplier);
        }

        private static Vector3 FindOptimalObjectivePosition(string mapName, string objectiveType)
        {
            // In a real implementation, this would analyze map layout
            return new Vector3(
                UnityEngine.Random.Range(-30f, 30f),
                0f,
                UnityEngine.Random.Range(-30f, 30f)
            );
        }

        private static void CreateObjectiveIndicator(GameObject objectiveGO, ProceduralObjective objective)
        {
            // Create a simple visual indicator
            var indicator = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            indicator.transform.parent = objectiveGO.transform;
            indicator.transform.localPosition = Vector3.up * 2f;
            indicator.transform.localScale = Vector3.one * 0.5f;
            
            var renderer = indicator.GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.material.color = objective.difficulty > 1.0f ? Color.red : 
                                        objective.difficulty < 0.8f ? Color.green : Color.yellow;
            }
        }

        private static void SetupTypeSpecificRewards(ProceduralObjective objective)
        {
            // Add type-specific rewards
            switch (objective.type.ToLower())
            {
                case "capture":
                    objective.experienceReward += 25;
                    break;
                case "defend":
                    objective.currencyReward += 10;
                    break;
                case "collect":
                    objective.experienceReward += 15;
                    objective.currencyReward += 5;
                    break;
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Component for handling objective behavior in the game world.
        /// </summary>
        public class ObjectiveComponent : MonoBehaviour
        {
            [SerializeField] private ProceduralObjective objectiveData;
            [SerializeField] private bool isCompleted = false;
            [SerializeField] private float startTime;

            public void Initialize(ProceduralObjective objective)
            {
                objectiveData = objective;
                startTime = Time.time;
                gameObject.name = $"Objective_{objective.name}";
            }

            public void CompleteObjective()
            {
                if (!isCompleted)
                {
                    isCompleted = true;
                    Debug.Log($"[ObjectiveComponent] Objective {objectiveData.name} completed!");
                    
                    // Award rewards
                    AwardRewards();
                    
                    // Trigger completion effects
                    OnObjectiveCompleted();
                }
            }

            private void AwardRewards()
            {
                // In a real implementation, this would integrate with player progression system
                Debug.Log($"[ObjectiveComponent] Awarded {objectiveData.experienceReward} XP and {objectiveData.currencyReward} currency");
            }

            private void OnObjectiveCompleted()
            {
                // Visual/audio feedback for completion
                var renderer = GetComponent<Renderer>();
                if (renderer != null)
                {
                    renderer.material.color = Color.green;
                }
            }

            public bool IsCompleted() => isCompleted;
            public ProceduralObjective GetObjectiveData() => objectiveData;
            public float GetElapsedTime() => Time.time - startTime;
        }
    }
}
