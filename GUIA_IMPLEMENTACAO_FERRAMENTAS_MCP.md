lyndra# Guia Completo para Implementação de Novas Ferramentas no Unity MCP Server

## Visão Geral da Arquitetura

O Unity MCP Server é composto por duas partes principais que se comunicam via TCP Socket:

1. **UnityMcpServer** (Python) - Servidor MCP que expõe ferramentas para clientes externos
2. **UnityMcpBridge** (C#) - Bridge no Unity Editor que executa comandos

### Fluxo de Comunicação
```
Cliente MCP → Python Server → TCP Socket (porta 6400) → Unity Bridge → Unity Editor
                                                                    ↓
Cliente MCP ← Python Server ← TCP Socket (porta 6400) ← Unity Bridge ← Unity Editor
```

## Estrutura de Arquivos

### Lado Python (UnityMcpServer/src/)
```
src/
├── server.py              # Servidor principal FastMCP
├── config.py              # Configurações do servidor
├── unity_connection.py    # Gerenciamento da conexão TCP
├── tools/                 # Diretório das ferramentas
│   ├── __init__.py       # Registro de todas as ferramentas
│   ├── manage_editor.py  # Exemplo de ferramenta
│   └── nova_ferramenta.py # Nova ferramenta
└── pyproject.toml         # Dependências Python
```

### Lado Unity (UnityMcpBridge/Editor/)
```
Editor/
├── UnityMcpBridge.cs     # Bridge principal com TCP listener
├── Models/
│   └── Command.cs        # Modelo de comando
├── Helpers/
│   └── Response.cs       # Helpers para respostas
└── Tools/
    ├── CommandRegistry.cs # Registro de handlers
    ├── ManageEditor.cs   # Exemplo de handler
    └── NovaFerramenta.cs # Novo handler
```

## Passo a Passo para Implementar Nova Ferramenta

### 1. Criar Ferramenta Python

Crie um arquivo `tools/nova_ferramenta.py`:

```python
from mcp.server.fastmcp import FastMCP, Context
from typing import Dict, Any, List, Optional
from unity_connection import get_unity_connection

def register_nova_ferramenta_tools(mcp: FastMCP):
    """Register nova ferramenta tools with the MCP server."""

    @mcp.tool()
    def nova_ferramenta(
        ctx: Context,
        action: str,
        # Adicione parâmetros específicos da sua ferramenta
        parametro1: str = None,
        parametro2: int = None,
        parametro3: bool = None,
        parametro4: List[str] = None,
        parametro5: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Descrição da nova ferramenta.

        Args:
            action: Operação a ser executada (ex: 'create', 'modify', 'delete')
            parametro1: Descrição do parâmetro 1
            parametro2: Descrição do parâmetro 2
            parametro3: Descrição do parâmetro 3
            parametro4: Lista de strings
            parametro5: Dicionário com dados adicionais

        Returns:
            Dictionary com resultados ('success', 'message', 'data')
        """
        try:
            # Preparar parâmetros, removendo valores None
            params = {
                "action": action,
                "parametro1": parametro1,
                "parametro2": parametro2,
                "parametro3": parametro3,
                "parametro4": parametro4,
                "parametro5": parametro5
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            # Enviar comando para Unity
            response = get_unity_connection().send_command("nova_ferramenta", params)

            # Processar resposta
            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Operação realizada com sucesso."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Erro desconhecido.")
                }

        except Exception as e:
            return {"success": False, "message": f"Erro Python: {str(e)}"}
```

### 2. Registrar Ferramenta Python

Edite `tools/__init__.py` para incluir sua nova ferramenta:

```python
from .nova_ferramenta import register_nova_ferramenta_tools
# ... outras importações

def register_all_tools(mcp):
    """Register all refactored tools with the MCP server."""
    print("Registering Unity MCP Server refactored tools...")
    # ... outros registros
    register_nova_ferramenta_tools(mcp)
    print("Unity MCP Server tool registration complete.")
```

### 3. Criar Handler Unity (C#)

Crie um arquivo `Tools/NovaFerramenta.cs`:

```csharp
using System;
using System.Collections.Generic;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEngine;
using UnityMcpBridge.Editor.Helpers;

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// Handles operations for nova ferramenta.
    /// </summary>
    public static class NovaFerramenta
    {
        /// <summary>
        /// Main handler for nova ferramenta actions.
        /// </summary>
        // Define a lista de ações válidas
        private static readonly List<string> ValidActions = new List<string>
        {
            "create",
            "modify", 
            "delete",
            "list",
            "get_info"
        };

        public static object HandleCommand(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            // Verificar se a ação é válida antes do switch
            if (!ValidActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidActions);
                return Response.Error(
                    $"Unknown action: '{action}'. Valid actions are: {validActionsList}"
                );
            }

            // Parâmetros comuns
            string parametro1 = @params["parametro1"]?.ToString();

            try
            {
                switch (action)
                {
                    case "create":
                        return CreateSomething(@params);
                    case "modify":
                        return ModifySomething(parametro1, @params["parametro5"] as JObject);
                    case "delete":
                        return DeleteSomething(parametro1);
                    case "list":
                        return ListSomething(@params);
                    case "get_info":
                        return GetInfo(
                            parametro1,
                            @params["generatePreview"]?.ToObject<bool>() ?? false
                        );
                    default:
                        // Esta mensagem de erro é menos provável de ser atingida agora
                        string validActionsListDefault = string.Join(", ", ValidActions);
                        return Response.Error(
                            $"Unknown action: '{action}'. Valid actions are: {validActionsListDefault}"
                        );
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[NovaFerramenta] Action '{action}' failed for parameter '{parametro1}': {e}");
                return Response.Error(
                    $"Internal error processing action '{action}' on '{parametro1}': {e.Message}"
                );
            }
        }

        private static object CreateSomething(string param1, int? param2, bool? param3)
        {
            // Implementar lógica de criação
            try
            {
                // Sua lógica aqui
                return Response.Success("Item criado com sucesso.", new { id = "123", name = param1 });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao criar: {e.Message}");
            }
        }

        private static object ModifySomething(string param1, string[] param4)
        {
            // Implementar lógica de modificação
            try
            {
                // Sua lógica aqui
                return Response.Success("Item modificado com sucesso.");
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao modificar: {e.Message}");
            }
        }

        private static object DeleteSomething(string param1)
        {
            // Implementar lógica de exclusão
            try
            {
                // Sua lógica aqui
                return Response.Success("Item excluído com sucesso.");
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao excluir: {e.Message}");
            }
        }

        private static object ListSomething(JObject filters)
        {
            // Implementar lógica de listagem
            try
            {
                // Sua lógica aqui
                var items = new[] { 
                    new { id = "1", name = "Item 1" },
                    new { id = "2", name = "Item 2" }
                };
                return Response.Success("Lista obtida com sucesso.", items);
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao listar: {e.Message}");
            }
        }
    }
}
```

### 4. Registrar Handler Unity

Edite `Tools/CommandRegistry.cs` para incluir seu novo handler:

```csharp
private static readonly Dictionary<string, Func<JObject, object>> _handlers = new()
{
    // ... handlers existentes
    { "HandleNovaFerramenta", NovaFerramenta.HandleCommand },
};
```

### 5. Adicionar Roteamento no Bridge

Edite `UnityMcpBridge.cs` no método `ExecuteCommand` para incluir sua nova ferramenta:

```csharp
object result = command.type switch
{
    // ... casos existentes
    "nova_ferramenta" => NovaFerramenta.HandleCommand(paramsObject),
    _ => throw new ArgumentException($"Unknown command type: {command.type}")
};
```

## Padrões e Convenções

### Nomenclatura
- **Python**: `snake_case` para nomes de ferramentas e parâmetros
- **C#**: `PascalCase` para classes e métodos, `camelCase` para parâmetros JSON
- **Comandos**: Use o mesmo nome em ambos os lados (ex: `nova_ferramenta`)

## Padrões Avançados de Implementação

### 1. Validação de Ações com Lista Estática

Sempre defina uma lista de ações válidas para melhor manutenibilidade:

```csharp
private static readonly List<string> ValidActions = new List<string>
{
    "create",
    "modify",
    "delete",
    "search",
    "get_info"
};

// No HandleCommand:
if (!ValidActions.Contains(action))
{
    string validActionsList = string.Join(", ", ValidActions);
    return Response.Error(
        $"Unknown action: '{action}'. Valid actions are: {validActionsList}"
    );
}
```

### 2. Métodos Auxiliares para Validação e Sanitização

```csharp
/// <summary>
/// Sanitiza e normaliza paths de assets.
/// </summary>
private static string SanitizeAssetPath(string path)
{
    if (string.IsNullOrEmpty(path)) return string.Empty;
    
    // Normalizar separadores e remover prefixos desnecessários
    path = path.Replace('\\', '/').Trim();
    if (path.StartsWith("Assets/")) return path;
    if (path.StartsWith("/")) path = path.Substring(1);
    if (!path.StartsWith("Assets/")) path = "Assets/" + path;
    
    return path;
}

/// <summary>
/// Verifica se um asset existe no caminho especificado.
/// </summary>
private static bool AssetExists(string sanitizedPath)
{
    if (!string.IsNullOrEmpty(AssetDatabase.AssetPathToGUID(sanitizedPath)))
        return true;
        
    // Verificar diretórios explicitamente
    if (Directory.Exists(Path.Combine(Directory.GetCurrentDirectory(), sanitizedPath)))
        return AssetDatabase.IsValidFolder(sanitizedPath);
        
    return File.Exists(Path.Combine(Directory.GetCurrentDirectory(), sanitizedPath));
}
```

### 3. Aplicação Genérica de Propriedades com Reflection

```csharp
/// <summary>
/// Helper genérico para definir propriedades em qualquer UnityEngine.Object usando reflection.
/// </summary>
private static bool ApplyObjectProperties(UnityEngine.Object target, JObject properties)
{
    if (target == null || properties == null) return false;
    
    bool modified = false;
    Type type = target.GetType();

    foreach (var prop in properties.Properties())
    {
        string propName = prop.Name;
        JToken propValue = prop.Value;
        if (SetPropertyOrField(target, propName, propValue, type))
        {
            modified = true;
        }
    }
    return modified;
}

/// <summary>
/// Define uma propriedade ou campo via reflection, tratando tipos básicos e objetos Unity.
/// </summary>
private static bool SetPropertyOrField(object target, string memberName, JToken value, Type type = null)
{
    type = type ?? target.GetType();
    var flags = System.Reflection.BindingFlags.Public | 
                System.Reflection.BindingFlags.Instance | 
                System.Reflection.BindingFlags.IgnoreCase;

    try
    {
        // Tentar propriedade primeiro
        var propInfo = type.GetProperty(memberName, flags);
        if (propInfo != null && propInfo.CanWrite)
        {
            object convertedValue = ConvertJTokenToType(value, propInfo.PropertyType);
            if (convertedValue != null && !object.Equals(propInfo.GetValue(target), convertedValue))
            {
                propInfo.SetValue(target, convertedValue);
                return true;
            }
        }
        else
        {
            // Tentar campo se propriedade não encontrada
            var fieldInfo = type.GetField(memberName, flags);
            if (fieldInfo != null)
            {
                object convertedValue = ConvertJTokenToType(value, fieldInfo.FieldType);
                if (convertedValue != null && !object.Equals(fieldInfo.GetValue(target), convertedValue))
                {
                    fieldInfo.SetValue(target, convertedValue);
                    return true;
                }
            }
        }
    }
    catch (Exception ex)
    {
        Debug.LogWarning($"[SetPropertyOrField] Failed to set '{memberName}' on {type.Name}: {ex.Message}");
    }
    return false;
}
```

### 4. Conversão Robusta de Tipos

```csharp
/// <summary>
/// Conversão de JToken para Type para tipos comuns Unity e primitivos.
/// </summary>
private static object ConvertJTokenToType(JToken token, Type targetType)
{
    try
    {
        if (token == null || token.Type == JTokenType.Null) return null;

        // Tipos primitivos
        if (targetType == typeof(string)) return token.ToObject<string>();
        if (targetType == typeof(int)) return token.ToObject<int>();
        if (targetType == typeof(float)) return token.ToObject<float>();
        if (targetType == typeof(bool)) return token.ToObject<bool>();
        
        // Tipos Unity comuns
        if (targetType == typeof(Vector2) && token is JArray arrV2 && arrV2.Count == 2)
            return new Vector2(arrV2[0].ToObject<float>(), arrV2[1].ToObject<float>());
        if (targetType == typeof(Vector3) && token is JArray arrV3 && arrV3.Count == 3)
            return new Vector3(arrV3[0].ToObject<float>(), arrV3[1].ToObject<float>(), arrV3[2].ToObject<float>());
        if (targetType == typeof(Color) && token is JArray arrC && arrC.Count >= 3)
            return new Color(arrC[0].ToObject<float>(), arrC[1].ToObject<float>(), arrC[2].ToObject<float>(), 
                           arrC.Count > 3 ? arrC[3].ToObject<float>() : 1.0f);
        
        // Enums
        if (targetType.IsEnum)
            return Enum.Parse(targetType, token.ToString(), true);

        // Assets Unity por path
        if (typeof(UnityEngine.Object).IsAssignableFrom(targetType) && token.Type == JTokenType.String)
        {
            string assetPath = SanitizeAssetPath(token.ToString());
            return AssetDatabase.LoadAssetAtPath(assetPath, targetType);
        }

        // Fallback: conversão direta
        return token.ToObject(targetType);
    }
    catch (Exception ex)
    {
        Debug.LogWarning($"[ConvertJTokenToType] Could not convert JToken '{token}' to type '{targetType.Name}': {ex.Message}");
        return null;
    }
}
```

### 5. Geração de Dados de Asset com Preview

```csharp
/// <summary>
/// Cria uma representação serializável de um asset com preview opcional.
/// </summary>
private static object GetAssetData(string path, bool generatePreview = false)
{
    if (string.IsNullOrEmpty(path) || !AssetExists(path)) return null;

    string guid = AssetDatabase.AssetPathToGUID(path);
    Type assetType = AssetDatabase.GetMainAssetTypeAtPath(path);
    UnityEngine.Object asset = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(path);
    string previewBase64 = null;
    int previewWidth = 0, previewHeight = 0;

    if (generatePreview && asset != null)
    {
        Texture2D preview = AssetPreview.GetAssetPreview(asset);
        if (preview != null)
        {
            try
            {
                // Criar cópia legível para EncodeToPNG
                RenderTexture rt = RenderTexture.GetTemporary(preview.width, preview.height);
                Graphics.Blit(preview, rt);
                RenderTexture previous = RenderTexture.active;
                RenderTexture.active = rt;
                Texture2D readablePreview = new Texture2D(preview.width, preview.height);
                readablePreview.ReadPixels(new Rect(0, 0, rt.width, rt.height), 0, 0);
                readablePreview.Apply();
                RenderTexture.active = previous;
                RenderTexture.ReleaseTemporary(rt);

                byte[] pngData = readablePreview.EncodeToPNG();
                previewBase64 = Convert.ToBase64String(pngData);
                previewWidth = readablePreview.width;
                previewHeight = readablePreview.height;
                UnityEngine.Object.DestroyImmediate(readablePreview);
            }
            catch (Exception ex)
            {
                Debug.LogWarning($"Failed to generate preview for '{path}': {ex.Message}");
            }
        }
    }

    return new
    {
        path = path,
        guid = guid,
        assetType = assetType?.FullName ?? "Unknown",
        name = Path.GetFileNameWithoutExtension(path),
        fileName = Path.GetFileName(path),
        isFolder = AssetDatabase.IsValidFolder(path),
        instanceID = asset?.GetInstanceID() ?? 0,
        lastWriteTimeUtc = File.GetLastWriteTimeUtc(Path.Combine(Directory.GetCurrentDirectory(), path)).ToString("o"),
        previewBase64 = previewBase64,
        previewWidth = previewWidth,
        previewHeight = previewHeight
    };
}
```

### Estrutura de Resposta Padrão

**Sucesso:**
```json
{
    "success": true,
    "message": "Operação realizada com sucesso.",
    "data": { /* dados opcionais */ }
}
```

**Erro:**
```json
{
    "success": false,
    "error": "Mensagem de erro detalhada"
}
```

### Tratamento de Parâmetros

1. **Python**: Remova valores `None` antes de enviar
2. **C#**: Use `?.ToObject<T>()` para conversões seguras
3. **Validação**: Sempre valide parâmetros obrigatórios
4. **Defaults**: Defina valores padrão quando apropriado

### Tratamento de Erros

1. **Try-Catch**: Sempre envolva operações em try-catch
2. **Logging**: Use `Debug.LogError` no Unity para debugging
3. **Mensagens**: Forneça mensagens de erro claras e específicas
4. **Contexto**: Inclua informações relevantes no erro

### 6. Padrões de Tratamento de Erros e Logging

```csharp
/// <summary>
/// Padrão para logging detalhado com contexto.
/// </summary>
private static void LogOperation(string operation, string details, bool isError = false)
{
    string message = $"[{typeof(ManageAsset).Name}] {operation}: {details}";
    if (isError)
        Debug.LogError(message);
    else
        Debug.Log(message);
}

/// <summary>
/// Wrapper para operações que podem falhar com logging automático.
/// </summary>
private static Response SafeExecute(string operationName, Func<Response> operation)
{
    try
    {
        LogOperation(operationName, "Starting operation");
        var result = operation();
        
        if (result.success)
            LogOperation(operationName, "Operation completed successfully");
        else
            LogOperation(operationName, $"Operation failed: {result.message}", true);
            
        return result;
    }
    catch (Exception ex)
    {
        string errorMsg = $"Exception in {operationName}: {ex.Message}";
        LogOperation(operationName, errorMsg, true);
        return Response.Error(errorMsg);
    }
}

// Exemplo de uso:
public static Response HandleCommand(string jsonCommand)
{
    return SafeExecute("HandleCommand", () => {
        var request = JsonConvert.DeserializeObject<JObject>(jsonCommand);
        string action = request["action"]?.ToString();
        
        if (!ValidActions.Contains(action))
        {
            return Response.Error($"Invalid action: {action}");
        }
        
        // Processar ação...
        return Response.Success("Action completed");
    });
}
```

### 7. Validação Robusta de Parâmetros

```csharp
/// <summary>
/// Classe helper para validação de parâmetros de entrada.
/// </summary>
public static class ParameterValidator
{
    public static bool ValidateRequired(JObject request, string[] requiredFields, out string errorMessage)
    {
        errorMessage = null;
        foreach (string field in requiredFields)
        {
            if (request[field] == null || string.IsNullOrEmpty(request[field].ToString()))
            {
                errorMessage = $"Missing required parameter: '{field}'";
                return false;
            }
        }
        return true;
    }
    
    public static T GetParameter<T>(JObject request, string paramName, T defaultValue = default(T))
    {
        try
        {
            var token = request[paramName];
            if (token == null) return defaultValue;
            return token.ToObject<T>();
        }
        catch
        {
            return defaultValue;
        }
    }
    
    public static bool ValidateAssetPath(string path, out string sanitizedPath, out string errorMessage)
    {
        errorMessage = null;
        sanitizedPath = SanitizeAssetPath(path);
        
        if (string.IsNullOrEmpty(sanitizedPath))
        {
            errorMessage = "Asset path cannot be empty";
            return false;
        }
        
        if (!sanitizedPath.StartsWith("Assets/"))
        {
            errorMessage = "Asset path must start with 'Assets/'";
            return false;
        }
        
        return true;
    }
}

// Exemplo de uso:
private static Response CreateAsset(JObject request)
{
    // Validar parâmetros obrigatórios
    if (!ParameterValidator.ValidateRequired(request, new[] { "path", "type" }, out string error))
    {
        return Response.Error(error);
    }
    
    // Validar e sanitizar path
    string rawPath = request["path"].ToString();
    if (!ParameterValidator.ValidateAssetPath(rawPath, out string sanitizedPath, out string pathError))
    {
        return Response.Error(pathError);
    }
    
    // Obter parâmetros opcionais com valores padrão
    string assetType = ParameterValidator.GetParameter<string>(request, "type", "Material");
    bool overwrite = ParameterValidator.GetParameter<bool>(request, "overwrite", false);
    
    // Continuar com a lógica...
}
```

## Tipos de Dados Suportados

### Tipos Simples
- `string` → `string`
- `int` → `int`
- `float` → `float`
- `bool` → `bool`

### Tipos Complexos
- `List[T]` → `T[]` (arrays)
- `Dict[str, Any]` → `JObject`
- `Optional[T]` → `T?` (nullable)

### Codificação Especial
- **Conteúdo de texto grande**: Use Base64 encoding
- **Paths**: Sempre use forward slashes `/`
- **Assets**: Use paths relativos a `Assets/`

### 8. Padrões de Performance e Otimização

```csharp
/// <summary>
/// Gerenciador de operações em lote para otimizar performance.
/// </summary>
public static class BatchOperationManager
{
    private static bool _batchMode = false;
    private static List<string> _modifiedPaths = new List<string>();
    
    public static void StartBatch()
    {
        _batchMode = true;
        _modifiedPaths.Clear();
        AssetDatabase.StartAssetEditing();
    }
    
    public static void EndBatch()
    {
        if (_batchMode)
        {
            AssetDatabase.StopAssetEditing();
            
            // Refresh apenas os paths modificados
            if (_modifiedPaths.Count > 0)
            {
                foreach (string path in _modifiedPaths)
                {
                    AssetDatabase.ImportAsset(path);
                }
                _modifiedPaths.Clear();
            }
            
            _batchMode = false;
        }
    }
    
    public static void RegisterModifiedPath(string path)
    {
        if (_batchMode && !_modifiedPaths.Contains(path))
        {
            _modifiedPaths.Add(path);
        }
        else if (!_batchMode)
        {
            // Refresh imediato se não estiver em modo batch
            AssetDatabase.ImportAsset(path);
        }
    }
}

/// <summary>
/// Padrão para operações que modificam múltiplos assets.
/// </summary>
private static Response ProcessMultipleAssets(JArray assetPaths, Func<string, Response> operation)
{
    if (assetPaths == null || assetPaths.Count == 0)
    {
        return Response.Error("No asset paths provided");
    }
    
    var results = new List<object>();
    var errors = new List<string>();
    
    // Usar batch mode para múltiplos assets
    bool useBatch = assetPaths.Count > 1;
    if (useBatch) BatchOperationManager.StartBatch();
    
    try
    {
        foreach (var pathToken in assetPaths)
        {
            string path = pathToken.ToString();
            var result = operation(path);
            
            if (result.success)
            {
                results.Add(new { path = path, success = true, data = result.data });
            }
            else
            {
                errors.Add($"{path}: {result.message}");
                results.Add(new { path = path, success = false, error = result.message });
            }
        }
    }
    finally
    {
        if (useBatch) BatchOperationManager.EndBatch();
    }
    
    return Response.Success(new
    {
        processed = results.Count,
        successful = results.Count - errors.Count,
        errors = errors.Count,
        results = results,
        errorDetails = errors
    });
}
```

### 9. Padrões de Cache e Memoização

```csharp
/// <summary>
/// Cache simples para operações custosas como AssetPreview.
/// </summary>
public static class AssetCache
{
    private static Dictionary<string, object> _cache = new Dictionary<string, object>();
    private static Dictionary<string, DateTime> _cacheTimestamps = new Dictionary<string, DateTime>();
    private static readonly TimeSpan CacheExpiry = TimeSpan.FromMinutes(5);
    
    public static T GetOrCompute<T>(string key, Func<T> computeFunction) where T : class
    {
        // Verificar se existe e não expirou
        if (_cache.ContainsKey(key) && _cacheTimestamps.ContainsKey(key))
        {
            if (DateTime.Now - _cacheTimestamps[key] < CacheExpiry)
            {
                return _cache[key] as T;
            }
            else
            {
                // Expirou, remover
                _cache.Remove(key);
                _cacheTimestamps.Remove(key);
            }
        }
        
        // Computar e cachear
        T result = computeFunction();
        if (result != null)
        {
            _cache[key] = result;
            _cacheTimestamps[key] = DateTime.Now;
        }
        
        return result;
    }
    
    public static void ClearCache()
    {
        _cache.Clear();
        _cacheTimestamps.Clear();
    }
    
    public static void InvalidateKey(string key)
    {
        _cache.Remove(key);
        _cacheTimestamps.Remove(key);
    }
}

// Exemplo de uso com preview de assets:
private static object GetAssetDataCached(string path, bool generatePreview = false)
{
    string cacheKey = $"asset_data_{path}_{generatePreview}";
    return AssetCache.GetOrCompute(cacheKey, () => GetAssetData(path, generatePreview));
}
```

## Exemplos de Ferramentas Existentes

### Ferramenta Simples (manage_editor)
- Controle básico do editor (play/pause/stop)
- Parâmetros simples
- Respostas diretas

### Ferramenta Complexa (manage_gameobject)
- Múltiplas ações (create, modify, delete, find)
- Parâmetros complexos (listas, dicionários)
- Processamento de componentes

### Ferramenta com Encoding (manage_script)
- Manipulação de conteúdo de arquivo
- Base64 encoding para evitar problemas JSON
- Validação de nomes e paths

### 10. Boas Práticas de Documentação e Comentários

```csharp
/// <summary>
/// [DESCRIÇÃO CLARA] - Gerencia operações de assets no Unity via MCP.
/// 
/// Suporta as seguintes ações:
/// - create: Cria novos assets (materiais, ScriptableObjects, pastas)
/// - modify: Modifica propriedades de assets existentes
/// - delete: Remove assets do projeto
/// - search: Busca assets por critérios
/// - get_info: Obtém informações detalhadas de um asset
/// 
/// [LIMITAÇÕES CONHECIDAS]:
/// - Prefabs requerem um GameObject fonte existente
/// - Preview de assets pode ser custoso para assets grandes
/// - Operações em lote são recomendadas para múltiplos assets
/// 
/// [DEPENDÊNCIAS]:
/// - UnityEditor.AssetDatabase
/// - Newtonsoft.Json
/// - UnityEngine.Rendering (para configurações gráficas)
/// </summary>
public static class ManageAsset
{
    // TODO: Adicionar suporte para mais tipos de assets
    // TODO: Implementar validação de propriedades específicas por tipo
    // TODO: Adicionar cache para operações de busca frequentes
    
    /// <summary>
    /// [PONTO DE ENTRADA] - Processa comandos JSON do servidor MCP.
    /// 
    /// Formato esperado do JSON:
    /// {
    ///   "action": "create|modify|delete|search|get_info",
    ///   "path": "Assets/caminho/para/asset",
    ///   "type": "Material|ScriptableObject|Folder", // para create
    ///   "properties": { ... }, // para modify
    ///   "query": "termo de busca", // para search
    ///   "generatePreview": true|false // para get_info
    /// }
    /// </summary>
    /// <param name="jsonCommand">Comando JSON serializado</param>
    /// <returns>Response com resultado da operação</returns>
    public static Response HandleCommand(string jsonCommand)
    {
        // Implementação...
    }
    
    /// <summary>
    /// [HELPER CRÍTICO] - Cria novos assets no projeto.
    /// 
    /// ATENÇÃO: Esta operação modifica o AssetDatabase.
    /// Certifique-se de que o path de destino é válido e não existe.
    /// 
    /// Tipos suportados:
    /// - "Material": Cria material com shader padrão
    /// - "ScriptableObject": Requer 'scriptType' nas propriedades
    /// - "Folder": Cria diretório no projeto
    /// 
    /// PERFORMANCE: Use BatchOperationManager para múltiplas criações.
    /// </summary>
    /// <param name="request">Objeto JSON com parâmetros</param>
    /// <returns>Response indicando sucesso/falha</returns>
    private static Response CreateAsset(JObject request)
    {
        // TODO: Adicionar validação de tipos de ScriptableObject
        // TODO: Implementar templates para diferentes tipos de assets
        
        // Implementação...
    }
}
```

### 11. Padrões de Comentários para TODOs e FIXMEs

```csharp
// TODO: [PRIORIDADE] [DESCRIÇÃO] - [CONTEXTO ADICIONAL]
// TODO: HIGH - Implementar suporte para Prefabs - Requer GameObject fonte
// TODO: MEDIUM - Adicionar cache para AssetPreview - Melhoria de performance
// TODO: LOW - Suporte para assets customizados - Extensibilidade futura

// FIXME: [PROBLEMA] - [IMPACTO] - [SOLUÇÃO TEMPORÁRIA]
// FIXME: AssetPreview pode falhar em assets corrompidos - Causa crash - Adicionado try-catch

// HACK: [RAZÃO] - [LIMITAÇÃO] - [PLANO FUTURO]
// HACK: Usando reflection para propriedades - Unity não expõe API direta - Aguardar Unity 2024.x

// NOTE: [INFORMAÇÃO IMPORTANTE] - [CONTEXTO]
// NOTE: AssetDatabase.Refresh() é custoso - Usar apenas quando necessário

// WARNING: [CUIDADO] - [CONSEQUÊNCIA]
// WARNING: Modificar assets durante play mode pode causar inconsistências
```

## Debugging e Testes

### Logs Python
```python
import logging
logger = logging.getLogger("unity-mcp-server")
logger.info("Mensagem informativa")
logger.error("Mensagem de erro")
```

### Logs Unity
```csharp
Debug.Log("Mensagem informativa");
Debug.LogWarning("Mensagem de aviso");
Debug.LogError("Mensagem de erro");
```

### Teste de Conexão
Use o comando `ping` para verificar se a conexão está funcionando:

```python
response = get_unity_connection().send_command("ping")
print(response)  # Deve retornar {"message": "pong"}
```

## Checklist de Implementação

- [ ] Criar arquivo Python da ferramenta
- [ ] Registrar ferramenta no `__init__.py`
- [ ] Criar handler C# no Unity
- [ ] Registrar handler no `CommandRegistry.cs`
- [ ] Adicionar roteamento no `UnityMcpBridge.cs`
- [ ] Testar conexão com ping
- [ ] Testar cada ação da ferramenta
- [ ] Validar tratamento de erros
- [ ] Documentar parâmetros e retornos
- [ ] Adicionar logs para debugging

## Configurações Importantes

### Porta TCP
- **Padrão**: 6400
- **Configurável**: `config.py` (Python) e `UnityMcpBridge.cs` (Unity)

### Timeouts
- **Conexão**: 24 horas (configurável em `config.py`)
- **Buffer**: 16MB (configurável em `config.py`)

### Dependências Python
- `mcp[cli]>=1.4.1`
- `httpx>=0.27.2`
- `Newtonsoft.Json` (Unity)

## Troubleshooting Comum

### Erro de Conexão
1. Verificar se Unity está rodando
2. Verificar se porta 6400 está livre
3. Verificar logs do Unity Console

### Erro de JSON
1. Validar estrutura de parâmetros
2. Verificar encoding de strings
3. Usar Base64 para conteúdo complexo

### Erro de Handler
1. Verificar registro no CommandRegistry
2. Verificar roteamento no ExecuteCommand
3. Verificar nome do comando (case-sensitive)

Este guia fornece uma base completa para implementar novas ferramentas no Unity MCP Server de forma consistente e sem erros.