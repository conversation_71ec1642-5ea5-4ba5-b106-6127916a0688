from .manage_script import register_manage_script_tools
from .manage_script import register_manage_script_tools
from .manage_scene import register_manage_scene_tools
from .manage_editor import register_manage_editor_tools
from .manage_gameobject import register_manage_gameobject_tools
from .manage_asset import register_manage_asset_tools
from .read_console import register_read_console_tools
from .execute_menu_item import register_execute_menu_item_tools
from .audio_system import register_audio_system_tools
from .lighting_rendering import register_lighting_rendering_tools
from .multiplayer_networking import register_multiplayer_networking_tools
from .ai_runtime import register_ai_runtime_tools
from .animation_runtime import register_animation_runtime_tools
from .xr_runtime import register_xr_runtime_tools
from .asset_processing import register_asset_processing_tools
from .project_code_operations import register_project_code_operations_tools
from .unity_testing import register_unity_testing_tools
from .multiplayer_sessions import register_multiplayer_sessions_tools
from .build_profiles_runtime import register_build_profiles_runtime_tools
from .advanced_audio import register_advanced_audio_tools
from .advanced_physics import register_advanced_physics_tools
from .webgpu_runtime import register_webgpu_runtime_tools
from .advanced_rendering import register_advanced_rendering_tools
from .raytracing_operations import register_raytracing_operations_tools
from .advanced_ai import register_advanced_ai_tools
from .ui_toolkit_runtime import register_ui_toolkit_runtime_tools
from .project_auditor_runtime import register_project_auditor_runtime_tools
from .toolkit_2d_runtime import register_2d_toolkit_runtime_tools
from .input_system_runtime import register_input_system_runtime_tools
from .navigation_enhanced import register_navigation_enhanced_tools
from .water_system_runtime import register_water_system_runtime_tools
from .variable_rate_shading import register_variable_rate_shading_tools
from .deferred_plus_rendering import register_deferred_plus_rendering_tools
from .project_auditor_enhanced import register_project_auditor_enhanced_tools
from .inference_neural_network import register_inference_neural_network_tools
from .graphics_state_collection import register_graphics_state_collection_tools
from .bicubic_lightmap import register_bicubic_lightmap_tools
from .vfx_shader_graph_control import register_vfx_shader_graph_control_tools
from .editor_build_tools import register_editor_build_tools
from .ai_asset_generation import register_ai_asset_generation_tools
from .generative_2d_world import register_generative_2d_world_tools
from .advanced_procedural_generation import register_advanced_procedural_generation_tools
from .procedural_character_assembly import register_procedural_character_assembly_tools
from .dynamic_game_systems import register_dynamic_game_systems_tools
from .procedural_narrative import register_procedural_narrative_tools
from .generative_game_economy import register_generative_game_economy_tools
from .world_simulation_systems import register_world_simulation_systems_tools
from .automated_cinematics import register_automated_cinematics_tools
from .unity_ai_assistant import register_unity_ai_assistant_tools
from .behavior_ai_generation import register_behavior_ai_generation_tools
from .unity_cloud_ai import register_unity_cloud_ai_tools


from .procedural_body_part_generation import register_procedural_body_part_generation_tools
from .procedural_texture_generation import register_procedural_texture_generation_tools
from .procedural_skeleton_generation import register_procedural_skeleton_generation_tools
from .procedural_animation_generation import register_procedural_animation_generation_tools
from .character_gameplay_system import register_character_gameplay_system_tools
from .one_click_character_creator import register_one_click_character_creator_tools

def register_all_tools(mcp):
    """Register all refactored tools with the MCP server."""
    print("Registering Unity MCP Server tools...")
    
    # Ferramentas básicas de gerenciamento
    register_manage_asset_tools(mcp)
    register_manage_editor_tools(mcp)
    register_manage_gameobject_tools(mcp)

    register_manage_scene_tools(mcp)
    register_manage_script_tools(mcp)
    
    # Ferramentas de geração procedural de personagens (Unity 6.2)
    register_procedural_character_assembly_tools(mcp)
    register_procedural_body_part_generation_tools(mcp)
    register_procedural_texture_generation_tools(mcp)
    register_procedural_skeleton_generation_tools(mcp)
    register_procedural_animation_generation_tools(mcp)
    register_character_gameplay_system_tools(mcp)
    
    # Ferramenta MASTER - One-Click Character Creator
    register_one_click_character_creator_tools(mcp)
    
    print("Unity MCP Server tool registration complete.")
