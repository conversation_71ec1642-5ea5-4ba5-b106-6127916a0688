from mcp.server.fastmcp import FastMCP, Context
from typing import Dict, Any, List, Optional
from unity_connection import get_unity_connection

def register_lightmap_visualization_tools(mcp: FastMCP):
    """Register Lightmap Visualization Tools with the MCP server."""

    @mcp.tool()
    def lightmap_visualization_tools(
        ctx: Context,
        action: str,
        visualization_mode: Optional[str] = None,
        lightmap_index: Optional[int] = None,
        color_space: Optional[str] = None,
        intensity_scale: Optional[float] = None,
        show_uv_layout: Optional[bool] = None,
        highlight_seams: Optional[bool] = None,
        overlay_mode: Optional[str] = None,
        export_visualization: Optional[bool] = None
    ) -> Dict[str, Any]:
        """Ferramentas de Visualização de Lightmaps usando Unity 6.2 Scene View APIs.

        Funcionalidades:
        - show_lightmap_preview: Mostrar preview de lightmap
        - visualize_uv_layout: Visualizar layout UV
        - highlight_lightmap_seams: Destacar costuras
        - show_lightmap_density: Mostrar densidade de lightmap
        - compare_lightmap_quality: Comparar qualidade
        - export_lightmap_visualization: Exportar visualização

        Args:
            action: Operação a executar
            visualization_mode: Modo (preview, density, seams, comparison)
            lightmap_index: Índice do lightmap
            color_space: Espaço de cor (linear, gamma)
            intensity_scale: Escala de intensidade
            show_uv_layout: Mostrar layout UV
            highlight_seams: Destacar costuras
            overlay_mode: Modo de overlay (none, wireframe, density)
            export_visualization: Exportar como imagem

        Returns:
            Dictionary com resultados ('success', 'message', 'data')
        """
        try:
            params = {
                "action": action,
                "visualization_mode": visualization_mode,
                "lightmap_index": lightmap_index,
                "color_space": color_space,
                "intensity_scale": intensity_scale,
                "show_uv_layout": show_uv_layout,
                "highlight_seams": highlight_seams,
                "overlay_mode": overlay_mode,
                "export_visualization": export_visualization
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("lightmap_visualization_tools", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Lightmap visualization completed successfully."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Failed to execute lightmap visualization.")
                }

        except Exception as e:
            return {"success": False, "message": f"Python error in lightmap visualization: {str(e)}"}
