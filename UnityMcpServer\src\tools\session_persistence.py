from mcp.server.fastmcp import FastMC<PERSON>, Context
from typing import Dict, Any, List, Optional
from unity_connection import get_unity_connection

def register_session_persistence_tools(mcp: FastMCP):
    """Register Session Persistence tools with the MCP server."""

    @mcp.tool()
    def session_persistence(
        ctx: Context,
        action: str,
        session_id: Optional[str] = None,
        persistence_mode: Optional[str] = None,
        save_interval: Optional[int] = None,
        compression_enabled: Optional[bool] = None,
        encryption_enabled: Optional[bool] = None,
        backup_count: Optional[int] = None,
        storage_location: Optional[str] = None,
        session_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Sistema de Persistência de Sessão para MOBA AURACRON usando Unity 6.2 Cloud Save.

        Funcionalidades:
        - save_session: Salvar estado da sessão
        - load_session: Carregar sessão salva
        - delete_session: <PERSON><PERSON><PERSON> sessão
        - backup_session: Fazer backup da sessão
        - restore_session: Restaurar sessão
        - configure_persistence: Configurar persistência
        - get_session_info: Obter informações da sessão

        Args:
            action: Operação a executar
            session_id: ID da sessão
            persistence_mode: Modo (auto, manual, checkpoint)
            save_interval: Intervalo de salvamento em segundos
            compression_enabled: Habilitar compressão
            encryption_enabled: Habilitar criptografia
            backup_count: Número de backups a manter
            storage_location: Local de armazenamento (cloud, local)
            session_data: Dados da sessão

        Returns:
            Dictionary com resultados ('success', 'message', 'data')
        """
        try:
            params = {
                "action": action,
                "session_id": session_id,
                "persistence_mode": persistence_mode,
                "save_interval": save_interval,
                "compression_enabled": compression_enabled,
                "encryption_enabled": encryption_enabled,
                "backup_count": backup_count,
                "storage_location": storage_location,
                "session_data": session_data
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("session_persistence", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Session persistence operation completed successfully."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Failed to execute session persistence operation.")
                }

        except Exception as e:
            return {"success": False, "message": f"Python error in session persistence: {str(e)}"}
