from mcp.server.fastmcp import FastMC<PERSON>, Context
from typing import Dict, Any, List, Optional
from unity_connection import get_unity_connection

def register_session_migration_tools(mcp: FastMCP):
    """Register Session Migration tools with the MCP server."""

    @mcp.tool()
    def session_migration(
        ctx: Context,
        action: str,
        session_id: Optional[str] = None,
        target_server: Optional[str] = None,
        migration_type: Optional[str] = None,
        preserve_state: Optional[bool] = None,
        migration_timeout: Optional[int] = None,
        rollback_on_failure: Optional[bool] = None,
        notify_players: Optional[bool] = None,
        migration_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Sistema de Migração de Sessão para MOBA AURACRON usando Unity 6.2 Netcode.

        Funcionalidades:
        - migrate_session: Migrar sessão para novo servidor
        - prepare_migration: Preparar migração
        - validate_migration: Validar migração
        - rollback_migration: Reverter migração
        - get_migration_status: Obter status da migração
        - configure_migration_rules: Configurar regras de migração

        Args:
            action: Operação a executar
            session_id: ID da sessão
            target_server: Servidor de destino
            migration_type: Tipo (host_migration, server_migration, region_migration)
            preserve_state: Preservar estado da partida
            migration_timeout: Timeout em segundos
            rollback_on_failure: Reverter em caso de falha
            notify_players: Notificar jogadores
            migration_data: Dados específicos da migração

        Returns:
            Dictionary com resultados ('success', 'message', 'data')
        """
        try:
            params = {
                "action": action,
                "session_id": session_id,
                "target_server": target_server,
                "migration_type": migration_type,
                "preserve_state": preserve_state,
                "migration_timeout": migration_timeout,
                "rollback_on_failure": rollback_on_failure,
                "notify_players": notify_players,
                "migration_data": migration_data
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("session_migration", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Session migration completed successfully."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Failed to execute session migration.")
                }

        except Exception as e:
            return {"success": False, "message": f"Python error in session migration: {str(e)}"}
