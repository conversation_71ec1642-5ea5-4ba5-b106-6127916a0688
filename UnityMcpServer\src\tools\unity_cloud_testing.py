from mcp.server.fastmcp import Fast<PERSON><PERSON>, Context
from typing import Dict, Any, List, Optional
from unity_connection import get_unity_connection

def register_unity_cloud_testing_tools(mcp: FastMCP):
    """Register Unity Cloud Testing tools with the MCP server."""

    @mcp.tool()
    def unity_cloud_testing(
        ctx: Context,
        action: str,
        test_suite: Optional[str] = None,
        target_platforms: Optional[List[str]] = None,
        test_configuration: Optional[Dict[str, Any]] = None,
        parallel_execution: Optional[bool] = None,
        timeout_minutes: Optional[int] = None,
        device_requirements: Optional[Dict[str, Any]] = None,
        test_data: Optional[Dict[str, Any]] = None,
        notification_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Sistema de Testes na Nuvem usando Unity Cloud Build e Device Testing.

        Funcionalidades:
        - run_cloud_tests: Executar testes na nuvem
        - schedule_test_run: Agendar execução de testes
        - get_test_results: Obter resultados dos testes
        - configure_test_environment: Configurar ambiente de teste
        - manage_test_devices: Gerenciar dispositivos de teste
        - analyze_test_coverage: Analisar cobertura de testes

        Args:
            action: Operação a executar
            test_suite: Suite de testes a executar
            target_platforms: Plataformas alvo (Android, iOS, WebGL)
            test_configuration: Configuração dos testes
            parallel_execution: Execução paralela
            timeout_minutes: Timeout em minutos
            device_requirements: Requisitos de dispositivos
            test_data: Dados de teste
            notification_settings: Configurações de notificação

        Returns:
            Dictionary com resultados ('success', 'message', 'data')
        """
        try:
            params = {
                "action": action,
                "test_suite": test_suite,
                "target_platforms": target_platforms,
                "test_configuration": test_configuration,
                "parallel_execution": parallel_execution,
                "timeout_minutes": timeout_minutes,
                "device_requirements": device_requirements,
                "test_data": test_data,
                "notification_settings": notification_settings
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("unity_cloud_testing", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Cloud testing operation completed successfully."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Failed to execute cloud testing operation.")
                }

        except Exception as e:
            return {"success": False, "message": f"Python error in unity cloud testing: {str(e)}"}
