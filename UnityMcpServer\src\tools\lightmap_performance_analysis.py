from mcp.server.fastmcp import FastMCP, Context
from typing import Dict, Any, List, Optional
from unity_connection import get_unity_connection

def register_lightmap_performance_analysis_tools(mcp: FastMCP):
    """Register Lightmap Performance Analysis tools with the MCP server."""

    @mcp.tool()
    def lightmap_performance_analysis(
        ctx: Context,
        action: str,
        scene_path: Optional[str] = None,
        analysis_type: Optional[str] = None,
        performance_metrics: Optional[List[str]] = None,
        target_platform: Optional[str] = None,
        optimization_level: Optional[str] = None,
        memory_budget: Optional[int] = None,
        quality_target: Optional[str] = None,
        generate_report: Optional[bool] = None
    ) -> Dict[str, Any]:
        """Análise de Performance de Lightmaps usando Unity 6.2 Profiler APIs.

        Funcionalidades:
        - analyze_lightmap_performance: Analisar performance de lightmaps
        - measure_memory_usage: Medir uso de memória
        - analyze_loading_times: Analisar tempos de carregamento
        - optimize_lightmap_streaming: O<PERSON><PERSON><PERSON> streaming de lightmaps
        - generate_performance_report: Gerar relatório de performance
        - benchmark_lightmap_quality: Benchmark de qualidade vs performance

        Args:
            action: Operação a executar
            scene_path: Caminho da cena para análise
            analysis_type: Tipo de análise (memory, loading, streaming, quality)
            performance_metrics: Métricas a analisar
            target_platform: Plataforma alvo (PC, Mobile, Console)
            optimization_level: Nível de otimização (low, medium, high)
            memory_budget: Orçamento de memória em MB
            quality_target: Alvo de qualidade (low, medium, high, ultra)
            generate_report: Gerar relatório detalhado

        Returns:
            Dictionary com resultados ('success', 'message', 'data')
        """
        try:
            params = {
                "action": action,
                "scene_path": scene_path,
                "analysis_type": analysis_type,
                "performance_metrics": performance_metrics,
                "target_platform": target_platform,
                "optimization_level": optimization_level,
                "memory_budget": memory_budget,
                "quality_target": quality_target,
                "generate_report": generate_report
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("lightmap_performance_analysis", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Lightmap performance analysis completed successfully."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Failed to analyze lightmap performance.")
                }

        except Exception as e:
            return {"success": False, "message": f"Python error in lightmap performance analysis: {str(e)}"}
