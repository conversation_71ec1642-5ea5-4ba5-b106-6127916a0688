from mcp.server.fastmcp import FastMCP, Context
from typing import Dict, Any, List, Optional
from unity_connection import get_unity_connection

def register_lightmap_edge_smoothing_tools(mcp: FastMCP):
    """Register Lightmap Edge Smoothing tools with the MCP server."""

    @mcp.tool()
    def lightmap_edge_smoothing(
        ctx: Context,
        action: str,
        lightmap_path: Optional[str] = None,
        smoothing_algorithm: Optional[str] = None,
        edge_threshold: Optional[float] = None,
        smoothing_intensity: Optional[float] = None,
        preserve_details: Optional[bool] = None,
        output_path: Optional[str] = None,
        batch_process: Optional[bool] = None,
        quality_settings: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Sistema de Suavização de Bordas de Lightmaps usando Unity 6.2.

        Funcionalidades:
        - smooth_lightmap_edges: Suavizar bordas de lightmaps
        - analyze_edge_artifacts: Analisar artefatos de borda
        - batch_smooth_lightmaps: Processamento em lote
        - optimize_lightmap_seams: Otimizar costuras de lightmaps
        - validate_smoothing: Validar resultado da suavização
        - export_smoothed_lightmaps: Exportar lightmaps suavizados

        Args:
            action: Operação a executar
            lightmap_path: Caminho do lightmap
            smoothing_algorithm: Algoritmo (gaussian, bilateral, edge_preserving)
            edge_threshold: Limite de detecção de bordas (0.0-1.0)
            smoothing_intensity: Intensidade da suavização (0.0-1.0)
            preserve_details: Preservar detalhes durante suavização
            output_path: Caminho de saída
            batch_process: Processar múltiplos lightmaps
            quality_settings: Configurações de qualidade

        Returns:
            Dictionary com resultados ('success', 'message', 'data')
        """
        try:
            params = {
                "action": action,
                "lightmap_path": lightmap_path,
                "smoothing_algorithm": smoothing_algorithm,
                "edge_threshold": edge_threshold,
                "smoothing_intensity": smoothing_intensity,
                "preserve_details": preserve_details,
                "output_path": output_path,
                "batch_process": batch_process,
                "quality_settings": quality_settings
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("lightmap_edge_smoothing", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Lightmap edge smoothing completed successfully."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Failed to process lightmap edge smoothing.")
                }

        except Exception as e:
            return {"success": False, "message": f"Python error in lightmap edge smoothing: {str(e)}"}
