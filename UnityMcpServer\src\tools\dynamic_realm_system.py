from mcp.server.fastmcp import FastMCP, Context
from typing import Dict, Any, List, Optional
from unity_connection import get_unity_connection

def register_dynamic_realm_system_tools(mcp: FastMCP):
    """Register Dynamic Realm System tools with the MCP server."""

    @mcp.tool()
    def dynamic_realm_system(
        ctx: Context,
        action: str,
        realm_name: Optional[str] = None,
        realm_type: Optional[str] = None,
        realm_size: Optional[Dict[str, float]] = None,
        biome_settings: Optional[Dict[str, Any]] = None,
        weather_config: Optional[Dict[str, Any]] = None,
        population_density: Optional[float] = None,
        resource_distribution: Optional[Dict[str, Any]] = None,
        event_triggers: Optional[List[Dict[str, Any]]] = None,
        realm_rules: Optional[Dict[str, Any]] = None,
        generation_seed: Optional[int] = None
    ) -> Dict[str, Any]:
        """Sistema de Reinos Dinâmicos para MOBA AURACRON.

        Funcionalidades:
        - create_realm: Criar novo reino dinâmico
        - modify_realm: Modificar reino existente
        - generate_biomes: Gerar biomas proceduralmente
        - setup_weather: Configurar sistema climático
        - manage_population: Gerenciar população do reino
        - distribute_resources: Distribuir recursos
        - trigger_events: Disparar eventos dinâmicos
        - analyze_realm: Analisar métricas do reino
        - save_realm: Salvar configuração do reino
        - load_realm: Carregar reino salvo

        Args:
            action: Operação a executar
            realm_name: Nome do reino
            realm_type: Tipo do reino (forest, desert, mountain, ocean, volcanic)
            realm_size: Dimensões do reino {width, height, depth}
            biome_settings: Configurações dos biomas
            weather_config: Configuração climática
            population_density: Densidade populacional (0.0-1.0)
            resource_distribution: Distribuição de recursos
            event_triggers: Gatilhos de eventos dinâmicos
            realm_rules: Regras específicas do reino
            generation_seed: Seed para geração procedural

        Returns:
            Dictionary com resultados ('success', 'message', 'data')
        """
        try:
            # Preparar parâmetros, removendo valores None
            params = {
                "action": action,
                "realm_name": realm_name,
                "realm_type": realm_type,
                "realm_size": realm_size,
                "biome_settings": biome_settings,
                "weather_config": weather_config,
                "population_density": population_density,
                "resource_distribution": resource_distribution,
                "event_triggers": event_triggers,
                "realm_rules": realm_rules,
                "generation_seed": generation_seed
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            # Enviar comando para Unity
            response = get_unity_connection().send_command("dynamic_realm_system", params)

            # Processar resposta
            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Dynamic Realm operation completed successfully."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Unknown error in Dynamic Realm operation.")
                }

        except Exception as e:
            return {"success": False, "message": f"Python error in Dynamic Realm System: {str(e)}"}
