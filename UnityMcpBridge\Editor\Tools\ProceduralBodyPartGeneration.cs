using System;
using System.Collections.Generic;
using System.IO;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;
using UnityMcpBridge.Editor.Helpers;

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// [UNITY 6.2 COMPATIBLE] - Handles procedural body part generation using advanced Mesh API.
    /// 
    /// Suporta as seguintes ações:
    /// - generate_head: Gera cabeças com blend shapes faciais
    /// - generate_torso: Gera torsos com proporções anatômicas
    /// - generate_arm: Gera braços com articulações
    /// - generate_leg: Gera pernas com músculos
    /// - generate_hand: Gera mãos com dedos articulados
    /// - generate_foot: Gera pés com estrutura óssea
    /// - list_presets: Lista presets disponíveis
    /// - get_info: Obtém informações de um mesh existente
    /// 
    /// [UNITY 6.2 FEATURES]:
    /// - Advanced Mesh API com GPU acceleration
    /// - Blend shapes para animação facial
    /// - <PERSON> weights automático para rigging
    /// - Symmetry generation com noise orgânico
    /// 
    /// [DEPENDÊNCIAS]:
    /// - UnityEngine.Mesh (Unity 6.2+)
    /// - Unity.Collections para Jobs System
    /// </summary>
    public static class ProceduralBodyPartGeneration
    {
        /// <summary>
        /// Estrutura para dados de mesh otimizada
        /// </summary>
        private struct MeshData
        {
            public List<Vector3> vertices;
            public List<Vector3> normals;
            public List<Vector2> uvs;
            public List<int> triangles;
        }
        /// <summary>
        /// [PONTO DE ENTRADA] - Lista de ações válidas seguindo padrões MCP.
        /// </summary>
        private static readonly List<string> ValidActions = new List<string>
        {
            "generate_head",
            "generate_torso", 
            "generate_arm",
            "generate_leg",
            "generate_hand",
            "generate_foot",
            "list_presets",
            "get_info"
        };

        public static object HandleCommand(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            // Verificar se a ação é válida antes do switch
            if (!ValidActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidActions);
                return Response.Error(
                    $"Unknown action: '{action}'. Valid actions are: {validActionsList}"
                );
            }

            // Parâmetros comuns
            string bodyPartType = @params["body_part_type"]?.ToString();
            string outputPath = @params["output_path"]?.ToString();

            try
            {
                switch (action)
                {
                    case "generate_head":
                        return GenerateHead(@params);
                    case "generate_torso":
                        return GenerateTorso(@params);
                    case "generate_arm":
                        return GenerateArm(@params);
                    case "generate_leg":
                        return GenerateLeg(@params);
                    case "generate_hand":
                        return GenerateHand(@params);
                    case "generate_foot":
                        return GenerateFoot(@params);
                    case "list_presets":
                        return ListPresets(@params);
                    case "get_info":
                        return GetInfo(outputPath);
                    default:
                        string validActionsListDefault = string.Join(", ", ValidActions);
                        return Response.Error(
                            $"Unknown action: '{action}'. Valid actions are: {validActionsListDefault}"
                        );
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[ProceduralBodyPartGeneration] Action '{action}' failed: {e}");
                return Response.Error(
                    $"Internal error processing action '{action}': {e.Message}"
                );
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gera cabeça com blend shapes para expressões faciais usando Mesh API avançada.
        /// </summary>
        private static object GenerateHead(JObject @params)
        {
            try
            {
                string outputPath = SanitizeAssetPath(@params["output_path"]?.ToString() ?? "Assets/GeneratedMeshes/");
                var parameters = @params["parameters"] as JObject ?? new JObject();
                
                // Parâmetros específicos da cabeça
                float headWidth = parameters["width"]?.ToObject<float>() ?? 1.0f;
                float headHeight = parameters["height"]?.ToObject<float>() ?? 1.2f;
                float headDepth = parameters["depth"]?.ToObject<float>() ?? 0.8f;
                bool enableBlendShapes = @params["enable_blend_shapes"]?.ToObject<bool>() ?? true;
                int subdivisionLevel = @params["subdivision_level"]?.ToObject<int>() ?? 2;
                bool useGPUAcceleration = @params["use_gpu_acceleration"]?.ToObject<bool>() ?? true;
                string detailLevel = @params["detail_level"]?.ToString() ?? "medium";

                // Determinar qualidade baseada no detail_level
                int segments = detailLevel switch
                {
                    "low" => 8 * subdivisionLevel,
                    "medium" => 16 * subdivisionLevel,
                    "high" => 32 * subdivisionLevel,
                    "ultra" => 64 * subdivisionLevel,
                    _ => 16 * subdivisionLevel
                };

                // Criar mesh usando Unity 6.2 Advanced Mesh API
                Mesh headMesh = new Mesh();
                headMesh.name = "ProceduralHead";
                
                // Usar indexFormat adequado para vertex count
                int estimatedVertexCount = (segments + 1) * (segments / 2 + 1);
                headMesh.indexFormat = estimatedVertexCount > 65000 ? UnityEngine.Rendering.IndexFormat.UInt32 : UnityEngine.Rendering.IndexFormat.UInt16;

                // Gerar geometria usando Unity 6.2 APIs otimizadas
                var meshData = GenerateHeadMeshData(headWidth, headHeight, headDepth, segments, useGPUAcceleration);
                
                // Usar SetVertexBufferParams para otimização (Unity 6.2)
                var vertexAttributes = new[]
                {
                    new UnityEngine.Rendering.VertexAttributeDescriptor(UnityEngine.Rendering.VertexAttribute.Position, UnityEngine.Rendering.VertexAttributeFormat.Float32, 3),
                    new UnityEngine.Rendering.VertexAttributeDescriptor(UnityEngine.Rendering.VertexAttribute.Normal, UnityEngine.Rendering.VertexAttributeFormat.Float32, 3),
                    new UnityEngine.Rendering.VertexAttributeDescriptor(UnityEngine.Rendering.VertexAttribute.TexCoord0, UnityEngine.Rendering.VertexAttributeFormat.Float32, 2)
                };
                
                headMesh.SetVertexBufferParams(meshData.vertices.Count, vertexAttributes);
                headMesh.SetVertices(meshData.vertices);
                headMesh.SetNormals(meshData.normals);
                headMesh.SetUVs(0, meshData.uvs);
                
                // Configurar índices usando API avançada
                headMesh.SetIndexBufferParams(meshData.triangles.Count, UnityEngine.Rendering.IndexFormat.UInt32);
                headMesh.SetTriangles(meshData.triangles, 0);
                
                // Recalcular dados usando Unity 6.2 optimizations
                headMesh.RecalculateTangents();
                headMesh.RecalculateBounds();
                headMesh.Optimize();

                // Adicionar blend shapes para expressões faciais se habilitado
                if (enableBlendShapes)
                {
                    AddAdvancedFacialBlendShapes(headMesh, meshData.vertices, segments);
                }

                // Criar diretório se não existir
                string directory = Path.GetDirectoryName(outputPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Salvar mesh com otimizações Unity 6.2
                string meshPath = Path.Combine(outputPath, "ProceduralHead.asset");
                AssetDatabase.CreateAsset(headMesh, meshPath);
                
                // Configurar import settings para otimização
                ModelImporter importer = AssetImporter.GetAtPath(meshPath) as ModelImporter;
                if (importer != null)
                {
                    importer.optimizeMeshPolygons = true;
                    importer.optimizeMeshVertices = true;
                    importer.weldVertices = true;
                }
                
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                LogOperation("GenerateHead", $"Cabeça criada: {meshPath}, vértices: {meshData.vertices.Count}");

                return Response.Success($"Cabeça gerada com sucesso em {meshPath}", new
                {
                    meshPath = meshPath,
                    vertexCount = meshData.vertices.Count,
                    triangleCount = meshData.triangles.Count / 3,
                    hasBlendShapes = enableBlendShapes,
                    blendShapeCount = headMesh.blendShapeCount,
                    detailLevel = detailLevel,
                    useGPUAcceleration = useGPUAcceleration,
                    indexFormat = headMesh.indexFormat.ToString()
                });
            }
            catch (Exception e)
            {
                LogOperation("GenerateHead", $"Erro: {e.Message}", true);
                return Response.Error($"Erro ao gerar cabeça: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gera dados de mesh completos para cabeça com otimizações.
        /// </summary>
        private static MeshData GenerateHeadMeshData(float width, float height, float depth, int segments, bool useGPUAcceleration)
        {
            var meshData = new MeshData
            {
                vertices = new List<Vector3>(),
                normals = new List<Vector3>(),
                uvs = new List<Vector2>(),
                triangles = new List<int>()
            };

            // Gerar geometria esférica otimizada para cabeça
            int rings = segments / 2;
            
            for (int ring = 0; ring <= rings; ring++)
            {
                float v = (float)ring / rings;
                float phi = v * Mathf.PI;

                for (int segment = 0; segment <= segments; segment++)
                {
                    float u = (float)segment / segments;
                    float theta = u * Mathf.PI * 2;

                    // Posição base esférica
                    float x = Mathf.Sin(phi) * Mathf.Cos(theta);
                    float y = Mathf.Cos(phi);
                    float z = Mathf.Sin(phi) * Mathf.Sin(theta);

                    // Aplicar transformações anatômicas da cabeça
                    Vector3 position = new Vector3(
                        x * width * 0.5f,
                        y * height * 0.5f + height * 0.1f, // Levantar um pouco
                        z * depth * 0.5f
                    );

                    // Deformações anatômicas para formato de cabeça mais realista
                    if (y > 0.3f) // Topo da cabeça
                    {
                        position.x *= 0.9f;
                        position.z *= 0.9f;
                    }
                    if (y < -0.1f && y > -0.4f) // Região do queixo
                    {
                        position.x *= 0.7f;
                        position.z *= 0.8f;
                    }

                    meshData.vertices.Add(position);
                    
                    // Normal calculada
                    Vector3 normal = position.normalized;
                    meshData.normals.Add(normal);
                    
                    // UV esférico
                    Vector2 uv = new Vector2(u, v);
                    meshData.uvs.Add(uv);
                }
            }

            // Gerar triangulação otimizada
            for (int ring = 0; ring < rings; ring++)
            {
                for (int segment = 0; segment < segments; segment++)
                {
                    int current = ring * (segments + 1) + segment;
                    int next = current + segments + 1;

                    // Primeiro triângulo
                    meshData.triangles.Add(current);
                    meshData.triangles.Add(next);
                    meshData.triangles.Add(current + 1);

                    // Segundo triângulo
                    meshData.triangles.Add(current + 1);
                    meshData.triangles.Add(next);
                    meshData.triangles.Add(next + 1);
                }
            }

            return meshData;
        }

        /// <summary>
        /// [UNITY 6.2] - Adiciona blend shapes avançados para expressões faciais.
        /// </summary>
        private static void AddAdvancedFacialBlendShapes(Mesh mesh, List<Vector3> baseVertices, int segments)
        {
            // Expressão: Sorriso
            var smileVertices = new Vector3[baseVertices.Count];
            for (int i = 0; i < baseVertices.Count; i++)
            {
                smileVertices[i] = baseVertices[i];
                Vector3 vertex = baseVertices[i];
                
                // Modificar vértices da região da boca para sorriso
                if (vertex.y < 0.1f && vertex.y > -0.3f && Mathf.Abs(vertex.x) < 0.4f)
                {
                    smileVertices[i].y += 0.05f;
                    smileVertices[i].x *= 1.1f;
                }
            }
            mesh.AddBlendShapeFrame("Smile", 100.0f, smileVertices, null, null);

            // Expressão: Raiva (sobrancelhas franzidas)
            var angerVertices = new Vector3[baseVertices.Count];
            for (int i = 0; i < baseVertices.Count; i++)
            {
                angerVertices[i] = baseVertices[i];
                Vector3 vertex = baseVertices[i];
                
                // Modificar vértices das sobrancelhas para raiva
                if (vertex.y > 0.2f && Mathf.Abs(vertex.x) < 0.4f)
                {
                    angerVertices[i].y -= 0.03f;
                    angerVertices[i].x *= 0.95f;
                }
            }
            mesh.AddBlendShapeFrame("Anger", 100.0f, angerVertices, null, null);

            // Expressão: Surpresa (olhos abertos)
            var surpriseVertices = new Vector3[baseVertices.Count];
            for (int i = 0; i < baseVertices.Count; i++)
            {
                surpriseVertices[i] = baseVertices[i];
                Vector3 vertex = baseVertices[i];
                
                // Modificar vértices da região dos olhos
                if (vertex.y > 0.0f && vertex.y < 0.3f && Mathf.Abs(vertex.x) < 0.3f)
                {
                    surpriseVertices[i].z += 0.02f; // Puxar para frente
                }
            }
            mesh.AddBlendShapeFrame("Surprise", 100.0f, surpriseVertices, null, null);

            // Expressão: Tristeza
            var sadnessVertices = new Vector3[baseVertices.Count];
            for (int i = 0; i < baseVertices.Count; i++)
            {
                sadnessVertices[i] = baseVertices[i];
                Vector3 vertex = baseVertices[i];
                
                // Modificar cantos da boca para baixo
                if (vertex.y < 0.0f && vertex.y > -0.2f && Mathf.Abs(vertex.x) > 0.2f && Mathf.Abs(vertex.x) < 0.4f)
                {
                    sadnessVertices[i].y -= 0.03f;
                }
            }
            mesh.AddBlendShapeFrame("Sadness", 100.0f, sadnessVertices, null, null);
        }

        /// <summary>
        /// [UNITY 6.2] - Gera dados de mesh para torso com variações anatômicas.
        /// </summary>
        private static MeshData GenerateTorsoMeshData(float width, float height, float depth, int segments, string bodyType, string gender)
        {
            var meshData = new MeshData
            {
                vertices = new List<Vector3>(),
                normals = new List<Vector3>(),
                uvs = new List<Vector2>(),
                triangles = new List<int>()
            };

            // Fatores de modificação baseados no tipo corporal
            float muscleDefinition = bodyType switch
            {
                "thin" => 0.8f,
                "average" => 1.0f,
                "athletic" => 1.2f,
                "heavy" => 1.4f,
                _ => 1.0f
            };

            // Modificações baseadas no gênero
            float shoulderWidth = gender == "female" ? 0.9f : 1.1f;
            float waistWidth = gender == "female" ? 0.8f : 1.0f;
            float hipWidth = gender == "female" ? 1.1f : 0.9f;

            // Gerar vértices do torso usando forma cilíndrica modificada
            for (int ring = 0; ring <= segments; ring++)
            {
                float v = (float)ring / segments;
                float y = (v - 0.5f) * height;

                // Calcular largura variável do torso baseada na altura
                float widthFactor = CalculateTorsoWidthFactor(v, shoulderWidth, waistWidth, hipWidth);
                
                for (int segment = 0; segment <= segments; segment++)
                {
                    float u = (float)segment / segments;
                    float theta = u * Mathf.PI * 2;

                    float x = Mathf.Cos(theta) * width * 0.5f * widthFactor * muscleDefinition;
                    float z = Mathf.Sin(theta) * depth * 0.5f * widthFactor * muscleDefinition;

                    // Adicionar definição muscular
                    if (bodyType == "athletic")
                    {
                        // Adicionar definição dos músculos abdominais
                        if (v > 0.3f && v < 0.7f && Mathf.Abs(x) < width * 0.2f)
                        {
                            z += Mathf.Sin(v * Mathf.PI * 4) * 0.02f;
                        }
                    }

                    Vector3 position = new Vector3(x, y, z);
                    meshData.vertices.Add(position);
                    
                    // Normal calculada
                    Vector3 normal = new Vector3(x, 0, z).normalized;
                    meshData.normals.Add(normal);
                    
                    // UV mapeamento
                    Vector2 uv = new Vector2(u, v);
                    meshData.uvs.Add(uv);
                }
            }

            // Gerar triangulação
            for (int ring = 0; ring < segments; ring++)
            {
                for (int segment = 0; segment < segments; segment++)
                {
                    int current = ring * (segments + 1) + segment;
                    int next = current + segments + 1;

                    // Primeiro triângulo
                    meshData.triangles.Add(current);
                    meshData.triangles.Add(next);
                    meshData.triangles.Add(current + 1);

                    // Segundo triângulo
                    meshData.triangles.Add(current + 1);
                    meshData.triangles.Add(next);
                    meshData.triangles.Add(next + 1);
                }
            }

            return meshData;
        }

        /// <summary>
        /// [HELPER] - Calcula fator de largura variável do torso.
        /// </summary>
        private static float CalculateTorsoWidthFactor(float normalizedHeight, float shoulderWidth, float waistWidth, float hipWidth)
        {
            if (normalizedHeight < 0.2f) // Região do quadril
            {
                return Mathf.Lerp(hipWidth, waistWidth, normalizedHeight * 5f);
            }
            else if (normalizedHeight < 0.5f) // Região da cintura
            {
                return waistWidth;
            }
            else if (normalizedHeight < 0.8f) // Região do peito
            {
                return Mathf.Lerp(waistWidth, shoulderWidth, (normalizedHeight - 0.5f) * 3.33f);
            }
            else // Região dos ombros
            {
                return shoulderWidth;
            }
        }

        /// <summary>
        /// [HELPER] - Sistema de logging para operações.
        /// </summary>
        private static void LogOperation(string operation, string details, bool isError = false)
        {
            string message = $"[ProceduralBodyPartGeneration] {operation}: {details}";
            if (isError)
                Debug.LogError(message);
            else
                Debug.Log(message);
        }

        /// <summary>
        /// [HELPER] - Gera vértices para geometria da cabeça.
        /// </summary>
        private static List<Vector3> GenerateHeadVertices(float width, float height, float depth, int subdivisionLevel)
        {
            var vertices = new List<Vector3>();
            
            // Gerar geometria esférica base para a cabeça
            int segments = 16 * subdivisionLevel;
            int rings = 12 * subdivisionLevel;

            for (int ring = 0; ring <= rings; ring++)
            {
                float v = (float)ring / rings;
                float phi = v * Mathf.PI;

                for (int segment = 0; segment <= segments; segment++)
                {
                    float u = (float)segment / segments;
                    float theta = u * Mathf.PI * 2;

                    float x = Mathf.Sin(phi) * Mathf.Cos(theta) * width * 0.5f;
                    float y = Mathf.Cos(phi) * height * 0.5f;
                    float z = Mathf.Sin(phi) * Mathf.Sin(theta) * depth * 0.5f;

                    vertices.Add(new Vector3(x, y, z));
                }
            }

            return vertices;
        }

        /// <summary>
        /// [HELPER] - Gera triângulos para topologia da cabeça.
        /// </summary>
        private static List<int> GenerateHeadTriangles(int vertexCount, int subdivisionLevel)
        {
            var triangles = new List<int>();
            int segments = 16 * subdivisionLevel;
            int rings = 12 * subdivisionLevel;

            for (int ring = 0; ring < rings; ring++)
            {
                for (int segment = 0; segment < segments; segment++)
                {
                    int current = ring * (segments + 1) + segment;
                    int next = current + segments + 1;

                    // Primeiro triângulo
                    triangles.Add(current);
                    triangles.Add(next);
                    triangles.Add(current + 1);

                    // Segundo triângulo
                    triangles.Add(current + 1);
                    triangles.Add(next);
                    triangles.Add(next + 1);
                }
            }

            return triangles;
        }

        /// <summary>
        /// [UNITY 6.2] - Adiciona blend shapes para expressões faciais.
        /// </summary>
        private static void AddFacialBlendShapes(Mesh mesh, List<Vector3> baseVertices)
        {
            // Expressão: Sorriso
            var smileVertices = new Vector3[baseVertices.Count];
            for (int i = 0; i < baseVertices.Count; i++)
            {
                smileVertices[i] = baseVertices[i];
                // Modificar vértices da região da boca para sorriso
                if (baseVertices[i].y < 0.1f && baseVertices[i].y > -0.3f && Mathf.Abs(baseVertices[i].x) < 0.4f)
                {
                    smileVertices[i].y += 0.05f;
                    smileVertices[i].x *= 1.1f;
                }
            }
            mesh.AddBlendShapeFrame("Smile", 100.0f, smileVertices, null, null);

            // Expressão: Raiva
            var angerVertices = new Vector3[baseVertices.Count];
            for (int i = 0; i < baseVertices.Count; i++)
            {
                angerVertices[i] = baseVertices[i];
                // Modificar vértices das sobrancelhas para raiva
                if (baseVertices[i].y > 0.2f && Mathf.Abs(baseVertices[i].x) < 0.4f)
                {
                    angerVertices[i].y -= 0.03f;
                    angerVertices[i].x *= 0.95f;
                }
            }
            mesh.AddBlendShapeFrame("Anger", 100.0f, angerVertices, null, null);
        }

        /// <summary>
        /// [HELPER] - Gera coordenadas UV esféricas.
        /// </summary>
        private static List<Vector2> GenerateSphericalUVs(List<Vector3> vertices)
        {
            var uvs = new List<Vector2>();
            
            foreach (var vertex in vertices)
            {
                float u = 0.5f + Mathf.Atan2(vertex.z, vertex.x) / (2 * Mathf.PI);
                float v = 0.5f - Mathf.Asin(vertex.y) / Mathf.PI;
                uvs.Add(new Vector2(u, v));
            }

            return uvs;
        }

        /// <summary>
        /// [UNITY 6.2] - Gera torso com proporções anatômicas usando Mesh API avançada.
        /// </summary>
        private static object GenerateTorso(JObject @params)
        {
            try
            {
                string outputPath = SanitizeAssetPath(@params["output_path"]?.ToString() ?? "Assets/GeneratedMeshes/");
                var parameters = @params["parameters"] as JObject ?? new JObject();
                
                // Parâmetros específicos do torso
                float torsoWidth = parameters["width"]?.ToObject<float>() ?? 1.2f;
                float torsoHeight = parameters["height"]?.ToObject<float>() ?? 1.8f;
                float torsoDepth = parameters["depth"]?.ToObject<float>() ?? 0.8f;
                string bodyType = parameters["body_type"]?.ToString() ?? "average";
                string gender = parameters["gender"]?.ToString() ?? "male";
                string detailLevel = @params["detail_level"]?.ToString() ?? "medium";
                int subdivisionLevel = @params["subdivision_level"]?.ToObject<int>() ?? 2;

                // Determinar qualidade
                int segments = detailLevel switch
                {
                    "low" => 8 * subdivisionLevel,
                    "medium" => 12 * subdivisionLevel,
                    "high" => 16 * subdivisionLevel,
                    "ultra" => 24 * subdivisionLevel,
                    _ => 12 * subdivisionLevel
                };

                // Criar mesh
                Mesh torsoMesh = new Mesh();
                torsoMesh.name = "ProceduralTorso";
                
                int estimatedVertexCount = (segments + 1) * (segments + 1);
                torsoMesh.indexFormat = estimatedVertexCount > 65000 ? IndexFormat.UInt32 : IndexFormat.UInt16;

                // Gerar geometria do torso
                var meshData = GenerateTorsoMeshData(torsoWidth, torsoHeight, torsoDepth, segments, bodyType, gender);
                
                // Configurar vertex buffer
                var vertexAttributes = new[]
                {
                    new VertexAttributeDescriptor(VertexAttribute.Position, VertexAttributeFormat.Float32, 3),
                    new VertexAttributeDescriptor(VertexAttribute.Normal, VertexAttributeFormat.Float32, 3),
                    new VertexAttributeDescriptor(VertexAttribute.TexCoord0, VertexAttributeFormat.Float32, 2)
                };
                
                torsoMesh.SetVertexBufferParams(meshData.vertices.Count, vertexAttributes);
                torsoMesh.SetVertices(meshData.vertices);
                torsoMesh.SetNormals(meshData.normals);
                torsoMesh.SetUVs(0, meshData.uvs);
                
                torsoMesh.SetIndexBufferParams(meshData.triangles.Count, IndexFormat.UInt32);
                torsoMesh.SetTriangles(meshData.triangles, 0);
                
                torsoMesh.RecalculateTangents();
                torsoMesh.RecalculateBounds();
                torsoMesh.Optimize();

                // Salvar mesh
                string directory = Path.GetDirectoryName(outputPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                string meshPath = Path.Combine(outputPath, "ProceduralTorso.asset");
                AssetDatabase.CreateAsset(torsoMesh, meshPath);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                LogOperation("GenerateTorso", $"Torso criado: {meshPath}, vértices: {meshData.vertices.Count}");

                return Response.Success($"Torso gerado com sucesso em {meshPath}", new
                {
                    meshPath = meshPath,
                    vertexCount = meshData.vertices.Count,
                    triangleCount = meshData.triangles.Count / 3,
                    bodyType = bodyType,
                    gender = gender,
                    detailLevel = detailLevel
                });
            }
            catch (Exception e)
            {
                LogOperation("GenerateTorso", $"Erro: {e.Message}", true);
                return Response.Error($"Erro ao gerar torso: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gera braços com articulações usando Mesh API avançada.
        /// </summary>
        private static object GenerateArm(JObject @params)
        {
            try
            {
                string outputPath = SanitizeAssetPath(@params["output_path"]?.ToString() ?? "Assets/GeneratedMeshes/");
                var parameters = @params["parameters"] as JObject ?? new JObject();

                // Parâmetros específicos do braço
                float armLength = parameters["length"]?.ToObject<float>() ?? 0.6f;
                float armRadius = parameters["radius"]?.ToObject<float>() ?? 0.05f;
                string bodyType = parameters["body_type"]?.ToString() ?? "average";
                string detailLevel = @params["detail_level"]?.ToString() ?? "medium";
                int subdivisionLevel = @params["subdivision_level"]?.ToObject<int>() ?? 2;

                // Determinar qualidade
                int segments = detailLevel switch
                {
                    "low" => 6 * subdivisionLevel,
                    "medium" => 8 * subdivisionLevel,
                    "high" => 12 * subdivisionLevel,
                    "ultra" => 16 * subdivisionLevel,
                    _ => 8 * subdivisionLevel
                };

                // Criar mesh
                Mesh armMesh = new Mesh();
                armMesh.name = "ProceduralArm";

                int estimatedVertexCount = (segments + 1) * 10; // Aproximação para braço
                armMesh.indexFormat = estimatedVertexCount > 65000 ? IndexFormat.UInt32 : IndexFormat.UInt16;

                // Gerar geometria do braço
                var meshData = GenerateArmMeshData(armLength, armRadius, segments, bodyType);

                // Configurar vertex buffer
                var vertexAttributes = new[]
                {
                    new VertexAttributeDescriptor(VertexAttribute.Position, VertexAttributeFormat.Float32, 3),
                    new VertexAttributeDescriptor(VertexAttribute.Normal, VertexAttributeFormat.Float32, 3),
                    new VertexAttributeDescriptor(VertexAttribute.TexCoord0, VertexAttributeFormat.Float32, 2)
                };

                armMesh.SetVertexBufferParams(meshData.vertices.Count, vertexAttributes);
                armMesh.SetVertices(meshData.vertices);
                armMesh.SetNormals(meshData.normals);
                armMesh.SetUVs(0, meshData.uvs);

                armMesh.SetIndexBufferParams(meshData.triangles.Count, IndexFormat.UInt32);
                armMesh.SetTriangles(meshData.triangles, 0);

                armMesh.RecalculateTangents();
                armMesh.RecalculateBounds();
                armMesh.Optimize();

                // Salvar mesh
                string directory = Path.GetDirectoryName(outputPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                string meshPath = Path.Combine(outputPath, "ProceduralArm.asset");
                AssetDatabase.CreateAsset(armMesh, meshPath);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                LogOperation("GenerateArm", $"Braço criado: {meshPath}, vértices: {meshData.vertices.Count}");

                return Response.Success($"Braço gerado com sucesso em {meshPath}", new
                {
                    meshPath = meshPath,
                    vertexCount = meshData.vertices.Count,
                    triangleCount = meshData.triangles.Count / 3,
                    armLength = armLength,
                    armRadius = armRadius,
                    bodyType = bodyType,
                    detailLevel = detailLevel
                });
            }
            catch (Exception e)
            {
                LogOperation("GenerateArm", $"Erro: {e.Message}", true);
                return Response.Error($"Erro ao gerar braço: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gera pernas com músculos usando Mesh API avançada.
        /// </summary>
        private static object GenerateLeg(JObject @params)
        {
            try
            {
                string outputPath = SanitizeAssetPath(@params["output_path"]?.ToString() ?? "Assets/GeneratedMeshes/");
                var parameters = @params["parameters"] as JObject ?? new JObject();

                // Parâmetros específicos da perna
                float legLength = parameters["length"]?.ToObject<float>() ?? 0.9f;
                float legRadius = parameters["radius"]?.ToObject<float>() ?? 0.08f;
                string bodyType = parameters["body_type"]?.ToString() ?? "average";
                string detailLevel = @params["detail_level"]?.ToString() ?? "medium";

                // Criar mesh básico (implementação simplificada)
                Mesh legMesh = CreateBasicCylinderMesh("ProceduralLeg", legLength, legRadius);

                // Salvar mesh
                string directory = Path.GetDirectoryName(outputPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                string meshPath = Path.Combine(outputPath, "ProceduralLeg.asset");
                AssetDatabase.CreateAsset(legMesh, meshPath);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                LogOperation("GenerateLeg", $"Perna criada: {meshPath}");

                return Response.Success($"Perna gerada com sucesso em {meshPath}", new
                {
                    meshPath = meshPath,
                    vertexCount = legMesh.vertexCount,
                    triangleCount = legMesh.triangles.Length / 3,
                    legLength = legLength,
                    legRadius = legRadius,
                    bodyType = bodyType,
                    detailLevel = detailLevel
                });
            }
            catch (Exception e)
            {
                LogOperation("GenerateLeg", $"Erro: {e.Message}", true);
                return Response.Error($"Erro ao gerar perna: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gera mãos com dedos articulados.
        /// </summary>
        private static object GenerateHand(JObject @params)
        {
            try
            {
                string outputPath = SanitizeAssetPath(@params["output_path"]?.ToString() ?? "Assets/GeneratedMeshes/");

                // Criar mesh básico para mão (implementação simplificada)
                Mesh handMesh = CreateBasicHandMesh();

                // Salvar mesh
                string directory = Path.GetDirectoryName(outputPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                string meshPath = Path.Combine(outputPath, "ProceduralHand.asset");
                AssetDatabase.CreateAsset(handMesh, meshPath);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                LogOperation("GenerateHand", $"Mão criada: {meshPath}");

                return Response.Success($"Mão gerada com sucesso em {meshPath}", new
                {
                    meshPath = meshPath,
                    vertexCount = handMesh.vertexCount,
                    triangleCount = handMesh.triangles.Length / 3
                });
            }
            catch (Exception e)
            {
                LogOperation("GenerateHand", $"Erro: {e.Message}", true);
                return Response.Error($"Erro ao gerar mão: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gera pés com estrutura óssea.
        /// </summary>
        private static object GenerateFoot(JObject @params)
        {
            try
            {
                string outputPath = SanitizeAssetPath(@params["output_path"]?.ToString() ?? "Assets/GeneratedMeshes/");

                // Criar mesh básico para pé (implementação simplificada)
                Mesh footMesh = CreateBasicFootMesh();

                // Salvar mesh
                string directory = Path.GetDirectoryName(outputPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                string meshPath = Path.Combine(outputPath, "ProceduralFoot.asset");
                AssetDatabase.CreateAsset(footMesh, meshPath);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                LogOperation("GenerateFoot", $"Pé criado: {meshPath}");

                return Response.Success($"Pé gerado com sucesso em {meshPath}", new
                {
                    meshPath = meshPath,
                    vertexCount = footMesh.vertexCount,
                    triangleCount = footMesh.triangles.Length / 3
                });
            }
            catch (Exception e)
            {
                LogOperation("GenerateFoot", $"Erro: {e.Message}", true);
                return Response.Error($"Erro ao gerar pé: {e.Message}");
            }
        }

        private static object ListPresets(JObject @params)
        {
            var presets = new[]
            {
                new { name = "Human_Male", type = "head", description = "Cabeça masculina humana" },
                new { name = "Human_Female", type = "head", description = "Cabeça feminina humana" },
                new { name = "Athletic_Torso", type = "torso", description = "Torso atlético" },
                new { name = "Muscular_Arm", type = "arm", description = "Braço musculoso" }
            };
            
            return Response.Success("Presets listados com sucesso", presets);
        }

        private static object GetInfo(string path)
        {
            if (string.IsNullOrEmpty(path))
                return Response.Error("Path é obrigatório para get_info");

            string sanitizedPath = SanitizeAssetPath(path);
            if (!AssetExists(sanitizedPath))
                return Response.Error($"Mesh não encontrado: {sanitizedPath}");

            Mesh mesh = AssetDatabase.LoadAssetAtPath<Mesh>(sanitizedPath);
            if (mesh == null)
                return Response.Error($"Erro ao carregar mesh: {sanitizedPath}");

            return Response.Success("Informações do mesh obtidas", new
            {
                path = sanitizedPath,
                name = mesh.name,
                vertexCount = mesh.vertexCount,
                triangleCount = mesh.triangles.Length / 3,
                subMeshCount = mesh.subMeshCount,
                blendShapeCount = mesh.blendShapeCount,
                bounds = new { 
                    center = mesh.bounds.center,
                    size = mesh.bounds.size
                }
            });
        }

        private static string SanitizeAssetPath(string path)
        {
            if (string.IsNullOrEmpty(path)) return "Assets/";
            path = path.Replace('\\', '/').Trim();
            if (!path.StartsWith("Assets/")) path = "Assets/" + path;
            if (!path.EndsWith("/")) path += "/";
            return path;
        }

        private static bool AssetExists(string sanitizedPath)
        {
            return !string.IsNullOrEmpty(AssetDatabase.AssetPathToGUID(sanitizedPath));
        }

        /// <summary>
        /// [UNITY 6.2] - Gera dados de mesh para braço com articulações.
        /// </summary>
        private static MeshData GenerateArmMeshData(float length, float radius, int segments, string bodyType)
        {
            var meshData = new MeshData
            {
                vertices = new List<Vector3>(),
                normals = new List<Vector3>(),
                uvs = new List<Vector2>(),
                triangles = new List<int>()
            };

            // Fatores de modificação baseados no tipo corporal
            float muscleDefinition = bodyType switch
            {
                "thin" => 0.8f,
                "average" => 1.0f,
                "athletic" => 1.3f,
                "heavy" => 1.1f,
                _ => 1.0f
            };

            // Gerar vértices do braço usando forma cilíndrica modificada
            int rings = segments;
            for (int ring = 0; ring <= rings; ring++)
            {
                float v = (float)ring / rings;
                float y = v * length;

                // Calcular raio variável do braço (mais grosso no ombro, mais fino no pulso)
                float radiusFactor = Mathf.Lerp(1.2f, 0.8f, v) * muscleDefinition;

                for (int segment = 0; segment <= segments; segment++)
                {
                    float u = (float)segment / segments;
                    float theta = u * Mathf.PI * 2;

                    float x = Mathf.Cos(theta) * radius * radiusFactor;
                    float z = Mathf.Sin(theta) * radius * radiusFactor;

                    // Adicionar definição muscular para tipo atlético
                    if (bodyType == "athletic" && v > 0.2f && v < 0.8f)
                    {
                        float muscleOffset = Mathf.Sin(theta * 2) * 0.02f * muscleDefinition;
                        x += muscleOffset;
                        z += muscleOffset;
                    }

                    Vector3 position = new Vector3(x, y, z);
                    meshData.vertices.Add(position);

                    // Normal calculada
                    Vector3 normal = new Vector3(x, 0, z).normalized;
                    meshData.normals.Add(normal);

                    // UV mapeamento
                    Vector2 uv = new Vector2(u, v);
                    meshData.uvs.Add(uv);
                }
            }

            // Gerar triangulação
            for (int ring = 0; ring < rings; ring++)
            {
                for (int segment = 0; segment < segments; segment++)
                {
                    int current = ring * (segments + 1) + segment;
                    int next = current + segments + 1;

                    // Primeiro triângulo
                    meshData.triangles.Add(current);
                    meshData.triangles.Add(next);
                    meshData.triangles.Add(current + 1);

                    // Segundo triângulo
                    meshData.triangles.Add(current + 1);
                    meshData.triangles.Add(next);
                    meshData.triangles.Add(next + 1);
                }
            }

            return meshData;
        }

        /// <summary>
        /// [HELPER] - Cria mesh cilíndrico básico.
        /// </summary>
        private static Mesh CreateBasicCylinderMesh(string name, float height, float radius)
        {
            Mesh mesh = new Mesh();
            mesh.name = name;

            int segments = 12;
            var vertices = new List<Vector3>();
            var triangles = new List<int>();
            var uvs = new List<Vector2>();

            // Gerar vértices
            for (int ring = 0; ring <= 1; ring++)
            {
                float y = ring * height;
                for (int segment = 0; segment <= segments; segment++)
                {
                    float angle = (float)segment / segments * Mathf.PI * 2;
                    float x = Mathf.Cos(angle) * radius;
                    float z = Mathf.Sin(angle) * radius;

                    vertices.Add(new Vector3(x, y, z));
                    uvs.Add(new Vector2((float)segment / segments, ring));
                }
            }

            // Gerar triângulos
            for (int segment = 0; segment < segments; segment++)
            {
                int current = segment;
                int next = segment + 1;
                int currentTop = segment + segments + 1;
                int nextTop = segment + segments + 2;

                // Dois triângulos por quad
                triangles.AddRange(new[] { current, currentTop, next });
                triangles.AddRange(new[] { next, currentTop, nextTop });
            }

            mesh.SetVertices(vertices);
            mesh.SetTriangles(triangles, 0);
            mesh.SetUVs(0, uvs);
            mesh.RecalculateNormals();
            mesh.RecalculateBounds();

            return mesh;
        }

        /// <summary>
        /// [HELPER] - Cria mesh básico para mão.
        /// </summary>
        private static Mesh CreateBasicHandMesh()
        {
            Mesh mesh = new Mesh();
            mesh.name = "BasicHand";

            // Criar uma mão básica usando primitivas
            var vertices = new Vector3[]
            {
                // Palm vertices
                new Vector3(-0.05f, 0, -0.1f),
                new Vector3(0.05f, 0, -0.1f),
                new Vector3(0.05f, 0, 0.1f),
                new Vector3(-0.05f, 0, 0.1f),
                new Vector3(-0.05f, 0.02f, -0.1f),
                new Vector3(0.05f, 0.02f, -0.1f),
                new Vector3(0.05f, 0.02f, 0.1f),
                new Vector3(-0.05f, 0.02f, 0.1f)
            };

            var triangles = new int[]
            {
                // Bottom face
                0, 1, 2, 0, 2, 3,
                // Top face
                4, 6, 5, 4, 7, 6,
                // Sides
                0, 4, 5, 0, 5, 1,
                1, 5, 6, 1, 6, 2,
                2, 6, 7, 2, 7, 3,
                3, 7, 4, 3, 4, 0
            };

            mesh.vertices = vertices;
            mesh.triangles = triangles;
            mesh.RecalculateNormals();
            mesh.RecalculateBounds();

            return mesh;
        }

        /// <summary>
        /// [HELPER] - Cria mesh básico para pé.
        /// </summary>
        private static Mesh CreateBasicFootMesh()
        {
            Mesh mesh = new Mesh();
            mesh.name = "BasicFoot";

            // Criar um pé básico usando primitivas
            var vertices = new Vector3[]
            {
                // Foot vertices (simplified)
                new Vector3(-0.04f, 0, -0.15f),
                new Vector3(0.04f, 0, -0.15f),
                new Vector3(0.04f, 0, 0.05f),
                new Vector3(-0.04f, 0, 0.05f),
                new Vector3(-0.04f, 0.03f, -0.15f),
                new Vector3(0.04f, 0.03f, -0.15f),
                new Vector3(0.04f, 0.03f, 0.05f),
                new Vector3(-0.04f, 0.03f, 0.05f)
            };

            var triangles = new int[]
            {
                // Bottom face
                0, 1, 2, 0, 2, 3,
                // Top face
                4, 6, 5, 4, 7, 6,
                // Sides
                0, 4, 5, 0, 5, 1,
                1, 5, 6, 1, 6, 2,
                2, 6, 7, 2, 7, 3,
                3, 7, 4, 3, 4, 0
            };

            mesh.vertices = vertices;
            mesh.triangles = triangles;
            mesh.RecalculateNormals();
            mesh.RecalculateBounds();

            return mesh;
        }
    }
}