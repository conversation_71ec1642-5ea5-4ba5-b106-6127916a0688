from mcp.server.fastmcp import Fast<PERSON><PERSON>, Context
from typing import Dict, Any, List, Optional
from unity_connection import get_unity_connection

def register_unity_test_runner_tools(mcp: FastMCP):
    """Register Unity Test Runner tools with the MCP server."""

    @mcp.tool()
    def unity_test_runner(
        ctx: Context,
        action: str,
        test_mode: Optional[str] = None,
        test_categories: Optional[List[str]] = None,
        test_assemblies: Optional[List[str]] = None,
        run_in_background: Optional[bool] = None,
        generate_report: Optional[bool] = None,
        coverage_analysis: Optional[bool] = None,
        performance_profiling: Optional[bool] = None,
        test_filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Sistema de Execução de Testes usando Unity Test Runner APIs.

        Funcionalidades:
        - run_edit_mode_tests: Executar testes do modo editor
        - run_play_mode_tests: Executar testes do modo play
        - run_all_tests: Executar todos os testes
        - generate_test_report: Gerar relatório de testes
        - analyze_test_coverage: Analisar cobertura de código
        - profile_test_performance: Analisar performance dos testes

        Args:
            action: Operação a executar
            test_mode: Modo (edit_mode, play_mode, all)
            test_categories: Categorias de teste a executar
            test_assemblies: Assemblies de teste
            run_in_background: Executar em background
            generate_report: Gerar relatório
            coverage_analysis: Análise de cobertura
            performance_profiling: Profiling de performance
            test_filters: Filtros de teste

        Returns:
            Dictionary com resultados ('success', 'message', 'data')
        """
        try:
            params = {
                "action": action,
                "test_mode": test_mode,
                "test_categories": test_categories,
                "test_assemblies": test_assemblies,
                "run_in_background": run_in_background,
                "generate_report": generate_report,
                "coverage_analysis": coverage_analysis,
                "performance_profiling": performance_profiling,
                "test_filters": test_filters
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("unity_test_runner", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Test runner operation completed successfully."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Failed to execute test runner operation.")
                }

        except Exception as e:
            return {"success": False, "message": f"Python error in unity test runner: {str(e)}"}
