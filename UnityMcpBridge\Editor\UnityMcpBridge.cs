using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEngine;
using UnityMcpBridge.Editor.Helpers;
using UnityMcpBridge.Editor.Models;
using UnityMcpBridge.Editor.Tools;

namespace UnityMcpBridge.Editor
{
    [InitializeOnLoad]
    public static partial class UnityMcpBridge
    {
        private static TcpListener listener;
        private static bool isRunning = false;
        private static readonly object lockObj = new();
        private static Dictionary<
            string,
            (string commandJson, TaskCompletionSource<string> tcs)
        > commandQueue = new();
        private static int unityPort = 6400; // Default port, can be changed if in use

        public static bool IsRunning => isRunning;

        private static bool IsPortAvailable(int port)
        {
            try
            {
                var tcpListener = new TcpListener(IPAddress.Any, port);
                tcpListener.Start();
                tcpListener.Stop();
                return true;
            }
            catch (SocketException)
            {
                return false;
            }
        }

        private static int FindAvailablePort(int startPort = 6400)
        {
            for (int port = startPort; port < startPort + 100; port++)
            {
                if (IsPortAvailable(port))
                {
                    return port;
                }
            }
            throw new InvalidOperationException($"No available ports found in range {startPort}-{startPort + 99}");
        }

        public static bool FolderExists(string path)
        {
            if (string.IsNullOrEmpty(path))
            {
                return false;
            }

            if (path.Equals("Assets", StringComparison.OrdinalIgnoreCase))
            {
                return true;
            }

            string fullPath = Path.Combine(
                Application.dataPath,
                path.StartsWith("Assets/") ? path[7..] : path
            );
            return Directory.Exists(fullPath);
        }

        static UnityMcpBridge()
        {
            Start();
            EditorApplication.quitting += Stop;
        }

        public static void Start()
        {
            Stop();

            try
            {
                ServerInstaller.EnsureServerInstalled();
            }
            catch (Exception ex)
            {
                Debug.LogError($"Failed to ensure UnityMcpServer is installed: {ex.Message}");
            }

            if (isRunning)
            {
                return;
            }

            try
            {
                // Find an available port if the default one is in use
                if (!IsPortAvailable(unityPort))
                {
                    int newPort = FindAvailablePort(unityPort);
                    Debug.LogWarning($"Port {unityPort} is in use. Using port {newPort} instead.");
                    unityPort = newPort;
                }
                
                listener = new TcpListener(IPAddress.Loopback, unityPort);
                listener.Start();
                isRunning = true;
                Debug.Log($"UnityMcpBridge started on port {unityPort}.");
                // Assuming ListenerLoop and ProcessCommands are defined elsewhere
                Task.Run(ListenerLoop);
                EditorApplication.update += ProcessCommands;
            }
            catch (SocketException ex)
            {
                if (ex.SocketErrorCode == SocketError.AddressAlreadyInUse)
                {
                    Debug.LogError(
                        $"Port {unityPort} is already in use. Ensure no other instances are running or change the port."
                    );
                }
                else
                {
                    Debug.LogError($"Failed to start TCP listener: {ex.Message}");
                }
            }
        }

        public static void Stop()
        {
            if (!isRunning)
            {
                return;
            }

            try
            {
                listener?.Stop();
                listener = null;
                isRunning = false;
                EditorApplication.update -= ProcessCommands;
                Debug.Log("UnityMcpBridge stopped.");
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error stopping UnityMcpBridge: {ex.Message}");
            }
        }

        private static async Task ListenerLoop()
        {
            while (isRunning)
            {
                try
                {
                    TcpClient client = await listener.AcceptTcpClientAsync();
                    // Enable basic socket keepalive
                    client.Client.SetSocketOption(
                        SocketOptionLevel.Socket,
                        SocketOptionName.KeepAlive,
                        true
                    );

                    // Set longer receive timeout to prevent quick disconnections
                    client.ReceiveTimeout = 60000; // 60 seconds

                    // Fire and forget each client connection
                    _ = HandleClientAsync(client);
                }
                catch (Exception ex)
                {
                    if (isRunning)
                    {
                        Debug.LogError($"Listener error: {ex.Message}");
                    }
                }
            }
        }

        private static async Task HandleClientAsync(TcpClient client)
        {
            using (client)
            using (NetworkStream stream = client.GetStream())
            {
                byte[] buffer = new byte[8192];
                while (isRunning)
                {
                    try
                    {
                        int bytesRead = await stream.ReadAsync(buffer, 0, buffer.Length);
                        if (bytesRead == 0)
                        {
                            break; // Client disconnected
                        }

                        string commandText = System.Text.Encoding.UTF8.GetString(
                            buffer,
                            0,
                            bytesRead
                        );
                        string commandId = Guid.NewGuid().ToString();
                        TaskCompletionSource<string> tcs = new();

                        // Special handling for ping command to avoid JSON parsing
                        if (commandText.Trim() == "ping")
                        {
                            // Direct response to ping without going through JSON parsing
                            byte[] pingResponseBytes = System.Text.Encoding.UTF8.GetBytes(
                                /*lang=json,strict*/
                                "{\"status\":\"success\",\"result\":{\"message\":\"pong\"}}"
                            );
                            await stream.WriteAsync(pingResponseBytes, 0, pingResponseBytes.Length);
                            continue;
                        }

                        lock (lockObj)
                        {
                            commandQueue[commandId] = (commandText, tcs);
                        }

                        string response = await tcs.Task;
                        byte[] responseBytes = System.Text.Encoding.UTF8.GetBytes(response);
                        await stream.WriteAsync(responseBytes, 0, responseBytes.Length);
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"Client handler error: {ex.Message}");
                        break;
                    }
                }
            }
        }

        private static void ProcessCommands()
        {
            List<string> processedIds = new();
            lock (lockObj)
            {
                foreach (
                    KeyValuePair<
                        string,
                        (string commandJson, TaskCompletionSource<string> tcs)
                    > kvp in commandQueue.ToList()
                )
                {
                    string id = kvp.Key;
                    string commandText = kvp.Value.commandJson;
                    TaskCompletionSource<string> tcs = kvp.Value.tcs;

                    try
                    {
                        // Special case handling
                        if (string.IsNullOrEmpty(commandText))
                        {
                            var emptyResponse = new
                            {
                                status = "error",
                                error = "Empty command received",
                            };
                            tcs.SetResult(JsonConvert.SerializeObject(emptyResponse));
                            processedIds.Add(id);
                            continue;
                        }

                        // Trim the command text to remove any whitespace
                        commandText = commandText.Trim();

                        // Non-JSON direct commands handling (like ping)
                        if (commandText == "ping")
                        {
                            var pingResponse = new
                            {
                                status = "success",
                                result = new { message = "pong" },
                            };
                            tcs.SetResult(JsonConvert.SerializeObject(pingResponse));
                            processedIds.Add(id);
                            continue;
                        }

                        // Check if the command is valid JSON before attempting to deserialize
                        if (!IsValidJson(commandText))
                        {
                            var invalidJsonResponse = new
                            {
                                status = "error",
                                error = "Invalid JSON format",
                                receivedText = commandText.Length > 50
                                    ? commandText[..50] + "..."
                                    : commandText,
                            };
                            tcs.SetResult(JsonConvert.SerializeObject(invalidJsonResponse));
                            processedIds.Add(id);
                            continue;
                        }

                        // Normal JSON command processing
                        Command command = JsonConvert.DeserializeObject<Command>(commandText);
                        if (command == null)
                        {
                            var nullCommandResponse = new
                            {
                                status = "error",
                                error = "Command deserialized to null",
                                details = "The command was valid JSON but could not be deserialized to a Command object",
                            };
                            tcs.SetResult(JsonConvert.SerializeObject(nullCommandResponse));
                        }
                        else
                        {
                            string responseJson = ExecuteCommand(command);
                            tcs.SetResult(responseJson);
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.LogError($"Error processing command: {ex.Message}\n{ex.StackTrace}");

                        var response = new
                        {
                            status = "error",
                            error = ex.Message,
                            commandType = "Unknown (error during processing)",
                            receivedText = commandText?.Length > 50
                                ? commandText[..50] + "..."
                                : commandText,
                        };
                        string responseJson = JsonConvert.SerializeObject(response);
                        tcs.SetResult(responseJson);
                    }

                    processedIds.Add(id);
                }

                foreach (string id in processedIds)
                {
                    commandQueue.Remove(id);
                }
            }
        }

        // Helper method to check if a string is valid JSON
        private static bool IsValidJson(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
            {
                return false;
            }

            text = text.Trim();
            if (
                (text.StartsWith("{") && text.EndsWith("}"))
                || // Object
                (text.StartsWith("[") && text.EndsWith("]"))
            ) // Array
            {
                try
                {
                    JToken.Parse(text);
                    return true;
                }
                catch
                {
                    return false;
                }
            }

            return false;
        }

        private static string ExecuteCommand(Command command)
        {
            try
            {
                if (string.IsNullOrEmpty(command.type))
                {
                    var errorResponse = new
                    {
                        status = "error",
                        error = "Command type cannot be empty",
                        details = "A valid command type is required for processing",
                    };
                    return JsonConvert.SerializeObject(errorResponse);
                }

                // Handle ping command for connection verification
                if (command.type.Equals("ping", StringComparison.OrdinalIgnoreCase))
                {
                    var pingResponse = new
                    {
                        status = "success",
                        result = new { message = "pong" },
                    };
                    return JsonConvert.SerializeObject(pingResponse);
                }

                // Use JObject for parameters as the new handlers likely expect this
                JObject paramsObject = command.@params ?? new JObject();

                // Debug logging to see what command type is received
                // Debug.LogError($"DEBUG: Received command type: '{command.type}'");

                // Route command based on the new tool structure from the refactor plan
                object result = command.type switch
                {
                    // Maps the command type (tool name) to the corresponding handler's static HandleCommand method
                    // Assumes each handler class has a static method named 'HandleCommand' that takes JObject parameters
                    "manage_script" => ManageScript.HandleCommand(paramsObject),
                    "manage_scene" => ManageScene.HandleCommand(paramsObject),
                    "manage_editor" => ManageEditor.HandleCommand(paramsObject),
                    "manage_gameobject" => ManageGameObject.HandleCommand(paramsObject),
                    "manage_asset" => ManageAsset.HandleCommand(paramsObject),
                    "read_console" => ReadConsole.HandleCommand(paramsObject),
                    "execute_menu_item" => ExecuteMenuItem.HandleCommand(paramsObject),
                    "audio_system" => AudioSystem.HandleCommand(paramsObject),
                    "advanced_audio" => AdvancedAudio.HandleCommand(paramsObject),
                    "lighting_rendering" => LightingRendering.HandleCommand(paramsObject),
                    "configure_multiplayer_netcode" => MultiplayerNetworking.ConfigureMultiplayerNetcode(paramsObject),
                    "setup_unity_gaming_services" => MultiplayerNetworking.SetupUnityGamingServices(paramsObject),
                    "start_multiplayer_host" => MultiplayerNetworking.StartMultiplayerHost(paramsObject),
                    "connect_to_multiplayer_server" => MultiplayerNetworking.ConnectToMultiplayerServer(paramsObject),
                    "join_multiplayer_lobby" => MultiplayerNetworking.JoinMultiplayerLobby(paramsObject),
                    "get_multiplayer_status" => MultiplayerNetworking.GetMultiplayerStatus(paramsObject),
                    "get_connected_players" => MultiplayerNetworking.GetConnectedPlayers(paramsObject),
                    "sync_player_data" => MultiplayerNetworking.SyncPlayerData(paramsObject),
                    "send_multiplayer_message" => MultiplayerNetworking.SendMultiplayerMessage(paramsObject),
                    "configure_anti_cheat" => MultiplayerNetworking.ConfigureAntiCheat(paramsObject),
                    "setup_network_culling" => MultiplayerNetworking.SetupNetworkCulling(paramsObject),
                    "configure_lag_compensation" => MultiplayerNetworking.ConfigureLagCompensation(paramsObject),
                    "setup_dedicated_servers" => MultiplayerNetworking.SetupDedicatedServers(paramsObject),
                    "monitor_network_performance" => MultiplayerNetworking.MonitorNetworkPerformance(paramsObject),
                    "optimize_bandwidth_usage" => MultiplayerNetworking.OptimizeBandwidthUsage(paramsObject),
                    
                    // Multiplayer Sessions Operations
                    "setup_multiplayer_sessions" => MultiplayerSessions.HandleCommand(paramsObject),
                    "configure_distributed_authority" => MultiplayerSessions.HandleCommand(paramsObject), // Using MultiplayerSessions as fallback
                    "setup_session_matchmaking" => SessionMatchmaking.HandleCommand(paramsObject),
                    "create_session_persistence" => SessionPersistence.HandleCommand(paramsObject),
                    "configure_session_migration" => SessionMigration.HandleCommand(paramsObject),
                    
                    "setup_navmesh_system" => AIRuntime.HandleCommand(paramsObject),
                    "setup_pathfinding" => AIRuntime.HandleCommand(paramsObject),
                    "setup_crowd_simulation" => AIRuntime.HandleCommand(paramsObject),
                    "setup_machine_learning" => AIRuntime.HandleCommand(paramsObject),
                    "setup_adaptive_difficulty" => AIRuntime.HandleCommand(paramsObject),
                    "create_ai_perception" => AIRuntime.HandleCommand(paramsObject),
                    "setup_ai_communication" => AIRuntime.HandleCommand(paramsObject),
                    "setup_ai_optimization" => AIRuntime.HandleCommand(paramsObject),
                    
                    // Navigation & Pathfinding Enhanced Operations
                    "setup_dynamic_navmesh_areas" => NavigationEnhanced.HandleSetupDynamicNavmeshAreas(paramsObject),
                    "configure_navmesh_costs" => NavigationEnhanced.HandleConfigureNavmeshCosts(paramsObject),
                    "setup_runtime_obstacle_avoidance" => NavigationEnhanced.HandleSetupRuntimeObstacleAvoidance(paramsObject),
                    "create_dynamic_path_modifiers" => NavigationEnhanced.HandleCreateDynamicPathModifiers(paramsObject),
                    "setup_navmesh_links" => NavigationEnhanced.HandleSetupNavmeshLinks(paramsObject),
                    "configure_navmesh_area_masks" => NavigationEnhanced.HandleConfigureAreaMasks(paramsObject),
                    
                    // Advanced AI Operations (Unity Inference Engine)
                    "advanced_ai" => AdvancedAI.HandleCommand(paramsObject),
                    "setup_inference_runtime" => AdvancedAI.HandleCommand(MergeCommandType(paramsObject, "setup_inference_runtime")),
                    "optimize_inference" => AdvancedAI.HandleCommand(MergeCommandType(paramsObject, "optimize_inference")),
                    "setup_frame_slicing" => AdvancedAI.HandleCommand(MergeCommandType(paramsObject, "setup_frame_slicing")),
                    "configure_ai_quantization" => AdvancedAI.HandleCommand(MergeCommandType(paramsObject, "configure_ai_quantization")),
                    "setup_custom_backend_dispatching" => AdvancedAI.HandleCommand(MergeCommandType(paramsObject, "setup_custom_backend_dispatching")),
                    "create_natural_language_processing" => AdvancedAI.HandleCommand(MergeCommandType(paramsObject, "create_natural_language_processing")),
                    "setup_object_recognition" => AdvancedAI.HandleCommand(MergeCommandType(paramsObject, "setup_object_recognition")),
                    "configure_sensor_data_classification" => AdvancedAI.HandleCommand(MergeCommandType(paramsObject, "configure_sensor_data_classification")),
                    
                    // Unity Inference Engine Neural Network Runtime Operations
                    "setup_inference_frame_slicing" => InferenceNeuralNetwork.HandleCommand(paramsObject),
                "create_inference_quantization_system" => InferenceNeuralNetwork.HandleCommand(paramsObject),
                "setup_inference_backend_selection" => InferenceNeuralNetwork.HandleCommand(paramsObject),
                "configure_inference_tensor_operations" => InferenceNeuralNetwork.HandleCommand(paramsObject),
                "setup_inference_memory_optimization" => InferenceNeuralNetwork.HandleCommand(paramsObject),
                "create_inference_model_visualization" => InferenceNeuralNetwork.HandleCommand(paramsObject),
                "setup_inference_async_inference" => InferenceNeuralNetwork.HandleCommand(paramsObject),
                "configure_inference_onnx_import" => InferenceNeuralNetwork.HandleCommand(paramsObject),
                    
                    // Animation Runtime Operations
                    "setup_ik_systems" => AnimationRuntime.HandleCommand(MergeCommandType(paramsObject, "setup_ik_systems")),
                    "create_facial_animation" => AnimationRuntime.HandleCommand(MergeCommandType(paramsObject, "create_facial_animation")),
                    "setup_motion_matching" => AnimationRuntime.HandleCommand(MergeCommandType(paramsObject, "setup_motion_matching")),
                    "setup_timeline_sequences" => AnimationRuntime.HandleCommand(MergeCommandType(paramsObject, "setup_timeline_sequences")),
                    "create_cinemachine_setup" => AnimationRuntime.HandleCommand(MergeCommandType(paramsObject, "create_cinemachine_setup")),
                    "create_ragdoll_physics" => AnimationRuntime.HandleCommand(MergeCommandType(paramsObject, "create_ragdoll_physics")),
                    "setup_motion_capture" => AnimationRuntime.HandleCommand(MergeCommandType(paramsObject, "setup_motion_capture")),
                    
                    // XR Runtime Operations
                    "setup_xr_toolkit" => XRRuntime.HandleCommand(MergeCommandType(paramsObject, "setup")),
                    "create_vr_player_rig" => XRRuntime.CreateVRPlayerRig(paramsObject),
                    "setup_hand_tracking" => XRRuntime.SetupHandTracking(paramsObject),
                    "create_ar_foundation_setup" => XRRuntime.CreateARFoundationSetup(paramsObject),
                    "setup_eye_tracking" => XRRuntime.HandleCommand(MergeCommandType(paramsObject, "configure")),
                    "create_spatial_anchors" => XRRuntime.HandleCommand(MergeCommandType(paramsObject, "configure")),
                    "setup_passthrough_ar" => XRRuntime.HandleCommand(MergeCommandType(paramsObject, "configure")),
                    "create_mixed_reality_setup" => XRRuntime.HandleCommand(MergeCommandType(paramsObject, "configure")),
                    "setup_haptic_feedback" => XRRuntime.HandleCommand(MergeCommandType(paramsObject, "configure")),
                    "create_xr_ui_system" => XRRuntime.HandleCommand(MergeCommandType(paramsObject, "configure")),
                    "setup_room_scale_tracking" => XRRuntime.HandleCommand(MergeCommandType(paramsObject, "configure")),
                    "create_teleportation_system" => XRRuntime.HandleCommand(MergeCommandType(paramsObject, "configure")),
                    "setup_comfort_settings" => XRRuntime.HandleCommand(MergeCommandType(paramsObject, "configure")),
                    "create_xr_input_system" => XRRuntime.HandleCommand(MergeCommandType(paramsObject, "configure")),
                    "setup_cross_platform_xr" => XRRuntime.HandleCommand(MergeCommandType(paramsObject, "configure")),
                    "create_ar_occlusion" => XRRuntime.HandleCommand(MergeCommandType(paramsObject, "configure")),
                    "setup_xr_performance_optimization" => XRRuntime.HandleCommand(MergeCommandType(paramsObject, "configure")),
                    "create_xr_analytics" => XRRuntime.CreateXRAnalytics(paramsObject),
                    
                    // Build Profiles & Graphics Runtime Operations
                    "setup_graphics_override_runtime" => BuildProfilesRuntime.HandleCommand(MergeCommandType(paramsObject, "setup_graphics_override")),
                    "configure_quality_settings_runtime" => BuildProfilesRuntime.HandleCommand(MergeCommandType(paramsObject, "configure_quality_settings")),
                    "setup_platform_specific_settings" => BuildProfilesRuntime.HandleCommand(MergeCommandType(paramsObject, "setup_platform_specific")),
                    "create_dynamic_build_variants" => BuildProfilesRuntime.HandleCommand(MergeCommandType(paramsObject, "create_dynamic_variants")),
                    "setup_runtime_asset_overrides" => BuildProfilesRuntime.HandleCommand(MergeCommandType(paramsObject, "setup_asset_overrides")),
                    "configure_compression_method_runtime" => BuildProfilesRuntime.HandleCommand(MergeCommandType(paramsObject, "configure_compression")),
                    "setup_shader_variant_collection" => BuildProfilesRuntime.HandleCommand(MergeCommandType(paramsObject, "setup_shader_variants")),
                    "configure_graphics_api_runtime" => BuildProfilesRuntime.HandleCommand(MergeCommandType(paramsObject, "configure_graphics_api")),
                    
                    // Asset Processing Operations
                    "generate_particle_system" => AssetProcessing.HandleCommand(paramsObject),
                    "create_texture_atlas" => AssetProcessing.HandleCommand(paramsObject),
                    "create_mesh_colliders" => AssetProcessing.HandleCommand(paramsObject),
                    "generate_lod_meshes" => AssetProcessing.HandleCommand(paramsObject),
                    "create_uv_mappings" => AssetProcessing.HandleCommand(paramsObject),
                    "optimize_asset_pipeline" => AssetProcessing.HandleCommand(paramsObject),
                    
                    // Project & Code Operations
                    "setup_project_dependencies" => ProjectCodeOperations.HandleCommand(paramsObject),
                    "configure_build_pipeline" => ProjectCodeOperations.HandleCommand(paramsObject),
                    "validate_project_integrity" => ProjectCodeOperations.HandleCommand(paramsObject),
                    "optimize_existing_code" => ProjectCodeOperations.HandleCommand(paramsObject),
                    "refactor_code_structure" => ProjectCodeOperations.HandleCommand(paramsObject),
                    
                    // Unity Testing Operations
                    "setup_unity_ai" => UnityTesting.HandleCommand(paramsObject),
                    "run_unity_test_suite" => UnityTestRunner.HandleCommand(paramsObject),
                    "configure_cloud_testing" => UnityCloudTesting.HandleCommand(paramsObject),
                    
                    // Advanced Physics Operations
                    "setup_physics_optimization" => AdvancedPhysics.HandleCommand(MergeCommandType(paramsObject, "setup_physics_optimization")),
                    "configure_physics_materials_runtime" => AdvancedPhysics.HandleCommand(MergeCommandType(paramsObject, "configure_physics_materials_runtime")),
                    "setup_physics_layers_runtime" => AdvancedPhysics.HandleCommand(MergeCommandType(paramsObject, "setup_physics_layers_runtime")),
                    "create_physics_events_system" => AdvancedPhysics.HandleCommand(MergeCommandType(paramsObject, "create_physics_events_system")),
                    "setup_physics_debug_visualization" => AdvancedPhysics.HandleCommand(MergeCommandType(paramsObject, "setup_physics_debug_visualization")),
                    "configure_physics_performance_profiling" => AdvancedPhysics.HandleCommand(MergeCommandType(paramsObject, "configure_physics_performance_profiling")),
                    "configure_area_masks" => AdvancedPhysics.HandleCommand(MergeCommandType(paramsObject, "configure_area_masks")),
                    
                    // WebGPU Runtime Features
                    "setup_webgpu_compute_shaders" => WebGPURuntime.HandleCommand(MergeCommandType(paramsObject, "setup_webgpu_compute_shaders")),
                    "configure_webgpu_indirect_rendering" => WebGPUIndirectRendering.HandleCommand(MergeCommandType(paramsObject, "configure_webgpu_indirect_rendering")),
                    "setup_webgpu_gpu_skinning" => WebGPUGPUSkinning.HandleCommand(MergeCommandType(paramsObject, "setup_webgpu_gpu_skinning")),
                    "create_webgpu_vfx_system" => WebGPUVFXSystem.HandleCommand(MergeCommandType(paramsObject, "create_webgpu_vfx_system")),
                    "setup_webgpu_compatibility_detection" => WebGPUCompatibilityDetection.HandleCommand(MergeCommandType(paramsObject, "setup_webgpu_compatibility_detection")),
                    
                    // Advanced Rendering Features
                    "setup_variable_rate_shading" => AdvancedRendering.HandleCommand(MergeCommandType(paramsObject, "setup_variable_rate_shading")),
                    "configure_shading_rate_image" => AdvancedRendering.HandleCommand(MergeCommandType(paramsObject, "configure_shading_rate_image")),
                    
                    // Variable Rate Shading (VRS) Features
                    "setup_vrs_custom_passes" => VariableRateShading.HandleSetupVrsCustomPasses(paramsObject),
                    "configure_vrs_shading_rate_image" => VariableRateShading.HandleConfigureShadingRateImage(paramsObject),
                    "setup_vrs_performance_scaling" => VariableRateShading.HandleSetupVrsPerformanceScaling(paramsObject),
                    "create_vrs_quality_regions" => VariableRateShading.HandleCreateVrsQualityRegions(paramsObject),
                    "setup_vrs_api_integration" => VariableRateShading.HandleSetupVrsApiIntegration(paramsObject),
                    "configure_vrs_debug_visualization" => VariableRateShading.HandleConfigureVrsDebugVisualization(paramsObject),
                    "create_deferred_plus_rendering" => AdvancedRendering.HandleCommand(MergeCommandType(paramsObject, "create_deferred_plus_rendering")),
                    "setup_pipeline_state_tracing" => AdvancedRendering.HandleCommand(MergeCommandType(paramsObject, "setup_pipeline_state_tracing")),
                    "configure_shader_variant_stripping" => AdvancedRendering.HandleCommand(MergeCommandType(paramsObject, "configure_shader_variant_stripping")),
                    "setup_3d_water_deformation" => AdvancedRendering.HandleCommand(MergeCommandType(paramsObject, "setup_3d_water_deformation")),
                    "create_water_caustics_system" => AdvancedRendering.HandleCommand(MergeCommandType(paramsObject, "create_water_caustics_system")),
                    "setup_compute_skinning" => AdvancedRendering.HandleCommand(MergeCommandType(paramsObject, "setup_compute_skinning")),
                    "configure_gpu_instancing_extensions" => AdvancedRendering.HandleCommand(MergeCommandType(paramsObject, "configure_gpu_instancing_extensions")),
                    "setup_graphics_state_collection" => GraphicsStateCollection.HandleCommand(paramsObject),
                    "setup_graphics_state_tracing" => GraphicsStateCollection.HandleCommand(paramsObject),
                    "configure_pso_prewarming" => GraphicsStateCollection.HandleCommand(paramsObject),
                    "setup_shader_compilation_monitoring" => GraphicsStateCollection.HandleCommand(paramsObject),
                    "create_shader_variant_collection" => GraphicsStateCollection.HandleCommand(paramsObject),
                    "setup_graphics_state_serialization" => GraphicsStateCollection.HandleCommand(paramsObject),
                    "configure_read_write_textures" => AdvancedRendering.HandleCommand(MergeCommandType(paramsObject, "configure_read_write_textures")),
                    
                    // Deferred+ Rendering System Operations
                    "setup_deferred_plus_pipeline" => DeferredPlusRendering.HandleCommand(MergeCommandType(paramsObject, "setup_deferred_plus_pipeline")),
                    "configure_deferred_plus_transparency" => DeferredPlusRendering.HandleCommand(MergeCommandType(paramsObject, "configure_deferred_plus_transparency")),
                    "manage_deferred_plus_lighting" => DeferredPlusRendering.HandleCommand(MergeCommandType(paramsObject, "manage_deferred_plus_lighting")),
                    "create_deferred_plus_material_system" => DeferredPlusRendering.HandleCommand(MergeCommandType(paramsObject, "create_deferred_plus_material_system")),
                    "setup_deferred_plus_debug_views" => DeferredPlusRendering.HandleCommand(MergeCommandType(paramsObject, "setup_deferred_plus_debug_views")),
                    "optimize_deferred_plus_performance" => DeferredPlusRendering.HandleCommand(MergeCommandType(paramsObject, "optimize_deferred_plus_performance")),
                    "manage_deferred_plus_shader_variants" => DeferredPlusRendering.HandleCommand(MergeCommandType(paramsObject, "manage_deferred_plus_shader_variants")),
                    
                    // UI Toolkit Runtime Features
                    "setup_ui_toolkit_runtime" => UIToolkitRuntime.HandleCommand(MergeCommandType(paramsObject, "setup")),
                    "configure_mask64field_controls" => UIToolkitRuntime.HandleCommand(MergeCommandType(paramsObject, "configure")),
                    "setup_ui_profiler_markers" => UIToolkitRuntime.HandleCommand(MergeCommandType(paramsObject, "setup")),
                    "create_ui_data_binding" => UIToolkitRuntime.HandleCommand(MergeCommandType(paramsObject, "create")),
                    "setup_ui_runtime_events" => UIToolkitRuntime.HandleCommand(MergeCommandType(paramsObject, "setup")),
                    "configure_ui_renderer_optimizations" => UIToolkitRuntime.HandleCommand(MergeCommandType(paramsObject, "optimize")),
                    "setup_ui_toolkit_animations" => UIToolkitRuntime.HandleCommand(MergeCommandType(paramsObject, "setup")),
                    "create_dynamic_ui_elements" => UIToolkitRuntime.HandleCommand(MergeCommandType(paramsObject, "create")),
                    
                    // Ray Tracing Operations
                    "update_acceleration_structure" => RaytracingOperations.HandleCommand(MergeCommandType(paramsObject, "update_acceleration_structure")),
                    "add_raytracing_instances" => RaytracingOperations.HandleCommand(MergeCommandType(paramsObject, "add_raytracing_instances")),
                    "remove_raytracing_instance" => RaytracingOperations.HandleCommand(MergeCommandType(paramsObject, "remove_raytracing_instance")),
                    "cull_raytracing_instances" => RaytracingOperations.HandleCommand(MergeCommandType(paramsObject, "cull_raytracing_instances")),
                    "configure_raytracing_pipeline" => RaytracingOperations.HandleCommand(MergeCommandType(paramsObject, "configure_raytracing_pipeline")),
                    "update_raytracing_geometry" => RaytracingOperations.HandleCommand(MergeCommandType(paramsObject, "update_raytracing_geometry")),
                    
                    // Project Auditor Runtime Operations
                    "setup_project_auditor" => ProjectAuditorRuntime.HandleCommand(paramsObject),
                    "configure_runtime_diagnostics" => ProjectAuditorRuntime.HandleCommand(paramsObject),
                    "setup_domain_reload_analysis" => ProjectAuditorRuntime.HandleCommand(paramsObject),
                    "create_asset_usage_report" => ProjectAuditorRuntime.HandleCommand(paramsObject),
                    "setup_shader_variant_analysis" => ProjectAuditorRuntime.HandleCommand(paramsObject),
                    "configure_memory_profiling" => ProjectAuditorRuntime.HandleCommand(paramsObject),
                    "configure_graphics_state_optimization" => GraphicsStateCollection.HandleCommand(paramsObject),
                    
                    // 2D Toolkit Runtime Operations
                    "setup_tilemap_generation" => TwoDToolkitRuntime.HandleCommand(paramsObject),
                    "configure_tile_sets" => TwoDToolkitRuntime.HandleCommand(paramsObject),
                    "setup_sprite_atlas_runtime" => TwoDToolkitRuntime.HandleCommand(paramsObject),
                    "create_2d_physics_system" => TwoDToolkitRuntime.HandleCommand(paramsObject),
                    "setup_2d_animation_runtime" => TwoDToolkitRuntime.HandleCommand(paramsObject),
                    
                    // Input System Runtime Operations
                    "setup_input_system_runtime" => InputSystemRuntime.HandleSetupInputSystemRuntime(paramsObject),
                    "create_input_action_maps" => InputSystemRuntime.HandleCreateInputActionMaps(paramsObject),
                    "configure_input_processors" => InputSystemRuntime.HandleConfigureInputProcessors(paramsObject),
                    "setup_input_device_management" => InputSystemRuntime.HandleSetupInputDeviceManagement(paramsObject),
                    "create_input_rebinding_ui" => InputSystemRuntime.HandleCreateInputRebindingUI(paramsObject),
                    "setup_input_event_routing" => InputSystemRuntime.HandleSetupInputEventRouting(paramsObject),
                    "configure_input_haptics" => InputSystemRuntime.HandleConfigureInputHaptics(paramsObject),
                    
                    // Water System Runtime Operations

                    "configure_water_rolling_waves" => WaterSystemRuntime.HandleCommand(MergeCommandType(paramsObject, "configure_water_rolling_waves")),
                    "setup_water_caustics_buffer" => WaterSystemRuntime.HandleCommand(MergeCommandType(paramsObject, "setup_water_caustics_buffer")),
                    "create_water_foam_system" => WaterSystemRuntime.HandleCommand(MergeCommandType(paramsObject, "create_water_foam_system")),
                    "setup_water_flow_maps" => WaterSystemRuntime.HandleCommand(MergeCommandType(paramsObject, "setup_water_flow_maps")),
                    "configure_water_mask_system" => WaterSystemRuntime.HandleCommand(MergeCommandType(paramsObject, "configure_water_mask_system")),
                    "setup_water_query_api" => WaterSystemRuntime.HandleCommand(MergeCommandType(paramsObject, "setup_water_query_api")),
                    "create_water_line_transition" => WaterSystemRuntime.HandleCommand(MergeCommandType(paramsObject, "create_water_line_transition")),
                    
                    // Bicubic Lightmap System Operations
                    "setup_bicubic_lightmap_sampling" => AdvancedRendering.HandleCommand(paramsObject), // Using AdvancedRendering for lightmap operations
                    "configure_lightmap_quality_settings" => LightmapQualitySettings.HandleCommand(paramsObject),
                    "setup_lightmap_edge_smoothing" => LightmapEdgeSmoothing.HandleCommand(paramsObject),
                    "create_lightmap_visualization_tools" => LightmapVisualizationTools.HandleCommand(paramsObject),
                    "setup_lightmap_performance_analysis" => LightmapPerformanceAnalysis.HandleCommand(paramsObject),
                    
                    // Editor Build Tools Operations
                    "setup_automated_build_pipeline" => EditorBuildTools.HandleCommand(MergeCommandType(paramsObject, "setup_automated_build_pipeline")),
                    "configure_build_profile_overrides" => EditorBuildTools.HandleCommand(MergeCommandType(paramsObject, "configure_build_profile_overrides")),
                    "setup_asset_dependency_analysis" => EditorBuildTools.HandleCommand(MergeCommandType(paramsObject, "setup_asset_dependency_analysis")),
                    "create_build_step_timing_report" => EditorBuildTools.HandleCommand(MergeCommandType(paramsObject, "create_build_step_timing_report")),
                    "setup_script_save_location_handler" => EditorBuildTools.HandleCommand(MergeCommandType(paramsObject, "setup_script_save_location_handler")),
                    "configure_main_menu_customization" => EditorBuildTools.HandleCommand(MergeCommandType(paramsObject, "configure_main_menu_customization")),
                    "setup_version_control_integration" => EditorBuildTools.HandleCommand(MergeCommandType(paramsObject, "setup_version_control_integration")),
                    
                    // AI Asset Generation Tools
                    "ai_asset_generation" => AIAssetGeneration.HandleCommand(paramsObject),
                    
                    // Advanced Procedural Generation Tools
                    "generate_procedural_mesh" => AdvancedProceduralGeneration.HandleGenerateProceduralMesh(paramsObject),
                    "create_procedural_terrain_system" => AdvancedProceduralGeneration.HandleCreateProceduralTerrainSystem(paramsObject),
                    "setup_procedural_vegetation" => AdvancedProceduralGeneration.HandleSetupProceduralVegetation(paramsObject),
                     "generate_mesh_from_heightmap" => AdvancedProceduralGeneration.HandleGenerateMeshFromHeightmap(paramsObject),
                    
                    // Procedural Character Assembly Tools
                    "assemble_character_from_parts" => ProceduralCharacterAssembly.HandleCommand(paramsObject),
                    "apply_generated_textures_to_character" => ProceduralCharacterAssembly.HandleCommand(paramsObject),
                    "setup_procedural_rigging" => ProceduralCharacterAssembly.HandleCommand(paramsObject),
                    "create_character_animation_controller" => ProceduralCharacterAssembly.HandleCommand(paramsObject),
                    "generate_character_variations" => ProceduralCharacterAssembly.HandleCommand(paramsObject),
                    "setup_character_lods_procedural" => ProceduralCharacterAssembly.HandleCommand(paramsObject),
                    "create_character_customization_system" => ProceduralCharacterAssembly.HandleCommand(paramsObject),
                    "export_assembled_character_prefab" => ProceduralCharacterAssembly.HandleCommand(paramsObject),
                    
                    // Dynamic Game Systems Operations
                    "create_generative_state_machine" => DynamicGameSystems.HandleCommand(MergeCommandType(paramsObject, "create_generative_state_machine")),
                    "attach_behaviour_script_to_state" => DynamicGameSystems.HandleCommand(MergeCommandType(paramsObject, "attach_behaviour_script_to_state")),
                    "setup_procedural_event_system" => DynamicGameSystems.HandleCommand(MergeCommandType(paramsObject, "setup_procedural_event_system")),
                    "generate_gameplay_rules_engine" => DynamicGameSystems.HandleCommand(MergeCommandType(paramsObject, "generate_gameplay_rules_engine")),
                    "create_dynamic_skill_tree" => DynamicGameSystems.HandleCommand(MergeCommandType(paramsObject, "create_dynamic_skill_tree")),
                    "setup_buff_debuff_system" => DynamicGameSystems.HandleCommand(MergeCommandType(paramsObject, "setup_buff_debuff_system")),
                    "generate_ai_behaviour_tree" => DynamicGameSystems.HandleCommand(MergeCommandType(paramsObject, "generate_ai_behaviour_tree")),
                    "configure_game_state_manager" => DynamicGameSystems.HandleCommand(MergeCommandType(paramsObject, "configure_game_state_manager")),
                    "create_objective_tracker_system" => DynamicGameSystems.HandleCommand(MergeCommandType(paramsObject, "create_objective_tracker_system")),
                    
                    // Procedural Narrative & Quest Generation Tools
                    "generate_quest_state_machine" => ProceduralNarrative.HandleCommand(MergeCommandType(paramsObject, "create")),
                    "create_quest_objective_generator" => ProceduralNarrative.HandleCommand(MergeCommandType(paramsObject, "create")),
                    "setup_inference_dialogue_generator" => ProceduralNarrative.HandleCommand(MergeCommandType(paramsObject, "setup")),
                    "generate_branching_dialogue_tree" => ProceduralNarrative.HandleCommand(MergeCommandType(paramsObject, "generate")),
                    "create_narrative_event_chain" => ProceduralNarrative.HandleCommand(MergeCommandType(paramsObject, "create")),
                    "setup_dynamic_lore_system" => ProceduralNarrative.HandleCommand(MergeCommandType(paramsObject, "setup")),
                    "generate_npc_backstory" => ProceduralNarrative.HandleCommand(MergeCommandType(paramsObject, "generate")),
                    "create_quest_reward_generator" => ProceduralNarrative.HandleCommand(MergeCommandType(paramsObject, "create")),
                    "link_quest_to_gameplay_events" => ProceduralNarrative.HandleCommand(MergeCommandType(paramsObject, "configure")),
                    
                    // Automated Cinematography Tools
                    "generate_timeline_from_events" => AutomatedCinematics.HandleCommand(paramsObject),
                    "setup_cinemachine_procedural_camera" => AutomatedCinematics.HandleCommand(paramsObject),
                    "create_dynamic_shot_list" => AutomatedCinematics.HandleCommand(paramsObject),
                    "sequence_generated_animations_on_timeline" => AutomatedCinematics.HandleCommand(paramsObject),
                    "add_generated_audio_to_timeline" => AutomatedCinematics.HandleCommand(paramsObject),
                    "trigger_cinematic_from_gameplay" => AutomatedCinematics.HandleCommand(paramsObject),
                    "control_post_processing_on_timeline" => AutomatedCinematics.HandleCommand(paramsObject),
                    
                    "HandleWorldSimulationSystems" => WorldSimulationSystems.HandleCommand(paramsObject),
                    
                    // Unity AI Assistant Operations
                    "setup_ai_assistant" => UnityAIAssistant.HandleCommand(MergeCommandType(paramsObject, "setup_ai_assistant")),
                    "generate_code_with_assistant" => UnityAIAssistant.HandleCommand(MergeCommandType(paramsObject, "generate_code_with_assistant")),
                    "troubleshoot_with_assistant" => UnityAIAssistant.HandleCommand(MergeCommandType(paramsObject, "troubleshoot_with_assistant")),
                    "automate_tasks_with_assistant" => UnityAIAssistant.HandleCommand(MergeCommandType(paramsObject, "automate_tasks_with_assistant")),
                    "scene_editing_with_assistant" => UnityAIAssistant.HandleCommand(MergeCommandType(paramsObject, "scene_editing_with_assistant")),
                    "asset_management_with_assistant" => UnityAIAssistant.HandleCommand(MergeCommandType(paramsObject, "asset_management_with_assistant")),
                    "project_analysis_with_assistant" => UnityAIAssistant.HandleCommand(MergeCommandType(paramsObject, "project_analysis_with_assistant")),
                    "assistant_workflow_automation" => UnityAIAssistant.HandleCommand(MergeCommandType(paramsObject, "assistant_workflow_automation")),
                    
                    // NEW Unity 6.2 Procedural Generation Tools
                    "procedural_body_part_generation" => ProceduralBodyPartGeneration.HandleCommand(paramsObject),
                    "procedural_texture_generation" => ProceduralTextureGeneration.HandleCommand(paramsObject),
                    "procedural_skeleton_generation" => ProceduralSkeletonGeneration.HandleCommand(paramsObject),
                    "procedural_animation_generation" => ProceduralAnimationGeneration.HandleCommand(paramsObject),
                    "character_gameplay_system" => CharacterGameplaySystem.HandleCommand(paramsObject),
                    
                    // MASTER TOOL - One-Click Character Creator
                    "one_click_character_creator" => OneClickCharacterCreator.HandleCommand(paramsObject),

                    // Novos comandos adicionados para sincronização
                    "ai_adaptive_jungle" => AIAdaptiveJungle.HandleCommand(paramsObject),
                    "champion_fusion_system" => ChampionFusionSystem.HandleCommand(paramsObject),
                    "dynamic_realm_system" => DynamicRealmSystem.HandleCommand(paramsObject),
                    "session_matchmaking" => SessionMatchmaking.HandleCommand(paramsObject),
                    "session_migration" => SessionMigration.HandleCommand(paramsObject),
                    "session_persistence" => SessionPersistence.HandleCommand(paramsObject),
                    "unity_cloud_testing" => UnityCloudTesting.HandleCommand(paramsObject),
                    "unity_test_runner" => UnityTestRunner.HandleCommand(paramsObject),

                    _ => throw new ArgumentException(
                        $"Unknown or unsupported command type: {command.type}"
                    ),
                };

                // Standard success response format
                var response = new { status = "success", result };
                return JsonConvert.SerializeObject(response);
            }
            catch (Exception ex)
            {
                // Log the detailed error in Unity for debugging
                Debug.LogError(
                    $"Error executing command '{command?.type ?? "Unknown"}': {ex.Message}\n{ex.StackTrace}"
                );

                // Standard error response format
                var response = new
                {
                    status = "error",
                    error = ex.Message, // Provide the specific error message
                    command = command?.type ?? "Unknown", // Include the command type if available
                    stackTrace = ex.StackTrace, // Include stack trace for detailed debugging
                    paramsSummary = command?.@params != null
                        ? GetParamsSummary(command.@params)
                        : "No parameters", // Summarize parameters for context
                };
                return JsonConvert.SerializeObject(response);
            }
        }

        // Helper method to get a summary of parameters for error reporting
        private static string GetParamsSummary(JObject @params)
        {
            try
            {
                return @params == null || !@params.HasValues
                    ? "No parameters"
                    : string.Join(
                        ", ",
                        @params
                            .Properties()
                            .Select(static p =>
                                $"{p.Name}: {p.Value?.ToString()?[..Math.Min(20, p.Value?.ToString()?.Length ?? 0)]}"
                            )
                    );
            }
            catch
            {
                return "Could not summarize parameters";
            }
        }

        // Helper method to merge command type with parameters
        private static JObject MergeCommandType(JObject @params, string commandType)
        {
            var result = new JObject { ["commandType"] = commandType };
            if (@params != null)
            {
                foreach (var property in @params.Properties())
                {
                    result[property.Name] = property.Value;
                }
            }
            return result;
        }

        // Helper method to convert JObject to object
        private static object ConvertJObjectToObject(JObject jObject)
        {
            if (jObject == null) return null;
            
            return new
            {
                success = jObject["success"]?.Value<bool>() ?? false,
                message = jObject["message"]?.ToString() ?? "",
                data = jObject["data"]?.ToObject<object>()
            };
        }
    }
}
