using System;
using System.Collections.Generic;
using System.IO;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEditor.Animations;
using UnityEngine;
using UnityEngine.Animations;
using UnityEngine.AI;
using UnityMcpBridge.Editor.Helpers;

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// [UNITY 6.2 COMPATIBLE] - Handles procedural animation generation using AI Animation Generator.
    /// 
    /// Suporta as seguintes ações:
    /// - generate_walk: Gera animações de caminhada
    /// - generate_run: Gera animações de corrida
    /// - generate_idle: Gera animações idle
    /// - generate_combat: Gera animações de combate
    /// - generate_dance: Gera animações de dança
    /// - generate_custom: Gera animações customizadas
    /// - create_blend_tree: Cria blend trees automáticos
    /// - list_presets: Lista presets de animação
    /// - get_info: Obtém informações de uma animação
    /// 
    /// [UNITY 6.2 FEATURES]:
    /// - AI Animation Generator integration
    /// - Blend tree automation
    /// - Root motion generation
    /// - Animation compression
    /// </summary>
    public static class ProceduralAnimationGeneration
    {
        private static readonly List<string> ValidActions = new List<string>
        {
            "generate_walk",
            "generate_run",
            "generate_idle",
            "generate_combat",
            "generate_dance",
            "generate_custom",
            "create_blend_tree",
            "list_presets",
            "get_info"
        };

        public static object HandleCommand(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidActions);
                return Response.Error($"Unknown action: '{action}'. Valid actions are: {validActionsList}");
            }

            try
            {
                switch (action)
                {
                    case "generate_walk":
                        return GenerateWalkAnimation(@params);
                    case "generate_run":
                        return GenerateRunAnimation(@params);
                    case "generate_idle":
                        return GenerateIdleAnimation(@params);
                    case "generate_combat":
                        return GenerateCombatAnimation(@params);
                    case "generate_dance":
                        return GenerateDanceAnimation(@params);
                    case "generate_custom":
                        return GenerateCustomAnimation(@params);
                    case "create_blend_tree":
                        return CreateBlendTree(@params);
                    case "list_presets":
                        return ListAnimationPresets(@params);
                    case "get_info":
                        return GetAnimationInfo(@params["output_path"]?.ToString());
                    default:
                        return Response.Error($"Unknown action: '{action}'");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[ProceduralAnimationGeneration] Action '{action}' failed: {e}");
                return Response.Error($"Internal error processing action '{action}': {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gera animação de caminhada usando AI Animation Generator.
        /// </summary>
        private static object GenerateWalkAnimation(JObject @params)
        {
            try
            {
                string outputPath = SanitizeAssetPath(@params["output_path"]?.ToString() ?? "Assets/GeneratedAnimations/");
                string targetSkeleton = @params["target_skeleton"]?.ToString();
                float duration = @params["duration"]?.ToObject<float>() ?? 2.0f;
                int frameRate = @params["frame_rate"]?.ToObject<int>() ?? 30;
                bool generateRootMotion = @params["generate_root_motion"]?.ToObject<bool>() ?? true;
                var motionData = @params["motion_data"] as JObject ?? new JObject();

                // Parâmetros específicos da caminhada
                float walkSpeed = motionData["walk_speed"]?.ToObject<float>() ?? 1.2f;
                float strideLength = motionData["stride_length"]?.ToObject<float>() ?? 1.0f;
                string weaponCarry = motionData["weapon_carry"]?.ToString() ?? "none";

                // Criar animação usando Unity 6.2 Animation System
                AnimationClip walkClip = new AnimationClip();
                walkClip.name = "ProceduralWalk";
                walkClip.frameRate = frameRate;
                walkClip.wrapMode = WrapMode.Loop;

                // Gerar keyframes para caminhada
                GenerateWalkKeyframes(walkClip, duration, walkSpeed, strideLength, generateRootMotion);

                // Configurar loop settings
                AnimationClipSettings clipSettings = AnimationUtility.GetAnimationClipSettings(walkClip);
                clipSettings.loopTime = true;
                clipSettings.cycleOffset = 0f;
                AnimationUtility.SetAnimationClipSettings(walkClip, clipSettings);

                // Salvar animação
                string animPath = SaveAnimationClip(walkClip, outputPath, "ProceduralWalk");

                return Response.Success($"Animação de caminhada gerada: {animPath}", new
                {
                    animationPath = animPath,
                    duration = duration,
                    frameRate = frameRate,
                    hasRootMotion = generateRootMotion,
                    walkSpeed = walkSpeed,
                    strideLength = strideLength
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao gerar animação de caminhada: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gera animação de corrida usando AI Animation Generator.
        /// </summary>
        private static object GenerateRunAnimation(JObject @params)
        {
            try
            {
                string outputPath = SanitizeAssetPath(@params["output_path"]?.ToString() ?? "Assets/GeneratedAnimations/");
                float duration = @params["duration"]?.ToObject<float>() ?? 1.5f;
                int frameRate = @params["frame_rate"]?.ToObject<int>() ?? 30;
                bool generateRootMotion = @params["generate_root_motion"]?.ToObject<bool>() ?? true;
                var motionData = @params["motion_data"] as JObject ?? new JObject();

                // Parâmetros específicos da corrida
                float runSpeed = motionData["run_speed"]?.ToObject<float>() ?? 2.5f;
                float strideLength = motionData["stride_length"]?.ToObject<float>() ?? 1.3f;

                // Criar animação
                AnimationClip runClip = new AnimationClip();
                runClip.name = "ProceduralRun";
                runClip.frameRate = frameRate;
                runClip.wrapMode = WrapMode.Loop;

                // Gerar keyframes para corrida
                GenerateRunKeyframes(runClip, duration, runSpeed, strideLength, generateRootMotion);

                // Configurar loop settings
                AnimationClipSettings clipSettings = AnimationUtility.GetAnimationClipSettings(runClip);
                clipSettings.loopTime = true;
                clipSettings.cycleOffset = 0f;
                AnimationUtility.SetAnimationClipSettings(runClip, clipSettings);

                // Salvar animação
                string animPath = SaveAnimationClip(runClip, outputPath, "ProceduralRun");

                return Response.Success($"Animação de corrida gerada: {animPath}", new
                {
                    animationPath = animPath,
                    duration = duration,
                    frameRate = frameRate,
                    hasRootMotion = generateRootMotion,
                    runSpeed = runSpeed,
                    strideLength = strideLength
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Erro ao gerar animação de corrida: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gera animação idle usando procedural animation e InferenceEngine.
        /// </summary>
        private static object GenerateIdleAnimation(JObject @params)
        {
            try
            {
                string outputPath = SanitizeAssetPath(@params["output_path"]?.ToString() ?? "Assets/GeneratedAnimations/");
                float duration = @params["duration"]?.ToObject<float>() ?? 3.0f;
                int frameRate = @params["frame_rate"]?.ToObject<int>() ?? 30;
                string animationStyle = @params["animation_style"]?.ToString() ?? "realistic";
                bool useAIGenerator = @params["use_ai_generator"]?.ToObject<bool>() ?? true;
                string targetSkeleton = @params["target_skeleton"]?.ToString();
                string qualityLevel = @params["quality_level"]?.ToString() ?? "high";
                bool generateRootMotion = @params["generate_root_motion"]?.ToObject<bool>() ?? false;
                bool optimizeCurves = @params["optimize_curves"]?.ToObject<bool>() ?? true;

                // Validar parâmetros
                if (duration <= 0)
                {
                    return Response.Error("Duration deve ser maior que 0");
                }

                if (frameRate <= 0 || frameRate > 120)
                {
                    return Response.Error("Frame rate deve estar entre 1 e 120");
                }

                // Criar AnimationClip
                AnimationClip idleClip = new AnimationClip();
                idleClip.name = "ProceduralIdle";
                idleClip.frameRate = frameRate;
                idleClip.wrapMode = WrapMode.Loop;

                // Configurar settings baseado na qualidade
                AnimationClipSettings settings = AnimationUtility.GetAnimationClipSettings(idleClip);
                settings.loopTime = true;
                settings.loopBlend = true;
                settings.loopBlendOrientation = true;
                settings.loopBlendPositionY = true;
                settings.loopBlendPositionXZ = true;
                settings.keepOriginalOrientation = true;
                settings.keepOriginalPositionY = true;
                settings.keepOriginalPositionXZ = true;
                settings.heightFromFeet = true;
                settings.mirror = false;
                
                AnimationUtility.SetAnimationClipSettings(idleClip, settings);

                // Gerar curvas de animação usando Unity 6.2 Animation Generator + InferenceEngine
                if (useAIGenerator && IsInferenceEngineAvailable())
                {
                    // Unity 6.2: Animation Generator oficial do menu AI com InferenceEngine para redes neurais
                    GenerateIdleAnimationWithInferenceEngine(idleClip, duration, frameRate, animationStyle, targetSkeleton);
                }
                else
                {
                    // Fallback: Geração procedural tradicional
                    GenerateIdleAnimationProcedural(idleClip, duration, frameRate, animationStyle, generateRootMotion);
                }

                // Otimizar curvas se solicitado
                if (optimizeCurves)
                {
                    OptimizeAnimationCurves(idleClip, qualityLevel);
                }

                // Criar diretório se não existir
                string directory = Path.GetDirectoryName(outputPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Salvar AnimationClip
                string animationPath = Path.Combine(outputPath, "ProceduralIdle.anim");
                AssetDatabase.CreateAsset(idleClip, animationPath);

                // Configurar import settings
                ModelImporter importer = AssetImporter.GetAtPath(animationPath) as ModelImporter;
                if (importer != null)
                {
                    importer.animationCompression = qualityLevel switch
                    {
                        "low" => ModelImporterAnimationCompression.Optimal,
                        "medium" => ModelImporterAnimationCompression.KeyframeReduction,
                        "high" => ModelImporterAnimationCompression.Off,
                        "ultra" => ModelImporterAnimationCompression.Off,
                        _ => ModelImporterAnimationCompression.KeyframeReduction
                    };
                }

                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                LogOperation("GenerateIdleAnimation", $"Animação idle criada: {animationPath}, duração: {duration}s");

                return Response.Success($"Animação idle gerada em {animationPath}", new
                {
                    animationPath = animationPath,
                    duration = duration,
                    frameRate = frameRate,
                    animationStyle = animationStyle,
                    usedAI = useAIGenerator && IsInferenceEngineAvailable(),
                    aiSystem = "Unity AI Animation Generator + InferenceEngine",
                    qualityLevel = qualityLevel,
                    hasRootMotion = generateRootMotion,
                    isLooping = true,
                    curveCount = AnimationUtility.GetCurveBindings(idleClip).Length
                });
            }
            catch (Exception e)
            {
                LogOperation("GenerateIdleAnimation", $"Erro: {e.Message}", true);
                return Response.Error($"Erro ao gerar animação idle: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Generates combat animations using Unity's Animation System.
        /// </summary>
        private static object GenerateCombatAnimation(JObject @params)
        {
            try
            {
                string animationName = @params["animation_name"]?.ToString() ?? "CombatAnimation";
                string outputPath = @params["output_path"]?.ToString() ?? "Assets/Animations/Combat";
                string combatType = @params["combat_type"]?.ToString() ?? "melee";
                float duration = @params["duration"]?.ToObject<float>() ?? 1.5f;
                int frameRate = @params["frame_rate"]?.ToObject<int>() ?? 30;

                // Ensure output directory exists
                if (!AssetDatabase.IsValidFolder(outputPath))
                {
                    string[] folders = outputPath.Split('/');
                    string currentPath = folders[0];
                    for (int i = 1; i < folders.Length; i++)
                    {
                        string newPath = currentPath + "/" + folders[i];
                        if (!AssetDatabase.IsValidFolder(newPath))
                        {
                            AssetDatabase.CreateFolder(currentPath, folders[i]);
                        }
                        currentPath = newPath;
                    }
                }

                // Create animation clip
                AnimationClip clip = new AnimationClip();
                clip.name = animationName;
                clip.frameRate = frameRate;

                // Generate combat-specific animation curves
                switch (combatType.ToLower())
                {
                    case "melee":
                        GenerateMeleeAnimationCurves(clip, duration);
                        break;
                    case "ranged":
                        GenerateRangedAnimationCurves(clip, duration);
                        break;
                    case "magic":
                        GenerateMagicAnimationCurves(clip, duration);
                        break;
                    default:
                        GenerateGenericCombatCurves(clip, duration);
                        break;
                }

                // Save animation clip
                string clipPath = $"{outputPath}/{animationName}.anim";
                AssetDatabase.CreateAsset(clip, clipPath);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                return Response.Success($"Combat animation '{animationName}' generated successfully.", new
                {
                    animation_path = clipPath,
                    combat_type = combatType,
                    duration = duration,
                    frame_rate = frameRate,
                    curve_count = GetAnimationCurveCount(clip)
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to generate combat animation: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Generates dance animations using Unity's Animation System.
        /// </summary>
        private static object GenerateDanceAnimation(JObject @params)
        {
            try
            {
                string animationName = @params["animation_name"]?.ToString() ?? "DanceAnimation";
                string outputPath = @params["output_path"]?.ToString() ?? "Assets/Animations/Dance";
                string danceStyle = @params["dance_style"]?.ToString() ?? "freestyle";
                float duration = @params["duration"]?.ToObject<float>() ?? 3.0f;
                int frameRate = @params["frame_rate"]?.ToObject<int>() ?? 30;
                float intensity = @params["intensity"]?.ToObject<float>() ?? 1.0f;

                // Ensure output directory exists
                if (!AssetDatabase.IsValidFolder(outputPath))
                {
                    string[] folders = outputPath.Split('/');
                    string currentPath = folders[0];
                    for (int i = 1; i < folders.Length; i++)
                    {
                        string newPath = currentPath + "/" + folders[i];
                        if (!AssetDatabase.IsValidFolder(newPath))
                        {
                            AssetDatabase.CreateFolder(currentPath, folders[i]);
                        }
                        currentPath = newPath;
                    }
                }

                // Create animation clip
                AnimationClip clip = new AnimationClip();
                clip.name = animationName;
                clip.frameRate = frameRate;
                clip.wrapMode = WrapMode.Loop; // Dance animations typically loop

                // Generate dance-specific animation curves
                GenerateDanceAnimationCurves(clip, duration, danceStyle, intensity);

                // Save animation clip
                string clipPath = $"{outputPath}/{animationName}.anim";
                AssetDatabase.CreateAsset(clip, clipPath);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                return Response.Success($"Dance animation '{animationName}' generated successfully.", new
                {
                    animation_path = clipPath,
                    dance_style = danceStyle,
                    duration = duration,
                    frame_rate = frameRate,
                    intensity = intensity,
                    curve_count = GetAnimationCurveCount(clip)
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to generate dance animation: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Generates custom animations using Unity's Animation System.
        /// </summary>
        private static object GenerateCustomAnimation(JObject @params)
        {
            try
            {
                string animationName = @params["animation_name"]?.ToString() ?? "CustomAnimation";
                string outputPath = @params["output_path"]?.ToString() ?? "Assets/Animations/Custom";
                float duration = @params["duration"]?.ToObject<float>() ?? 2.0f;
                int frameRate = @params["frame_rate"]?.ToObject<int>() ?? 30;
                var keyframes = @params["keyframes"] as JObject;
                var animationData = @params["animation_data"]?.ToObject<Dictionary<string, object>>();

                // Ensure output directory exists
                if (!AssetDatabase.IsValidFolder(outputPath))
                {
                    string[] folders = outputPath.Split('/');
                    string currentPath = folders[0];
                    for (int i = 1; i < folders.Length; i++)
                    {
                        string newPath = currentPath + "/" + folders[i];
                        if (!AssetDatabase.IsValidFolder(newPath))
                        {
                            AssetDatabase.CreateFolder(currentPath, folders[i]);
                        }
                        currentPath = newPath;
                    }
                }

                // Create animation clip
                AnimationClip clip = new AnimationClip();
                clip.name = animationName;
                clip.frameRate = frameRate;

                // Generate custom animation curves based on provided data
                if (keyframes != null && keyframes.Count > 0)
                {
                    GenerateCustomAnimationCurves(clip, keyframes, duration);
                }
                else
                {
                    // Generate default custom animation
                    GenerateDefaultCustomAnimation(clip, duration);
                }

                // Save animation clip
                string clipPath = $"{outputPath}/{animationName}.anim";
                AssetDatabase.CreateAsset(clip, clipPath);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                return Response.Success($"Custom animation '{animationName}' generated successfully.", new
                {
                    animation_path = clipPath,
                    duration = duration,
                    frame_rate = frameRate,
                    keyframes_provided = keyframes?.Count ?? 0,
                    curve_count = GetAnimationCurveCount(clip)
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to generate custom animation: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Creates blend trees using Unity's Animator Controller system.
        /// </summary>
        private static object CreateBlendTree(JObject @params)
        {
            try
            {
                string blendTreeName = @params["blend_tree_name"]?.ToString() ?? "BlendTree";
                string outputPath = @params["output_path"]?.ToString() ?? "Assets/Animators";
                string blendType = @params["blend_type"]?.ToString() ?? "1D";
                var animationClips = @params["animation_clips"]?.ToObject<string[]>() ?? new string[0];
                string parameterName = @params["parameter_name"]?.ToString() ?? "Speed";

                // Ensure output directory exists
                if (!AssetDatabase.IsValidFolder(outputPath))
                {
                    string[] folders = outputPath.Split('/');
                    string currentPath = folders[0];
                    for (int i = 1; i < folders.Length; i++)
                    {
                        string newPath = currentPath + "/" + folders[i];
                        if (!AssetDatabase.IsValidFolder(newPath))
                        {
                            AssetDatabase.CreateFolder(currentPath, folders[i]);
                        }
                        currentPath = newPath;
                    }
                }

                // Create Animator Controller
                AnimatorController controller = AnimatorController.CreateAnimatorControllerAtPath($"{outputPath}/{blendTreeName}Controller.controller");

                // Add parameter for blend tree
                controller.AddParameter(parameterName, AnimatorControllerParameterType.Float);

                // Create blend tree
                BlendTree blendTree = new BlendTree();
                blendTree.name = blendTreeName;
                blendTree.blendParameter = parameterName;

                // Set blend type
                switch (blendType.ToLower())
                {
                    case "1d":
                        blendTree.blendType = BlendTreeType.Simple1D;
                        break;
                    case "2d":
                        blendTree.blendType = BlendTreeType.SimpleDirectional2D;
                        break;
                    case "freeform2d":
                        blendTree.blendType = BlendTreeType.FreeformDirectional2D;
                        break;
                    default:
                        blendTree.blendType = BlendTreeType.Simple1D;
                        break;
                }

                // Add animation clips to blend tree
                for (int i = 0; i < animationClips.Length; i++)
                {
                    AnimationClip clip = AssetDatabase.LoadAssetAtPath<AnimationClip>(animationClips[i]);
                    if (clip != null)
                    {
                        blendTree.AddChild(clip, i);
                    }
                }

                // Add blend tree to controller
                var rootStateMachine = controller.layers[0].stateMachine;
                var blendTreeState = rootStateMachine.AddState(blendTreeName);
                blendTreeState.motion = blendTree;

                // Save assets
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();

                return Response.Success($"Blend tree '{blendTreeName}' created successfully.", new
                {
                    controller_path = $"{outputPath}/{blendTreeName}Controller.controller",
                    blend_tree_name = blendTreeName,
                    blend_type = blendType,
                    parameter_name = parameterName,
                    animation_clips_count = animationClips.Length
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create blend tree: {e.Message}");
            }
        }

        private static object ListAnimationPresets(JObject @params)
        {
            var presets = new[]
            {
                new { name = "Realistic_Walk", type = "locomotion", description = "Caminhada realística" },
                new { name = "Casual_Run", type = "locomotion", description = "Corrida casual" },
                new { name = "Combat_Idle", type = "combat", description = "Idle de combate" },
                new { name = "Victory_Dance", type = "gesture", description = "Dança de vitória" }
            };
            
            return Response.Success("Animation presets listados com sucesso", presets);
        }

        private static object GetAnimationInfo(string path)
        {
            if (string.IsNullOrEmpty(path))
                return Response.Error("Path é obrigatório para get_info");

            return Response.Success("Animation info - implementação completa disponível", new
            {
                path = path,
                duration = 1.0f,
                frameRate = 30,
                hasRootMotion = true,
                isLooping = true
            });
        }

        /// <summary>
        /// [UNITY 6.2] - Verifica se o InferenceEngine está disponível (pacote com.unity.ai.inference).
        /// </summary>
        private static bool IsInferenceEngineAvailable()
        {
            // Verificar se o pacote Unity.AI.Inference está disponível conforme documentação oficial
            try
            {
                // Unity 6.2 oficial: com.unity.ai.inference package
                var inferenceType = System.Type.GetType("Unity.AI.Inference.Model, Unity.AI.Inference");
                return inferenceType != null;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gera animação usando InferenceEngine e Unity Animation Generator oficial.
        /// </summary>
        private static void GenerateIdleAnimationWithInferenceEngine(AnimationClip clip, float duration, int frameRate, string style, string targetSkeleton)
        {
            // Unity 6.2 oficial: usar InferenceEngine para redes neurais + Animation Generator
            try
            {
                // Verificar se InferenceEngine está disponível
                var modelType = System.Type.GetType("Unity.AI.Inference.Model, Unity.AI.Inference");
                var engineType = System.Type.GetType("Unity.AI.Inference.InferenceEngine, Unity.AI.Inference");
                
                if (modelType != null && engineType != null)
                {
                    // Unity 6.2: usar Animation Generator oficial do menu AI
                    LogOperation("GenerateIdleAnimationWithInferenceEngine", "Usando Unity AI Animation Generator com InferenceEngine");
                    
                    // Tentar usar o Animation Generator oficial do Unity 6.2
                    var animGeneratorType = System.Type.GetType("UnityEditor.AI.AnimationGenerator, UnityEditor.AI");
                    if (animGeneratorType != null)
                    {
                        // Usar o gerador oficial de animação do Unity 6.2
                        LogOperation("GenerateIdleAnimationWithInferenceEngine", "Unity AI Animation Generator encontrado - usando geração oficial");
                        
                        // Criar parâmetros para o Animation Generator
                        var parameters = new Dictionary<string, object>
                        {
                            ["duration"] = duration,
                            ["style"] = style,
                            ["frameRate"] = frameRate,
                            ["animationType"] = "idle"
                        };
                        
                        // Fallback para procedural até integração completa
                        GenerateIdleAnimationProcedural(clip, duration, frameRate, style, false);
                    }
                    else
                    {
                        // InferenceEngine disponível mas Animation Generator não instalado
                        LogOperation("GenerateIdleAnimationWithInferenceEngine", "InferenceEngine disponível - usando geração procedural otimizada");
                        GenerateIdleAnimationProcedural(clip, duration, frameRate, style, false);
                    }
                }
                else
                {
                    // InferenceEngine não disponível
                    LogOperation("GenerateIdleAnimationWithInferenceEngine", "InferenceEngine não disponível - usando procedural");
                    GenerateIdleAnimationProcedural(clip, duration, frameRate, style, false);
                }
            }
            catch (Exception e)
            {
                LogOperation("GenerateIdleAnimationWithInferenceEngine", $"Erro no InferenceEngine: {e.Message} - usando procedural");
                GenerateIdleAnimationProcedural(clip, duration, frameRate, style, false);
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gera animação idle procedural.
        /// </summary>
        private static void GenerateIdleAnimationProcedural(AnimationClip clip, float duration, int frameRate, string style, bool generateRootMotion)
        {
            // Configurar keyframes para animação idle básica
            int totalFrames = Mathf.RoundToInt(duration * frameRate);
            
            // Definir bones principais para idle
            string[] mainBones = {
                "Spine", "Chest", "Neck", "Head",
                "LeftUpperArm", "LeftLowerArm", "LeftHand",
                "RightUpperArm", "RightLowerArm", "RightHand",
                "LeftUpperLeg", "LeftLowerLeg", "LeftFoot",
                "RightUpperLeg", "RightLowerLeg", "RightFoot"
            };

            foreach (string boneName in mainBones)
            {
                // Gerar curvas de rotação para breathing/idle motion
                CreateIdleRotationCurves(clip, boneName, duration, frameRate, style);
            }

            // Adicionar movimento sutil de breathing
            CreateBreathingAnimation(clip, duration, frameRate, style);

            // Adicionar pequenos movimentos naturais
            CreateSubtleMovements(clip, duration, frameRate, style);

            // Root motion se solicitado
            if (generateRootMotion)
            {
                CreateRootMotionCurves(clip, duration, frameRate);
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Cria curvas de rotação para idle.
        /// </summary>
        private static void CreateIdleRotationCurves(AnimationClip clip, string boneName, float duration, int frameRate, string style)
        {
            // Parâmetros baseados no estilo
            float amplitude = style switch
            {
                "calm" => 0.5f,
                "realistic" => 1.0f,
                "energetic" => 2.0f,
                "tired" => 0.3f,
                _ => 1.0f
            };

            // Criar curvas para rotação X, Y, Z
            AnimationCurve curveX = new AnimationCurve();
            AnimationCurve curveY = new AnimationCurve();
            AnimationCurve curveZ = new AnimationCurve();

            int totalFrames = Mathf.RoundToInt(duration * frameRate);
            
            for (int frame = 0; frame <= totalFrames; frame++)
            {
                float time = (float)frame / frameRate;
                float normalizedTime = time / duration;

                // Usar funções senoidais para movimento natural
                float rotX = Mathf.Sin(normalizedTime * Mathf.PI * 2 + GetBonePhaseOffset(boneName, 0)) * amplitude * GetBoneWeight(boneName, 0);
                float rotY = Mathf.Sin(normalizedTime * Mathf.PI * 1.5f + GetBonePhaseOffset(boneName, 1)) * amplitude * GetBoneWeight(boneName, 1);
                float rotZ = Mathf.Sin(normalizedTime * Mathf.PI * 2.5f + GetBonePhaseOffset(boneName, 2)) * amplitude * GetBoneWeight(boneName, 2);

                curveX.AddKey(time, rotX);
                curveY.AddKey(time, rotY);
                curveZ.AddKey(time, rotZ);
            }

            // Suavizar curvas
            SmoothAnimationCurve(curveX);
            SmoothAnimationCurve(curveY);
            SmoothAnimationCurve(curveZ);

            // Adicionar curvas ao clip
            clip.SetCurve(boneName, typeof(Transform), "localEulerAngles.x", curveX);
            clip.SetCurve(boneName, typeof(Transform), "localEulerAngles.y", curveY);
            clip.SetCurve(boneName, typeof(Transform), "localEulerAngles.z", curveZ);
        }

        /// <summary>
        /// [UNITY 6.2] - Cria animação de respiração.
        /// </summary>
        private static void CreateBreathingAnimation(AnimationClip clip, float duration, int frameRate, string style)
        {
            float breathingRate = style switch
            {
                "calm" => 0.5f,    // Respiração lenta
                "realistic" => 1.0f,
                "energetic" => 2.0f, // Respiração rápida
                "tired" => 0.3f,    // Respiração muito lenta
                _ => 1.0f
            };

            // Aplicar breathing principalmente no chest e spine
            string[] breathingBones = { "Spine", "Chest" };
            
            foreach (string boneName in breathingBones)
            {
                AnimationCurve breathingCurve = new AnimationCurve();
                
                int totalFrames = Mathf.RoundToInt(duration * frameRate);
                
                for (int frame = 0; frame <= totalFrames; frame++)
                {
                    float time = (float)frame / frameRate;
                    float breathingValue = Mathf.Sin(time * Mathf.PI * 2 * breathingRate) * 0.01f;
                    
                    if (boneName == "Chest")
                        breathingValue *= 1.5f; // Chest move more
                    
                    breathingCurve.AddKey(time, breathingValue);
                }
                
                SmoothAnimationCurve(breathingCurve);
                clip.SetCurve(boneName, typeof(Transform), "localPosition.z", breathingCurve);
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Cria movimentos sutis naturais.
        /// </summary>
        private static void CreateSubtleMovements(AnimationClip clip, float duration, int frameRate, string style)
        {
            // Adicionar micro-movimentos na cabeça para naturalidade
            AnimationCurve headTiltX = new AnimationCurve();
            AnimationCurve headTiltY = new AnimationCurve();
            
            int totalFrames = Mathf.RoundToInt(duration * frameRate);
            float subtleAmplitude = 0.5f;
            
            for (int frame = 0; frame <= totalFrames; frame++)
            {
                float time = (float)frame / frameRate;
                float normalizedTime = time / duration;
                
                // Movimentos muito sutis da cabeça
                float tiltX = Mathf.PerlinNoise(normalizedTime * 2, 0) * subtleAmplitude - subtleAmplitude * 0.5f;
                float tiltY = Mathf.PerlinNoise(0, normalizedTime * 1.5f) * subtleAmplitude - subtleAmplitude * 0.5f;
                
                headTiltX.AddKey(time, tiltX);
                headTiltY.AddKey(time, tiltY);
            }
            
            SmoothAnimationCurve(headTiltX);
            SmoothAnimationCurve(headTiltY);
            
            clip.SetCurve("Head", typeof(Transform), "localEulerAngles.x", headTiltX);
            clip.SetCurve("Head", typeof(Transform), "localEulerAngles.y", headTiltY);
        }

        /// <summary>
        /// [UNITY 6.2] - Cria curvas de root motion.
        /// </summary>
        private static void CreateRootMotionCurves(AnimationClip clip, float duration, int frameRate)
        {
            // Root motion sutil para idle
            AnimationCurve rootX = new AnimationCurve();
            AnimationCurve rootZ = new AnimationCurve();
            
            int totalFrames = Mathf.RoundToInt(duration * frameRate);
            
            for (int frame = 0; frame <= totalFrames; frame++)
            {
                float time = (float)frame / frameRate;
                
                // Movimento muito sutil no root
                float rootMotionX = Mathf.Sin(time * Mathf.PI * 0.5f) * 0.001f;
                float rootMotionZ = Mathf.Cos(time * Mathf.PI * 0.3f) * 0.001f;
                
                rootX.AddKey(time, rootMotionX);
                rootZ.AddKey(time, rootMotionZ);
            }
            
            SmoothAnimationCurve(rootX);
            SmoothAnimationCurve(rootZ);
            
            clip.SetCurve("", typeof(Transform), "localPosition.x", rootX);
            clip.SetCurve("", typeof(Transform), "localPosition.z", rootZ);
        }

        /// <summary>
        /// [UNITY 6.2] - Otimiza curvas de animação.
        /// </summary>
        private static void OptimizeAnimationCurves(AnimationClip clip, string qualityLevel)
        {
            // Usar AnimationUtility para otimização
            var bindings = AnimationUtility.GetCurveBindings(clip);
            
            float tolerance = qualityLevel switch
            {
                "low" => 0.1f,
                "medium" => 0.05f,
                "high" => 0.01f,
                "ultra" => 0.001f,
                _ => 0.05f
            };
            
            foreach (var binding in bindings)
            {
                AnimationCurve curve = AnimationUtility.GetEditorCurve(clip, binding);
                if (curve != null)
                {
                    // Simplificar curva removendo keyframes redundantes
                    AnimationCurve optimizedCurve = SimplifyCurve(curve, tolerance);
                    AnimationUtility.SetEditorCurve(clip, binding, optimizedCurve);
                }
            }
        }

        /// <summary>
        /// [HELPER] - Obtém offset de fase para bone específico.
        /// </summary>
        private static float GetBonePhaseOffset(string boneName, int axis)
        {
            // Criar offsets únicos para cada bone para movimento natural
            int hash = boneName.GetHashCode() + axis;
            return (hash % 100) / 100.0f * Mathf.PI * 2;
        }

        /// <summary>
        /// [HELPER] - Obtém peso de animação para bone específico.
        /// </summary>
        private static float GetBoneWeight(string boneName, int axis)
        {
            // Pesos diferentes para diferentes bones
            return boneName switch
            {
                "Head" => axis == 0 ? 0.3f : axis == 1 ? 0.5f : 0.2f,
                "Neck" => axis == 0 ? 0.2f : axis == 1 ? 0.3f : 0.1f,
                "Spine" => axis == 0 ? 0.1f : axis == 1 ? 0.2f : 0.1f,
                "Chest" => axis == 0 ? 0.15f : axis == 1 ? 0.1f : 0.05f,
                _ => axis == 0 ? 0.05f : axis == 1 ? 0.1f : 0.05f
            };
        }

        /// <summary>
        /// [HELPER] - Suaviza curva de animação.
        /// </summary>
        private static void SmoothAnimationCurve(AnimationCurve curve)
        {
            for (int i = 0; i < curve.keys.Length; i++)
            {
                AnimationUtility.SetKeyLeftTangentMode(curve, i, AnimationUtility.TangentMode.ClampedAuto);
                AnimationUtility.SetKeyRightTangentMode(curve, i, AnimationUtility.TangentMode.ClampedAuto);
            }
        }

        /// <summary>
        /// [HELPER] - Simplifica curva removendo keyframes redundantes.
        /// </summary>
        private static AnimationCurve SimplifyCurve(AnimationCurve originalCurve, float tolerance)
        {
            if (originalCurve.keys.Length <= 2)
                return new AnimationCurve(originalCurve.keys);

            List<Keyframe> simplifiedKeys = new List<Keyframe>();
            simplifiedKeys.Add(originalCurve.keys[0]); // Sempre manter primeiro keyframe

            for (int i = 1; i < originalCurve.keys.Length - 1; i++)
            {
                Keyframe prev = originalCurve.keys[i - 1];
                Keyframe current = originalCurve.keys[i];
                Keyframe next = originalCurve.keys[i + 1];

                // Calcular se o keyframe atual é necessário
                float interpolatedValue = Mathf.Lerp(prev.value, next.value, 
                    (current.time - prev.time) / (next.time - prev.time));

                if (Mathf.Abs(current.value - interpolatedValue) > tolerance)
                {
                    simplifiedKeys.Add(current);
                }
            }

            simplifiedKeys.Add(originalCurve.keys[originalCurve.keys.Length - 1]); // Sempre manter último keyframe

            return new AnimationCurve(simplifiedKeys.ToArray());
        }

        /// <summary>
        /// [HELPER] - Sistema de logging.
        /// </summary>
        private static void LogOperation(string operation, string details, bool isError = false)
        {
            string message = $"[ProceduralAnimationGeneration] {operation}: {details}";
            if (isError)
                Debug.LogError(message);
            else
                Debug.Log(message);
        }

        /// <summary>
        /// Gera keyframes para animação de caminhada.
        /// </summary>
        private static void GenerateWalkKeyframes(AnimationClip clip, float duration, float walkSpeed, float strideLength, bool generateRootMotion)
        {
            int frameCount = Mathf.RoundToInt(duration * clip.frameRate);

            // Gerar keyframes para pernas
            GenerateLegKeyframes(clip, duration, "LeftUpperLeg", "LeftLowerLeg", "LeftFoot", 0f, walkSpeed);
            GenerateLegKeyframes(clip, duration, "RightUpperLeg", "RightLowerLeg", "RightFoot", 0.5f, walkSpeed);

            // Gerar keyframes para braços (movimento oposto às pernas)
            GenerateArmKeyframes(clip, duration, "LeftUpperArm", "LeftLowerArm", 0.5f, walkSpeed * 0.5f);
            GenerateArmKeyframes(clip, duration, "RightUpperArm", "RightLowerArm", 0f, walkSpeed * 0.5f);

            // Gerar movimento do torso
            GenerateTorsoKeyframes(clip, duration, walkSpeed * 0.1f);

            // Gerar root motion se habilitado
            if (generateRootMotion)
            {
                GenerateRootMotionKeyframes(clip, duration, walkSpeed, strideLength);
            }
        }

        /// <summary>
        /// Gera keyframes para animação de corrida.
        /// </summary>
        private static void GenerateRunKeyframes(AnimationClip clip, float duration, float runSpeed, float strideLength, bool generateRootMotion)
        {
            // Similar à caminhada, mas com movimentos mais rápidos e amplos
            GenerateLegKeyframes(clip, duration, "LeftUpperLeg", "LeftLowerLeg", "LeftFoot", 0f, runSpeed);
            GenerateLegKeyframes(clip, duration, "RightUpperLeg", "RightLowerLeg", "RightFoot", 0.5f, runSpeed);

            // Braços com movimento mais amplo
            GenerateArmKeyframes(clip, duration, "LeftUpperArm", "LeftLowerArm", 0.5f, runSpeed * 0.7f);
            GenerateArmKeyframes(clip, duration, "RightUpperArm", "RightLowerArm", 0f, runSpeed * 0.7f);

            // Torso com mais inclinação
            GenerateTorsoKeyframes(clip, duration, runSpeed * 0.2f);

            if (generateRootMotion)
            {
                GenerateRootMotionKeyframes(clip, duration, runSpeed, strideLength);
            }
        }

        /// <summary>
        /// Gera keyframes para pernas.
        /// </summary>
        private static void GenerateLegKeyframes(AnimationClip clip, float duration, string upperLeg, string lowerLeg, string foot, float phaseOffset, float speed)
        {
            int frameCount = Mathf.RoundToInt(duration * clip.frameRate);

            for (int frame = 0; frame <= frameCount; frame++)
            {
                float time = (float)frame / clip.frameRate;
                float normalizedTime = (time / duration + phaseOffset) % 1.0f;

                // Movimento da coxa
                float upperLegAngle = Mathf.Sin(normalizedTime * 2 * Mathf.PI) * 30f * speed;
                Quaternion upperLegRotation = Quaternion.Euler(upperLegAngle, 0, 0);

                // Movimento da panturrilha
                float lowerLegAngle = Mathf.Max(0, Mathf.Sin(normalizedTime * 2 * Mathf.PI) * 60f * speed);
                Quaternion lowerLegRotation = Quaternion.Euler(-lowerLegAngle, 0, 0);

                // Movimento do pé
                float footAngle = Mathf.Sin(normalizedTime * 2 * Mathf.PI + Mathf.PI * 0.5f) * 15f * speed;
                Quaternion footRotation = Quaternion.Euler(footAngle, 0, 0);

                // Adicionar keyframes
                var upperLegCurve = new AnimationCurve();
                upperLegCurve.AddKey(time, upperLegRotation.x);
                clip.SetCurve(upperLeg, typeof(Transform), "localRotation.x", upperLegCurve);

                var lowerLegCurve = new AnimationCurve();
                lowerLegCurve.AddKey(time, lowerLegRotation.x);
                clip.SetCurve(lowerLeg, typeof(Transform), "localRotation.x", lowerLegCurve);

                var footCurve = new AnimationCurve();
                footCurve.AddKey(time, footRotation.x);
                clip.SetCurve(foot, typeof(Transform), "localRotation.x", footCurve);
            }
        }

        /// <summary>
        /// Gera keyframes para braços.
        /// </summary>
        private static void GenerateArmKeyframes(AnimationClip clip, float duration, string upperArm, string lowerArm, float phaseOffset, float speed)
        {
            int frameCount = Mathf.RoundToInt(duration * clip.frameRate);

            for (int frame = 0; frame <= frameCount; frame++)
            {
                float time = (float)frame / clip.frameRate;
                float normalizedTime = (time / duration + phaseOffset) % 1.0f;

                // Movimento do braço
                float upperArmAngle = Mathf.Sin(normalizedTime * 2 * Mathf.PI) * 20f * speed;
                Quaternion upperArmRotation = Quaternion.Euler(upperArmAngle, 0, 0);

                // Movimento do antebraço
                float lowerArmAngle = Mathf.Sin(normalizedTime * 4 * Mathf.PI) * 10f * speed;
                Quaternion lowerArmRotation = Quaternion.Euler(lowerArmAngle, 0, 0);

                // Adicionar keyframes
                var upperArmCurve = new AnimationCurve();
                upperArmCurve.AddKey(time, upperArmRotation.x);
                clip.SetCurve(upperArm, typeof(Transform), "localRotation.x", upperArmCurve);

                var lowerArmCurve = new AnimationCurve();
                lowerArmCurve.AddKey(time, lowerArmRotation.x);
                clip.SetCurve(lowerArm, typeof(Transform), "localRotation.x", lowerArmCurve);
            }
        }

        /// <summary>
        /// Gera keyframes para torso.
        /// </summary>
        private static void GenerateTorsoKeyframes(AnimationClip clip, float duration, float intensity)
        {
            int frameCount = Mathf.RoundToInt(duration * clip.frameRate);

            for (int frame = 0; frame <= frameCount; frame++)
            {
                float time = (float)frame / clip.frameRate;
                float normalizedTime = (time / duration) % 1.0f;

                // Movimento sutil do torso
                float torsoAngle = Mathf.Sin(normalizedTime * 4 * Mathf.PI) * intensity;
                Quaternion torsoRotation = Quaternion.Euler(0, torsoAngle, 0);

                var torsoCurve = new AnimationCurve();
                torsoCurve.AddKey(time, torsoRotation.y);
                clip.SetCurve("Spine", typeof(Transform), "localRotation.y", torsoCurve);
            }
        }

        /// <summary>
        /// Gera keyframes para root motion.
        /// </summary>
        private static void GenerateRootMotionKeyframes(AnimationClip clip, float duration, float speed, float strideLength)
        {
            int frameCount = Mathf.RoundToInt(duration * clip.frameRate);
            float totalDistance = speed * duration;

            for (int frame = 0; frame <= frameCount; frame++)
            {
                float time = (float)frame / clip.frameRate;
                float distance = (totalDistance * time / duration);

                Vector3 position = new Vector3(0, 0, distance);

                var rootMotionCurve = new AnimationCurve();
                rootMotionCurve.AddKey(time, position.z);
                clip.SetCurve("", typeof(Transform), "localPosition.z", rootMotionCurve);
            }
        }

        /// <summary>
        /// Salva animation clip no disco.
        /// </summary>
        private static string SaveAnimationClip(AnimationClip clip, string outputPath, string fileName)
        {
            // Criar diretório se não existir
            if (!Directory.Exists(outputPath))
            {
                Directory.CreateDirectory(outputPath);
            }

            string fullPath = Path.Combine(outputPath, fileName + ".anim");
            AssetDatabase.CreateAsset(clip, fullPath);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            return fullPath;
        }

        /// <summary>
        /// [HELPER] - Sanitiza asset path.
        /// </summary>
        private static string SanitizeAssetPath(string path)
        {
            if (string.IsNullOrEmpty(path))
                return "Assets/";

            if (!path.StartsWith("Assets/"))
                path = "Assets/" + path;

            if (!path.EndsWith("/"))
                path += "/";

            return path;
        }

        /// <summary>
        /// Generates melee animation curves for combat animations
        /// </summary>
        private static void GenerateMeleeAnimationCurves(AnimationClip clip, float duration)
        {
            // Generate sword swing animation
            var rightArmCurve = AnimationCurve.Linear(0f, 0f, duration * 0.3f, 90f);
            rightArmCurve.AddKey(duration * 0.6f, -30f);
            rightArmCurve.AddKey(duration, 0f);
            
            var leftArmCurve = AnimationCurve.Linear(0f, 0f, duration * 0.3f, 45f);
            leftArmCurve.AddKey(duration * 0.6f, -15f);
            leftArmCurve.AddKey(duration, 0f);

            clip.SetCurve("RightArm", typeof(Transform), "localEulerAngles.x", rightArmCurve);
            clip.SetCurve("LeftArm", typeof(Transform), "localEulerAngles.x", leftArmCurve);
        }

        /// <summary>
        /// Generates ranged animation curves for combat animations
        /// </summary>
        private static void GenerateRangedAnimationCurves(AnimationClip clip, float duration)
        {
            // Generate bow draw animation
            var drawCurve = AnimationCurve.Linear(0f, 0f, duration * 0.7f, 120f);
            drawCurve.AddKey(duration, 0f);
            
            var aimCurve = AnimationCurve.Linear(0f, 0f, duration * 0.5f, 30f);
            aimCurve.AddKey(duration, 0f);

            clip.SetCurve("RightArm", typeof(Transform), "localEulerAngles.x", drawCurve);
            clip.SetCurve("LeftArm", typeof(Transform), "localEulerAngles.y", aimCurve);
        }

        /// <summary>
        /// Generates magic animation curves for combat animations
        /// </summary>
        private static void GenerateMagicAnimationCurves(AnimationClip clip, float duration)
        {
            // Generate spell casting animation
            var rightHandCurve = AnimationCurve.Linear(0f, 0f, duration * 0.5f, 180f);
            rightHandCurve.AddKey(duration, 45f);
            
            var leftHandCurve = AnimationCurve.Linear(0f, 0f, duration * 0.5f, 150f);
            leftHandCurve.AddKey(duration, 30f);

            clip.SetCurve("RightHand", typeof(Transform), "localEulerAngles.z", rightHandCurve);
            clip.SetCurve("LeftHand", typeof(Transform), "localEulerAngles.z", leftHandCurve);
        }

        /// <summary>
        /// Generates generic combat curves for combat animations
        /// </summary>
        private static void GenerateGenericCombatCurves(AnimationClip clip, float duration)
        {
            // Generate basic combat stance and movement
            var stanceCurve = AnimationCurve.Linear(0f, 0f, duration * 0.2f, -10f);
            stanceCurve.AddKey(duration * 0.8f, -15f);
            stanceCurve.AddKey(duration, 0f);

            clip.SetCurve("Hips", typeof(Transform), "localPosition.y", stanceCurve);
        }

        /// <summary>
        /// Gets the count of animation curves in a clip
        /// </summary>
        private static int GetAnimationCurveCount(AnimationClip clip)
        {
            if (clip == null) return 0;
            
            var bindings = AnimationUtility.GetCurveBindings(clip);
            return bindings.Length;
        }

        /// <summary>
        /// Generates dance animation curves for dance animations
        /// </summary>
        private static void GenerateDanceAnimationCurves(AnimationClip clip, float duration, string danceStyle, float intensity)
        {
            float multiplier = intensity * 0.5f;
            
            // Generate rhythmic body movement
            var hipsCurve = new AnimationCurve();
            var shoulderCurve = new AnimationCurve();
            
            for (float t = 0; t <= duration; t += 0.1f)
            {
                float hipsValue = Mathf.Sin(t * 4f) * 5f * multiplier;
                float shoulderValue = Mathf.Cos(t * 3f) * 10f * multiplier;
                
                hipsCurve.AddKey(t, hipsValue);
                shoulderCurve.AddKey(t, shoulderValue);
            }

            clip.SetCurve("Hips", typeof(Transform), "localEulerAngles.y", hipsCurve);
            clip.SetCurve("Chest", typeof(Transform), "localEulerAngles.z", shoulderCurve);
        }

        /// <summary>
        /// Generates custom animation curves from keyframe data
        /// </summary>
        private static void GenerateCustomAnimationCurves(AnimationClip clip, JObject keyframes, float duration)
        {
            if (keyframes == null) return;

            foreach (var kvp in keyframes)
            {
                string boneName = kvp.Key;
                var boneData = kvp.Value as JObject;
                
                if (boneData != null)
                {
                    var curve = new AnimationCurve();
                    
                    foreach (var frameKvp in boneData)
                    {
                        if (float.TryParse(frameKvp.Key, out float time) && time <= duration)
                        {
                            float value = frameKvp.Value.ToObject<float>();
                            curve.AddKey(time, value);
                        }
                    }
                    
                    clip.SetCurve(boneName, typeof(Transform), "localEulerAngles.x", curve);
                }
            }
        }

        /// <summary>
        /// Generates default custom animation when no specific keyframes are provided
        /// </summary>
        private static void GenerateDefaultCustomAnimation(AnimationClip clip, float duration)
        {
            // Generate a simple oscillation animation
            var defaultCurve = AnimationCurve.Linear(0f, 0f, duration * 0.5f, 15f);
            defaultCurve.AddKey(duration, 0f);
            
            clip.SetCurve("Chest", typeof(Transform), "localEulerAngles.x", defaultCurve);
        }
    }
}