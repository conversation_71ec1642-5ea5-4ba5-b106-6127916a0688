using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEngine;
using UnityMcpBridge.Editor.Helpers;

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// [UNITY 6.2] - <PERSON>les executing Unity Editor menu items by path using real Unity APIs.
    /// Provides comprehensive menu item execution and discovery functionality.
    /// </summary>
    public static class ExecuteMenuItem
    {
        // Safety blacklist to prevent execution of potentially disruptive menu items
        private static readonly HashSet<string> _menuPathBlacklist = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "File/Quit",
            "File/Exit",
            "Edit/Preferences...",
            "Unity/Quit Unity",
            "Unity/Preferences...",
            "Window/General/Console/Clear on Play",
            "Assets/Reimport All"
        };

        // Common Unity menu items mapping for aliases
        private static readonly Dictionary<string, string> _menuAliases = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase)
        {
            // GameObject creation aliases
            { "create_cube", "GameObject/3D Object/Cube" },
            { "create_sphere", "GameObject/3D Object/Sphere" },
            { "create_capsule", "GameObject/3D Object/Capsule" },
            { "create_cylinder", "GameObject/3D Object/Cylinder" },
            { "create_plane", "GameObject/3D Object/Plane" },
            { "create_quad", "GameObject/3D Object/Quad" },
            { "create_empty", "GameObject/Create Empty" },
            { "create_camera", "GameObject/Camera" },
            { "create_light", "GameObject/Light/Directional Light" },

            // Asset creation aliases
            { "create_material", "Assets/Create/Material" },
            { "create_script", "Assets/Create/C# Script" },
            { "create_scene", "File/New Scene" },
            { "create_folder", "Assets/Create/Folder" },

            // Window aliases
            { "console", "Window/General/Console" },
            { "inspector", "Window/General/Inspector" },
            { "hierarchy", "Window/General/Hierarchy" },
            { "project", "Window/General/Project" },
            { "scene_view", "Window/General/Scene" },
            { "game_view", "Window/General/Game" },

            // Build aliases
            { "build_settings", "File/Build Settings..." },
            { "player_settings", "Edit/Project Settings..." },

            // Tools aliases
            { "package_manager", "Window/Package Manager" },
            { "lighting", "Window/Rendering/Lighting" },
            { "profiler", "Window/Analysis/Profiler" }
        };

        /// <summary>
        /// [UNITY 6.2] - Main handler for executing menu items and menu discovery operations.
        /// </summary>
        public static object HandleCommand(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower() ?? "execute";

            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            try
            {
                switch (action)
                {
                    case "execute":
                        return ExecuteItem(@params);
                    case "get_available_menus":
                        return GetAvailableMenus(@params);
                    case "get_aliases":
                        return GetMenuAliases();
                    case "validate_menu":
                        return ValidateMenuItem(@params);
                    case "get_menu_info":
                        return GetMenuItemInfo(@params);
                    default:
                        return Response.Error($"Unknown action: '{action}'. Valid actions are: execute, get_available_menus, get_aliases, validate_menu, get_menu_info");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[ExecuteMenuItem] Action '{action}' failed: {e}");
                return Response.Error($"Internal error processing action '{action}': {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Executes a specific menu item using Unity's EditorApplication.ExecuteMenuItem API.
        /// </summary>
        private static object ExecuteItem(JObject @params)
        {
            string menuPath = @params["menu_path"]?.ToString();
            string alias = @params["alias"]?.ToString();
            bool validateOnly = @params["validate_only"]?.ToObject<bool>() ?? false;
            bool ignoreBlacklist = @params["ignore_blacklist"]?.ToObject<bool>() ?? false;

            // Handle alias resolution
            if (!string.IsNullOrEmpty(alias))
            {
                if (_menuAliases.TryGetValue(alias, out string resolvedPath))
                {
                    menuPath = resolvedPath;
                }
                else
                {
                    return Response.Error($"Unknown menu alias: '{alias}'. Use 'get_aliases' action to see available aliases.");
                }
            }

            if (string.IsNullOrWhiteSpace(menuPath))
            {
                return Response.Error("Required parameter 'menu_path' or 'alias' is missing or empty.");
            }

            // Validate against blacklist (unless explicitly ignored)
            if (!ignoreBlacklist && _menuPathBlacklist.Contains(menuPath))
            {
                return Response.Error($"Execution of menu item '{menuPath}' is blocked for safety reasons. Use 'ignore_blacklist: true' to override.");
            }

            // Validation only mode
            if (validateOnly)
            {
                return ValidateMenuItemInternal(menuPath);
            }

            try
            {
                // Execute menu item using Unity 6.2 EditorApplication API
                bool executed = EditorApplication.ExecuteMenuItem(menuPath);

                if (executed)
                {
                    return Response.Success($"Successfully executed menu item: '{menuPath}'", new
                    {
                        menu_path = menuPath,
                        alias_used = alias,
                        executed = true,
                        execution_time = DateTime.UtcNow
                    });
                }
                else
                {
                    // Try to provide more specific error information
                    var validationResult = ValidateMenuItemInternal(menuPath);
                    // Check if validation returned an error by inspecting the result dynamically
                    var resultType = validationResult.GetType();
                    var successProp = resultType.GetProperty("success");
                    if (successProp != null && !(bool)successProp.GetValue(validationResult))
                    {
                        var errorProp = resultType.GetProperty("error");
                        string errorMsg = errorProp?.GetValue(validationResult)?.ToString() ?? "Unknown error";
                        return Response.Error($"Failed to execute menu item '{menuPath}': {errorMsg}");
                    }

                    return Response.Error($"Failed to execute menu item '{menuPath}'. The menu item may be disabled or require specific context.");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[ExecuteMenuItem] Exception executing '{menuPath}': {e}");
                return Response.Error($"Exception executing menu item '{menuPath}': {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gets available menu items using reflection and known Unity menu structure.
        /// </summary>
        private static object GetAvailableMenus(JObject @params)
        {
            try
            {
                string category = @params["category"]?.ToString()?.ToLower();
                bool includeAliases = @params["include_aliases"]?.ToObject<bool>() ?? true;

                var menuItems = new List<object>();

                // Add known Unity menu categories
                var knownMenus = GetKnownUnityMenus();

                if (!string.IsNullOrEmpty(category))
                {
                    knownMenus = knownMenus.Where(m => m.Item2.Equals(category, StringComparison.OrdinalIgnoreCase)).ToList();
                }

                foreach (var menu in knownMenus)
                {
                    menuItems.Add(new
                    {
                        path = menu.Item1,
                        category = menu.Item2,
                        description = menu.Item3,
                        is_safe = !_menuPathBlacklist.Contains(menu.Item1)
                    });
                }

                var result = new
                {
                    menu_items = menuItems,
                    total_count = menuItems.Count,
                    categories = knownMenus.Select(m => m.Item2).Distinct().ToArray(),
                    aliases_available = includeAliases ? _menuAliases.Keys.ToArray() : new string[0]
                };

                return Response.Success($"Retrieved {menuItems.Count} available menu items.", result);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get available menus: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gets all available menu aliases.
        /// </summary>
        private static object GetMenuAliases()
        {
            try
            {
                var aliases = _menuAliases.Select(kvp => new
                {
                    alias = kvp.Key,
                    menu_path = kvp.Value,
                    is_safe = !_menuPathBlacklist.Contains(kvp.Value)
                }).ToArray();

                return Response.Success($"Retrieved {aliases.Length} menu aliases.", new
                {
                    aliases = aliases,
                    total_count = aliases.Length
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get menu aliases: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Validates if a menu item exists and can be executed.
        /// </summary>
        private static object ValidateMenuItem(JObject @params)
        {
            string menuPath = @params["menu_path"]?.ToString();

            if (string.IsNullOrWhiteSpace(menuPath))
            {
                return Response.Error("Required parameter 'menu_path' is missing or empty.");
            }

            return ValidateMenuItemInternal(menuPath);
        }

        /// <summary>
        /// [UNITY 6.2] - Internal validation method for menu items.
        /// </summary>
        private static object ValidateMenuItemInternal(string menuPath)
        {
            try
            {
                // Check if menu path is in blacklist
                bool isBlacklisted = _menuPathBlacklist.Contains(menuPath);

                // Check if it's a known menu item
                var knownMenus = GetKnownUnityMenus();
                bool isKnown = knownMenus.Any(m => m.Item1.Equals(menuPath, StringComparison.OrdinalIgnoreCase));

                // Try to validate by attempting execution in validation mode
                // Note: Unity doesn't provide a direct way to check if a menu item exists without executing it
                bool exists = true; // Assume exists unless we can prove otherwise

                var result = new
                {
                    menu_path = menuPath,
                    exists = exists,
                    is_known = isKnown,
                    is_blacklisted = isBlacklisted,
                    is_safe = !isBlacklisted,
                    validation_time = DateTime.UtcNow
                };

                if (isBlacklisted)
                {
                    return Response.Error($"Menu item '{menuPath}' is blacklisted for safety reasons.", result);
                }

                return Response.Success($"Menu item '{menuPath}' validation completed.", result);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to validate menu item '{menuPath}': {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gets detailed information about a menu item.
        /// </summary>
        private static object GetMenuItemInfo(JObject @params)
        {
            string menuPath = @params["menu_path"]?.ToString();
            string alias = @params["alias"]?.ToString();

            if (!string.IsNullOrEmpty(alias))
            {
                if (_menuAliases.TryGetValue(alias, out string resolvedPath))
                {
                    menuPath = resolvedPath;
                }
                else
                {
                    return Response.Error($"Unknown menu alias: '{alias}'");
                }
            }

            if (string.IsNullOrWhiteSpace(menuPath))
            {
                return Response.Error("Required parameter 'menu_path' or 'alias' is missing or empty.");
            }

            try
            {
                var knownMenus = GetKnownUnityMenus();
                var menuInfo = knownMenus.FirstOrDefault(m => m.Item1.Equals(menuPath, StringComparison.OrdinalIgnoreCase));

                bool isKnown = !string.IsNullOrEmpty(menuInfo.Item1);
                var result = new
                {
                    menu_path = menuPath,
                    alias_used = alias,
                    category = isKnown ? menuInfo.Item2 : "Unknown",
                    description = isKnown ? menuInfo.Item3 : "No description available",
                    is_known = isKnown,
                    is_blacklisted = _menuPathBlacklist.Contains(menuPath),
                    is_safe = !_menuPathBlacklist.Contains(menuPath),
                    available_aliases = _menuAliases.Where(kvp => kvp.Value.Equals(menuPath, StringComparison.OrdinalIgnoreCase)).Select(kvp => kvp.Key).ToArray()
                };

                return Response.Success($"Menu item information retrieved for '{menuPath}'.", result);
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to get menu item info: {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Gets a comprehensive list of known Unity menu items.
        /// </summary>
        private static List<(string path, string category, string description)> GetKnownUnityMenus()
        {
            return new List<(string, string, string)>
            {
                // File Menu
                ("File/New Scene", "File", "Create a new scene"),
                ("File/Open Scene", "File", "Open an existing scene"),
                ("File/Save", "File", "Save the current scene"),
                ("File/Save As...", "File", "Save the current scene with a new name"),
                ("File/Save Project", "File", "Save the entire project"),
                ("File/Build Settings...", "File", "Open build settings window"),
                ("File/Build And Run", "File", "Build and run the project"),

                // Edit Menu
                ("Edit/Undo", "Edit", "Undo the last action"),
                ("Edit/Redo", "Edit", "Redo the last undone action"),
                ("Edit/Cut", "Edit", "Cut selected objects"),
                ("Edit/Copy", "Edit", "Copy selected objects"),
                ("Edit/Paste", "Edit", "Paste copied objects"),
                ("Edit/Duplicate", "Edit", "Duplicate selected objects"),
                ("Edit/Delete", "Edit", "Delete selected objects"),
                ("Edit/Select All", "Edit", "Select all objects"),
                ("Edit/Project Settings...", "Edit", "Open project settings"),
                ("Edit/Preferences...", "Edit", "Open Unity preferences"),

                // Assets Menu
                ("Assets/Create/Folder", "Assets", "Create a new folder"),
                ("Assets/Create/C# Script", "Assets", "Create a new C# script"),
                ("Assets/Create/Material", "Assets", "Create a new material"),
                ("Assets/Create/Shader/Unlit Shader", "Assets", "Create an unlit shader"),
                ("Assets/Create/Shader/Standard Surface Shader", "Assets", "Create a standard surface shader"),
                ("Assets/Create/Animation", "Assets", "Create an animation clip"),
                ("Assets/Create/Animator Controller", "Assets", "Create an animator controller"),
                ("Assets/Create/Audio Mixer", "Assets", "Create an audio mixer"),
                ("Assets/Create/Render Texture", "Assets", "Create a render texture"),
                ("Assets/Create/Custom Render Texture", "Assets", "Create a custom render texture"),
                ("Assets/Refresh", "Assets", "Refresh the asset database"),
                ("Assets/Reimport", "Assets", "Reimport selected assets"),
                ("Assets/Reimport All", "Assets", "Reimport all assets"),

                // GameObject Menu
                ("GameObject/Create Empty", "GameObject", "Create an empty GameObject"),
                ("GameObject/Create Empty Child", "GameObject", "Create an empty child GameObject"),
                ("GameObject/3D Object/Cube", "GameObject", "Create a cube primitive"),
                ("GameObject/3D Object/Sphere", "GameObject", "Create a sphere primitive"),
                ("GameObject/3D Object/Capsule", "GameObject", "Create a capsule primitive"),
                ("GameObject/3D Object/Cylinder", "GameObject", "Create a cylinder primitive"),
                ("GameObject/3D Object/Plane", "GameObject", "Create a plane primitive"),
                ("GameObject/3D Object/Quad", "GameObject", "Create a quad primitive"),
                ("GameObject/3D Object/Terrain", "GameObject", "Create a terrain"),
                ("GameObject/2D Object/Sprite", "GameObject", "Create a sprite"),
                ("GameObject/Light/Directional Light", "GameObject", "Create a directional light"),
                ("GameObject/Light/Point Light", "GameObject", "Create a point light"),
                ("GameObject/Light/Spot Light", "GameObject", "Create a spot light"),
                ("GameObject/Light/Area Light", "GameObject", "Create an area light"),
                ("GameObject/Audio/Audio Source", "GameObject", "Create an audio source"),
                ("GameObject/Audio/Audio Reverb Zone", "GameObject", "Create an audio reverb zone"),
                ("GameObject/Video/Video Player", "GameObject", "Create a video player"),
                ("GameObject/Camera", "GameObject", "Create a camera"),
                ("GameObject/UI/Canvas", "GameObject", "Create a UI canvas"),
                ("GameObject/UI/Button", "GameObject", "Create a UI button"),
                ("GameObject/UI/Text", "GameObject", "Create a UI text"),
                ("GameObject/UI/Image", "GameObject", "Create a UI image"),

                // Component Menu
                ("Component/Add...", "Component", "Add a component to selected GameObject"),
                ("Component/Remove Component", "Component", "Remove a component from selected GameObject"),

                // Window Menu
                ("Window/General/Console", "Window", "Open the console window"),
                ("Window/General/Hierarchy", "Window", "Open the hierarchy window"),
                ("Window/General/Project", "Window", "Open the project window"),
                ("Window/General/Inspector", "Window", "Open the inspector window"),
                ("Window/General/Scene", "Window", "Open the scene view"),
                ("Window/General/Game", "Window", "Open the game view"),
                ("Window/Animation/Animation", "Window", "Open the animation window"),
                ("Window/Animation/Animator", "Window", "Open the animator window"),
                ("Window/Audio/Audio Mixer", "Window", "Open the audio mixer window"),
                ("Window/Rendering/Lighting", "Window", "Open the lighting window"),
                ("Window/Rendering/Occlusion Culling", "Window", "Open the occlusion culling window"),
                ("Window/Analysis/Profiler", "Window", "Open the profiler window"),
                ("Window/Analysis/Memory Profiler", "Window", "Open the memory profiler"),
                ("Window/Analysis/Frame Debugger", "Window", "Open the frame debugger"),
                ("Window/Package Manager", "Window", "Open the package manager"),
                ("Window/TextMeshPro/Font Asset Creator", "Window", "Open TextMeshPro font asset creator"),

                // Help Menu
                ("Help/About Unity", "Help", "Show Unity version information"),
                ("Help/Unity Manual", "Help", "Open Unity manual"),
                ("Help/Scripting Reference", "Help", "Open scripting reference"),
                ("Help/Unity Connect", "Help", "Open Unity Connect"),
                ("Help/Check for Updates", "Help", "Check for Unity updates"),
                ("Help/Report a Bug", "Help", "Report a bug to Unity"),

                // Tools Menu (varies by packages installed)
                ("Tools/Package Manager", "Tools", "Open package manager"),
                ("Tools/Console", "Tools", "Open console")
            };
        }
    }
}
