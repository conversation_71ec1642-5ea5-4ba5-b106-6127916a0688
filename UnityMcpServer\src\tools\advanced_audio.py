from mcp.server.fastmcp import FastMC<PERSON>, Context
from typing import Dict, Any, List, Optional
from unity_connection import get_unity_connection

def register_advanced_audio_tools(mcp: FastMCP):
    """Register advanced audio system tools with the MCP server."""

    @mcp.tool()
    def convert_audio_format(
        ctx: Context,
        action: str,
        source_clip_path: Optional[str] = None,
        target_format: Optional[str] = None,
        output_path: Optional[str] = None,
        quality_settings: Optional[Dict[str, Any]] = None,
        cache_converted_audio: Optional[bool] = None,
        enable_compression: Optional[bool] = None,
        sample_rate: Optional[int] = None,
        channels: Optional[int] = None,
        bit_depth: Optional[int] = None
    ) -> Dict[str, Any]:
        """Conversão avançada de formatos de áudio usando Unity 6.2 AudioClip APIs.

        Funcionalidades:
        - convert_audio: Converter formato de áudio
        - batch_convert: Conversão em lote
        - optimize_audio: Otimizar áudio para performance
        - analyze_audio: <PERSON><PERSON><PERSON> propriedades do áudio
        - apply_effects: Aplicar efeitos de áudio

        Args:
            action: Ação a executar (convert_audio, batch_convert, optimize_audio, analyze_audio, apply_effects)
            source_clip_path: Caminho do clip de áudio fonte
            target_format: Formato alvo (wav, mp3, ogg, aiff)
            output_path: Caminho de saída
            quality_settings: Configurações de qualidade
            cache_converted_audio: Cache de áudio convertido
            enable_compression: Habilitar compressão
            sample_rate: Taxa de amostragem
            channels: Número de canais
            bit_depth: Profundidade de bits

        Returns:
            Dictionary com resultados da conversão
        """
        try:
            params = {
                "action": action,
                "source_clip_path": source_clip_path,
                "target_format": target_format,
                "output_path": output_path,
                "quality_settings": quality_settings,
                "cache_converted_audio": cache_converted_audio,
                "enable_compression": enable_compression,
                "sample_rate": sample_rate,
                "channels": channels,
                "bit_depth": bit_depth
            }
            params = {k: v for k, v in params.items() if v is not None}

            response = get_unity_connection().send_command("advanced_audio", params)

            if response.get("success"):
                return {
                    "success": True,
                    "message": response.get("message", "Audio conversion completed successfully."),
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False,
                    "message": response.get("error", "Failed to convert audio.")
                }

        except Exception as e:
            return {"success": False, "message": f"Python error in audio conversion: {str(e)}"}

    @mcp.tool()
    def setup_audio_dsp_time_sync(
        ctx: Context,
        target_gameobject: str,
        sync_mode: str = "DSPTime",
        buffer_size: int = 1024,
        sample_rate: int = 48000,
        enable_precise_timing: bool = True,
        latency_compensation: float = 0.0
    ) -> Dict[str, Any]:
        """Setup audio and video synchronization via DSP clock for Unity 6.2.

        Args:
            target_gameobject: GameObject with AudioSource to sync
            sync_mode: Synchronization mode ("DSPTime", "UnscaledTime", "FixedTime")
            buffer_size: DSP buffer size for latency control
            sample_rate: Audio sample rate for synchronization
            enable_precise_timing: Enable high-precision timing
            latency_compensation: Manual latency compensation in seconds

        Returns:
            Dictionary with DSP time sync setup results
        """
        try:
            params = {
                "action": "setup_dsp_time_sync",
                "target_gameobject": target_gameobject,
                "sync_mode": sync_mode,
                "buffer_size": buffer_size,
                "sample_rate": sample_rate,
                "enable_precise_timing": enable_precise_timing,
                "latency_compensation": latency_compensation
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("advanced_audio", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "DSP time sync setup successfully."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Unknown error.")
                }

        except Exception as e:
            return {"success": False, "message": f"Python error: {str(e)}"}

    @mcp.tool()
    def configure_audio_dsp_buffer_size(
        ctx: Context,
        buffer_size: int = 1024,
        num_buffers: int = 4,
        optimize_for: str = "balanced",
        enable_adaptive_buffering: bool = True,
        target_latency_ms: float = 20.0,
        performance_mode: str = "automatic"
    ) -> Dict[str, Any]:
        """Configure DSP buffer size optimization for latency vs performance balance.

        Args:
            buffer_size: DSP buffer size (64, 128, 256, 512, 1024, 2048)
            num_buffers: Number of audio buffers to use
            optimize_for: Optimization target ("latency", "performance", "balanced")
            enable_adaptive_buffering: Enable dynamic buffer size adjustment
            target_latency_ms: Target latency in milliseconds
            performance_mode: Performance mode ("automatic", "manual", "realtime")

        Returns:
            Dictionary with DSP buffer configuration results
        """
        try:
            params = {
                "action": "configure_dsp_buffer",
                "buffer_size": buffer_size,
                "num_buffers": num_buffers,
                "optimize_for": optimize_for,
                "enable_adaptive_buffering": enable_adaptive_buffering,
                "target_latency_ms": target_latency_ms,
                "performance_mode": performance_mode
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("advanced_audio", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "DSP buffer configured successfully."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Unknown error.")
                }

        except Exception as e:
            return {"success": False, "message": f"Python error: {str(e)}"}

    @mcp.tool()
    def setup_audio_virtualization_runtime(
        ctx: Context,
        max_virtual_voices: int = 512,
        max_real_voices: int = 32,
        virtualization_mode: str = "FadeOut",
        priority_threshold: float = 0.5,
        distance_factor: float = 1.0,
        enable_voice_stealing: bool = True,
        cpu_usage_limit: float = 0.8
    ) -> Dict[str, Any]:
        """Setup real-time voice virtualization system for performance optimization.

        Args:
            max_virtual_voices: Maximum number of virtual voices
            max_real_voices: Maximum number of real (audible) voices
            virtualization_mode: How to handle virtualization ("FadeOut", "Stop", "Pause")
            priority_threshold: Priority threshold for voice stealing (0-1)
            distance_factor: Distance factor for virtualization decisions
            enable_voice_stealing: Enable voice stealing for priority management
            cpu_usage_limit: CPU usage limit for audio processing (0-1)

        Returns:
            Dictionary with audio virtualization setup results
        """
        try:
            params = {
                "action": "setup_virtualization",
                "max_virtual_voices": max_virtual_voices,
                "max_real_voices": max_real_voices,
                "virtualization_mode": virtualization_mode,
                "priority_threshold": priority_threshold,
                "distance_factor": distance_factor,
                "enable_voice_stealing": enable_voice_stealing,
                "cpu_usage_limit": cpu_usage_limit
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("advanced_audio", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Audio virtualization setup successfully."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Unknown error.")
                }

        except Exception as e:
            return {"success": False, "message": f"Python error: {str(e)}"}

    @mcp.tool()
    def create_adaptive_audio_system(
        ctx: Context,
        system_name: str,
        event_triggers: List[Dict[str, Any]],
        audio_layers: List[Dict[str, Any]],
        transition_settings: Optional[Dict[str, Any]] = None,
        enable_dynamic_mixing: bool = True,
        response_curve: str = "smooth",
        update_frequency: float = 0.1
    ) -> Dict[str, Any]:
        """Create adaptive audio system that responds to game events dynamically.

        Args:
            system_name: Name for the adaptive audio system
            event_triggers: List of game events that trigger audio changes
            audio_layers: List of audio layers with their properties
            transition_settings: Settings for transitions between states
            enable_dynamic_mixing: Enable real-time audio mixing adjustments
            response_curve: Response curve type ("linear", "smooth", "exponential")
            update_frequency: How often to update the system (seconds)

        Returns:
            Dictionary with adaptive audio system creation results
        """
        try:
            params = {
                "action": "create_adaptive_system",
                "system_name": system_name,
                "event_triggers": event_triggers,
                "audio_layers": audio_layers,
                "transition_settings": transition_settings,
                "enable_dynamic_mixing": enable_dynamic_mixing,
                "response_curve": response_curve,
                "update_frequency": update_frequency
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("advanced_audio", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Adaptive audio system created successfully."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Unknown error.")
                }

        except Exception as e:
            return {"success": False, "message": f"Python error: {str(e)}"}

    @mcp.tool()
    def setup_audio_mixer_snapshot_blending(
        ctx: Context,
        mixer_name: str,
        snapshots: List[Dict[str, Any]],
        blend_mode: str = "weighted",
        transition_time: float = 1.0,
        enable_crossfade: bool = True,
        interpolation_curve: str = "smooth_step",
        priority_system: bool = True
    ) -> Dict[str, Any]:
        """Setup smooth transitions between audio mixer snapshots.

        Args:
            mixer_name: Target audio mixer name
            snapshots: List of snapshots with their blend weights and properties
            blend_mode: Blending mode ("weighted", "additive", "override")
            transition_time: Default transition time between snapshots
            enable_crossfade: Enable crossfading between snapshots
            interpolation_curve: Interpolation curve ("linear", "smooth_step", "ease_in_out")
            priority_system: Enable priority-based snapshot management

        Returns:
            Dictionary with mixer snapshot blending setup results
        """
        try:
            params = {
                "action": "setup_snapshot_blending",
                "mixer_name": mixer_name,
                "snapshots": snapshots,
                "blend_mode": blend_mode,
                "transition_time": transition_time,
                "enable_crossfade": enable_crossfade,
                "interpolation_curve": interpolation_curve,
                "priority_system": priority_system
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("advanced_audio", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Mixer snapshot blending setup successfully."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Unknown error.")
                }

        except Exception as e:
            return {"success": False, "message": f"Python error: {str(e)}"}

    @mcp.tool()
    def configure_realtime_audio_profiling(
        ctx: Context,
        enable_profiling: bool = True,
        profiling_mode: str = "detailed",
        sample_rate: float = 10.0,
        track_voice_count: bool = True,
        track_cpu_usage: bool = True,
        track_memory_usage: bool = True,
        track_dsp_load: bool = True,
        export_format: str = "json"
    ) -> Dict[str, Any]:
        """Configure detailed real-time audio performance profiling and analysis.

        Args:
            enable_profiling: Enable audio profiling system
            profiling_mode: Profiling detail level ("basic", "detailed", "comprehensive")
            sample_rate: Profiling sample rate (samples per second)
            track_voice_count: Track active voice count
            track_cpu_usage: Track audio CPU usage
            track_memory_usage: Track audio memory usage
            track_dsp_load: Track DSP processing load
            export_format: Data export format ("json", "csv", "binary")

        Returns:
            Dictionary with audio profiling configuration results
        """
        try:
            params = {
                "action": "configure_profiling",
                "enable_profiling": enable_profiling,
                "profiling_mode": profiling_mode,
                "sample_rate": sample_rate,
                "track_voice_count": track_voice_count,
                "track_cpu_usage": track_cpu_usage,
                "track_memory_usage": track_memory_usage,
                "track_dsp_load": track_dsp_load,
                "export_format": export_format
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("advanced_audio", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Audio profiling configured successfully."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Unknown error.")
                }

        except Exception as e:
            return {"success": False, "message": f"Python error: {str(e)}"}

    @mcp.tool()
    def setup_audio_format_conversion(
        ctx: Context,
        source_format: str,
        target_format: str,
        conversion_quality: str = "high",
        enable_runtime_conversion: bool = True,
        compression_settings: Optional[Dict[str, Any]] = None,
        streaming_support: bool = True,
        cache_converted_audio: bool = True,
        batch_conversion: bool = False
    ) -> Dict[str, Any]:
        """Setup runtime audio format conversion for compatibility across platforms.

        Args:
            source_format: Source audio format ("wav", "mp3", "ogg", "aiff", "flac")
            target_format: Target audio format for conversion
            conversion_quality: Conversion quality ("low", "medium", "high", "lossless")
            enable_runtime_conversion: Enable real-time format conversion
            compression_settings: Compression settings for target format
            streaming_support: Enable streaming during conversion
            cache_converted_audio: Cache converted audio for reuse
            batch_conversion: Enable batch conversion mode

        Returns:
            Dictionary with audio format conversion setup results
        """
        try:
            params = {
                "action": "setup_format_conversion",
                "source_format": source_format,
                "target_format": target_format,
                "conversion_quality": conversion_quality,
                "enable_runtime_conversion": enable_runtime_conversion,
                "compression_settings": compression_settings,
                "streaming_support": streaming_support,
                "cache_converted_audio": cache_converted_audio,
                "batch_conversion": batch_conversion
            }
            params = {k: v for k, v in params.items() if v is not None}
            
            response = get_unity_connection().send_command("advanced_audio", params)

            if response.get("success"):
                return {
                    "success": True, 
                    "message": response.get("message", "Audio format conversion setup successfully."), 
                    "data": response.get("data")
                }
            else:
                return {
                    "success": False, 
                    "message": response.get("error", "Unknown error.")
                }

        except Exception as e:
            return {"success": False, "message": f"Python error: {str(e)}"}