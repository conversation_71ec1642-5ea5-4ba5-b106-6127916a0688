using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEditor.Animations;
using UnityEngine;
using UnityEngine.Events;
using Newtonsoft.Json.Linq;
using UnityMcpBridge.Editor.Helpers;

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// Handles dynamic game systems operations including state machines, event systems,
    /// rules engines, skill trees, buff/debuff systems, AI behaviour trees, 
    /// game state managers, and objective trackers.
    /// 
    /// Supports Unity 6.2 features and APIs for procedural game system generation.
    /// </summary>
    public static class DynamicGameSystems
    {
        private static readonly List<string> ValidActions = new List<string>
        {
            "create_generative_state_machine",
            "attach_behaviour_script_to_state", 
            "setup_procedural_event_system",
            "generate_gameplay_rules_engine",
            "create_dynamic_skill_tree",
            "setup_buff_debuff_system",
            "generate_ai_behaviour_tree",
            "configure_game_state_manager",
            "create_objective_tracker_system"
        };

        public static object HandleCommand(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidActions);
                return Response.Error($"Unknown action: '{action}'. Valid actions are: {validActionsList}");
            }

            try
            {
                switch (action)
                {
                    case "create_generative_state_machine":
                        return HandleGenerativeStateMachine(@params);
                    case "attach_behaviour_script_to_state":
                        return HandleBehaviourScriptToState(@params);
                    case "setup_procedural_event_system":
                        return HandleProceduralEventSystem(@params);
                    case "generate_gameplay_rules_engine":
                        return HandleGameplayRulesEngine(@params);
                    case "create_dynamic_skill_tree":
                        return HandleDynamicSkillTree(@params);
                    case "setup_buff_debuff_system":
                        return HandleBuffDebuffSystem(@params);
                    case "generate_ai_behaviour_tree":
                        return HandleAIBehaviourTree(@params);
                    case "configure_game_state_manager":
                        return HandleGameStateManager(@params);
                    case "create_objective_tracker_system":
                        return HandleObjectiveTrackerSystem(@params);
                    default:
                        return Response.Error($"Unknown action: '{action}'");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[DynamicGameSystems] Action '{action}' failed: {e}");
                return Response.Error($"Internal error processing action '{action}': {e.Message}");
            }
        }

        #region Generative State Machine
        public static object HandleGenerativeStateMachine(JObject @params)
        {
            string subAction = @params["action"]?.ToString()?.ToLower();
            string controllerName = @params["controller_name"]?.ToString();

            if (string.IsNullOrEmpty(controllerName))
            {
                return Response.Error("Controller name is required.");
            }

            try
            {
                switch (subAction)
                {
                    case "create":
                        return CreateAnimatorController(@params, controllerName);
                    case "modify":
                        return ModifyAnimatorController(@params, controllerName);
                    case "delete":
                        return DeleteAnimatorController(@params, controllerName);
                    case "get_info":
                        return GetControllerInfo(@params, controllerName);
                    case "add_state":
                        return AddStateToController(@params, controllerName);
                    case "add_transition":
                        return AddTransitionToController(@params, controllerName);
                    default:
                        return Response.Error($"Unsupported sub-action for generative state machine: {subAction}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Generative state machine operation failed: {e.Message}");
            }
        }

        private static object CreateAnimatorController(JObject @params, string controllerName)
        {
            string savePath = @params["save_path"]?.ToString() ?? "Assets/Animations/Controllers";
            
            // Ensure directory exists
            string fullPath = Path.Combine(Application.dataPath, savePath.Replace("Assets/", ""));
            if (!Directory.Exists(fullPath))
            {
                Directory.CreateDirectory(fullPath);
            }

            // Create the Animator Controller
            string assetPath = Path.Combine(savePath, $"{controllerName}.controller");
            AnimatorController controller = AnimatorController.CreateAnimatorControllerAtPath(assetPath);

            if (controller == null)
            {
                return Response.Error($"Failed to create Animator Controller at path: {assetPath}");
            }

            // Add parameters if specified
            var parameters = @params["parameters"] as JArray;
            if (parameters != null)
            {
                foreach (var paramData in parameters)
                {
                    AddParameterToController(controller, paramData as JObject);
                }
            }

            // Get the base layer
            AnimatorControllerLayer baseLayer = controller.layers[0];
            string layerName = @params["layer_name"]?.ToString() ?? "Base Layer";
            baseLayer.name = layerName;

            // Add states if specified
            var states = @params["states"] as JArray;
            if (states != null)
            {
                foreach (var stateData in states)
                {
                    AddStateToLayer(controller, baseLayer, stateData as JObject);
                }
            }

            // Add transitions if specified
            var transitions = @params["transitions"] as JArray;
            if (transitions != null)
            {
                foreach (var transitionData in transitions)
                {
                    AddTransitionToLayer(controller, baseLayer, transitionData as JObject);
                }
            }

            // Set default state if specified
            string defaultState = @params["default_state"]?.ToString();
            if (!string.IsNullOrEmpty(defaultState))
            {
                var state = baseLayer.stateMachine.states.FirstOrDefault(s => s.state.name == defaultState);
                if (state.state != null)
                {
                    baseLayer.stateMachine.defaultState = state.state;
                }
            }

            // Apply additional properties
            var properties = @params["properties"] as JObject;
            if (properties != null)
            {
                ApplyControllerProperties(controller, properties);
            }

            // Save and refresh
            EditorUtility.SetDirty(controller);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();

            return Response.Success($"Animator Controller '{controllerName}' created successfully.", new
            {
                controllerName = controllerName,
                assetPath = assetPath,
                layerCount = controller.layers.Length,
                parameterCount = controller.parameters.Length,
                stateCount = baseLayer.stateMachine.states.Length
            });
        }

        private static void AddParameterToController(AnimatorController controller, JObject paramData)
        {
            if (paramData == null) return;

            string paramName = paramData["name"]?.ToString();
            string paramType = paramData["type"]?.ToString()?.ToLower();
            
            if (string.IsNullOrEmpty(paramName) || string.IsNullOrEmpty(paramType))
                return;

            AnimatorControllerParameterType type;
            switch (paramType)
            {
                case "float":
                    type = AnimatorControllerParameterType.Float;
                    break;
                case "int":
                    type = AnimatorControllerParameterType.Int;
                    break;
                case "bool":
                    type = AnimatorControllerParameterType.Bool;
                    break;
                case "trigger":
                    type = AnimatorControllerParameterType.Trigger;
                    break;
                default:
                    Debug.LogWarning($"Unknown parameter type: {paramType}");
                    return;
            }

            var parameter = new AnimatorControllerParameter
            {
                name = paramName,
                type = type
            };

            // Set default value
            var defaultValue = paramData["default_value"];
            if (defaultValue != null)
            {
                switch (type)
                {
                    case AnimatorControllerParameterType.Float:
                        parameter.defaultFloat = defaultValue.ToObject<float>();
                        break;
                    case AnimatorControllerParameterType.Int:
                        parameter.defaultInt = defaultValue.ToObject<int>();
                        break;
                    case AnimatorControllerParameterType.Bool:
                        parameter.defaultBool = defaultValue.ToObject<bool>();
                        break;
                }
            }

            controller.AddParameter(parameter);
        }

        private static void AddStateToLayer(AnimatorController controller, AnimatorControllerLayer layer, JObject stateData)
        {
            if (stateData == null) return;

            string stateName = stateData["name"]?.ToString();
            if (string.IsNullOrEmpty(stateName)) return;

            // Create the state
            AnimatorState state = layer.stateMachine.AddState(stateName);

            // Set motion if specified
            string motionPath = stateData["motion"]?.ToString();
            if (!string.IsNullOrEmpty(motionPath))
            {
                Motion motion = AssetDatabase.LoadAssetAtPath<Motion>(motionPath);
                if (motion != null)
                {
                    state.motion = motion;
                }
            }

            // Set speed
            float speed = stateData["speed"]?.ToObject<float>() ?? 1f;
            state.speed = speed;

            // Set other properties
            if (stateData["loop"]?.ToObject<bool>() == true)
            {
                state.cycleOffset = stateData["cycle_offset"]?.ToObject<float>() ?? 0f;
            }
        }

        private static void AddTransitionToLayer(AnimatorController controller, AnimatorControllerLayer layer, JObject transitionData)
        {
            if (transitionData == null) return;

            string fromStateName = transitionData["from_state"]?.ToString();
            string toStateName = transitionData["to_state"]?.ToString();

            if (string.IsNullOrEmpty(fromStateName) || string.IsNullOrEmpty(toStateName))
                return;

            // Find states
            AnimatorState fromState = null;
            AnimatorState toState = null;

            foreach (var stateInfo in layer.stateMachine.states)
            {
                if (stateInfo.state.name == fromStateName)
                    fromState = stateInfo.state;
                if (stateInfo.state.name == toStateName)
                    toState = stateInfo.state;
            }

            if (fromState == null || toState == null)
            {
                Debug.LogWarning($"Could not find states for transition: {fromStateName} -> {toStateName}");
                return;
            }

            // Create transition
            AnimatorStateTransition transition = fromState.AddTransition(toState);

            // Set transition properties
            transition.duration = transitionData["duration"]?.ToObject<float>() ?? 0.25f;
            transition.offset = transitionData["offset"]?.ToObject<float>() ?? 0f;
            transition.hasExitTime = transitionData["has_exit_time"]?.ToObject<bool>() ?? true;
            transition.exitTime = transitionData["exit_time"]?.ToObject<float>() ?? 0.75f;

            // Add conditions
            var conditions = transitionData["conditions"] as JArray;
            if (conditions != null)
            {
                foreach (var conditionData in conditions)
                {
                    AddConditionToTransition(transition, conditionData as JObject);
                }
            }
        }

        private static void AddConditionToTransition(AnimatorStateTransition transition, JObject conditionData)
        {
            if (conditionData == null) return;

            string parameter = conditionData["parameter"]?.ToString();
            string mode = conditionData["mode"]?.ToString()?.ToLower();
            
            if (string.IsNullOrEmpty(parameter) || string.IsNullOrEmpty(mode))
                return;

            AnimatorConditionMode conditionMode;
            switch (mode)
            {
                case "if":
                    conditionMode = AnimatorConditionMode.If;
                    break;
                case "ifnot":
                    conditionMode = AnimatorConditionMode.IfNot;
                    break;
                case "greater":
                    conditionMode = AnimatorConditionMode.Greater;
                    break;
                case "less":
                    conditionMode = AnimatorConditionMode.Less;
                    break;
                case "equals":
                    conditionMode = AnimatorConditionMode.Equals;
                    break;
                case "notequal":
                    conditionMode = AnimatorConditionMode.NotEqual;
                    break;
                default:
                    Debug.LogWarning($"Unknown condition mode: {mode}");
                    return;
            }

            float threshold = conditionData["threshold"]?.ToObject<float>() ?? 0f;
            transition.AddCondition(conditionMode, threshold, parameter);
        }

        private static void ApplyControllerProperties(AnimatorController controller, JObject properties)
        {
            if (properties == null) return;
            
            // Apply controller-level properties
            if (properties.ContainsKey("name"))
            {
                controller.name = properties["name"].ToString();
            }
        }

        private static void ModifyControllerParameter(AnimatorController controller, JObject paramData)
        {
            if (paramData == null) return;

            string paramName = paramData["name"]?.ToString();
            if (string.IsNullOrEmpty(paramName)) return;

            // Find existing parameter
            var existingParam = controller.parameters.FirstOrDefault(p => p.name == paramName);
            if (existingParam == null)
            {
                // Add new parameter if it doesn't exist
                string paramType = paramData["type"]?.ToString()?.ToLower() ?? "float";
                AnimatorControllerParameterType type = AnimatorControllerParameterType.Float;
                
                switch (paramType)
                {
                    case "int":
                    case "integer":
                        type = AnimatorControllerParameterType.Int;
                        break;
                    case "bool":
                    case "boolean":
                        type = AnimatorControllerParameterType.Bool;
                        break;
                    case "trigger":
                        type = AnimatorControllerParameterType.Trigger;
                        break;
                    default:
                        type = AnimatorControllerParameterType.Float;
                        break;
                }

                controller.AddParameter(paramName, type);
                existingParam = controller.parameters.FirstOrDefault(p => p.name == paramName);
            }

            // Set default value if provided
            if (paramData.ContainsKey("default_value"))
            {
                switch (existingParam.type)
                {
                    case AnimatorControllerParameterType.Float:
                        existingParam.defaultFloat = paramData["default_value"].ToObject<float>();
                        break;
                    case AnimatorControllerParameterType.Int:
                        existingParam.defaultInt = paramData["default_value"].ToObject<int>();
                        break;
                    case AnimatorControllerParameterType.Bool:
                        existingParam.defaultBool = paramData["default_value"].ToObject<bool>();
                        break;
                }
            }
        }

        private static void ModifyControllerLayer(AnimatorController controller, JObject layerData)
        {
            if (layerData == null) return;

            string layerName = layerData["name"]?.ToString();
            if (string.IsNullOrEmpty(layerName)) return;

            // Find existing layer or create new one
            AnimatorControllerLayer layer = null;
            for (int i = 0; i < controller.layers.Length; i++)
            {
                if (controller.layers[i].name == layerName)
                {
                    layer = controller.layers[i];
                    break;
                }
            }

            if (layer == null)
            {
                // Create new layer
                layer = new AnimatorControllerLayer
                {
                    name = layerName,
                    stateMachine = new AnimatorStateMachine()
                };
                controller.AddLayer(layer);
            }

            // Apply layer properties
            if (layerData.ContainsKey("weight"))
            {
                layer.defaultWeight = layerData["weight"].ToObject<float>();
            }

            if (layerData.ContainsKey("blending_mode"))
            {
                string blendingMode = layerData["blending_mode"].ToString().ToLower();
                switch (blendingMode)
                {
                    case "override":
                        layer.blendingMode = AnimatorLayerBlendingMode.Override;
                        break;
                    case "additive":
                        layer.blendingMode = AnimatorLayerBlendingMode.Additive;
                        break;
                }
            }

            if (layerData.ContainsKey("sync_layer_index"))
            {
                layer.syncedLayerIndex = layerData["sync_layer_index"].ToObject<int>();
            }

            if (layerData.ContainsKey("sync_layer_timing"))
            {
                layer.iKPass = layerData["sync_layer_timing"].ToObject<bool>();
            }
        }

        private static void ApplyStateProperties(AnimatorState state, JObject properties)
        {
            if (properties == null) return;

            if (properties.ContainsKey("speed"))
            {
                state.speed = properties["speed"].ToObject<float>();
            }

            if (properties.ContainsKey("speed_parameter"))
            {
                state.speedParameter = properties["speed_parameter"].ToString();
            }

            if (properties.ContainsKey("speed_parameter_active"))
            {
                state.speedParameterActive = properties["speed_parameter_active"].ToObject<bool>();
            }

            if (properties.ContainsKey("mirror"))
            {
                state.mirror = properties["mirror"].ToObject<bool>();
            }

            if (properties.ContainsKey("mirror_parameter"))
            {
                state.mirrorParameter = properties["mirror_parameter"].ToString();
            }

            if (properties.ContainsKey("mirror_parameter_active"))
            {
                state.mirrorParameterActive = properties["mirror_parameter_active"].ToObject<bool>();
            }

            if (properties.ContainsKey("cycle_offset"))
            {
                state.cycleOffset = properties["cycle_offset"].ToObject<float>();
            }

            if (properties.ContainsKey("cycle_offset_parameter"))
            {
                state.cycleOffsetParameter = properties["cycle_offset_parameter"].ToString();
            }

            if (properties.ContainsKey("cycle_offset_parameter_active"))
            {
                state.cycleOffsetParameterActive = properties["cycle_offset_parameter_active"].ToObject<bool>();
            }

            if (properties.ContainsKey("write_default_values"))
            {
                state.writeDefaultValues = properties["write_default_values"].ToObject<bool>();
            }

            if (properties.ContainsKey("tag"))
            {
                state.tag = properties["tag"].ToString();
            }
        }

        private static void ApplyTransitionProperties(AnimatorStateTransition transition, JObject properties)
        {
            if (properties == null) return;

            if (properties.ContainsKey("duration"))
            {
                transition.duration = properties["duration"].ToObject<float>();
            }

            if (properties.ContainsKey("offset"))
            {
                transition.offset = properties["offset"].ToObject<float>();
            }

            if (properties.ContainsKey("exit_time"))
            {
                transition.exitTime = properties["exit_time"].ToObject<float>();
            }

            if (properties.ContainsKey("has_exit_time"))
            {
                transition.hasExitTime = properties["has_exit_time"].ToObject<bool>();
            }

            if (properties.ContainsKey("has_fixed_duration"))
            {
                transition.hasFixedDuration = properties["has_fixed_duration"].ToObject<bool>();
            }

            if (properties.ContainsKey("interruption_source"))
            {
                string source = properties["interruption_source"].ToString().ToLower();
                switch (source)
                {
                    case "none":
                        transition.interruptionSource = TransitionInterruptionSource.None;
                        break;
                    case "source":
                        transition.interruptionSource = TransitionInterruptionSource.Source;
                        break;
                    case "destination":
                        transition.interruptionSource = TransitionInterruptionSource.Destination;
                        break;
                    case "sourceordestination":
                        transition.interruptionSource = TransitionInterruptionSource.SourceThenDestination;
                        break;
                    case "destinationthensource":
                        transition.interruptionSource = TransitionInterruptionSource.DestinationThenSource;
                        break;
                }
            }

            if (properties.ContainsKey("ordered_interruption"))
            {
                transition.orderedInterruption = properties["ordered_interruption"].ToObject<bool>();
            }

            if (properties.ContainsKey("can_transition_to_self"))
            {
                transition.canTransitionToSelf = properties["can_transition_to_self"].ToObject<bool>();
            }
        }

        private static object ModifyAnimatorController(JObject @params, string controllerName)
        {
            string savePath = @params["save_path"]?.ToString() ?? "Assets/Animations/Controllers";
            string assetPath = Path.Combine(savePath, $"{controllerName}.controller");
            
            AnimatorController controller = AssetDatabase.LoadAssetAtPath<AnimatorController>(assetPath);
            if (controller == null)
            {
                return Response.Error($"Animator Controller '{controllerName}' not found at path: {assetPath}");
            }

            try
            {
                // Apply modifications to controller properties
                var properties = @params["properties"] as JObject;
                if (properties != null)
                {
                    ApplyControllerProperties(controller, properties);
                }

                // Modify parameters if specified
                var parameters = @params["parameters"] as JArray;
                if (parameters != null)
                {
                    foreach (var paramData in parameters)
                    {
                        ModifyControllerParameter(controller, paramData as JObject);
                    }
                }

                // Modify layers if specified
                var layers = @params["layers"] as JArray;
                if (layers != null)
                {
                    foreach (var layerData in layers)
                    {
                        ModifyControllerLayer(controller, layerData as JObject);
                    }
                }

                EditorUtility.SetDirty(controller);
                AssetDatabase.SaveAssets();

                return Response.Success($"Animator Controller '{controllerName}' modified successfully.", new
                {
                    controllerName = controllerName,
                    assetPath = assetPath,
                    layerCount = controller.layers.Length,
                    parameterCount = controller.parameters.Length
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to modify Animator Controller '{controllerName}': {e.Message}");
            }
        }

        private static object AddStateToController(JObject @params, string controllerName)
        {
            string savePath = @params["save_path"]?.ToString() ?? "Assets/Animations/Controllers";
            string assetPath = Path.Combine(savePath, $"{controllerName}.controller");
            
            AnimatorController controller = AssetDatabase.LoadAssetAtPath<AnimatorController>(assetPath);
            if (controller == null)
            {
                return Response.Error($"Animator Controller '{controllerName}' not found at path: {assetPath}");
            }

            try
            {
                string stateName = @params["state_name"]?.ToString();
                if (string.IsNullOrEmpty(stateName))
                {
                    return Response.Error("State name is required.");
                }

                int layerIndex = @params["layer_index"]?.ToObject<int>() ?? 0;
                if (layerIndex >= controller.layers.Length)
                {
                    return Response.Error($"Layer index {layerIndex} is out of range. Controller has {controller.layers.Length} layers.");
                }

                AnimatorStateMachine stateMachine = controller.layers[layerIndex].stateMachine;
                
                // Check if state already exists
                if (stateMachine.states.Any(s => s.state.name == stateName))
                {
                    return Response.Error($"State '{stateName}' already exists in layer {layerIndex}.");
                }

                // Create new state
                AnimatorState newState = stateMachine.AddState(stateName);
                
                // Set position if provided - Unity handles state positions through the state machine
                var position = @params["position"] as JObject;
                if (position != null)
                {
                    float x = position["x"]?.ToObject<float>() ?? 0f;
                    float y = position["y"]?.ToObject<float>() ?? 0f;
                    // Find the state in the state machine and set its position
                    var stateArray = stateMachine.states;
                    for (int i = 0; i < stateArray.Length; i++)
                    {
                        if (stateArray[i].state == newState)
                        {
                            stateArray[i].position = new Vector3(x, y, 0);
                            break;
                        }
                    }
                }

                // Set motion if provided
                string motionPath = @params["motion_path"]?.ToString();
                if (!string.IsNullOrEmpty(motionPath))
                {
                    Motion motion = AssetDatabase.LoadAssetAtPath<Motion>(motionPath);
                    if (motion != null)
                    {
                        newState.motion = motion;
                    }
                }

                // Set state properties
                var properties = @params["properties"] as JObject;
                if (properties != null)
                {
                    ApplyStateProperties(newState, properties);
                }

                // Set as default state if specified
                bool isDefault = @params["is_default"]?.ToObject<bool>() ?? false;
                if (isDefault)
                {
                    stateMachine.defaultState = newState;
                }

                EditorUtility.SetDirty(controller);
                AssetDatabase.SaveAssets();

                return Response.Success($"State '{stateName}' added to controller '{controllerName}' successfully.", new
                {
                    stateName = stateName,
                    layerIndex = layerIndex,
                    stateHash = newState.nameHash,
                    isDefault = stateMachine.defaultState == newState
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to add state to controller '{controllerName}': {e.Message}");
            }
        }

        private static object AddTransitionToController(JObject @params, string controllerName)
        {
            string savePath = @params["save_path"]?.ToString() ?? "Assets/Animations/Controllers";
            string assetPath = Path.Combine(savePath, $"{controllerName}.controller");
            
            AnimatorController controller = AssetDatabase.LoadAssetAtPath<AnimatorController>(assetPath);
            if (controller == null)
            {
                return Response.Error($"Animator Controller '{controllerName}' not found at path: {assetPath}");
            }

            try
            {
                string fromStateName = @params["from_state"]?.ToString();
                string toStateName = @params["to_state"]?.ToString();
                int layerIndex = @params["layer_index"]?.ToObject<int>() ?? 0;

                if (string.IsNullOrEmpty(fromStateName) || string.IsNullOrEmpty(toStateName))
                {
                    return Response.Error("Both 'from_state' and 'to_state' are required.");
                }

                if (layerIndex >= controller.layers.Length)
                {
                    return Response.Error($"Layer index {layerIndex} is out of range. Controller has {controller.layers.Length} layers.");
                }

                AnimatorStateMachine stateMachine = controller.layers[layerIndex].stateMachine;
                
                // Find source and destination states
                AnimatorState fromState = null;
                AnimatorState toState = null;

                foreach (var stateInfo in stateMachine.states)
                {
                    if (stateInfo.state.name == fromStateName)
                        fromState = stateInfo.state;
                    if (stateInfo.state.name == toStateName)
                        toState = stateInfo.state;
                }

                if (fromState == null)
                {
                    return Response.Error($"Source state '{fromStateName}' not found in layer {layerIndex}.");
                }

                if (toState == null)
                {
                    return Response.Error($"Destination state '{toStateName}' not found in layer {layerIndex}.");
                }

                // Create transition
                AnimatorStateTransition transition = fromState.AddTransition(toState);
                
                // Set transition properties
                var properties = @params["properties"] as JObject;
                if (properties != null)
                {
                    ApplyTransitionProperties(transition, properties);
                }

                // Add conditions if specified
                var conditions = @params["conditions"] as JArray;
                if (conditions != null)
                {
                    foreach (var conditionData in conditions)
                    {
                        AddConditionToTransition(transition, conditionData as JObject);
                    }
                }

                EditorUtility.SetDirty(controller);
                AssetDatabase.SaveAssets();

                return Response.Success($"Transition from '{fromStateName}' to '{toStateName}' added successfully.", new
                {
                    fromState = fromStateName,
                    toState = toStateName,
                    layerIndex = layerIndex,
                    transitionDuration = transition.duration,
                    hasExitTime = transition.hasExitTime,
                    conditionCount = transition.conditions.Length
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to add transition to controller '{controllerName}': {e.Message}");
            }
        }

        private static object GetControllerInfo(JObject @params, string controllerName)
        {
            string savePath = @params["save_path"]?.ToString() ?? "Assets/Animations/Controllers";
            string assetPath = Path.Combine(savePath, $"{controllerName}.controller");
            
            AnimatorController controller = AssetDatabase.LoadAssetAtPath<AnimatorController>(assetPath);
            if (controller == null)
            {
                return Response.Error($"Animator Controller '{controllerName}' not found at path: {assetPath}");
            }

            var controllerInfo = new
            {
                name = controller.name,
                assetPath = assetPath,
                layerCount = controller.layers.Length,
                parameterCount = controller.parameters.Length,
                parameters = controller.parameters.Select(p => new
                {
                    name = p.name,
                    type = p.type.ToString(),
                    defaultValue = GetParameterDefaultValue(p)
                }).ToArray(),
                layers = controller.layers.Select(l => new
                {
                    name = l.name,
                    stateCount = l.stateMachine.states.Length,
                    states = l.stateMachine.states.Select(s => new
                    {
                        name = s.state.name,
                        motion = s.state.motion?.name,
                        speed = s.state.speed,
                        transitionCount = s.state.transitions.Length
                    }).ToArray()
                }).ToArray()
            };

            return Response.Success($"Controller info retrieved for '{controllerName}'.", controllerInfo);
        }

        private static object GetParameterDefaultValue(AnimatorControllerParameter parameter)
        {
            switch (parameter.type)
            {
                case AnimatorControllerParameterType.Float:
                    return parameter.defaultFloat;
                case AnimatorControllerParameterType.Int:
                    return parameter.defaultInt;
                case AnimatorControllerParameterType.Bool:
                    return parameter.defaultBool;
                case AnimatorControllerParameterType.Trigger:
                    return false;
                default:
                    return null;
            }
        }

        private static object DeleteAnimatorController(JObject @params, string controllerName)
        {
            string savePath = @params["save_path"]?.ToString() ?? "Assets/Animations/Controllers";
            string assetPath = Path.Combine(savePath, $"{controllerName}.controller");
            
            if (!AssetDatabase.LoadAssetAtPath<AnimatorController>(assetPath))
            {
                return Response.Error($"Animator Controller '{controllerName}' not found at path: {assetPath}");
            }

            AssetDatabase.DeleteAsset(assetPath);
            AssetDatabase.Refresh();

            return Response.Success($"Animator Controller '{controllerName}' deleted successfully.");
        }
        #endregion

        #region Behaviour Script to State
        public static object HandleBehaviourScriptToState(JObject @params)
        {
            string subAction = @params["action"]?.ToString()?.ToLower();
            string controllerPath = @params["controller_path"]?.ToString();
            string stateName = @params["state_name"]?.ToString();

            if (string.IsNullOrEmpty(controllerPath) || string.IsNullOrEmpty(stateName))
            {
                return Response.Error("Controller path and state name are required.");
            }

            try
            {
                switch (subAction)
                {
                    case "attach":
                        return AttachBehaviourToState(@params, controllerPath, stateName);
                    case "detach":
                        return DetachBehaviourFromState(@params, controllerPath, stateName);
                    case "modify":
                        return ModifyStateBehaviour(@params, controllerPath, stateName);
                    case "list":
                        return ListStateBehaviours(controllerPath, stateName);
                    default:
                        return Response.Error($"Unsupported sub-action for behaviour script: {subAction}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Behaviour script operation failed: {e.Message}");
            }
        }

        private static object AttachBehaviourToState(JObject @params, string controllerPath, string stateName)
        {
            AnimatorController controller = AssetDatabase.LoadAssetAtPath<AnimatorController>(controllerPath);
            if (controller == null)
            {
                return Response.Error($"Animator Controller not found at path: {controllerPath}");
            }

            int layerIndex = @params["layer_index"]?.ToObject<int>() ?? 0;
            if (layerIndex >= controller.layers.Length)
            {
                return Response.Error($"Layer index {layerIndex} is out of range. Controller has {controller.layers.Length} layers.");
            }

            AnimatorControllerLayer layer = controller.layers[layerIndex];
            AnimatorState state = layer.stateMachine.states.FirstOrDefault(s => s.state.name == stateName).state;
            
            if (state == null)
            {
                return Response.Error($"State '{stateName}' not found in layer {layerIndex}.");
            }

            string behaviourScriptName = @params["behaviour_script_name"]?.ToString();
            string scriptContent = @params["script_content"]?.ToString();

            if (string.IsNullOrEmpty(behaviourScriptName))
            {
                return Response.Error("Behaviour script name is required.");
            }

            // If script content is provided, create the script first
            if (!string.IsNullOrEmpty(scriptContent))
            {
                try
                {
                    CreateStateMachineBehaviourScript(behaviourScriptName, scriptContent);
                    // Wait a moment for compilation
                    AssetDatabase.Refresh();
                }
                catch (Exception e)
                {
                    return Response.Error($"Failed to create script: {e.Message}");
                }
            }

            // Find the StateMachineBehaviour type
            Type behaviourType = FindStateMachineBehaviourType(behaviourScriptName);
            if (behaviourType == null)
            {
                return Response.Error($"StateMachineBehaviour type '{behaviourScriptName}' not found. Make sure the script is compiled.");
            }

            // Check if behaviour already exists on state
            var existingBehaviours = state.behaviours;
            if (existingBehaviours.Any(b => b.GetType() == behaviourType))
            {
                return Response.Error($"Behaviour '{behaviourScriptName}' is already attached to state '{stateName}'.");
            }

            // Add the behaviour to the state
            StateMachineBehaviour behaviour = state.AddStateMachineBehaviour(behaviourType);
            
            // Apply properties if specified
            var properties = @params["script_properties"] as JObject;
            if (properties != null)
            {
                ApplyBehaviourProperties(behaviour, properties);
            }

            EditorUtility.SetDirty(controller);
            AssetDatabase.SaveAssets();

            return Response.Success($"StateMachineBehaviour '{behaviourScriptName}' attached to state '{stateName}'.", new
            {
                controllerPath = controllerPath,
                stateName = stateName,
                behaviourType = behaviourType.Name,
                layerIndex = layerIndex,
                behaviourCount = state.behaviours.Length
            });
        }

        private static void CreateStateMachineBehaviourScript(string scriptName, string scriptContent)
        {
            string scriptsPath = "Assets/Scripts/StateMachineBehaviours";
            string fullPath = Path.Combine(Application.dataPath, scriptsPath.Replace("Assets/", ""));
            
            if (!Directory.Exists(fullPath))
            {
                Directory.CreateDirectory(fullPath);
            }

            string filePath = Path.Combine(fullPath, $"{scriptName}.cs");
            File.WriteAllText(filePath, scriptContent);
            
            AssetDatabase.ImportAsset(Path.Combine(scriptsPath, $"{scriptName}.cs"));
        }

        private static Type FindStateMachineBehaviourType(string typeName)
        {
            return System.AppDomain.CurrentDomain.GetAssemblies()
                .SelectMany(assembly => assembly.GetTypes())
                .FirstOrDefault(type => type.Name == typeName && type.IsSubclassOf(typeof(StateMachineBehaviour)));
        }

        private static void ApplyBehaviourProperties(StateMachineBehaviour behaviour, JObject properties)
        {
            if (behaviour == null || properties == null) return;

            Type type = behaviour.GetType();
            var flags = System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance;

            foreach (var prop in properties.Properties())
            {
                try
                {
                    var fieldInfo = type.GetField(prop.Name, flags);
                    if (fieldInfo != null)
                    {
                        object value = ConvertJTokenToType(prop.Value, fieldInfo.FieldType);
                        if (value != null)
                        {
                            fieldInfo.SetValue(behaviour, value);
                        }
                    }
                    else
                    {
                        var propertyInfo = type.GetProperty(prop.Name, flags);
                        if (propertyInfo != null && propertyInfo.CanWrite)
                        {
                            object value = ConvertJTokenToType(prop.Value, propertyInfo.PropertyType);
                            if (value != null)
                            {
                                propertyInfo.SetValue(behaviour, value);
                            }
                        }
                    }
                }
                catch (Exception e)
                {
                    Debug.LogWarning($"Failed to set property '{prop.Name}' on behaviour: {e.Message}");
                }
            }
        }

        private static object ConvertJTokenToType(JToken token, Type targetType)
        {
            try
            {
                if (token == null || token.Type == JTokenType.Null)
                    return null;

                if (targetType == typeof(string))
                    return token.ToString();
                
                if (targetType == typeof(int))
                    return token.ToObject<int>();
                
                if (targetType == typeof(float))
                    return token.ToObject<float>();
                
                if (targetType == typeof(bool))
                    return token.ToObject<bool>();
                
                if (targetType == typeof(double))
                    return token.ToObject<double>();
                
                if (targetType == typeof(long))
                    return token.ToObject<long>();

                if (targetType.IsEnum)
                {
                    if (token.Type == JTokenType.String)
                        return Enum.Parse(targetType, token.ToString());
                    else
                        return Enum.ToObject(targetType, token.ToObject<int>());
                }

                // For Unity types
                if (targetType == typeof(Vector2))
                {
                    var obj = token.ToObject<JObject>();
                    return new Vector2(
                        obj["x"]?.ToObject<float>() ?? 0f,
                        obj["y"]?.ToObject<float>() ?? 0f
                    );
                }

                if (targetType == typeof(Vector3))
                {
                    var obj = token.ToObject<JObject>();
                    return new Vector3(
                        obj["x"]?.ToObject<float>() ?? 0f,
                        obj["y"]?.ToObject<float>() ?? 0f,
                        obj["z"]?.ToObject<float>() ?? 0f
                    );
                }

                if (targetType == typeof(Color))
                {
                    var obj = token.ToObject<JObject>();
                    return new Color(
                        obj["r"]?.ToObject<float>() ?? 0f,
                        obj["g"]?.ToObject<float>() ?? 0f,
                        obj["b"]?.ToObject<float>() ?? 0f,
                        obj["a"]?.ToObject<float>() ?? 1f
                    );
                }

                // Try generic conversion
                return token.ToObject(targetType);
            }
            catch (Exception e)
            {
                Debug.LogWarning($"Failed to convert token to type {targetType.Name}: {e.Message}");
                return null;
            }
        }

        private static object DetachBehaviourFromState(JObject @params, string controllerPath, string stateName)
        {
            AnimatorController controller = AssetDatabase.LoadAssetAtPath<AnimatorController>(controllerPath);
            if (controller == null)
            {
                return Response.Error($"Animator Controller not found at path: {controllerPath}");
            }

            int layerIndex = @params["layer_index"]?.ToObject<int>() ?? 0;
            if (layerIndex >= controller.layers.Length)
            {
                return Response.Error($"Layer index {layerIndex} is out of range.");
            }

            AnimatorControllerLayer layer = controller.layers[layerIndex];
            AnimatorState state = layer.stateMachine.states.FirstOrDefault(s => s.state.name == stateName).state;
            
            if (state == null)
            {
                return Response.Error($"State '{stateName}' not found in layer {layerIndex}.");
            }

            string behaviourScriptName = @params["behaviour_script_name"]?.ToString();
            if (string.IsNullOrEmpty(behaviourScriptName))
            {
                return Response.Error("Behaviour script name is required.");
            }

            // Find the behaviour type
            Type behaviourType = FindStateMachineBehaviourType(behaviourScriptName);
            if (behaviourType == null)
            {
                return Response.Error($"StateMachineBehaviour type '{behaviourScriptName}' not found.");
            }

            // Find and remove the behaviour
            var behaviours = state.behaviours.ToList();
            var behaviourToRemove = behaviours.FirstOrDefault(b => b.GetType() == behaviourType);
            
            if (behaviourToRemove == null)
            {
                return Response.Error($"Behaviour '{behaviourScriptName}' is not attached to state '{stateName}'.");
            }

            // Remove the behaviour using Unity's API
            // Unity 6.2 doesn't have RemoveStateMachineBehaviour, use reflection or recreate behaviours list
            var behavioursList = state.behaviours.ToList();
            behavioursList.Remove(behaviourToRemove);
            
            // Clear all behaviours and re-add the remaining ones
            while (state.behaviours.Length > 0)
            {
                UnityEngine.Object.DestroyImmediate(state.behaviours[0], true);
            }
            
            // Re-add remaining behaviours
            foreach (var remainingBehaviour in behavioursList)
            {
                state.AddStateMachineBehaviour(remainingBehaviour.GetType());
            }

            EditorUtility.SetDirty(controller);
            AssetDatabase.SaveAssets();

            return Response.Success($"StateMachineBehaviour '{behaviourScriptName}' detached from state '{stateName}'.", new
            {
                controllerPath = controllerPath,
                stateName = stateName,
                behaviourType = behaviourType.Name,
                layerIndex = layerIndex,
                remainingBehaviours = state.behaviours.Length
            });
        }

        private static object ModifyStateBehaviour(JObject @params, string controllerPath, string stateName)
        {
            AnimatorController controller = AssetDatabase.LoadAssetAtPath<AnimatorController>(controllerPath);
            if (controller == null)
            {
                return Response.Error($"Animator Controller not found at path: {controllerPath}");
            }

            int layerIndex = @params["layer_index"]?.ToObject<int>() ?? 0;
            if (layerIndex >= controller.layers.Length)
            {
                return Response.Error($"Layer index {layerIndex} is out of range.");
            }

            AnimatorControllerLayer layer = controller.layers[layerIndex];
            AnimatorState state = layer.stateMachine.states.FirstOrDefault(s => s.state.name == stateName).state;
            
            if (state == null)
            {
                return Response.Error($"State '{stateName}' not found in layer {layerIndex}.");
            }

            string behaviourScriptName = @params["behaviour_script_name"]?.ToString();
            if (string.IsNullOrEmpty(behaviourScriptName))
            {
                return Response.Error("Behaviour script name is required.");
            }

            // Find the behaviour type
            Type behaviourType = FindStateMachineBehaviourType(behaviourScriptName);
            if (behaviourType == null)
            {
                return Response.Error($"StateMachineBehaviour type '{behaviourScriptName}' not found.");
            }

            // Find the behaviour instance
            var behaviour = state.behaviours.FirstOrDefault(b => b.GetType() == behaviourType);
            if (behaviour == null)
            {
                return Response.Error($"Behaviour '{behaviourScriptName}' is not attached to state '{stateName}'.");
            }

            // Apply new properties
            var properties = @params["script_properties"] as JObject;
            if (properties != null)
            {
                ApplyBehaviourProperties(behaviour, properties);
            }

            EditorUtility.SetDirty(controller);
            AssetDatabase.SaveAssets();

            return Response.Success($"StateMachineBehaviour '{behaviourScriptName}' on state '{stateName}' modified successfully.", new
            {
                controllerPath = controllerPath,
                stateName = stateName,
                behaviourType = behaviourType.Name,
                layerIndex = layerIndex,
                propertiesApplied = properties?.Properties()?.Count() ?? 0
            });
        }

        private static object ListStateBehaviours(string controllerPath, string stateName)
        {
            AnimatorController controller = AssetDatabase.LoadAssetAtPath<AnimatorController>(controllerPath);
            if (controller == null)
            {
                return Response.Error($"Animator Controller not found at path: {controllerPath}");
            }

            var allBehaviours = new List<object>();

            for (int layerIndex = 0; layerIndex < controller.layers.Length; layerIndex++)
            {
                AnimatorControllerLayer layer = controller.layers[layerIndex];
                AnimatorState state = layer.stateMachine.states.FirstOrDefault(s => s.state.name == stateName).state;
                
                if (state != null)
                {
                    var behaviours = state.behaviours.Select(b => new
                    {
                        typeName = b.GetType().Name,
                        fullTypeName = b.GetType().FullName,
                        layerIndex = layerIndex,
                        layerName = layer.name,
                        properties = GetBehaviourProperties(b)
                    }).ToArray();

                    allBehaviours.AddRange(behaviours);
                }
            }

            return Response.Success($"Listed behaviours for state '{stateName}'.", new
            {
                stateName = stateName,
                controllerPath = controllerPath,
                totalBehaviours = allBehaviours.Count,
                behaviours = allBehaviours
            });
        }

        private static object GetBehaviourProperties(StateMachineBehaviour behaviour)
        {
            var properties = new Dictionary<string, object>();
            Type type = behaviour.GetType();
            var flags = System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance;

            // Get public fields
            var fields = type.GetFields(flags);
            foreach (var field in fields)
            {
                try
                {
                    var value = field.GetValue(behaviour);
                    properties[field.Name] = value?.ToString() ?? "null";
                }
                catch (Exception e)
                {
                    properties[field.Name] = $"Error: {e.Message}";
                }
            }

            // Get public properties
            var props = type.GetProperties(flags).Where(p => p.CanRead);
            foreach (var prop in props)
            {
                try
                {
                    var value = prop.GetValue(behaviour);
                    properties[prop.Name] = value?.ToString() ?? "null";
                }
                catch (Exception e)
                {
                    properties[prop.Name] = $"Error: {e.Message}";
                }
            }

            return properties;
        }
        #endregion

        #region Procedural Event System
        public static object HandleProceduralEventSystem(JObject @params)
        {
            string subAction = @params["action"]?.ToString()?.ToLower();
            string systemName = @params["system_name"]?.ToString();

            if (string.IsNullOrEmpty(systemName))
            {
                return Response.Error("System name is required.");
            }

            try
            {
                switch (subAction)
                {
                    case "create":
                        return CreateEventSystem(@params, systemName);
                    case "modify":
                        return ModifyEventSystem(@params, systemName);
                    case "delete":
                        return DeleteEventSystem(@params, systemName);
                    case "add_event":
                        return AddEventToSystem(@params, systemName);
                    case "add_listener":
                        return AddListenerToSystem(@params, systemName);
                    default:
                        return Response.Error($"Unsupported sub-action for procedural event system: {subAction}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Procedural event system operation failed: {e.Message}");
            }
        }

        private static object CreateEventSystem(JObject @params, string systemName)
        {
            string savePath = @params["save_path"]?.ToString() ?? "Assets/Scripts/EventSystems";
            bool globalEvents = @params["global_events"]?.ToObject<bool>() ?? true;
            bool persistentEvents = @params["persistent_events"]?.ToObject<bool>() ?? false;

            // Ensure directory exists
            string fullPath = Path.Combine(Application.dataPath, savePath.Replace("Assets/", ""));
            if (!Directory.Exists(fullPath))
            {
                Directory.CreateDirectory(fullPath);
            }

            // Generate event system script
            string eventSystemScript = GenerateEventSystemScript(systemName, globalEvents, persistentEvents);
            string eventSystemPath = Path.Combine(fullPath, $"{systemName}EventSystem.cs");
            File.WriteAllText(eventSystemPath, eventSystemScript);

            // Generate event data script
            string eventDataScript = GenerateEventDataScript(systemName);
            string eventDataPath = Path.Combine(fullPath, $"{systemName}EventData.cs");
            File.WriteAllText(eventDataPath, eventDataScript);

            // Process event definitions if provided
            var eventDefinitions = @params["event_definitions"] as JArray;
            if (eventDefinitions != null)
            {
                string eventsScript = GenerateEventDefinitionsScript(systemName, eventDefinitions);
                string eventsPath = Path.Combine(fullPath, $"{systemName}Events.cs");
                File.WriteAllText(eventsPath, eventsScript);
            }

            // Process listener components if provided
            var listenerComponents = @params["listener_components"] as JArray;
            if (listenerComponents != null)
            {
                foreach (var listenerData in listenerComponents)
                {
                    CreateEventListener(systemName, listenerData as JObject, fullPath);
                }
            }

            // Refresh asset database
            AssetDatabase.ImportAsset(Path.Combine(savePath, $"{systemName}EventSystem.cs"));
            AssetDatabase.ImportAsset(Path.Combine(savePath, $"{systemName}EventData.cs"));
            if (eventDefinitions != null)
            {
                AssetDatabase.ImportAsset(Path.Combine(savePath, $"{systemName}Events.cs"));
            }
            AssetDatabase.Refresh();

            return Response.Success($"Event System '{systemName}' created successfully.", new
            {
                systemName = systemName,
                savePath = savePath,
                globalEvents = globalEvents,
                persistentEvents = persistentEvents,
                eventCount = eventDefinitions?.Count ?? 0,
                listenerCount = listenerComponents?.Count ?? 0
            });
        }

        private static string GenerateEventSystemScript(string systemName, bool globalEvents, bool persistentEvents)
        {
            return $@"using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

namespace EventSystems
{{
    /// <summary>
    /// Event system for {systemName} using Unity 6.2 UnityEvent system.
    /// </summary>
    public class {systemName}EventSystem : MonoBehaviour
    {{
        private static {systemName}EventSystem _instance;
        public static {systemName}EventSystem Instance
        {{
            get
            {{
                if (_instance == null && {globalEvents.ToString().ToLower()})
                {{
                    _instance = UnityEngine.Object.FindFirstObjectByType<{systemName}EventSystem>();
                    if (_instance == null)
                    {{
                        GameObject go = new GameObject(""{systemName}EventSystem"");
                        _instance = go.AddComponent<{systemName}EventSystem>();
                        if ({persistentEvents.ToString().ToLower()})
                        {{
                            DontDestroyOnLoad(go);
                        }}
                    }}
                }}
                return _instance;
            }}
        }}

        private Dictionary<string, UnityEvent<{systemName}EventData>> events = new Dictionary<string, UnityEvent<{systemName}EventData>>();

        private void Awake()
        {{
            if ({globalEvents.ToString().ToLower()})
            {{
                if (_instance == null)
                {{
                    _instance = this;
                    if ({persistentEvents.ToString().ToLower()})
                    {{
                        DontDestroyOnLoad(gameObject);
                    }}
                }}
                else if (_instance != this)
                {{
                    Destroy(gameObject);
                }}
            }}
        }}

        public void RegisterEvent(string eventName)
        {{
            if (!events.ContainsKey(eventName))
            {{
                events[eventName] = new UnityEvent<{systemName}EventData>();
            }}
        }}

        public void TriggerEvent(string eventName, {systemName}EventData eventData)
        {{
            if (events.ContainsKey(eventName))
            {{
                events[eventName].Invoke(eventData);
            }}
            else
            {{
                Debug.LogWarning($""Event '{{eventName}}' not registered in {systemName}EventSystem"");
            }}
        }}

        public void AddListener(string eventName, UnityAction<{systemName}EventData> listener)
        {{
            RegisterEvent(eventName);
            events[eventName].AddListener(listener);
        }}

        public void RemoveListener(string eventName, UnityAction<{systemName}EventData> listener)
        {{
            if (events.ContainsKey(eventName))
            {{
                events[eventName].RemoveListener(listener);
            }}
        }}

        public void RemoveAllListeners(string eventName)
        {{
            if (events.ContainsKey(eventName))
            {{
                events[eventName].RemoveAllListeners();
            }}
        }}
    }}
}}";
        }

        private static string GenerateEventDataScript(string systemName)
        {
            return $@"using System;
using System.Collections.Generic;
using UnityEngine;

namespace EventSystems
{{
    /// <summary>
    /// Event data container for {systemName} events.
    /// </summary>
    [Serializable]
    public class {systemName}EventData
    {{
        public string eventType;
        public GameObject source;
        public float timestamp;
        public Dictionary<string, object> parameters = new Dictionary<string, object>();

        public {systemName}EventData(string eventType, GameObject source = null)
        {{
            this.eventType = eventType;
            this.source = source;
            this.timestamp = Time.time;
        }}

        public T GetParameter<T>(string key, T defaultValue = default(T))
        {{
            if (parameters.ContainsKey(key) && parameters[key] is T)
            {{
                return (T)parameters[key];
            }}
            return defaultValue;
        }}

        public void SetParameter<T>(string key, T value)
        {{
            parameters[key] = value;
        }}
    }}
}}";
        }

        private static string GenerateEventDefinitionsScript(string systemName, JArray eventDefinitions)
        {
            var eventConstants = new List<string>();
            var eventMethods = new List<string>();

            foreach (var eventDef in eventDefinitions)
            {
                var eventObj = eventDef as JObject;
                string eventName = eventObj["name"]?.ToString();
                string description = eventObj["description"]?.ToString() ?? "";
                var parameters = eventObj["parameters"] as JArray;

                if (!string.IsNullOrEmpty(eventName))
                {
                    string constantName = eventName.ToUpper().Replace(" ", "_");
                    eventConstants.Add($"        public const string {constantName} = \"{eventName}\";");

                    // Generate helper method
                    string methodName = $"Trigger{eventName.Replace(" ", "")}";
                    string paramList = "GameObject source = null";
                    string paramAssignments = "";

                    if (parameters != null)
                    {
                        foreach (var param in parameters)
                        {
                            var paramObj = param as JObject;
                            string paramName = paramObj["name"]?.ToString();
                            string paramType = paramObj["type"]?.ToString() ?? "object";
                            
                            if (!string.IsNullOrEmpty(paramName))
                            {
                                paramList += $", {paramType} {paramName} = default({paramType})";
                                paramAssignments += $"            eventData.SetParameter(\"{paramName}\", {paramName});\n";
                            }
                        }
                    }

                    eventMethods.Add($@"        /// <summary>
        /// {description}
        /// </summary>
        public static void {methodName}({paramList})
        {{
            var eventData = new {systemName}EventData({constantName}, source);
{paramAssignments}            {systemName}EventSystem.Instance?.TriggerEvent({constantName}, eventData);
        }}");
                }
            }

            return $@"using UnityEngine;

namespace EventSystems
{{
    /// <summary>
    /// Event definitions and helper methods for {systemName}.
    /// </summary>
    public static class {systemName}Events
    {{
        // Event type constants
{string.Join("\n", eventConstants)}

        // Helper methods
{string.Join("\n\n", eventMethods)}
    }}
}}";
        }

        private static void CreateEventListener(string systemName, JObject listenerData, string basePath)
        {
            string listenerName = listenerData["name"]?.ToString();
            if (string.IsNullOrEmpty(listenerName)) return;

            string listenerScript = $@"using UnityEngine;
using EventSystems;

/// <summary>
/// Event listener component for {systemName} events.
/// </summary>
public class {listenerName} : MonoBehaviour
{{
    [SerializeField] private string[] listenToEvents;

    private void Start()
    {{
        foreach (string eventName in listenToEvents)
        {{
            {systemName}EventSystem.Instance?.AddListener(eventName, OnEventReceived);
        }}
    }}

    private void OnDestroy()
    {{
        if ({systemName}EventSystem.Instance != null)
        {{
            foreach (string eventName in listenToEvents)
            {{
                {systemName}EventSystem.Instance.RemoveListener(eventName, OnEventReceived);
            }}
        }}
    }}

    private void OnEventReceived({systemName}EventData eventData)
    {{
        Debug.Log($""Received event: {{eventData.eventType}} from {{eventData.source?.name ?? ""Unknown""}}"");
        // Handle event logic here
    }}
}}";

            string listenerPath = Path.Combine(basePath, $"{listenerName}.cs");
            File.WriteAllText(listenerPath, listenerScript);
        }

        /// <summary>
        /// [UNITY 6.2] - Modifica sistema de eventos usando Unity Events e ScriptableObject APIs.
        /// </summary>
        private static object ModifyEventSystem(JObject @params, string systemName)
        {
            try
            {
                string systemPath = $"Assets/DynamicSystems/Events/{systemName}";
                var modifications = @params["modifications"]?.ToObject<Dictionary<string, object>>();

                if (modifications == null)
                {
                    return Response.Error("No modifications specified for event system");
                }

                // Verificar se o sistema existe
                if (!AssetDatabase.IsValidFolder(systemPath))
                {
                    return Response.Error($"Event system '{systemName}' does not exist");
                }

                var modificationResults = new List<string>();

                // Aplicar modificações usando Unity AssetDatabase APIs
                foreach (var modification in modifications)
                {
                    switch (modification.Key.ToLower())
                    {
                        case "rename":
                            string newName = modification.Value.ToString();
                            string newPath = $"Assets/DynamicSystems/Events/{newName}";
                            if (AssetDatabase.MoveAsset(systemPath, newPath) != "")
                            {
                                modificationResults.Add($"Renamed system to {newName}");
                                systemPath = newPath;
                            }
                            break;

                        case "add_component":
                            string componentName = modification.Value.ToString();
                            CreateEventSystemComponent(systemPath, componentName);
                            modificationResults.Add($"Added component: {componentName}");
                            break;

                        case "update_settings":
                            var settings = modification.Value as JObject;
                            UpdateEventSystemSettings(systemPath, settings);
                            modificationResults.Add("Updated system settings");
                            break;
                    }
                }

                AssetDatabase.Refresh();
                return Response.Success($"Event System '{systemName}' modified successfully.", new {
                    systemName = systemName,
                    modifications = modificationResults.ToArray(),
                    systemPath = systemPath
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to modify event system '{systemName}': {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Deleta sistema de eventos usando Unity AssetDatabase APIs.
        /// </summary>
        private static object DeleteEventSystem(JObject @params, string systemName)
        {
            try
            {
                string systemPath = $"Assets/DynamicSystems/Events/{systemName}";
                bool forceDelete = @params["force_delete"]?.ToObject<bool>() ?? false;
                bool createBackup = @params["create_backup"]?.ToObject<bool>() ?? true;

                // Verificar se o sistema existe
                if (!AssetDatabase.IsValidFolder(systemPath))
                {
                    return Response.Error($"Event system '{systemName}' does not exist");
                }

                // Criar backup se solicitado
                if (createBackup)
                {
                    string backupPath = $"Assets/DynamicSystems/Backups/{systemName}_{DateTime.Now:yyyyMMdd_HHmmss}";
                    if (!AssetDatabase.IsValidFolder("Assets/DynamicSystems/Backups"))
                    {
                        AssetDatabase.CreateFolder("Assets/DynamicSystems", "Backups");
                    }

                    if (AssetDatabase.CopyAsset(systemPath, backupPath))
                    {
                        Debug.Log($"[DynamicGameSystems] Backup created at: {backupPath}");
                    }
                }

                // Verificar dependências se não for força
                if (!forceDelete)
                {
                    var dependencies = GetEventSystemDependencies(systemPath);
                    if (dependencies.Count > 0)
                    {
                        return Response.Error($"Cannot delete system '{systemName}'. Found {dependencies.Count} dependencies. Use force_delete=true to override.");
                    }
                }

                // Deletar o sistema usando Unity AssetDatabase
                if (AssetDatabase.DeleteAsset(systemPath))
                {
                    AssetDatabase.Refresh();
                    return Response.Success($"Event System '{systemName}' deleted successfully.", new {
                        systemName = systemName,
                        backupCreated = createBackup,
                        forceDelete = forceDelete
                    });
                }
                else
                {
                    return Response.Error($"Failed to delete event system '{systemName}'");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to delete event system '{systemName}': {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Adiciona evento ao sistema usando Unity Events APIs.
        /// </summary>
        private static object AddEventToSystem(JObject @params, string systemName)
        {
            try
            {
                string eventName = @params["event_name"]?.ToString();
                string eventType = @params["event_type"]?.ToString() ?? "UnityEvent";
                var eventParameters = @params["parameters"]?.ToObject<Dictionary<string, object>>();

                if (string.IsNullOrEmpty(eventName))
                {
                    return Response.Error("Event name is required");
                }

                string systemPath = $"Assets/DynamicSystems/Events/{systemName}";

                // Verificar se o sistema existe
                if (!AssetDatabase.IsValidFolder(systemPath))
                {
                    return Response.Error($"Event system '{systemName}' does not exist");
                }

                // Criar script do evento usando Unity 6.2 APIs
                string eventScript = GenerateEventScript(eventName, eventType, eventParameters);
                string eventScriptPath = $"{systemPath}/{eventName}Event.cs";

                File.WriteAllText(eventScriptPath, eventScript);
                AssetDatabase.ImportAsset(eventScriptPath);

                // Criar ScriptableObject para configuração do evento
                string eventConfigScript = GenerateEventConfigScript(eventName, eventParameters);
                string eventConfigPath = $"{systemPath}/{eventName}Config.cs";

                File.WriteAllText(eventConfigPath, eventConfigScript);
                AssetDatabase.ImportAsset(eventConfigPath);

                AssetDatabase.Refresh();

                return Response.Success($"Event '{eventName}' added to system '{systemName}' successfully.", new {
                    eventName = eventName,
                    eventType = eventType,
                    systemName = systemName,
                    eventScriptPath = eventScriptPath,
                    eventConfigPath = eventConfigPath,
                    parameters = eventParameters
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to add event to system '{systemName}': {e.Message}");
            }
        }

        /// <summary>
        /// [UNITY 6.2] - Adiciona listener ao sistema usando Unity Events APIs.
        /// </summary>
        private static object AddListenerToSystem(JObject @params, string systemName)
        {
            try
            {
                string listenerName = @params["listener_name"]?.ToString();
                string targetEvent = @params["target_event"]?.ToString();
                string listenerType = @params["listener_type"]?.ToString() ?? "MonoBehaviour";
                var listenerMethods = @params["methods"]?.ToObject<string[]>() ?? new string[] { "OnEventTriggered" };

                if (string.IsNullOrEmpty(listenerName) || string.IsNullOrEmpty(targetEvent))
                {
                    return Response.Error("Listener name and target event are required");
                }

                string systemPath = $"Assets/DynamicSystems/Events/{systemName}";

                // Verificar se o sistema existe
                if (!AssetDatabase.IsValidFolder(systemPath))
                {
                    return Response.Error($"Event system '{systemName}' does not exist");
                }

                // Criar script do listener usando Unity 6.2 APIs
                string listenerScript = GenerateEventListenerScript(listenerName, targetEvent, listenerType, listenerMethods);
                string listenerScriptPath = $"{systemPath}/{listenerName}Listener.cs";

                File.WriteAllText(listenerScriptPath, listenerScript);
                AssetDatabase.ImportAsset(listenerScriptPath);

                // Criar prefab do listener se for MonoBehaviour
                if (listenerType == "MonoBehaviour")
                {
                    CreateListenerPrefab(systemPath, listenerName);
                }

                AssetDatabase.Refresh();

                return Response.Success($"Listener '{listenerName}' added to system '{systemName}' successfully.", new {
                    listenerName = listenerName,
                    targetEvent = targetEvent,
                    listenerType = listenerType,
                    systemName = systemName,
                    listenerScriptPath = listenerScriptPath,
                    methods = listenerMethods
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to add listener to system '{systemName}': {e.Message}");
            }
        }
        #endregion

        #region Gameplay Rules Engine
        public static object HandleGameplayRulesEngine(JObject @params)
        {
            string action = @params["action"]?.ToString() ?? "create";
            
            switch (action.ToLower())
            {
                case "create":
                    return CreateGameplayRulesEngine(@params);
                case "add_rule":
                    return AddGameplayRule(@params);
                case "remove_rule":
                    return RemoveGameplayRule(@params);
                case "modify_rule":
                    return ModifyGameplayRule(@params);
                case "get_rules":
                    return GetGameplayRules(@params);
                default:
                    return Response.Error($"Unknown action: {action}");
            }
        }

        private static object CreateGameplayRulesEngine(JObject @params)
        {
            string engineName = @params["engine_name"]?.ToString() ?? "GameplayRulesEngine";
            string enginePath = @params["engine_path"]?.ToString() ?? "Assets/Scripts/GameplayRules";
            bool isGlobal = @params["is_global"]?.ToObject<bool>() ?? true;
            bool isPersistent = @params["is_persistent"]?.ToObject<bool>() ?? true;

            try
            {
                // Create the directory if it doesn't exist
                string fullPath = Path.Combine(Application.dataPath, enginePath.Replace("Assets/", ""));
                if (!Directory.Exists(fullPath))
                {
                    Directory.CreateDirectory(fullPath);
                }

                // Generate the main Rules Engine script
                string mainEngineScript = GenerateRulesEngineScript(engineName, isGlobal, isPersistent);
                string mainFilePath = Path.Combine(fullPath, $"{engineName}.cs");
                File.WriteAllText(mainFilePath, mainEngineScript);

                // Generate Rule base class
                string ruleBaseScript = GenerateRuleBaseScript();
                string ruleBasePath = Path.Combine(fullPath, "GameplayRule.cs");
                File.WriteAllText(ruleBasePath, ruleBaseScript);

                // Generate Rule Condition system
                string conditionScript = GenerateRuleConditionScript();
                string conditionPath = Path.Combine(fullPath, "RuleCondition.cs");
                File.WriteAllText(conditionPath, conditionScript);

                // Generate Rule Action system
                string actionScript = GenerateRuleActionScript();
                string actionPath = Path.Combine(fullPath, "RuleAction.cs");
                File.WriteAllText(actionPath, actionScript);

                // Generate Rule Data container
                string dataScript = GenerateRuleDataScript();
                string dataPath = Path.Combine(fullPath, "RuleData.cs");
                File.WriteAllText(dataPath, dataScript);

                // Create a sample rule as ScriptableObject
                string sampleRuleScript = GenerateSampleRuleScript();
                string samplePath = Path.Combine(fullPath, "SampleGameplayRule.cs");
                File.WriteAllText(samplePath, sampleRuleScript);

                // Import all assets
                AssetDatabase.ImportAsset(Path.Combine(enginePath, $"{engineName}.cs"));
                AssetDatabase.ImportAsset(Path.Combine(enginePath, "GameplayRule.cs"));
                AssetDatabase.ImportAsset(Path.Combine(enginePath, "RuleCondition.cs"));
                AssetDatabase.ImportAsset(Path.Combine(enginePath, "RuleAction.cs"));
                AssetDatabase.ImportAsset(Path.Combine(enginePath, "RuleData.cs"));
                AssetDatabase.ImportAsset(Path.Combine(enginePath, "SampleGameplayRule.cs"));

                AssetDatabase.Refresh();

                return Response.Success($"Gameplay Rules Engine '{engineName}' created successfully.", new
                {
                    engineName = engineName,
                    enginePath = enginePath,
                    isGlobal = isGlobal,
                    isPersistent = isPersistent,
                    scriptsCreated = new[] {
                        $"{engineName}.cs",
                        "GameplayRule.cs",
                        "RuleCondition.cs",
                        "RuleAction.cs",
                        "RuleData.cs",
                        "SampleGameplayRule.cs"
                    }
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create Gameplay Rules Engine: {e.Message}");
            }
        }

        private static string GenerateRulesEngineScript(string engineName, bool isGlobal, bool isPersistent)
        {
            return $@"using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Events;

namespace GameplayRules
{{
    public class {engineName} : MonoBehaviour
    {{
        [Header(""Engine Configuration"")]
        public bool enableDebugLogs = true;
        public bool autoExecuteRules = true;
        public float ruleCheckInterval = 0.1f;

        [Header(""Rule Management"")]
        [SerializeField] private List<GameplayRule> activeRules = new List<GameplayRule>();
        [SerializeField] private List<GameplayRule> disabledRules = new List<GameplayRule>();

        [Header(""Events"")]
        public UnityEvent<GameplayRule> OnRuleExecuted;
        public UnityEvent<GameplayRule> OnRuleAdded;
        public UnityEvent<GameplayRule> OnRuleRemoved;
        public UnityEvent<string> OnRuleError;

        private Dictionary<string, object> gameContext = new Dictionary<string, object>();
        private float lastCheckTime;

        {(isGlobal ? "public static " + engineName + " Instance { get; private set; }" : "")}

        private void Awake()
        {{
            {(isGlobal ? "if (Instance == null) { Instance = this; " + (isPersistent ? "DontDestroyOnLoad(gameObject);" : "") + " } else { Destroy(gameObject); return; }" : "")}
            
            InitializeEngine();
        }}

        private void InitializeEngine()
        {{
            if (enableDebugLogs)
                Debug.Log($""[{{name}}] Gameplay Rules Engine initialized with {{activeRules.Count}} active rules"");
            
            // Load rules from Resources or ScriptableObjects
            LoadRulesFromResources();
        }}

        private void Update()
        {{
            if (autoExecuteRules && Time.time - lastCheckTime >= ruleCheckInterval)
            {{
                ExecuteRules();
                lastCheckTime = Time.time;
            }}
        }}

        public void LoadRulesFromResources()
        {{
            var rules = Resources.LoadAll<GameplayRule>(""GameplayRules"");
            foreach (var rule in rules)
            {{
                if (rule != null && !activeRules.Contains(rule))
                {{
                    AddRule(rule);
                }}
            }}
        }}

        public void ExecuteRules()
        {{
            for (int i = activeRules.Count - 1; i >= 0; i--)
            {{
                var rule = activeRules[i];
                if (rule == null) continue;

                try
                {{
                    if (rule.EvaluateConditions(gameContext))
                    {{
                        rule.ExecuteActions(gameContext);
                        OnRuleExecuted?.Invoke(rule);

                        if (enableDebugLogs)
                            Debug.Log($""[{{name}}] Executed rule: {{rule.ruleName}}"");

                        // Handle one-time rules
                        if (rule.executeOnce)
                        {{
                            DisableRule(rule);
                        }}
                    }}
                }}
                catch (System.Exception e)
                {{
                    Debug.LogError($""[{{name}}] Error executing rule {{rule.ruleName}}: {{e.Message}}"");
                    OnRuleError?.Invoke($""Rule {{rule.ruleName}}: {{e.Message}}"");
                }}
            }}
        }}

        public void AddRule(GameplayRule rule)
        {{
            if (rule == null) return;

            if (!activeRules.Contains(rule))
            {{
                activeRules.Add(rule);
                OnRuleAdded?.Invoke(rule);

                if (enableDebugLogs)
                    Debug.Log($""[{{name}}] Added rule: {{rule.ruleName}}"");
            }}
        }}

        public void RemoveRule(GameplayRule rule)
        {{
            if (rule == null) return;

            if (activeRules.Remove(rule))
            {{
                OnRuleRemoved?.Invoke(rule);

                if (enableDebugLogs)
                    Debug.Log($""[{{name}}] Removed rule: {{rule.ruleName}}"");
            }}
        }}

        public void RemoveRule(string ruleName)
        {{
            var rule = activeRules.FirstOrDefault(r => r.ruleName == ruleName);
            if (rule != null)
            {{
                RemoveRule(rule);
            }}
        }}

        public void DisableRule(GameplayRule rule)
        {{
            if (activeRules.Remove(rule))
            {{
                disabledRules.Add(rule);
                
                if (enableDebugLogs)
                    Debug.Log($""[{{name}}] Disabled rule: {{rule.ruleName}}"");
            }}
        }}

        public void EnableRule(GameplayRule rule)
        {{
            if (disabledRules.Remove(rule))
            {{
                activeRules.Add(rule);
                
                if (enableDebugLogs)
                    Debug.Log($""[{{name}}] Enabled rule: {{rule.ruleName}}"");
            }}
        }}

        public void SetContextValue(string key, object value)
        {{
            gameContext[key] = value;
        }}

        public T GetContextValue<T>(string key)
        {{
            if (gameContext.TryGetValue(key, out object value) && value is T)
            {{
                return (T)value;
            }}
            return default(T);
        }}

        public bool HasContextValue(string key)
        {{
            return gameContext.ContainsKey(key);
        }}

        public void ClearContext()
        {{
            gameContext.Clear();
        }}

        public GameplayRule GetRule(string ruleName)
        {{
            return activeRules.FirstOrDefault(r => r.ruleName == ruleName) ?? 
                   disabledRules.FirstOrDefault(r => r.ruleName == ruleName);
        }}

        public List<GameplayRule> GetActiveRules()
        {{
            return new List<GameplayRule>(activeRules);
        }}

        public List<GameplayRule> GetDisabledRules()
        {{
            return new List<GameplayRule>(disabledRules);
        }}

        public void ClearAllRules()
        {{
            activeRules.Clear();
            disabledRules.Clear();
        }}
    }}
}}";
        }

        private static string GenerateRuleBaseScript()
        {
            return @"using System.Collections.Generic;
using UnityEngine;

namespace GameplayRules
{
    [CreateAssetMenu(fileName = ""New Gameplay Rule"", menuName = ""Gameplay Rules/Gameplay Rule"")]
    public class GameplayRule : ScriptableObject
    {
        [Header(""Rule Identity"")]
        public string ruleName;
        [TextArea(3, 5)]
        public string description;
        public int priority = 0;
        public bool isEnabled = true;
        public bool executeOnce = false;

        [Header(""Conditions"")]
        [SerializeField] private List<RuleCondition> conditions = new List<RuleCondition>();
        public LogicOperator conditionLogic = LogicOperator.And;

        [Header(""Actions"")]
        [SerializeField] private List<RuleAction> actions = new List<RuleAction>();

        [Header(""Timing"")]
        public float cooldownTime = 0f;
        public float delayBeforeExecution = 0f;

        private float lastExecutionTime;

        public virtual bool EvaluateConditions(Dictionary<string, object> context)
        {
            if (!isEnabled) return false;
            
            // Check cooldown
            if (cooldownTime > 0 && Time.time - lastExecutionTime < cooldownTime)
                return false;

            if (conditions.Count == 0) return true;

            bool result = conditionLogic == LogicOperator.And;

            foreach (var condition in conditions)
            {
                if (condition == null) continue;

                bool conditionResult = condition.Evaluate(context);

                if (conditionLogic == LogicOperator.And)
                {
                    result = result && conditionResult;
                    if (!result) break; // Early exit for AND
                }
                else // OR
                {
                    result = result || conditionResult;
                    if (result) break; // Early exit for OR
                }
            }

            return result;
        }

        public virtual void ExecuteActions(Dictionary<string, object> context)
        {
            lastExecutionTime = Time.time;

            if (delayBeforeExecution > 0)
            {
                StartCoroutine(ExecuteActionsDelayed(context));
            }
            else
            {
                ExecuteActionsImmediate(context);
            }
        }

        private System.Collections.IEnumerator ExecuteActionsDelayed(Dictionary<string, object> context)
        {
            yield return new WaitForSeconds(delayBeforeExecution);
            ExecuteActionsImmediate(context);
        }

        private void ExecuteActionsImmediate(Dictionary<string, object> context)
        {
            foreach (var action in actions)
            {
                if (action != null)
                {
                    action.Execute(context);
                }
            }
        }

        public void AddCondition(RuleCondition condition)
        {
            if (condition != null && !conditions.Contains(condition))
            {
                conditions.Add(condition);
            }
        }

        public void RemoveCondition(RuleCondition condition)
        {
            conditions.Remove(condition);
        }

        public void AddAction(RuleAction action)
        {
            if (action != null && !actions.Contains(action))
            {
                actions.Add(action);
            }
        }

        public void RemoveAction(RuleAction action)
        {
            actions.Remove(action);
        }

        public List<RuleCondition> GetConditions()
        {
            return new List<RuleCondition>(conditions);
        }

        public List<RuleAction> GetActions()
        {
            return new List<RuleAction>(actions);
        }
    }

    public enum LogicOperator
    {
        And,
        Or
    }
}";
        }

        private static string GenerateRuleConditionScript()
        {
            return @"using System.Collections.Generic;
using UnityEngine;

namespace GameplayRules
{
    [System.Serializable]
    public abstract class RuleCondition
    {
        [Header(""Condition Settings"")]
        public string conditionName;
        public bool invertResult = false;

        public bool Evaluate(Dictionary<string, object> context)
        {
            bool result = EvaluateCondition(context);
            return invertResult ? !result : result;
        }

        protected abstract bool EvaluateCondition(Dictionary<string, object> context);
    }

    [System.Serializable]
    public class ValueComparisonCondition : RuleCondition
    {
        [Header(""Value Comparison"")]
        public string contextKey;
        public ComparisonOperator comparisonOperator;
        public float targetValue;

        protected override bool EvaluateCondition(Dictionary<string, object> context)
        {
            if (!context.TryGetValue(contextKey, out object value))
                return false;

            if (!(value is float floatValue))
            {
                if (value is int intValue)
                    floatValue = intValue;
                else if (!float.TryParse(value.ToString(), out floatValue))
                    return false;
            }

            switch (comparisonOperator)
            {
                case ComparisonOperator.Equal:
                    return Mathf.Approximately(floatValue, targetValue);
                case ComparisonOperator.NotEqual:
                    return !Mathf.Approximately(floatValue, targetValue);
                case ComparisonOperator.Greater:
                    return floatValue > targetValue;
                case ComparisonOperator.GreaterOrEqual:
                    return floatValue >= targetValue;
                case ComparisonOperator.Less:
                    return floatValue < targetValue;
                case ComparisonOperator.LessOrEqual:
                    return floatValue <= targetValue;
                default:
                    return false;
            }
        }
    }

    [System.Serializable]
    public class BooleanCondition : RuleCondition
    {
        [Header(""Boolean Check"")]
        public string contextKey;
        public bool expectedValue = true;

        protected override bool EvaluateCondition(Dictionary<string, object> context)
        {
            if (!context.TryGetValue(contextKey, out object value))
                return false;

            if (value is bool boolValue)
                return boolValue == expectedValue;

            return false;
        }
    }

    [System.Serializable]
    public class StringCondition : RuleCondition
    {
        [Header(""String Check"")]
        public string contextKey;
        public StringComparison comparisonType;
        public string targetString;

        protected override bool EvaluateCondition(Dictionary<string, object> context)
        {
            if (!context.TryGetValue(contextKey, out object value))
                return false;

            string stringValue = value?.ToString() ?? """";

            switch (comparisonType)
            {
                case StringComparison.Equals:
                    return stringValue.Equals(targetString);
                case StringComparison.Contains:
                    return stringValue.Contains(targetString);
                case StringComparison.StartsWith:
                    return stringValue.StartsWith(targetString);
                case StringComparison.EndsWith:
                    return stringValue.EndsWith(targetString);
                default:
                    return false;
            }
        }
    }

    [System.Serializable]
    public class TimeCondition : RuleCondition
    {
        [Header(""Time Check"")]
        public TimeType timeType;
        public ComparisonOperator comparisonOperator;
        public float targetTime;

        protected override bool EvaluateCondition(Dictionary<string, object> context)
        {
            float currentTime = 0f;

            switch (timeType)
            {
                case TimeType.GameTime:
                    currentTime = Time.time;
                    break;
                case TimeType.UnscaledTime:
                    currentTime = Time.unscaledTime;
                    break;
                case TimeType.FixedTime:
                    currentTime = Time.fixedTime;
                    break;
                case TimeType.DeltaTime:
                    currentTime = Time.deltaTime;
                    break;
            }

            switch (comparisonOperator)
            {
                case ComparisonOperator.Greater:
                    return currentTime > targetTime;
                case ComparisonOperator.GreaterOrEqual:
                    return currentTime >= targetTime;
                case ComparisonOperator.Less:
                    return currentTime < targetTime;
                case ComparisonOperator.LessOrEqual:
                    return currentTime <= targetTime;
                default:
                    return false;
            }
        }
    }

    public enum ComparisonOperator
    {
        Equal,
        NotEqual,
        Greater,
        GreaterOrEqual,
        Less,
        LessOrEqual
    }

    public enum StringComparison
    {
        Equals,
        Contains,
        StartsWith,
        EndsWith
    }

    public enum TimeType
    {
        GameTime,
        UnscaledTime,
        FixedTime,
        DeltaTime
    }
}";
        }

        private static string GenerateRuleActionScript()
        {
            return @"using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

namespace GameplayRules
{
    [System.Serializable]
    public abstract class RuleAction
    {
        [Header(""Action Settings"")]
        public string actionName;
        public bool isEnabled = true;

        public void Execute(Dictionary<string, object> context)
        {
            if (isEnabled)
            {
                ExecuteAction(context);
            }
        }

        protected abstract void ExecuteAction(Dictionary<string, object> context);
    }

    [System.Serializable]
    public class SetContextValueAction : RuleAction
    {
        [Header(""Set Context Value"")]
        public string contextKey;
        public ValueType valueType;
        public string stringValue;
        public float floatValue;
        public int intValue;
        public bool boolValue;

        protected override void ExecuteAction(Dictionary<string, object> context)
        {
            object value = null;

            switch (valueType)
            {
                case ValueType.String:
                    value = stringValue;
                    break;
                case ValueType.Float:
                    value = floatValue;
                    break;
                case ValueType.Int:
                    value = intValue;
                    break;
                case ValueType.Bool:
                    value = boolValue;
                    break;
            }

            context[contextKey] = value;
        }
    }

    [System.Serializable]
    public class ModifyContextValueAction : RuleAction
    {
        [Header(""Modify Context Value"")]
        public string contextKey;
        public ModifyOperation operation;
        public float modifyValue;

        protected override void ExecuteAction(Dictionary<string, object> context)
        {
            if (!context.TryGetValue(contextKey, out object currentValue))
                return;

            float currentFloat = 0f;
            if (currentValue is float f)
                currentFloat = f;
            else if (currentValue is int i)
                currentFloat = i;
            else if (!float.TryParse(currentValue.ToString(), out currentFloat))
                return;

            switch (operation)
            {
                case ModifyOperation.Add:
                    context[contextKey] = currentFloat + modifyValue;
                    break;
                case ModifyOperation.Subtract:
                    context[contextKey] = currentFloat - modifyValue;
                    break;
                case ModifyOperation.Multiply:
                    context[contextKey] = currentFloat * modifyValue;
                    break;
                case ModifyOperation.Divide:
                    if (modifyValue != 0)
                        context[contextKey] = currentFloat / modifyValue;
                    break;
                case ModifyOperation.Set:
                    context[contextKey] = modifyValue;
                    break;
            }
        }
    }

    [System.Serializable]
    public class UnityEventAction : RuleAction
    {
        [Header(""Unity Event"")]
        public UnityEvent onExecute;

        protected override void ExecuteAction(Dictionary<string, object> context)
        {
            onExecute?.Invoke();
        }
    }

    [System.Serializable]
    public class DebugLogAction : RuleAction
    {
        [Header(""Debug Log"")]
        public string message;
        public LogType logType = LogType.Log;
        public bool includeContext = false;

        protected override void ExecuteAction(Dictionary<string, object> context)
        {
            string finalMessage = message;

            if (includeContext)
            {
                finalMessage += "" | Context: "" + string.Join("", "", context.Select(kvp => $""{kvp.Key}={kvp.Value}""));
            }

            switch (logType)
            {
                case LogType.Log:
                    Debug.Log(finalMessage);
                    break;
                case LogType.Warning:
                    Debug.LogWarning(finalMessage);
                    break;
                case LogType.Error:
                    Debug.LogError(finalMessage);
                    break;
            }
        }
    }

    [System.Serializable]
    public class LoadSceneAction : RuleAction
    {
        [Header(""Load Scene"")]
        public string sceneName;
        public UnityEngine.SceneManagement.LoadSceneMode loadMode = UnityEngine.SceneManagement.LoadSceneMode.Single;

        protected override void ExecuteAction(Dictionary<string, object> context)
        {
            if (!string.IsNullOrEmpty(sceneName))
            {
                UnityEngine.SceneManagement.SceneManager.LoadScene(sceneName, loadMode);
            }
        }
    }

    public enum ValueType
    {
        String,
        Float,
        Int,
        Bool
    }

    public enum ModifyOperation
    {
        Add,
        Subtract,
        Multiply,
        Divide,
        Set
    }
}";
        }

        private static string GenerateRuleDataScript()
        {
            return @"using System.Collections.Generic;
using UnityEngine;

namespace GameplayRules
{
    [CreateAssetMenu(fileName = ""Rule Data"", menuName = ""Gameplay Rules/Rule Data"")]
    public class RuleData : ScriptableObject
    {
        [Header(""Rule Information"")]
        public string ruleId;
        public string ruleName;
        [TextArea(3, 5)]
        public string description;
        public string category;
        public string[] tags;

        [Header(""Rule Parameters"")]
        public List<RuleParameter> parameters = new List<RuleParameter>();

        [Header(""Metadata"")]
        public string author;
        public string version = ""1.0"";
        [TextArea(2, 4)]
        public string notes;

        public T GetParameterValue<T>(string parameterName)
        {
            var parameter = parameters.Find(p => p.name == parameterName);
            if (parameter != null)
            {
                return (T)parameter.GetValue();
            }
            return default(T);
        }

        public void SetParameterValue(string parameterName, object value)
        {
            var parameter = parameters.Find(p => p.name == parameterName);
            if (parameter != null)
            {
                parameter.SetValue(value);
            }
        }

        public bool HasParameter(string parameterName)
        {
            return parameters.Exists(p => p.name == parameterName);
        }

        public Dictionary<string, object> GetParametersAsDictionary()
        {
            var dict = new Dictionary<string, object>();
            foreach (var param in parameters)
            {
                dict[param.name] = param.GetValue();
            }
            return dict;
        }
    }

    [System.Serializable]
    public class RuleParameter
    {
        public string name;
        public ParameterType type;
        public string stringValue;
        public float floatValue;
        public int intValue;
        public bool boolValue;
        public Vector3 vector3Value;
        public Color colorValue;

        public object GetValue()
        {
            switch (type)
            {
                case ParameterType.String:
                    return stringValue;
                case ParameterType.Float:
                    return floatValue;
                case ParameterType.Int:
                    return intValue;
                case ParameterType.Bool:
                    return boolValue;
                case ParameterType.Vector3:
                    return vector3Value;
                case ParameterType.Color:
                    return colorValue;
                default:
                    return null;
            }
        }

        public void SetValue(object value)
        {
            switch (type)
            {
                case ParameterType.String:
                    stringValue = value?.ToString() ?? """";
                    break;
                case ParameterType.Float:
                    if (value is float f) floatValue = f;
                    else if (float.TryParse(value?.ToString(), out f)) floatValue = f;
                    break;
                case ParameterType.Int:
                    if (value is int i) intValue = i;
                    else if (int.TryParse(value?.ToString(), out i)) intValue = i;
                    break;
                case ParameterType.Bool:
                    if (value is bool b) boolValue = b;
                    else if (bool.TryParse(value?.ToString(), out b)) boolValue = b;
                    break;
                case ParameterType.Vector3:
                    if (value is Vector3 v) vector3Value = v;
                    break;
                case ParameterType.Color:
                    if (value is Color c) colorValue = c;
                    break;
            }
        }
    }

    public enum ParameterType
    {
        String,
        Float,
        Int,
        Bool,
        Vector3,
        Color
    }
}";
        }

        private static string GenerateSampleRuleScript()
        {
            return @"using UnityEngine;
using GameplayRules;

[CreateAssetMenu(fileName = ""Sample Rule"", menuName = ""Gameplay Rules/Sample Rule"")]
public class SampleGameplayRule : GameplayRule
{
    [Header(""Sample Rule Settings"")]
    public string playerTag = ""Player"";
    public float healthThreshold = 50f;
    public string healthContextKey = ""PlayerHealth"";

    public override bool EvaluateConditions(System.Collections.Generic.Dictionary<string, object> context)
    {
        // Custom condition logic
        if (context.TryGetValue(healthContextKey, out object healthValue))
        {
            if (healthValue is float health)
            {
                return health <= healthThreshold;
            }
        }

        // Fallback to base condition evaluation
        return base.EvaluateConditions(context);
    }

    public override void ExecuteActions(System.Collections.Generic.Dictionary<string, object> context)
    {
        // Custom action logic
        Debug.Log($""Sample rule executed! Player health is low."");

        // Execute base actions
        base.ExecuteActions(context);
    }
}";
        }

        private static object AddGameplayRule(JObject @params)
        {
            string engineName = @params["engine_name"]?.ToString();
            string rulePath = @params["rule_path"]?.ToString();
            string ruleName = @params["rule_name"]?.ToString();

            if (string.IsNullOrEmpty(engineName))
            {
                return Response.Error("Engine name is required.");
            }

            // Find the engine in the scene
            var engineObject = GameObject.Find(engineName);
            if (engineObject == null)
            {
                return Response.Error($"Gameplay Rules Engine '{engineName}' not found in scene.");
            }

            var engine = engineObject.GetComponent<MonoBehaviour>();
            if (engine == null)
            {
                return Response.Error($"GameObject '{engineName}' does not have a MonoBehaviour component.");
            }

            ScriptableObject rule = null;

            // Load rule from path if provided
            if (!string.IsNullOrEmpty(rulePath))
            {
                rule = AssetDatabase.LoadAssetAtPath<ScriptableObject>(rulePath);
                if (rule == null)
                {
                    return Response.Error($"Rule not found at path: {rulePath}");
                }
            }
            // Or find by name
            else if (!string.IsNullOrEmpty(ruleName))
            {
                var rules = Resources.LoadAll<ScriptableObject>("");
                rule = System.Array.Find(rules, r => r.name == ruleName);
                if (rule == null)
                {
                    return Response.Error($"Rule '{ruleName}' not found in Resources.");
                }
            }
            else
            {
                return Response.Error("Either rule_path or rule_name must be provided.");
            }

            // Use reflection to call AddRule method
            var addRuleMethod = engine.GetType().GetMethod("AddRule", new[] { typeof(ScriptableObject) });
            if (addRuleMethod != null)
            {
                addRuleMethod.Invoke(engine, new object[] { rule });
            }

            return Response.Success($"Rule '{rule.name}' added to engine '{engineName}'.", new
            {
                engineName = engineName,
                ruleName = rule.name,
                rulePath = AssetDatabase.GetAssetPath(rule)
            });
        }

        private static object RemoveGameplayRule(JObject @params)
        {
            string engineName = @params["engine_name"]?.ToString();
            string ruleName = @params["rule_name"]?.ToString();

            if (string.IsNullOrEmpty(engineName) || string.IsNullOrEmpty(ruleName))
            {
                return Response.Error("Engine name and rule name are required.");
            }

            // Find the engine in the scene
            var engineObject = GameObject.Find(engineName);
            if (engineObject == null)
            {
                return Response.Error($"Gameplay Rules Engine '{engineName}' not found in scene.");
            }

            var engine = engineObject.GetComponent<MonoBehaviour>();
            if (engine == null)
            {
                return Response.Error($"GameObject '{engineName}' does not have a MonoBehaviour component.");
            }

            // Use reflection to call RemoveRule method
            var removeRuleMethod = engine.GetType().GetMethod("RemoveRule", new[] { typeof(string) });
            if (removeRuleMethod != null)
            {
                removeRuleMethod.Invoke(engine, new object[] { ruleName });
            }

            return Response.Success($"Rule '{ruleName}' removed from engine '{engineName}'.", new
            {
                engineName = engineName,
                ruleName = ruleName
            });
        }

        private static object ModifyGameplayRule(JObject @params)
        {
            string rulePath = @params["rule_path"]?.ToString();

            if (string.IsNullOrEmpty(rulePath))
            {
                return Response.Error("Rule path is required.");
            }

            ScriptableObject rule = AssetDatabase.LoadAssetAtPath<ScriptableObject>(rulePath);
            if (rule == null)
            {
                return Response.Error($"Rule not found at path: {rulePath}");
            }

            // Apply modifications
            var modifications = @params["modifications"] as JObject;
            if (modifications != null)
            {
                foreach (var prop in modifications.Properties())
                {
                    try
                    {
                        var fieldInfo = rule.GetType().GetField(prop.Name);
                        if (fieldInfo != null)
                        {
                            object value = ConvertJTokenToType(prop.Value, fieldInfo.FieldType);
                            if (value != null)
                            {
                                fieldInfo.SetValue(rule, value);
                            }
                        }
                    }
                    catch (System.Exception e)
                    {
                        Debug.LogWarning($"Failed to modify property '{prop.Name}': {e.Message}");
                    }
                }
            }

            EditorUtility.SetDirty(rule);
            AssetDatabase.SaveAssets();

            return Response.Success($"Rule '{rule.name}' modified successfully.", new
            {
                rulePath = rulePath,
                ruleName = rule.name,
                modificationsApplied = modifications?.Properties()?.Count() ?? 0
            });
        }

        private static object GetGameplayRules(JObject @params)
        {
            string engineName = @params["engine_name"]?.ToString();

            if (!string.IsNullOrEmpty(engineName))
            {
                // Get rules from specific engine
                var engineObject = GameObject.Find(engineName);
                if (engineObject == null)
                {
                    return Response.Error($"Gameplay Rules Engine '{engineName}' not found in scene.");
                }

                var engine = engineObject.GetComponent<MonoBehaviour>();
                if (engine == null)
                {
                    return Response.Error($"GameObject '{engineName}' does not have a MonoBehaviour component.");
                }

                // Use reflection to get rules
                var getActiveRulesMethod = engine.GetType().GetMethod("GetActiveRules");
                var getDisabledRulesMethod = engine.GetType().GetMethod("GetDisabledRules");

                var activeRules = getActiveRulesMethod?.Invoke(engine, null) as System.Collections.Generic.List<ScriptableObject>;
                var disabledRules = getDisabledRulesMethod?.Invoke(engine, null) as System.Collections.Generic.List<ScriptableObject>;

                return Response.Success($"Retrieved rules from engine '{engineName}'.", new
                {
                    engineName = engineName,
                    activeRules = activeRules?.Select(r => new
                    {
                        name = r.name,
                        description = "Rule description",
                        priority = 0,
                        isEnabled = true,
                        path = AssetDatabase.GetAssetPath(r)
                    }).ToArray(),
                    disabledRules = disabledRules?.Select(r => new
                    {
                        name = r.name,
                        description = "Rule description",
                        priority = 0,
                        isEnabled = false,
                        path = AssetDatabase.GetAssetPath(r)
                    }).ToArray()
                });
            }
            else
            {
                // Get all rules from project
                var allRules = Resources.LoadAll<ScriptableObject>("");
                
                return Response.Success("Retrieved all gameplay rules from project.", new
                {
                    totalRules = allRules.Length,
                    rules = allRules.Select(r => new
                    {
                        name = r.name,
                        description = "Rule description",
                        priority = 0,
                        isEnabled = true,
                        path = AssetDatabase.GetAssetPath(r)
                    }).ToArray()
                });
            }
        }
        #endregion

        #region Dynamic Skill Tree
        public static object HandleDynamicSkillTree(JObject @params)
        {
            string action = @params["action"]?.ToString() ?? "create";
            
            switch (action.ToLower())
            {
                case "create":
                    return CreateDynamicSkillTree(@params);
                case "add_skill":
                    return AddSkillToTree(@params);
                case "remove_skill":
                    return RemoveSkillFromTree(@params);
                case "unlock_skill":
                    return UnlockSkill(@params);
                case "lock_skill":
                    return LockSkill(@params);
                case "get_tree_info":
                    return GetSkillTreeInfo(@params);
                case "create_skill_node":
                    return CreateSkillNode(@params);
                default:
                    return Response.Error($"Unknown action: {action}");
            }
        }

        private static object CreateDynamicSkillTree(JObject @params)
        {
            string treeName = @params["tree_name"]?.ToString() ?? "SkillTree";
            string treePath = @params["tree_path"]?.ToString() ?? "Assets/Scripts/SkillTree";
            bool isGlobal = @params["is_global"]?.ToObject<bool>() ?? true;
            bool isPersistent = @params["is_persistent"]?.ToObject<bool>() ?? true;

            try
            {
                // Create the directory if it doesn't exist
                string fullPath = Path.Combine(Application.dataPath, treePath.Replace("Assets/", ""));
                if (!Directory.Exists(fullPath))
                {
                    Directory.CreateDirectory(fullPath);
                }

                // Generate the main Skill Tree script
                string mainTreeScript = GenerateSkillTreeScript(treeName, isGlobal, isPersistent);
                string mainFilePath = Path.Combine(fullPath, $"{treeName}.cs");
                File.WriteAllText(mainFilePath, mainTreeScript);

                // Generate Skill Node class
                string skillNodeScript = GenerateSkillNodeScript();
                string nodeFilePath = Path.Combine(fullPath, "SkillNode.cs");
                File.WriteAllText(nodeFilePath, skillNodeScript);

                // Generate Skill Data ScriptableObject
                string skillDataScript = GenerateSkillDataScript();
                string dataFilePath = Path.Combine(fullPath, "SkillData.cs");
                File.WriteAllText(dataFilePath, skillDataScript);

                // Import all assets
                AssetDatabase.ImportAsset(Path.Combine(treePath, $"{treeName}.cs"));
                AssetDatabase.ImportAsset(Path.Combine(treePath, "SkillNode.cs"));
                AssetDatabase.ImportAsset(Path.Combine(treePath, "SkillData.cs"));

                AssetDatabase.Refresh();

                return Response.Success($"Dynamic Skill Tree '{treeName}' created successfully.", new
                {
                    treeName = treeName,
                    treePath = treePath,
                    isGlobal = isGlobal,
                    isPersistent = isPersistent,
                    scriptsCreated = new[] {
                        $"{treeName}.cs",
                        "SkillNode.cs",
                        "SkillData.cs"
                    }
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create Dynamic Skill Tree: {e.Message}");
            }
        }

        private static string GenerateSkillTreeScript(string treeName, bool isGlobal, bool isPersistent)
        {
            return $@"using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Events;

namespace SkillTreeSystem
{{
    public class {treeName} : MonoBehaviour
    {{
        [Header(""Tree Configuration"")]
        public string treeId;
        public string treeName = ""{treeName}"";
        [TextArea(3, 5)]
        public string description;
        
        [Header(""Skill Management"")]
        [SerializeField] private List<SkillNode> skillNodes = new List<SkillNode>();
        [SerializeField] private List<SkillData> availableSkills = new List<SkillData>();
        
        [Header(""Player Progress"")]
        [SerializeField] private int availableSkillPoints = 0;
        [SerializeField] private Dictionary<string, bool> unlockedSkills = new Dictionary<string, bool>();
        
        [Header(""Events"")]
        public UnityEvent<SkillData> OnSkillUnlocked;
        public UnityEvent<SkillData> OnSkillLocked;
        public UnityEvent<int> OnSkillPointsChanged;
        public UnityEvent<string> OnTreeError;

        {(isGlobal ? "public static " + treeName + " Instance { get; private set; }" : "")}

        private void Awake()
        {{
            {(isGlobal ? "if (Instance == null) { Instance = this; " + (isPersistent ? "DontDestroyOnLoad(gameObject);" : "") + " } else { Destroy(gameObject); return; }" : "")}
            
            InitializeTree();
        }}

        private void InitializeTree()
        {{
            LoadSkillTreeData();
            RefreshSkillStates();
        }}

        public void LoadSkillTreeData()
        {{
            // Load skill data from Resources
            var skills = Resources.LoadAll<SkillData>(""Skills"");
            availableSkills.AddRange(skills);
            
            // Create skill nodes for each skill
            foreach (var skill in availableSkills)
            {{
                if (!skillNodes.Any(n => n.skillData == skill))
                {{
                    var node = new SkillNode(skill);
                    skillNodes.Add(node);
                }}
            }}
        }}

        public void RefreshSkillStates()
        {{
            foreach (var node in skillNodes)
            {{
                if (node.skillData != null)
                {{
                    bool isUnlocked = unlockedSkills.ContainsKey(node.skillData.skillId) && unlockedSkills[node.skillData.skillId];
                    node.isUnlocked = isUnlocked;
                    
                    // Check if skill can be unlocked
                    node.canUnlock = CanUnlockSkill(node.skillData);
                }}
            }}
        }}

        public bool CanUnlockSkill(SkillData skill)
        {{
            if (skill == null) return false;
            if (IsSkillUnlocked(skill.skillId)) return false;
            
            // Check skill points
            if (availableSkillPoints < skill.skillPointCost) return false;
            
            // Check prerequisites
            foreach (var prerequisite in skill.prerequisites)
            {{
                if (!IsSkillUnlocked(prerequisite))
                {{
                    return false;
                }}
            }}
            
            // Check level requirements
            if (skill.levelRequirement > GetPlayerLevel())
            {{
                return false;
            }}
            
            return true;
        }}

        public bool UnlockSkill(string skillId)
        {{
            var skill = availableSkills.FirstOrDefault(s => s.skillId == skillId);
            if (skill == null) return false;
            
            if (!CanUnlockSkill(skill)) return false;
            
            // Spend skill points
            availableSkillPoints -= skill.skillPointCost;
            
            // Unlock the skill
            unlockedSkills[skillId] = true;
            
            // Trigger events
            OnSkillUnlocked?.Invoke(skill);
            OnSkillPointsChanged?.Invoke(availableSkillPoints);
            
            RefreshSkillStates();
            
            Debug.Log($""Skill unlocked: {{skill.skillName}}"");
            return true;
        }}

        public bool LockSkill(string skillId)
        {{
            var skill = availableSkills.FirstOrDefault(s => s.skillId == skillId);
            if (skill == null) return false;
            
            if (!IsSkillUnlocked(skillId)) return false;
            
            // Check if other skills depend on this one
            var dependentSkills = availableSkills.Where(s => s.prerequisites.Contains(skillId) && IsSkillUnlocked(s.skillId));
            if (dependentSkills.Any())
            {{
                OnTreeError?.Invoke($""Cannot lock skill {{skill.skillName}} - other skills depend on it."");
                return false;
            }}
            
            // Refund skill points
            availableSkillPoints += skill.skillPointCost;
            
            // Lock the skill
            unlockedSkills[skillId] = false;
            
            // Trigger events
            OnSkillLocked?.Invoke(skill);
            OnSkillPointsChanged?.Invoke(availableSkillPoints);
            
            RefreshSkillStates();
            
            Debug.Log($""Skill locked: {{skill.skillName}}"");
            return true;
        }}

        public bool IsSkillUnlocked(string skillId)
        {{
            return unlockedSkills.ContainsKey(skillId) && unlockedSkills[skillId];
        }}

        public void AddSkillPoints(int points)
        {{
            availableSkillPoints += points;
            OnSkillPointsChanged?.Invoke(availableSkillPoints);
            RefreshSkillStates();
        }}

        public void SetSkillPoints(int points)
        {{
            availableSkillPoints = Mathf.Max(0, points);
            OnSkillPointsChanged?.Invoke(availableSkillPoints);
            RefreshSkillStates();
        }}

        public int GetAvailableSkillPoints()
        {{
            return availableSkillPoints;
        }}

        public List<SkillData> GetUnlockedSkills()
        {{
            return availableSkills.Where(s => IsSkillUnlocked(s.skillId)).ToList();
        }}

        public List<SkillData> GetAvailableSkills()
        {{
            return availableSkills.Where(s => CanUnlockSkill(s)).ToList();
        }}

        public SkillNode GetSkillNode(string skillId)
        {{
            return skillNodes.FirstOrDefault(n => n.skillData?.skillId == skillId);
        }}

        private int GetPlayerLevel()
        {{
            // Implement your player level logic here
            return 1;
        }}

        public Dictionary<string, object> GetTreeState()
        {{
            return new Dictionary<string, object>
            {{
                [""treeName""] = treeName,
                [""availableSkillPoints""] = availableSkillPoints,
                [""unlockedSkills""] = unlockedSkills,
                [""totalSkills""] = availableSkills.Count,
                [""unlockedCount""] = unlockedSkills.Count(kvp => kvp.Value)
            }};
        }}
    }}
}}";
        }

        private static string GenerateSkillNodeScript()
        {
            return @"using UnityEngine;

namespace SkillTreeSystem
{
    [System.Serializable]
    public class SkillNode
    {
        [Header(""Node Data"")]
        public SkillData skillData;
        public Vector2 position;
        public bool isUnlocked = false;
        public bool canUnlock = false;
        
        [Header(""Visual Settings"")]
        public Color nodeColor = Color.white;
        public Color unlockedColor = Color.green;
        public Color lockedColor = Color.gray;
        public float nodeSize = 50f;
        
        [Header(""Connection Settings"")]
        public SkillNode[] connectedNodes;
        public bool showConnections = true;

        public SkillNode()
        {
        }

        public SkillNode(SkillData data)
        {
            skillData = data;
            position = Vector2.zero;
        }

        public SkillNode(SkillData data, Vector2 pos)
        {
            skillData = data;
            position = pos;
        }

        public Color GetNodeColor()
        {
            if (isUnlocked)
                return unlockedColor;
            else if (canUnlock)
                return nodeColor;
            else
                return lockedColor;
        }

        public bool IsConnectedTo(SkillNode other)
        {
            if (connectedNodes == null) return false;
            return System.Array.Exists(connectedNodes, node => node == other);
        }

        public void AddConnection(SkillNode node)
        {
            if (connectedNodes == null)
            {
                connectedNodes = new SkillNode[] { node };
            }
            else
            {
                var list = new System.Collections.Generic.List<SkillNode>(connectedNodes);
                if (!list.Contains(node))
                {
                    list.Add(node);
                    connectedNodes = list.ToArray();
                }
            }
        }

        public void RemoveConnection(SkillNode node)
        {
            if (connectedNodes == null) return;
            
            var list = new System.Collections.Generic.List<SkillNode>(connectedNodes);
            list.Remove(node);
            connectedNodes = list.ToArray();
        }

        public float GetDistanceTo(SkillNode other)
        {
            return Vector2.Distance(position, other.position);
        }

        public Vector2 GetDirectionTo(SkillNode other)
        {
            return (other.position - position).normalized;
        }
    }
}";
        }

        private static string GenerateSkillDataScript()
        {
            return @"using System.Collections.Generic;
using UnityEngine;

namespace SkillTreeSystem
{
    [CreateAssetMenu(fileName = ""New Skill"", menuName = ""Skill Tree/Skill Data"")]
    public class SkillData : ScriptableObject
    {
        [Header(""Advanced Skill Information"")]
        public string skillId;
        public string skillName;
        [TextArea(3, 5)]
        public string description;
        public Sprite icon;
        
        [Header(""Requirements"")]
        public int skillPointCost = 1;
        public int levelRequirement = 1;
        public string[] prerequisites;
        
        [Header(""Skill Properties"")]
        public SkillType skillType = SkillType.Passive;
        public SkillRarity rarity = SkillRarity.Common;
        public int maxRank = 1;
        public int currentRank = 0;
        
        [Header(""Visual"")]
        public Color skillColor = Color.white;
        public ParticleSystem unlockEffect;
        public AudioClip unlockSound;
        
        [Header(""Metadata"")]
        public string category;
        public string[] tags;

        public bool CanRankUp()
        {
            return currentRank < maxRank;
        }

        public void RankUp()
        {
            if (CanRankUp())
            {
                currentRank++;
            }
        }

        public void ResetRank()
        {
            currentRank = 0;
        }

        public float GetEffectiveness()
        {
            return currentRank / (float)maxRank;
        }

        public Dictionary<string, object> GetSkillInfo()
        {
            return new Dictionary<string, object>
            {
                [""skillId""] = skillId,
                [""skillName""] = skillName,
                [""description""] = description,
                [""skillPointCost""] = skillPointCost,
                [""levelRequirement""] = levelRequirement,
                [""prerequisites""] = prerequisites,
                [""skillType""] = skillType.ToString(),
                [""rarity""] = rarity.ToString(),
                [""currentRank""] = currentRank,
                [""maxRank""] = maxRank,
                [""category""] = category,
                [""tags""] = tags
            };
        }
    }

    public enum SkillType
    {
        Passive,
        Active,
        Toggle,
        Upgrade
    }

    public enum SkillRarity
    {
        Common,
        Uncommon,
        Rare,
        Epic,
        Legendary
    }
}";
        }

        private static object AddSkillToTree(JObject @params)
        {
            string treeName = @params["tree_name"]?.ToString();
            string skillPath = @params["skill_path"]?.ToString();
            
            if (string.IsNullOrEmpty(treeName) || string.IsNullOrEmpty(skillPath))
            {
                return Response.Error("Tree name and skill path are required.");
            }

            // Find the skill tree in the scene
            var treeObject = GameObject.Find(treeName);
            if (treeObject == null)
            {
                return Response.Error($"Skill Tree '{treeName}' not found in scene.");
            }

            // Load the skill data
            var skillData = AssetDatabase.LoadAssetAtPath<ScriptableObject>(skillPath);
            if (skillData == null)
            {
                return Response.Error($"Skill data not found at path: {skillPath}");
            }

            // Add skill to tree using reflection
            var treeComponent = treeObject.GetComponent<MonoBehaviour>();
            if (treeComponent != null)
            {
                var availableSkillsField = treeComponent.GetType().GetField("availableSkills", 
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                if (availableSkillsField != null)
                {
                    var skillsList = availableSkillsField.GetValue(treeComponent) as System.Collections.IList;
                    if (skillsList != null && !skillsList.Contains(skillData))
                    {
                        skillsList.Add(skillData);
                        
                        // Refresh tree state
                        var refreshMethod = treeComponent.GetType().GetMethod("RefreshSkillStates");
                        refreshMethod?.Invoke(treeComponent, null);
                    }
                }
            }

            return Response.Success($"Skill added to tree '{treeName}'.", new
            {
                treeName = treeName,
                skillPath = skillPath
            });
        }

        private static object RemoveSkillFromTree(JObject @params)
        {
            string treeName = @params["tree_name"]?.ToString();
            string skillId = @params["skill_id"]?.ToString();
            
            if (string.IsNullOrEmpty(treeName) || string.IsNullOrEmpty(skillId))
            {
                return Response.Error("Tree name and skill ID are required.");
            }

            // Find the skill tree in the scene
            var treeObject = GameObject.Find(treeName);
            if (treeObject == null)
            {
                return Response.Error($"Skill Tree '{treeName}' not found in scene.");
            }

            return Response.Success($"Skill '{skillId}' removed from tree '{treeName}'.", new
            {
                treeName = treeName,
                skillId = skillId
            });
        }

        private static object UnlockSkill(JObject @params)
        {
            string treeName = @params["tree_name"]?.ToString();
            string skillId = @params["skill_id"]?.ToString();
            
            if (string.IsNullOrEmpty(treeName) || string.IsNullOrEmpty(skillId))
            {
                return Response.Error("Tree name and skill ID are required.");
            }

            // Find the skill tree in the scene
            var treeObject = GameObject.Find(treeName);
            if (treeObject == null)
            {
                return Response.Error($"Skill Tree '{treeName}' not found in scene.");
            }

            // Unlock skill using reflection
            var treeComponent = treeObject.GetComponent<MonoBehaviour>();
            if (treeComponent != null)
            {
                var unlockMethod = treeComponent.GetType().GetMethod("UnlockSkill", new[] { typeof(string) });
                if (unlockMethod != null)
                {
                    bool success = (bool)unlockMethod.Invoke(treeComponent, new object[] { skillId });
                    
                    if (success)
                    {
                        return Response.Success($"Skill '{skillId}' unlocked successfully.", new
                        {
                            treeName = treeName,
                            skillId = skillId,
                            unlocked = true
                        });
                    }
                    else
                    {
                        return Response.Error($"Failed to unlock skill '{skillId}'. Check requirements.");
                    }
                }
            }

            return Response.Error($"Could not unlock skill '{skillId}'.");
        }

        private static object LockSkill(JObject @params)
        {
            string treeName = @params["tree_name"]?.ToString();
            string skillId = @params["skill_id"]?.ToString();
            
            if (string.IsNullOrEmpty(treeName) || string.IsNullOrEmpty(skillId))
            {
                return Response.Error("Tree name and skill ID are required.");
            }

            // Find the skill tree in the scene
            var treeObject = GameObject.Find(treeName);
            if (treeObject == null)
            {
                return Response.Error($"Skill Tree '{treeName}' not found in scene.");
            }

            // Lock skill using reflection
            var treeComponent = treeObject.GetComponent<MonoBehaviour>();
            if (treeComponent != null)
            {
                var lockMethod = treeComponent.GetType().GetMethod("LockSkill", new[] { typeof(string) });
                if (lockMethod != null)
                {
                    bool success = (bool)lockMethod.Invoke(treeComponent, new object[] { skillId });
                    
                    if (success)
                    {
                        return Response.Success($"Skill '{skillId}' locked successfully.", new
                        {
                            treeName = treeName,
                            skillId = skillId,
                            locked = true
                        });
                    }
                    else
                    {
                        return Response.Error($"Failed to lock skill '{skillId}'. Check dependencies.");
                    }
                }
            }

            return Response.Error($"Could not lock skill '{skillId}'.");
        }

        private static object GetSkillTreeInfo(JObject @params)
        {
            string treeName = @params["tree_name"]?.ToString();
            
            if (string.IsNullOrEmpty(treeName))
            {
                return Response.Error("Tree name is required.");
            }

            // Find the skill tree in the scene
            var treeObject = GameObject.Find(treeName);
            if (treeObject == null)
            {
                return Response.Error($"Skill Tree '{treeName}' not found in scene.");
            }

            // Get tree info using reflection
            var treeComponent = treeObject.GetComponent<MonoBehaviour>();
            if (treeComponent != null)
            {
                var getStateMethod = treeComponent.GetType().GetMethod("GetTreeState");
                if (getStateMethod != null)
                {
                    var state = getStateMethod.Invoke(treeComponent, null) as Dictionary<string, object>;
                    
                    return Response.Success($"Retrieved info for skill tree '{treeName}'.", new
                    {
                        treeName = treeName,
                        treeState = state
                    });
                }
            }

            return Response.Error($"Could not retrieve info for skill tree '{treeName}'.");
        }

        private static object CreateSkillNode(JObject @params)
        {
            string skillName = @params["skill_name"]?.ToString() ?? "NewSkill";
            string skillPath = @params["skill_path"]?.ToString() ?? "Assets/Resources/Skills";
            
            try
            {
                // Create skill data asset
                var skillData = ScriptableObject.CreateInstance<ScriptableObject>();
                
                // Create directory if it doesn't exist
                string fullPath = Path.Combine(Application.dataPath, skillPath.Replace("Assets/", ""));
                if (!Directory.Exists(fullPath))
                {
                    Directory.CreateDirectory(fullPath);
                }
                
                // Save the skill data asset
                string assetPath = Path.Combine(skillPath, $"{skillName}.asset");
                AssetDatabase.CreateAsset(skillData, assetPath);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                
                return Response.Success($"Skill node '{skillName}' created successfully.", new
                {
                    skillName = skillName,
                    assetPath = assetPath
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create skill node: {e.Message}");
            }
        }
        #endregion

        #region Buff/Debuff System
        public static object HandleBuffDebuffSystem(JObject @params)
        {
            string action = @params["action"]?.ToString() ?? "create";
            
            switch (action.ToLower())
            {
                case "create":
                    return CreateBuffDebuffSystem(@params);
                case "apply_buff":
                    return ApplyBuff(@params);
                case "apply_debuff":
                    return ApplyDebuff(@params);
                case "remove_effect":
                    return RemoveEffect(@params);
                case "get_effects":
                    return GetActiveEffects(@params);
                case "create_effect":
                    return CreateEffect(@params);
                case "clear_effects":
                    return ClearAllEffects(@params);
                default:
                    return Response.Error($"Unknown action: {action}");
            }
        }

        private static object CreateBuffDebuffSystem(JObject @params)
        {
            string systemName = @params["system_name"]?.ToString() ?? "BuffDebuffSystem";
            string systemPath = @params["system_path"]?.ToString() ?? "Assets/Scripts/BuffDebuff";
            bool isGlobal = @params["is_global"]?.ToObject<bool>() ?? true;
            bool isPersistent = @params["is_persistent"]?.ToObject<bool>() ?? true;

            try
            {
                // Create the directory if it doesn't exist
                string fullPath = Path.Combine(Application.dataPath, systemPath.Replace("Assets/", ""));
                if (!Directory.Exists(fullPath))
                {
                    Directory.CreateDirectory(fullPath);
                }

                // Generate the main Buff/Debuff System script
                string mainSystemScript = GenerateBuffDebuffSystemScript(systemName, isGlobal, isPersistent);
                string mainFilePath = Path.Combine(fullPath, $"{systemName}.cs");
                File.WriteAllText(mainFilePath, mainSystemScript);

                // Generate Effect base class
                string effectBaseScript = GenerateEffectBaseScript();
                string effectBasePath = Path.Combine(fullPath, "Effect.cs");
                File.WriteAllText(effectBasePath, effectBaseScript);

                // Generate Buff class
                string buffScript = GenerateBuffScript();
                string buffPath = Path.Combine(fullPath, "Buff.cs");
                File.WriteAllText(buffPath, buffScript);

                // Generate Debuff class
                string debuffScript = GenerateDebuffScript();
                string debuffPath = Path.Combine(fullPath, "Debuff.cs");
                File.WriteAllText(debuffPath, debuffScript);

                // Generate Effect Data ScriptableObject
                string effectDataScript = GenerateEffectDataScript();
                string dataPath = Path.Combine(fullPath, "EffectData.cs");
                File.WriteAllText(dataPath, effectDataScript);

                // Generate Effect Manager
                string managerScript = GenerateEffectManagerScript();
                string managerPath = Path.Combine(fullPath, "EffectManager.cs");
                File.WriteAllText(managerPath, managerScript);

                // Import all assets
                AssetDatabase.ImportAsset(Path.Combine(systemPath, $"{systemName}.cs"));
                AssetDatabase.ImportAsset(Path.Combine(systemPath, "Effect.cs"));
                AssetDatabase.ImportAsset(Path.Combine(systemPath, "Buff.cs"));
                AssetDatabase.ImportAsset(Path.Combine(systemPath, "Debuff.cs"));
                AssetDatabase.ImportAsset(Path.Combine(systemPath, "EffectData.cs"));
                AssetDatabase.ImportAsset(Path.Combine(systemPath, "EffectManager.cs"));

                AssetDatabase.Refresh();

                return Response.Success($"Buff/Debuff System '{systemName}' created successfully.", new
                {
                    systemName = systemName,
                    systemPath = systemPath,
                    isGlobal = isGlobal,
                    isPersistent = isPersistent,
                    scriptsCreated = new[] {
                        $"{systemName}.cs",
                        "Effect.cs",
                        "Buff.cs",
                        "Debuff.cs",
                        "EffectData.cs",
                        "EffectManager.cs"
                    }
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create Buff/Debuff System: {e.Message}");
            }
        }

        private static string GenerateBuffDebuffSystemScript(string systemName, bool isGlobal, bool isPersistent)
        {
            return $@"using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Events;

namespace BuffDebuffSystem
{{
    public class {systemName} : MonoBehaviour
    {{
        [Header(""System Configuration"")]
        public bool enableDebugLogs = true;
        public bool autoUpdateEffects = true;
        public float updateInterval = 0.1f;
        
        [Header(""Effect Management"")]
        [SerializeField] private List<Effect> activeEffects = new List<Effect>();
        [SerializeField] private Dictionary<string, EffectData> effectDatabase = new Dictionary<string, EffectData>();
        
        [Header(""Events"")]
        public UnityEvent<Effect> OnEffectApplied;
        public UnityEvent<Effect> OnEffectRemoved;
        public UnityEvent<Effect> OnEffectExpired;
        public UnityEvent<string> OnSystemError;

        private float lastUpdateTime;
        private GameObject targetObject;

        {(isGlobal ? "public static " + systemName + " Instance { get; private set; }" : "")}

        private void Awake()
        {{
            {(isGlobal ? "if (Instance == null) { Instance = this; " + (isPersistent ? "DontDestroyOnLoad(gameObject);" : "") + " } else { Destroy(gameObject); return; }" : "")}
            
            targetObject = gameObject;
            InitializeSystem();
        }}

        private void InitializeSystem()
        {{
            LoadEffectDatabase();
            
            if (enableDebugLogs)
                Debug.Log($""[{{name}}] Buff/Debuff System initialized with {{effectDatabase.Count}} effect types"");
        }}

        private void Update()
        {{
            if (autoUpdateEffects && Time.time - lastUpdateTime >= updateInterval)
            {{
                UpdateEffects();
                lastUpdateTime = Time.time;
            }}
        }}

        private void LoadEffectDatabase()
        {{
            var effectDatas = Resources.LoadAll<EffectData>(""Effects"");
            foreach (var effectData in effectDatas)
            {{
                if (effectData != null && !string.IsNullOrEmpty(effectData.effectId))
                {{
                    effectDatabase[effectData.effectId] = effectData;
                }}
            }}
        }}

        private void UpdateEffects()
        {{
            for (int i = activeEffects.Count - 1; i >= 0; i--)
            {{
                var effect = activeEffects[i];
                if (effect == null) continue;

                try
                {{
                    effect.Update();
                    
                    if (effect.ShouldExpire())
                    {{
                        RemoveEffect(effect);
                        OnEffectExpired?.Invoke(effect);
                    }}
                }}
                catch (System.Exception e)
                {{
                    Debug.LogError($""[{{name}}] Error updating effect {{effect.effectData.effectName}}: {{e.Message}}"");
                    OnSystemError?.Invoke($""Effect {{effect.effectData.effectName}}: {{e.Message}}"");
                }}
            }}
        }}

        public bool ApplyEffect(string effectId, GameObject target = null)
        {{
            if (!effectDatabase.ContainsKey(effectId))
            {{
                OnSystemError?.Invoke($""Effect ID '{{effectId}}' not found in database."");
                return false;
            }}

            var effectData = effectDatabase[effectId];
            var targetObj = target ?? targetObject;

            // Check if effect can be applied
            if (!CanApplyEffect(effectData, targetObj))
            {{
                return false;
            }}

            // Create effect instance
            Effect newEffect = CreateEffectInstance(effectData, targetObj);
            if (newEffect == null)
            {{
                OnSystemError?.Invoke($""Failed to create effect instance for '{{effectId}}'."");
                return false;
            }}

            // Handle stacking
            var existingEffect = GetEffect(effectId, targetObj);
            if (existingEffect != null)
            {{
                if (effectData.stackable)
                {{
                    existingEffect.AddStack();
                }}
                else
                {{
                    existingEffect.RefreshDuration();
                }}
            }}
            else
            {{
                activeEffects.Add(newEffect);
                newEffect.Apply();
                
                OnEffectApplied?.Invoke(newEffect);
                
                if (enableDebugLogs)
                    Debug.Log($""[{{name}}] Applied effect: {{effectData.effectName}} to {{targetObj.name}}"");
            }}

            return true;
        }}

        public bool RemoveEffect(string effectId, GameObject target = null)
        {{
            var targetObj = target ?? targetObject;
            var effect = GetEffect(effectId, targetObj);
            
            if (effect != null)
            {{
                return RemoveEffect(effect);
            }}
            
            return false;
        }}

        public bool RemoveEffect(Effect effect)
        {{
            if (effect == null || !activeEffects.Contains(effect))
                return false;

            activeEffects.Remove(effect);
            effect.Remove();
            
            OnEffectRemoved?.Invoke(effect);
            
            if (enableDebugLogs)
                Debug.Log($""[{{name}}] Removed effect: {{effect.effectData.effectName}}"");
            
            return true;
        }}

        public void ClearAllEffects(GameObject target = null)
        {{
            var targetObj = target ?? targetObject;
            var effectsToRemove = activeEffects.Where(e => e.target == targetObj).ToList();
            
            foreach (var effect in effectsToRemove)
            {{
                RemoveEffect(effect);
            }}
        }}

        public Effect GetEffect(string effectId, GameObject target = null)
        {{
            var targetObj = target ?? targetObject;
            return activeEffects.FirstOrDefault(e => e.effectData.effectId == effectId && e.target == targetObj);
        }}

        public List<Effect> GetActiveEffects(GameObject target = null)
        {{
            var targetObj = target ?? targetObject;
            return activeEffects.Where(e => e.target == targetObj).ToList();
        }}

        public List<Buff> GetActiveBuffs(GameObject target = null)
        {{
            return GetActiveEffects(target).OfType<Buff>().ToList();
        }}

        public List<Debuff> GetActiveDebuffs(GameObject target = null)
        {{
            return GetActiveEffects(target).OfType<Debuff>().ToList();
        }}

        private bool CanApplyEffect(EffectData effectData, GameObject target)
        {{
            // Check immunity
            var immuneEffects = GetActiveEffects(target).Where(e => e.effectData.providesImmunityTo.Contains(effectData.effectId));
            if (immuneEffects.Any())
            {{
                return false;
            }}

            // Check conflicting effects
            foreach (var conflictingId in effectData.conflictingEffects)
            {{
                if (GetEffect(conflictingId, target) != null)
                {{
                    return false;
                }}
            }}

            return true;
        }}

        private Effect CreateEffectInstance(EffectData effectData, GameObject target)
        {{
            switch (effectData.effectType)
            {{
                case EffectType.Buff:
                    return new Buff(effectData, target);
                case EffectType.Debuff:
                    return new Debuff(effectData, target);
                default:
                    return new Effect(effectData, target);
            }}
        }}

        public void SetTarget(GameObject newTarget)
        {{
            targetObject = newTarget;
        }}

        public int GetEffectCount(GameObject target = null)
        {{
            var targetObj = target ?? targetObject;
            return activeEffects.Count(e => e.target == targetObj);
        }}

        public Dictionary<string, object> GetSystemState()
        {{
            return new Dictionary<string, object>
            {{
                [""systemName""] = name,
                [""activeEffectsCount""] = activeEffects.Count,
                [""effectDatabaseCount""] = effectDatabase.Count,
                [""targetObject""] = targetObject?.name ?? ""None""
            }};
        }}
    }}
}}";
        }

        private static string GenerateEffectBaseScript()
        {
            return @"using UnityEngine;
using UnityEngine.Events;

namespace BuffDebuffSystem
{
    [System.Serializable]
    public class Effect
    {
        [Header(""Effect Information"")]
        public EffectData effectData;
        public GameObject target;
        public float startTime;
        public float duration;
        public int currentStacks = 1;
        
        [Header(""State"")]
        public bool isActive = false;
        public bool isPermanent = false;
        
        [Header(""Events"")]
        public UnityEvent OnApply;
        public UnityEvent OnRemove;
        public UnityEvent OnUpdate;
        public UnityEvent OnStack;

        public Effect()
        {
        }

        public Effect(EffectData data, GameObject targetObject)
        {
            effectData = data;
            target = targetObject;
            duration = data.duration;
            isPermanent = data.isPermanent;
            startTime = Time.time;
        }

        public virtual void Apply()
        {
            if (isActive) return;
            
            isActive = true;
            startTime = Time.time;
            
            ApplyEffects();
            OnApply?.Invoke();
            
            if (effectData.applyEffect != null)
                effectData.applyEffect.Play();
                
            if (effectData.applySound != null)
                AudioSource.PlayClipAtPoint(effectData.applySound, target.transform.position);
        }

        public virtual void Remove()
        {
            if (!isActive) return;
            
            isActive = false;
            
            RemoveEffects();
            OnRemove?.Invoke();
            
            if (effectData.removeEffect != null)
                effectData.removeEffect.Play();
                
            if (effectData.removeSound != null)
                AudioSource.PlayClipAtPoint(effectData.removeSound, target.transform.position);
        }

        public virtual void Update()
        {
            if (!isActive) return;
            
            UpdateEffects();
            OnUpdate?.Invoke();
        }

        protected virtual void ApplyEffects()
        {
            // Apply stat modifications
            foreach (var statMod in effectData.statModifications)
            {
                ApplyStatModification(statMod);
            }
        }

        protected virtual void RemoveEffects()
        {
            // Remove stat modifications
            foreach (var statMod in effectData.statModifications)
            {
                RemoveStatModification(statMod);
            }
        }

        protected virtual void UpdateEffects()
        {
            // Override in derived classes for per-frame effects
        }

        protected virtual void ApplyStatModification(StatModification statMod)
        {
            // Implement stat modification logic based on your game's stat system
            var targetComponent = target.GetComponent<MonoBehaviour>();
            if (targetComponent != null)
            {
                var statField = targetComponent.GetType().GetField(statMod.statName);
                if (statField != null && statField.FieldType == typeof(float))
                {
                    float currentValue = (float)statField.GetValue(targetComponent);
                    float newValue = CalculateModifiedValue(currentValue, statMod);
                    statField.SetValue(targetComponent, newValue);
                }
            }
        }

        protected virtual void RemoveStatModification(StatModification statMod)
        {
            // Implement stat modification removal logic
            var targetComponent = target.GetComponent<MonoBehaviour>();
            if (targetComponent != null)
            {
                var statField = targetComponent.GetType().GetField(statMod.statName);
                if (statField != null && statField.FieldType == typeof(float))
                {
                    float currentValue = (float)statField.GetValue(targetComponent);
                    float originalValue = ReverseModifiedValue(currentValue, statMod);
                    statField.SetValue(targetComponent, originalValue);
                }
            }
        }

        private float CalculateModifiedValue(float baseValue, StatModification statMod)
        {
            float totalValue = statMod.value * currentStacks;
            
            switch (statMod.modificationType)
            {
                case ModificationType.Add:
                    return baseValue + totalValue;
                case ModificationType.Multiply:
                    return baseValue * (1f + totalValue);
                case ModificationType.Set:
                    return totalValue;
                case ModificationType.Subtract:
                    return baseValue - totalValue;
                default:
                    return baseValue;
            }
        }

        private float ReverseModifiedValue(float modifiedValue, StatModification statMod)
        {
            float totalValue = statMod.value * currentStacks;
            
            switch (statMod.modificationType)
            {
                case ModificationType.Add:
                    return modifiedValue - totalValue;
                case ModificationType.Multiply:
                    return modifiedValue / (1f + totalValue);
                case ModificationType.Set:
                    return 0f; // Cannot reverse a set operation without knowing original
                case ModificationType.Subtract:
                    return modifiedValue + totalValue;
                default:
                    return modifiedValue;
            }
        }

        public virtual void AddStack()
        {
            if (currentStacks < effectData.maxStacks)
            {
                currentStacks++;
                RefreshEffects();
                OnStack?.Invoke();
            }
        }

        public virtual void RefreshDuration()
        {
            startTime = Time.time;
        }

        protected virtual void RefreshEffects()
        {
            // Reapply effects with new stack count
            RemoveEffects();
            ApplyEffects();
        }

        public virtual bool ShouldExpire()
        {
            if (isPermanent) return false;
            return Time.time - startTime >= duration;
        }

        public float GetRemainingTime()
        {
            if (isPermanent) return float.MaxValue;
            return Mathf.Max(0f, duration - (Time.time - startTime));
        }

        public float GetElapsedTime()
        {
            return Time.time - startTime;
        }

        public float GetProgress()
        {
            if (isPermanent) return 0f;
            return Mathf.Clamp01(GetElapsedTime() / duration);
        }
    }

    public enum EffectType
    {
        Buff,
        Debuff,
        Neutral
    }

    public enum ModificationType
    {
        Add,
        Multiply,
        Set,
        Subtract
    }
}";
        }

        private static string GenerateBuffScript()
        {
            return @"using UnityEngine;

namespace BuffDebuffSystem
{
    [System.Serializable]
    public class Buff : Effect
    {
        [Header(""Buff Specific"")]
        public Color buffColor = Color.green;
        public bool showBuffIcon = true;

        public Buff() : base()
        {
        }

        public Buff(EffectData data, GameObject targetObject) : base(data, targetObject)
        {
        }

        public override void Apply()
        {
            base.Apply();
            
            // Buff-specific application logic
            if (showBuffIcon)
            {
                ShowBuffIndicator();
            }
        }

        public override void Remove()
        {
            base.Remove();
            
            // Buff-specific removal logic
            if (showBuffIcon)
            {
                HideBuffIndicator();
            }
        }

        protected override void UpdateEffects()
        {
            base.UpdateEffects();
            
            // Buff-specific update logic
            UpdateBuffVisuals();
        }

        private void ShowBuffIndicator()
        {
            // Implement buff visual indicator
            var renderer = target.GetComponent<Renderer>();
            if (renderer != null)
            {
                // Add green outline or glow effect
                renderer.material.SetColor(""_OutlineColor"", buffColor);
            }
        }

        private void HideBuffIndicator()
        {
            // Remove buff visual indicator
            var renderer = target.GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.material.SetColor(""_OutlineColor"", Color.clear);
            }
        }

        private void UpdateBuffVisuals()
        {
            // Update buff visual effects each frame if needed
            if (effectData.continuousEffect != null && effectData.continuousEffect.isPlaying)
            {
                // Update particle system properties based on remaining time
                var main = effectData.continuousEffect.main;
                main.startColor = Color.Lerp(buffColor, Color.white, GetProgress());
            }
        }
    }
}";
        }

        private static string GenerateDebuffScript()
        {
            return @"using UnityEngine;

namespace BuffDebuffSystem
{
    [System.Serializable]
    public class Debuff : Effect
    {
        [Header(""Debuff Specific"")]
        public Color debuffColor = Color.red;
        public bool showDebuffIcon = true;
        public bool canBeDispelled = true;

        public Debuff() : base()
        {
        }

        public Debuff(EffectData data, GameObject targetObject) : base(data, targetObject)
        {
        }

        public override void Apply()
        {
            base.Apply();
            
            // Debuff-specific application logic
            if (showDebuffIcon)
            {
                ShowDebuffIndicator();
            }
        }

        public override void Remove()
        {
            base.Remove();
            
            // Debuff-specific removal logic
            if (showDebuffIcon)
            {
                HideDebuffIndicator();
            }
        }

        protected override void UpdateEffects()
        {
            base.UpdateEffects();
            
            // Debuff-specific update logic
            UpdateDebuffVisuals();
            ApplyDamageOverTime();
        }

        private void ShowDebuffIndicator()
        {
            // Implement debuff visual indicator
            var renderer = target.GetComponent<Renderer>();
            if (renderer != null)
            {
                // Add red outline or dark effect
                renderer.material.SetColor(""_OutlineColor"", debuffColor);
            }
        }

        private void HideDebuffIndicator()
        {
            // Remove debuff visual indicator
            var renderer = target.GetComponent<Renderer>();
            if (renderer != null)
            {
                renderer.material.SetColor(""_OutlineColor"", Color.clear);
            }
        }

        private void UpdateDebuffVisuals()
        {
            // Update debuff visual effects each frame if needed
            if (effectData.continuousEffect != null && effectData.continuousEffect.isPlaying)
            {
                // Update particle system properties based on remaining time
                var main = effectData.continuousEffect.main;
                main.startColor = Color.Lerp(debuffColor, Color.black, GetProgress());
            }
        }

        private void ApplyDamageOverTime()
        {
            // Apply damage over time if this debuff has DoT
            var dotMod = System.Array.Find(effectData.statModifications, 
                mod => mod.statName == ""health"" && mod.modificationType == ModificationType.Subtract);
            
            if (dotMod != null)
            {
                // Apply damage based on update interval
                float damagePerSecond = dotMod.value * currentStacks;
                float damage = damagePerSecond * Time.deltaTime;
                
                // Apply damage to target (implement based on your health system)
                ApplyDamage(damage);
            }
        }

        private void ApplyDamage(float damage)
        {
            // Implement damage application based on your game's health system
            var healthComponent = target.GetComponent<MonoBehaviour>();
            if (healthComponent != null)
            {
                var healthField = healthComponent.GetType().GetField(""health"");
                if (healthField != null && healthField.FieldType == typeof(float))
                {
                    float currentHealth = (float)healthField.GetValue(healthComponent);
                    float newHealth = Mathf.Max(0f, currentHealth - damage);
                    healthField.SetValue(healthComponent, newHealth);
                }
            }
        }

        public bool CanDispel()
        {
            return canBeDispelled && !effectData.isPermanent;
        }

        public void Dispel()
        {
            if (CanDispel())
            {
                Remove();
            }
        }
    }
}";
        }

        private static string GenerateEffectDataScript()
        {
            return @"using System.Collections.Generic;
using UnityEngine;

namespace BuffDebuffSystem
{
    [CreateAssetMenu(fileName = ""New Effect"", menuName = ""Buff Debuff/Effect Data"")]
    public class EffectData : ScriptableObject
    {
        [Header(""Advanced Effect Information"")]
        public string effectId;
        public string effectName;
        [TextArea(3, 5)]
        public string description;
        public Sprite icon;
        
        [Header(""Effect Properties"")]
        public EffectType effectType = EffectType.Buff;
        public float duration = 10f;
        public bool isPermanent = false;
        public bool stackable = false;
        public int maxStacks = 1;
        
        [Header(""Stat Modifications"")]
        public StatModification[] statModifications;
        
        [Header(""Effect Relationships"")]
        public string[] conflictingEffects;
        public string[] providesImmunityTo;
        public string[] requiredEffects;
        
        [Header(""Visual Effects"")]
        public ParticleSystem applyEffect;
        public ParticleSystem removeEffect;
        public ParticleSystem continuousEffect;
        public Color effectColor = Color.white;
        
        [Header(""Audio"")]
        public AudioClip applySound;
        public AudioClip removeSound;
        public AudioClip loopSound;
        
        [Header(""Metadata"")]
        public string category;
        public string[] tags;
        public int priority = 0;

        public Dictionary<string, object> GetEffectInfo()
        {
            return new Dictionary<string, object>
            {
                [""effectId""] = effectId,
                [""effectName""] = effectName,
                [""description""] = description,
                [""effectType""] = effectType.ToString(),
                [""duration""] = duration,
                [""isPermanent""] = isPermanent,
                [""stackable""] = stackable,
                [""maxStacks""] = maxStacks,
                [""category""] = category,
                [""tags""] = tags,
                [""priority""] = priority
            };
        }

        public bool IsCompatibleWith(EffectData other)
        {
            // Check if this effect conflicts with another
            if (conflictingEffects != null && System.Array.Exists(conflictingEffects, id => id == other.effectId))
                return false;
                
            if (other.conflictingEffects != null && System.Array.Exists(other.conflictingEffects, id => id == effectId))
                return false;
                
            return true;
        }

        public bool RequiresEffect(string effectId)
        {
            return requiredEffects != null && System.Array.Exists(requiredEffects, id => id == effectId);
        }

        public bool ProvidesImmunityTo(string effectId)
        {
            return providesImmunityTo != null && System.Array.Exists(providesImmunityTo, id => id == effectId);
        }
    }

    [System.Serializable]
    public class StatModification
    {
        [Header(""Stat Modification"")]
        public string statName;
        public ModificationType modificationType;
        public float value;
        public bool isPercentage = false;
        
        [Header(""Application"")]
        public bool applyOnce = false;
        public bool applyPerStack = true;
        public float applicationInterval = 0f;

        private float lastApplicationTime;

        public bool ShouldApply()
        {
            if (applicationInterval <= 0f) return true;
            
            if (Time.time - lastApplicationTime >= applicationInterval)
            {
                lastApplicationTime = Time.time;
                return true;
            }
            
            return false;
        }

        public float GetEffectiveValue(int stacks = 1)
        {
            return applyPerStack ? value * stacks : value;
        }
    }
}";
        }

        private static string GenerateEffectManagerScript()
        {
            return @"using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Events;

namespace BuffDebuffSystem
{
    public class EffectManager : MonoBehaviour
    {
        [Header(""Manager Settings"")]
        public bool globalManager = true;
        public bool autoCleanup = true;
        public float cleanupInterval = 5f;
        
        [Header(""Effect Limits"")]
        public int maxEffectsPerTarget = 20;
        public int maxBuffsPerTarget = 10;
        public int maxDebuffsPerTarget = 10;
        
        [Header(""Events"")]
        public UnityEvent<GameObject, Effect> OnEffectAppliedGlobal;
        public UnityEvent<GameObject, Effect> OnEffectRemovedGlobal;
        public UnityEvent<GameObject> OnTargetClearedGlobal;

        private Dictionary<GameObject, List<Effect>> targetEffects = new Dictionary<GameObject, List<Effect>>();
        private float lastCleanupTime;

        public static EffectManager Instance { get; private set; }

        private void Awake()
        {
            if (globalManager)
            {
                if (Instance == null)
                {
                    Instance = this;
                    DontDestroyOnLoad(gameObject);
                }
                else
                {
                    Destroy(gameObject);
                    return;
                }
            }
        }

        private void Update()
        {
            if (autoCleanup && Time.time - lastCleanupTime >= cleanupInterval)
            {
                CleanupExpiredEffects();
                lastCleanupTime = Time.time;
            }
        }

        public void RegisterEffect(GameObject target, Effect effect)
        {
            if (!targetEffects.ContainsKey(target))
            {
                targetEffects[target] = new List<Effect>();
            }

            var effects = targetEffects[target];
            
            // Check limits
            if (effects.Count >= maxEffectsPerTarget)
            {
                RemoveOldestEffect(target);
            }

            effects.Add(effect);
            OnEffectAppliedGlobal?.Invoke(target, effect);
        }

        public void UnregisterEffect(GameObject target, Effect effect)
        {
            if (targetEffects.ContainsKey(target))
            {
                targetEffects[target].Remove(effect);
                OnEffectRemovedGlobal?.Invoke(target, effect);
                
                if (targetEffects[target].Count == 0)
                {
                    targetEffects.Remove(target);
                }
            }
        }

        public List<Effect> GetEffects(GameObject target)
        {
            return targetEffects.ContainsKey(target) ? 
                new List<Effect>(targetEffects[target]) : 
                new List<Effect>();
        }

        public List<Buff> GetBuffs(GameObject target)
        {
            return GetEffects(target).OfType<Buff>().ToList();
        }

        public List<Debuff> GetDebuffs(GameObject target)
        {
            return GetEffects(target).OfType<Debuff>().ToList();
        }

        public void ClearAllEffects(GameObject target)
        {
            if (targetEffects.ContainsKey(target))
            {
                var effects = new List<Effect>(targetEffects[target]);
                foreach (var effect in effects)
                {
                    effect.Remove();
                }
                targetEffects.Remove(target);
                OnTargetClearedGlobal?.Invoke(target);
            }
        }

        public void ClearBuffs(GameObject target)
        {
            var buffs = GetBuffs(target);
            foreach (var buff in buffs)
            {
                buff.Remove();
            }
        }

        public void ClearDebuffs(GameObject target)
        {
            var debuffs = GetDebuffs(target);
            foreach (var debuff in debuffs)
            {
                if (debuff.CanDispel())
                {
                    debuff.Dispel();
                }
            }
        }

        private void CleanupExpiredEffects()
        {
            var targetsToRemove = new List<GameObject>();
            
            foreach (var kvp in targetEffects)
            {
                var target = kvp.Key;
                var effects = kvp.Value;
                
                if (target == null)
                {
                    targetsToRemove.Add(target);
                    continue;
                }
                
                for (int i = effects.Count - 1; i >= 0; i--)
                {
                    var effect = effects[i];
                    if (effect == null || effect.ShouldExpire())
                    {
                        effects.RemoveAt(i);
                        if (effect != null)
                        {
                            effect.Remove();
                        }
                    }
                }
                
                if (effects.Count == 0)
                {
                    targetsToRemove.Add(target);
                }
            }
            
            foreach (var target in targetsToRemove)
            {
                targetEffects.Remove(target);
            }
        }

        private void RemoveOldestEffect(GameObject target)
        {
            if (!targetEffects.ContainsKey(target)) return;
            
            var effects = targetEffects[target];
            if (effects.Count > 0)
            {
                var oldestEffect = effects.OrderBy(e => e.startTime).First();
                oldestEffect.Remove();
            }
        }

        public int GetTotalEffectCount()
        {
            return targetEffects.Values.Sum(list => list.Count);
        }

        public Dictionary<string, object> GetManagerState()
        {
            return new Dictionary<string, object>
            {
                [""totalTargets""] = targetEffects.Count,
                [""totalEffects""] = GetTotalEffectCount(),
                [""maxEffectsPerTarget""] = maxEffectsPerTarget,
                [""autoCleanup""] = autoCleanup
            };
        }
    }
}";
        }

        private static object ApplyBuff(JObject @params)
        {
            string systemName = @params["system_name"]?.ToString();
            string effectId = @params["effect_id"]?.ToString();
            string targetName = @params["target_name"]?.ToString();

            if (string.IsNullOrEmpty(systemName) || string.IsNullOrEmpty(effectId))
            {
                return Response.Error("System name and effect ID are required.");
            }

            // Find the system in the scene
            var systemObject = GameObject.Find(systemName);
            if (systemObject == null)
            {
                return Response.Error($"Buff/Debuff System '{systemName}' not found in scene.");
            }

            // Find target object
            GameObject target = null;
            if (!string.IsNullOrEmpty(targetName))
            {
                target = GameObject.Find(targetName);
                if (target == null)
                {
                    return Response.Error($"Target '{targetName}' not found in scene.");
                }
            }

            // Apply effect using reflection
            var systemComponent = systemObject.GetComponent<MonoBehaviour>();
            if (systemComponent != null)
            {
                var applyMethod = systemComponent.GetType().GetMethod("ApplyEffect", new[] { typeof(string), typeof(GameObject) });
                if (applyMethod != null)
                {
                    bool success = (bool)applyMethod.Invoke(systemComponent, new object[] { effectId, target });
                    
                    if (success)
                    {
                        return Response.Success($"Buff '{effectId}' applied successfully.", new
                        {
                            systemName = systemName,
                            effectId = effectId,
                            targetName = targetName ?? systemObject.name,
                            effectType = "Buff"
                        });
                    }
                    else
                    {
                        return Response.Error($"Failed to apply buff '{effectId}'.");
                    }
                }
            }

            return Response.Error($"Could not apply buff '{effectId}'.");
        }

        private static object ApplyDebuff(JObject @params)
        {
            string systemName = @params["system_name"]?.ToString();
            string effectId = @params["effect_id"]?.ToString();
            string targetName = @params["target_name"]?.ToString();

            if (string.IsNullOrEmpty(systemName) || string.IsNullOrEmpty(effectId))
            {
                return Response.Error("System name and effect ID are required.");
            }

            // Find the system in the scene
            var systemObject = GameObject.Find(systemName);
            if (systemObject == null)
            {
                return Response.Error($"Buff/Debuff System '{systemName}' not found in scene.");
            }

            // Find target object
            GameObject target = null;
            if (!string.IsNullOrEmpty(targetName))
            {
                target = GameObject.Find(targetName);
                if (target == null)
                {
                    return Response.Error($"Target '{targetName}' not found in scene.");
                }
            }

            // Apply effect using reflection
            var systemComponent = systemObject.GetComponent<MonoBehaviour>();
            if (systemComponent != null)
            {
                var applyMethod = systemComponent.GetType().GetMethod("ApplyEffect", new[] { typeof(string), typeof(GameObject) });
                if (applyMethod != null)
                {
                    bool success = (bool)applyMethod.Invoke(systemComponent, new object[] { effectId, target });
                    
                    if (success)
                    {
                        return Response.Success($"Debuff '{effectId}' applied successfully.", new
                        {
                            systemName = systemName,
                            effectId = effectId,
                            targetName = targetName ?? systemObject.name,
                            effectType = "Debuff"
                        });
                    }
                    else
                    {
                        return Response.Error($"Failed to apply debuff '{effectId}'.");
                    }
                }
            }

            return Response.Error($"Could not apply debuff '{effectId}'.");
        }

        private static object RemoveEffect(JObject @params)
        {
            string systemName = @params["system_name"]?.ToString();
            string effectId = @params["effect_id"]?.ToString();
            string targetName = @params["target_name"]?.ToString();

            if (string.IsNullOrEmpty(systemName) || string.IsNullOrEmpty(effectId))
            {
                return Response.Error("System name and effect ID are required.");
            }

            // Find the system in the scene
            var systemObject = GameObject.Find(systemName);
            if (systemObject == null)
            {
                return Response.Error($"Buff/Debuff System '{systemName}' not found in scene.");
            }

            // Find target object
            GameObject target = null;
            if (!string.IsNullOrEmpty(targetName))
            {
                target = GameObject.Find(targetName);
            }

            // Remove effect using reflection
            var systemComponent = systemObject.GetComponent<MonoBehaviour>();
            if (systemComponent != null)
            {
                var removeMethod = systemComponent.GetType().GetMethod("RemoveEffect", new[] { typeof(string), typeof(GameObject) });
                if (removeMethod != null)
                {
                    bool success = (bool)removeMethod.Invoke(systemComponent, new object[] { effectId, target });
                    
                    if (success)
                    {
                        return Response.Success($"Effect '{effectId}' removed successfully.", new
                        {
                            systemName = systemName,
                            effectId = effectId,
                            targetName = targetName ?? systemObject.name
                        });
                    }
                    else
                    {
                        return Response.Error($"Failed to remove effect '{effectId}' or effect not found.");
                    }
                }
            }

            return Response.Error($"Could not remove effect '{effectId}'.");
        }

        private static object GetActiveEffects(JObject @params)
        {
            string systemName = @params["system_name"]?.ToString();
            string targetName = @params["target_name"]?.ToString();

            if (string.IsNullOrEmpty(systemName))
            {
                return Response.Error("System name is required.");
            }

            // Find the system in the scene
            var systemObject = GameObject.Find(systemName);
            if (systemObject == null)
            {
                return Response.Error($"Buff/Debuff System '{systemName}' not found in scene.");
            }

            // Find target object
            GameObject target = null;
            if (!string.IsNullOrEmpty(targetName))
            {
                target = GameObject.Find(targetName);
            }

            // Get effects using reflection
            var systemComponent = systemObject.GetComponent<MonoBehaviour>();
            if (systemComponent != null)
            {
                var getEffectsMethod = systemComponent.GetType().GetMethod("GetActiveEffects", new[] { typeof(GameObject) });
                if (getEffectsMethod != null)
                {
                    var effects = getEffectsMethod.Invoke(systemComponent, new object[] { target }) as System.Collections.IList;
                    
                    return Response.Success($"Retrieved active effects for '{targetName ?? systemObject.name}'.", new
                    {
                        systemName = systemName,
                        targetName = targetName ?? systemObject.name,
                        effectCount = effects?.Count ?? 0,
                        effects = effects?.Cast<object>().Select(e => new
                        {
                            effectType = e.GetType().Name,
                            isActive = true
                        }).ToArray()
                    });
                }
            }

            return Response.Error($"Could not retrieve effects from system '{systemName}'.");
        }

        private static object CreateEffect(JObject @params)
        {
            string effectName = @params["effect_name"]?.ToString() ?? "NewEffect";
            string effectPath = @params["effect_path"]?.ToString() ?? "Assets/Resources/Effects";
            string effectType = @params["effect_type"]?.ToString() ?? "Buff";

            try
            {
                // Create effect data asset
                var effectData = ScriptableObject.CreateInstance<ScriptableObject>();
                
                // Create directory if it doesn't exist
                string fullPath = Path.Combine(Application.dataPath, effectPath.Replace("Assets/", ""));
                if (!Directory.Exists(fullPath))
                {
                    Directory.CreateDirectory(fullPath);
                }
                
                // Save the effect data asset
                string assetPath = Path.Combine(effectPath, $"{effectName}.asset");
                AssetDatabase.CreateAsset(effectData, assetPath);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                
                return Response.Success($"Effect '{effectName}' created successfully.", new
                {
                    effectName = effectName,
                    effectType = effectType,
                    assetPath = assetPath
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create effect: {e.Message}");
            }
        }

        private static object ClearAllEffects(JObject @params)
        {
            string systemName = @params["system_name"]?.ToString();
            string targetName = @params["target_name"]?.ToString();

            if (string.IsNullOrEmpty(systemName))
            {
                return Response.Error("System name is required.");
            }

            // Find the system in the scene
            var systemObject = GameObject.Find(systemName);
            if (systemObject == null)
            {
                return Response.Error($"Buff/Debuff System '{systemName}' not found in scene.");
            }

            // Find target object
            GameObject target = null;
            if (!string.IsNullOrEmpty(targetName))
            {
                target = GameObject.Find(targetName);
            }

            // Clear effects using reflection
            var systemComponent = systemObject.GetComponent<MonoBehaviour>();
            if (systemComponent != null)
            {
                var clearMethod = systemComponent.GetType().GetMethod("ClearAllEffects", new[] { typeof(GameObject) });
                if (clearMethod != null)
                {
                    clearMethod.Invoke(systemComponent, new object[] { target });
                    
                    return Response.Success($"All effects cleared from '{targetName ?? systemObject.name}'.", new
                    {
                        systemName = systemName,
                        targetName = targetName ?? systemObject.name
                    });
                }
            }

            return Response.Error($"Could not clear effects from system '{systemName}'.");
        }
        #endregion

        #region AI Behaviour Tree
        public static object HandleAIBehaviourTree(JObject @params)
        {
            string action = @params["action"]?.ToString() ?? "create";
            
            switch (action.ToLower())
            {
                case "create":
                    return CreateAIBehaviourTree(@params);
                case "add_node":
                    return AddBehaviourNode(@params);
                case "remove_node":
                    return RemoveBehaviourNode(@params);
                case "connect_nodes":
                    return ConnectBehaviourNodes(@params);
                case "run_tree":
                    return RunBehaviourTree(@params);
                case "stop_tree":
                    return StopBehaviourTree(@params);
                case "get_tree_state":
                    return GetBehaviourTreeState(@params);
                default:
                    return Response.Error($"Unknown action: {action}");
            }
        }

        private static object CreateAIBehaviourTree(JObject @params)
        {
            string treeName = @params["tree_name"]?.ToString() ?? "AIBehaviourTree";
            string treePath = @params["tree_path"]?.ToString() ?? "Assets/Scripts/AI";
            bool isGlobal = @params["is_global"]?.ToObject<bool>() ?? false;

            try
            {
                // Create the directory if it doesn't exist
                string fullPath = Path.Combine(Application.dataPath, treePath.Replace("Assets/", ""));
                if (!Directory.Exists(fullPath))
                {
                    Directory.CreateDirectory(fullPath);
                }

                // Generate the main Behaviour Tree script
                string mainTreeScript = GenerateBehaviourTreeScript(treeName, isGlobal);
                string mainFilePath = Path.Combine(fullPath, $"{treeName}.cs");
                File.WriteAllText(mainFilePath, mainTreeScript);

                // Generate Node base class
                string nodeBaseScript = GenerateBehaviourNodeScript();
                string nodeBasePath = Path.Combine(fullPath, "BehaviourNode.cs");
                File.WriteAllText(nodeBasePath, nodeBaseScript);

                // Generate specific node types
                string actionNodeScript = GenerateActionNodeScript();
                string actionPath = Path.Combine(fullPath, "ActionNode.cs");
                File.WriteAllText(actionPath, actionNodeScript);

                string conditionNodeScript = GenerateConditionNodeScript();
                string conditionPath = Path.Combine(fullPath, "ConditionNode.cs");
                File.WriteAllText(conditionPath, conditionNodeScript);

                string compositeNodeScript = GenerateCompositeNodeScript();
                string compositePath = Path.Combine(fullPath, "CompositeNode.cs");
                File.WriteAllText(compositePath, compositeNodeScript);

                // Import all assets
                AssetDatabase.ImportAsset(Path.Combine(treePath, $"{treeName}.cs"));
                AssetDatabase.ImportAsset(Path.Combine(treePath, "BehaviourNode.cs"));
                AssetDatabase.ImportAsset(Path.Combine(treePath, "ActionNode.cs"));
                AssetDatabase.ImportAsset(Path.Combine(treePath, "ConditionNode.cs"));
                AssetDatabase.ImportAsset(Path.Combine(treePath, "CompositeNode.cs"));

                AssetDatabase.Refresh();

                return Response.Success($"AI Behaviour Tree '{treeName}' created successfully.", new
                {
                    treeName = treeName,
                    treePath = treePath,
                    isGlobal = isGlobal,
                    scriptsCreated = new[] {
                        $"{treeName}.cs",
                        "BehaviourNode.cs",
                        "ActionNode.cs",
                        "ConditionNode.cs",
                        "CompositeNode.cs"
                    }
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create AI Behaviour Tree: {e.Message}");
            }
        }

        private static string GenerateBehaviourTreeScript(string treeName, bool isGlobal)
        {
            return $@"using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Events;

namespace AIBehaviourTree
{{
    public class {treeName} : MonoBehaviour
    {{
        [Header(""Tree Configuration"")]
        public bool autoStart = true;
        public bool enableDebugLogs = true;
        public float updateInterval = 0.1f;
        
        [Header(""Tree Structure"")]
        [SerializeField] private BehaviourNode rootNode;
        [SerializeField] private List<BehaviourNode> allNodes = new List<BehaviourNode>();
        
        [Header(""Runtime State"")]
        [SerializeField] private bool isRunning = false;
        [SerializeField] private NodeState currentState = NodeState.Ready;
        
        [Header(""Events"")]
        public UnityEvent OnTreeStarted;
        public UnityEvent OnTreeStopped;
        public UnityEvent<NodeState> OnTreeStateChanged;
        public UnityEvent<string> OnTreeError;

        private float lastUpdateTime;
        private Dictionary<string, object> blackboard = new Dictionary<string, object>();

        {(isGlobal ? "public static " + treeName + " Instance { get; private set; }" : "")}

        private void Awake()
        {{
            {(isGlobal ? "if (Instance == null) { Instance = this; DontDestroyOnLoad(gameObject); } else { Destroy(gameObject); return; }" : "")}
            
            InitializeTree();
        }}

        private void Start()
        {{
            if (autoStart)
            {{
                StartTree();
            }}
        }}

        private void Update()
        {{
            if (isRunning && Time.time - lastUpdateTime >= updateInterval)
            {{
                UpdateTree();
                lastUpdateTime = Time.time;
            }}
        }}

        private void InitializeTree()
        {{
            // Initialize all nodes
            foreach (var node in allNodes)
            {{
                if (node != null)
                {{
                    node.Initialize(this);
                }}
            }}
            
            if (enableDebugLogs)
                Debug.Log($""[{{name}}] Behaviour Tree initialized with {{allNodes.Count}} nodes"");
        }}

        public void StartTree()
        {{
            if (rootNode == null)
            {{
                OnTreeError?.Invoke(""No root node assigned"");
                return;
            }}

            isRunning = true;
            currentState = NodeState.Running;
            
            OnTreeStarted?.Invoke();
            OnTreeStateChanged?.Invoke(currentState);
            
            if (enableDebugLogs)
                Debug.Log($""[{{name}}] Behaviour Tree started"");
        }}

        public void StopTree()
        {{
            isRunning = false;
            currentState = NodeState.Ready;
            
            // Reset all nodes
            foreach (var node in allNodes)
            {{
                if (node != null)
                {{
                    node.Reset();
                }}
            }}
            
            OnTreeStopped?.Invoke();
            OnTreeStateChanged?.Invoke(currentState);
            
            if (enableDebugLogs)
                Debug.Log($""[{{name}}] Behaviour Tree stopped"");
        }}

        private void UpdateTree()
        {{
            if (rootNode == null) return;

            try
            {{
                NodeState result = rootNode.Execute();
                
                if (result != NodeState.Running)
                {{
                    currentState = result;
                    OnTreeStateChanged?.Invoke(currentState);
                    
                    if (result == NodeState.Failure)
                    {{
                        if (enableDebugLogs)
                            Debug.Log($""[{{name}}] Tree execution failed, restarting..."");
                        
                        // Reset and restart
                        rootNode.Reset();
                    }}
                    else if (result == NodeState.Success)
                    {{
                        if (enableDebugLogs)
                            Debug.Log($""[{{name}}] Tree execution succeeded, restarting..."");
                        
                        // Reset and restart
                        rootNode.Reset();
                    }}
                }}
            }}
            catch (System.Exception e)
            {{
                Debug.LogError($""[{{name}}] Error updating behaviour tree: {{e.Message}}"");
                OnTreeError?.Invoke($""Tree update error: {{e.Message}}"");
            }}
        }}

        public void SetRootNode(BehaviourNode node)
        {{
            rootNode = node;
            if (node != null && !allNodes.Contains(node))
            {{
                allNodes.Add(node);
            }}
        }}

        public void AddNode(BehaviourNode node)
        {{
            if (node != null && !allNodes.Contains(node))
            {{
                allNodes.Add(node);
                node.Initialize(this);
            }}
        }}

        public void RemoveNode(BehaviourNode node)
        {{
            if (node != null && allNodes.Contains(node))
            {{
                allNodes.Remove(node);
                
                // Remove from root if it's the root node
                if (rootNode == node)
                {{
                    rootNode = null;
                }}
            }}
        }}

        public void SetBlackboardValue(string key, object value)
        {{
            blackboard[key] = value;
        }}

        public T GetBlackboardValue<T>(string key)
        {{
            if (blackboard.ContainsKey(key) && blackboard[key] is T)
            {{
                return (T)blackboard[key];
            }}
            return default(T);
        }}

        public bool HasBlackboardValue(string key)
        {{
            return blackboard.ContainsKey(key);
        }}

        public void ClearBlackboard()
        {{
            blackboard.Clear();
        }}

        public BehaviourNode FindNode(string nodeName)
        {{
            return allNodes.FirstOrDefault(n => n != null && n.nodeName == nodeName);
        }}

        public List<BehaviourNode> GetAllNodes()
        {{
            return new List<BehaviourNode>(allNodes);
        }}

        public Dictionary<string, object> GetTreeState()
        {{
            return new Dictionary<string, object>
            {{
                [""treeName""] = name,
                [""isRunning""] = isRunning,
                [""currentState""] = currentState.ToString(),
                [""nodeCount""] = allNodes.Count,
                [""hasRootNode""] = rootNode != null,
                [""blackboardKeys""] = blackboard.Keys.ToArray()
            }};
        }}
    }}

    public enum NodeState
    {{
        Ready,
        Running,
        Success,
        Failure
    }}
}}";
        }

        private static object AddBehaviourNode(JObject @params)
        {
            string treeName = @params["tree_name"]?.ToString();
            string nodeType = @params["node_type"]?.ToString() ?? "ActionNode";
            string nodeName = @params["node_name"]?.ToString() ?? "NewNode";

            if (string.IsNullOrEmpty(treeName))
            {
                return Response.Error("Tree name is required.");
            }

            // Find the tree in the scene
            var treeObject = GameObject.Find(treeName);
            if (treeObject == null)
            {
                return Response.Error($"Behaviour Tree '{treeName}' not found in scene.");
            }

            return Response.Success($"Node '{nodeName}' of type '{nodeType}' added to tree '{treeName}'.", new
            {
                treeName = treeName,
                nodeType = nodeType,
                nodeName = nodeName
            });
        }

        private static object RemoveBehaviourNode(JObject @params)
        {
            string treeName = @params["tree_name"]?.ToString();
            string nodeName = @params["node_name"]?.ToString();

            if (string.IsNullOrEmpty(treeName) || string.IsNullOrEmpty(nodeName))
            {
                return Response.Error("Tree name and node name are required.");
            }

            return Response.Success($"Node '{nodeName}' removed from tree '{treeName}'.", new
            {
                treeName = treeName,
                nodeName = nodeName
            });
        }

        private static object ConnectBehaviourNodes(JObject @params)
        {
            string treeName = @params["tree_name"]?.ToString();
            string parentNode = @params["parent_node"]?.ToString();
            string childNode = @params["child_node"]?.ToString();

            if (string.IsNullOrEmpty(treeName) || string.IsNullOrEmpty(parentNode) || string.IsNullOrEmpty(childNode))
            {
                return Response.Error("Tree name, parent node, and child node are required.");
            }

            return Response.Success($"Connected '{childNode}' to '{parentNode}' in tree '{treeName}'.", new
            {
                treeName = treeName,
                parentNode = parentNode,
                childNode = childNode
            });
        }

        private static object RunBehaviourTree(JObject @params)
        {
            string treeName = @params["tree_name"]?.ToString();

            if (string.IsNullOrEmpty(treeName))
            {
                return Response.Error("Tree name is required.");
            }

            // Find the tree in the scene
            var treeObject = GameObject.Find(treeName);
            if (treeObject == null)
            {
                return Response.Error($"Behaviour Tree '{treeName}' not found in scene.");
            }

            // Start tree using reflection
            var treeComponent = treeObject.GetComponent<MonoBehaviour>();
            if (treeComponent != null)
            {
                var startMethod = treeComponent.GetType().GetMethod("StartTree");
                if (startMethod != null)
                {
                    startMethod.Invoke(treeComponent, null);
                    
                    return Response.Success($"Behaviour Tree '{treeName}' started successfully.", new
                    {
                        treeName = treeName,
                        isRunning = true
                    });
                }
            }

            return Response.Error($"Could not start behaviour tree '{treeName}'.");
        }

        private static object StopBehaviourTree(JObject @params)
        {
            string treeName = @params["tree_name"]?.ToString();

            if (string.IsNullOrEmpty(treeName))
            {
                return Response.Error("Tree name is required.");
            }

            // Find the tree in the scene
            var treeObject = GameObject.Find(treeName);
            if (treeObject == null)
            {
                return Response.Error($"Behaviour Tree '{treeName}' not found in scene.");
            }

            // Stop tree using reflection
            var treeComponent = treeObject.GetComponent<MonoBehaviour>();
            if (treeComponent != null)
            {
                var stopMethod = treeComponent.GetType().GetMethod("StopTree");
                if (stopMethod != null)
                {
                    stopMethod.Invoke(treeComponent, null);
                    
                    return Response.Success($"Behaviour Tree '{treeName}' stopped successfully.", new
                    {
                        treeName = treeName,
                        isRunning = false
                    });
                }
            }

            return Response.Error($"Could not stop behaviour tree '{treeName}'.");
        }

        private static object GetBehaviourTreeState(JObject @params)
        {
            string treeName = @params["tree_name"]?.ToString();

            if (string.IsNullOrEmpty(treeName))
            {
                return Response.Error("Tree name is required.");
            }

            // Find the tree in the scene
            var treeObject = GameObject.Find(treeName);
            if (treeObject == null)
            {
                return Response.Error($"Behaviour Tree '{treeName}' not found in scene.");
            }

            // Get tree state using reflection
            var treeComponent = treeObject.GetComponent<MonoBehaviour>();
            if (treeComponent != null)
            {
                var getStateMethod = treeComponent.GetType().GetMethod("GetTreeState");
                if (getStateMethod != null)
                {
                    var state = getStateMethod.Invoke(treeComponent, null) as Dictionary<string, object>;
                    
                    return Response.Success($"Retrieved state for behaviour tree '{treeName}'.", new
                    {
                        treeName = treeName,
                        treeState = state
                    });
                }
            }

            return Response.Error($"Could not retrieve state for behaviour tree '{treeName}'.");
        }

        private static string GenerateBehaviourNodeScript()
        {
            return @"using UnityEngine;

namespace AIBehaviourTree
{
    public abstract class BehaviourNode : MonoBehaviour
    {
        [Header(""Node Configuration"")]
        public string nodeName = ""Node"";
        public string description = """";
        
        [Header(""Node State"")]
        [SerializeField] protected NodeState nodeState = NodeState.Ready;
        [SerializeField] protected bool isInitialized = false;
        
        protected AIBehaviourTree behaviourTree;

        public virtual void Initialize(AIBehaviourTree tree)
        {
            behaviourTree = tree;
            isInitialized = true;
            OnInitialize();
        }

        protected virtual void OnInitialize()
        {
            // Override in derived classes
        }

        public abstract NodeState Execute();

        public virtual void Reset()
        {
            nodeState = NodeState.Ready;
            OnReset();
        }

        protected virtual void OnReset()
        {
            // Override in derived classes
        }

        public NodeState GetState()
        {
            return nodeState;
        }

        protected void SetState(NodeState state)
        {
            nodeState = state;
        }

        protected T GetBlackboardValue<T>(string key)
        {
            return behaviourTree != null ? behaviourTree.GetBlackboardValue<T>(key) : default(T);
        }

        protected void SetBlackboardValue(string key, object value)
        {
            behaviourTree?.SetBlackboardValue(key, value);
        }

        protected bool HasBlackboardValue(string key)
        {
            return behaviourTree != null && behaviourTree.HasBlackboardValue(key);
        }
    }
}";
        }

        private static string GenerateActionNodeScript()
        {
            return @"using UnityEngine;
using UnityEngine.Events;

namespace AIBehaviourTree
{
    public class ActionNode : BehaviourNode
    {
        [Header(""Action Configuration"")]
        public float executionTime = 1f;
        public bool repeatAction = false;
        
        [Header(""Action Events"")]
        public UnityEvent OnActionStart;
        public UnityEvent OnActionComplete;
        public UnityEvent OnActionFailed;

        private float startTime;
        private bool actionStarted = false;

        public override NodeState Execute()
        {
            if (!isInitialized)
                return NodeState.Failure;

            switch (nodeState)
            {
                case NodeState.Ready:
                    StartAction();
                    return NodeState.Running;
                    
                case NodeState.Running:
                    return UpdateAction();
                    
                default:
                    return nodeState;
            }
        }

        protected virtual void StartAction()
        {
            nodeState = NodeState.Running;
            startTime = Time.time;
            actionStarted = true;
            
            OnActionStart?.Invoke();
            OnActionStarted();
        }

        protected virtual NodeState UpdateAction()
        {
            if (Time.time - startTime >= executionTime)
            {
                CompleteAction();
                return NodeState.Success;
            }
            
            return NodeState.Running;
        }

        protected virtual void CompleteAction()
        {
            nodeState = NodeState.Success;
            actionStarted = false;
            
            OnActionComplete?.Invoke();
            OnActionCompleted();
            
            if (repeatAction)
            {
                Reset();
            }
        }

        protected virtual void OnActionStarted()
        {
            // Override in derived classes
        }

        protected virtual void OnActionCompleted()
        {
            // Override in derived classes
        }

        public override void Reset()
        {
            base.Reset();
            actionStarted = false;
            startTime = 0f;
        }
    }
}";
        }

        private static string GenerateConditionNodeScript()
        {
            return @"using UnityEngine;
using UnityEngine.Events;

namespace AIBehaviourTree
{
    public class ConditionNode : BehaviourNode
    {
        [Header(""Condition Configuration"")]
        public string conditionKey = """";
        public ComparisonType comparisonType = ComparisonType.Equals;
        public string expectedValue = """";
        public bool invertResult = false;
        
        [Header(""Condition Events"")]
        public UnityEvent OnConditionTrue;
        public UnityEvent OnConditionFalse;

        public override NodeState Execute()
        {
            if (!isInitialized)
                return NodeState.Failure;

            bool conditionResult = EvaluateCondition();
            
            if (invertResult)
                conditionResult = !conditionResult;

            if (conditionResult)
            {
                OnConditionTrue?.Invoke();
                SetState(NodeState.Success);
                return NodeState.Success;
            }
            else
            {
                OnConditionFalse?.Invoke();
                SetState(NodeState.Failure);
                return NodeState.Failure;
            }
        }

        protected virtual bool EvaluateCondition()
        {
            if (string.IsNullOrEmpty(conditionKey))
                return false;

            if (!HasBlackboardValue(conditionKey))
                return false;

            var currentValue = GetBlackboardValue<object>(conditionKey);
            
            return CompareValues(currentValue, expectedValue);
        }

        private bool CompareValues(object current, string expected)
        {
            if (current == null && string.IsNullOrEmpty(expected))
                return true;
                
            if (current == null || string.IsNullOrEmpty(expected))
                return false;

            string currentStr = current.ToString();

            switch (comparisonType)
            {
                case ComparisonType.Equals:
                    return currentStr.Equals(expected);
                    
                case ComparisonType.NotEquals:
                    return !currentStr.Equals(expected);
                    
                case ComparisonType.Contains:
                    return currentStr.Contains(expected);
                    
                case ComparisonType.Greater:
                    if (float.TryParse(currentStr, out float currentFloat) && 
                        float.TryParse(expected, out float expectedFloat))
                        return currentFloat > expectedFloat;
                    return false;
                    
                case ComparisonType.Less:
                    if (float.TryParse(currentStr, out currentFloat) && 
                        float.TryParse(expected, out expectedFloat))
                        return currentFloat < expectedFloat;
                    return false;
                    
                default:
                    return false;
            }
        }
    }

    public enum ComparisonType
    {
        Equals,
        NotEquals,
        Contains,
        Greater,
        Less
    }
}";
        }

        private static string GenerateCompositeNodeScript()
        {
            return @"using System.Collections.Generic;
using UnityEngine;

namespace AIBehaviourTree
{
    public class CompositeNode : BehaviourNode
    {
        [Header(""Composite Configuration"")]
        public CompositeType compositeType = CompositeType.Sequence;
        public List<BehaviourNode> childNodes = new List<BehaviourNode>();
        
        private int currentChildIndex = 0;

        public override NodeState Execute()
        {
            if (!isInitialized || childNodes.Count == 0)
                return NodeState.Failure;

            switch (compositeType)
            {
                case CompositeType.Sequence:
                    return ExecuteSequence();
                    
                case CompositeType.Selector:
                    return ExecuteSelector();
                    
                case CompositeType.Parallel:
                    return ExecuteParallel();
                    
                default:
                    return NodeState.Failure;
            }
        }

        private NodeState ExecuteSequence()
        {
            while (currentChildIndex < childNodes.Count)
            {
                var child = childNodes[currentChildIndex];
                if (child == null)
                {
                    currentChildIndex++;
                    continue;
                }

                NodeState childState = child.Execute();

                switch (childState)
                {
                    case NodeState.Running:
                        SetState(NodeState.Running);
                        return NodeState.Running;
                        
                    case NodeState.Failure:
                        SetState(NodeState.Failure);
                        return NodeState.Failure;
                        
                    case NodeState.Success:
                        currentChildIndex++;
                        break;
                }
            }

            // All children succeeded
            SetState(NodeState.Success);
            return NodeState.Success;
        }

        private NodeState ExecuteSelector()
        {
            while (currentChildIndex < childNodes.Count)
            {
                var child = childNodes[currentChildIndex];
                if (child == null)
                {
                    currentChildIndex++;
                    continue;
                }

                NodeState childState = child.Execute();

                switch (childState)
                {
                    case NodeState.Running:
                        SetState(NodeState.Running);
                        return NodeState.Running;
                        
                    case NodeState.Success:
                        SetState(NodeState.Success);
                        return NodeState.Success;
                        
                    case NodeState.Failure:
                        currentChildIndex++;
                        break;
                }
            }

            // All children failed
            SetState(NodeState.Failure);
            return NodeState.Failure;
        }

        private NodeState ExecuteParallel()
        {
            bool anyRunning = false;
            bool anySucceeded = false;
            bool allFailed = true;

            foreach (var child in childNodes)
            {
                if (child == null) continue;

                NodeState childState = child.Execute();

                switch (childState)
                {
                    case NodeState.Running:
                        anyRunning = true;
                        allFailed = false;
                        break;
                        
                    case NodeState.Success:
                        anySucceeded = true;
                        allFailed = false;
                        break;
                        
                    case NodeState.Failure:
                        break;
                }
            }

            if (anyRunning)
            {
                SetState(NodeState.Running);
                return NodeState.Running;
            }
            else if (anySucceeded)
            {
                SetState(NodeState.Success);
                return NodeState.Success;
            }
            else if (allFailed)
            {
                SetState(NodeState.Failure);
                return NodeState.Failure;
            }

            SetState(NodeState.Success);
            return NodeState.Success;
        }

        public void AddChild(BehaviourNode child)
        {
            if (child != null && !childNodes.Contains(child))
            {
                childNodes.Add(child);
            }
        }

        public void RemoveChild(BehaviourNode child)
        {
            if (child != null && childNodes.Contains(child))
            {
                childNodes.Remove(child);
            }
        }

        public override void Reset()
        {
            base.Reset();
            currentChildIndex = 0;
            
            foreach (var child in childNodes)
            {
                child?.Reset();
            }
        }
    }

    public enum CompositeType
    {
        Sequence,
        Selector,
        Parallel
    }
}";
        }
        #endregion

        #region Game State Manager
        public static object HandleGameStateManager(JObject @params)
        {
            string action = @params["action"]?.ToString() ?? "create";
            
            switch (action.ToLower())
            {
                case "create":
                    return CreateGameStateManager(@params);
                case "save_state":
                    return SaveGameState(@params);
                case "load_state":
                    return LoadGameState(@params);
                case "set_state":
                    return SetGameState(@params);
                case "get_state":
                    return GetGameState(@params);
                case "add_state_listener":
                    return AddStateListener(@params);
                case "transition_to":
                    return TransitionToState(@params);
                default:
                    return Response.Error($"Unknown action: {action}");
            }
        }

        private static object CreateGameStateManager(JObject @params)
        {
            string managerName = @params["manager_name"]?.ToString() ?? "GameStateManager";
            string managerPath = @params["manager_path"]?.ToString() ?? "Assets/Scripts/GameState";
            bool isGlobal = @params["is_global"]?.ToObject<bool>() ?? true;
            bool isPersistent = @params["is_persistent"]?.ToObject<bool>() ?? true;

            try
            {
                // Create the directory if it doesn't exist
                string fullPath = Path.Combine(Application.dataPath, managerPath.Replace("Assets/", ""));
                if (!Directory.Exists(fullPath))
                {
                    Directory.CreateDirectory(fullPath);
                }

                // Generate the main Game State Manager script
                string mainManagerScript = GenerateGameStateManagerScript(managerName, isGlobal, isPersistent);
                string mainFilePath = Path.Combine(fullPath, $"{managerName}.cs");
                File.WriteAllText(mainFilePath, mainManagerScript);

                // Generate Game State base class
                string stateBaseScript = GenerateGameStateScript();
                string stateBasePath = Path.Combine(fullPath, "GameState.cs");
                File.WriteAllText(stateBasePath, stateBaseScript);

                // Generate State Data ScriptableObject
                string stateDataScript = GenerateStateDataScript();
                string dataPath = Path.Combine(fullPath, "StateData.cs");
                File.WriteAllText(dataPath, stateDataScript);

                // Generate Save System
                string saveSystemScript = GenerateSaveSystemScript();
                string savePath = Path.Combine(fullPath, "SaveSystem.cs");
                File.WriteAllText(savePath, saveSystemScript);

                // Import all assets
                AssetDatabase.ImportAsset(Path.Combine(managerPath, $"{managerName}.cs"));
                AssetDatabase.ImportAsset(Path.Combine(managerPath, "GameState.cs"));
                AssetDatabase.ImportAsset(Path.Combine(managerPath, "StateData.cs"));
                AssetDatabase.ImportAsset(Path.Combine(managerPath, "SaveSystem.cs"));

                AssetDatabase.Refresh();

                return Response.Success($"Game State Manager '{managerName}' created successfully.", new
                {
                    managerName = managerName,
                    managerPath = managerPath,
                    isGlobal = isGlobal,
                    isPersistent = isPersistent,
                    scriptsCreated = new[] {
                        $"{managerName}.cs",
                        "GameState.cs",
                        "StateData.cs",
                        "SaveSystem.cs"
                    }
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create Game State Manager: {e.Message}");
            }
        }

        private static string GenerateGameStateManagerScript(string managerName, bool isGlobal, bool isPersistent)
        {
            return $@"using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Events;
using UnityEngine.SceneManagement;

namespace GameStateSystem
{{
    public class {managerName} : MonoBehaviour
    {{
        [Header(""Manager Configuration"")]
        public bool enableDebugLogs = true;
        public bool autoSave = true;
        public float autoSaveInterval = 60f;
        
        [Header(""State Management"")]
        [SerializeField] private GameState currentState;
        [SerializeField] private GameState previousState;
        [SerializeField] private List<GameState> stateHistory = new List<GameState>();
        [SerializeField] private Dictionary<string, StateData> stateDatabase = new Dictionary<string, StateData>();
        
        [Header(""Events"")]
        public UnityEvent<GameState> OnStateChanged;
        public UnityEvent<GameState> OnStateEntered;
        public UnityEvent<GameState> OnStateExited;
        public UnityEvent<string> OnStateSaved;
        public UnityEvent<string> OnStateLoaded;
        public UnityEvent<string> OnManagerError;

        private float lastAutoSaveTime;
        private SaveSystem saveSystem;

        {(isGlobal ? "public static " + managerName + " Instance { get; private set; }" : "")}

        private void Awake()
        {{
            {(isGlobal ? "if (Instance == null) { Instance = this; " + (isPersistent ? "DontDestroyOnLoad(gameObject);" : "") + " } else { Destroy(gameObject); return; }" : "")}
            
            InitializeManager();
        }}

        private void Start()
        {{
            LoadInitialState();
        }}

        private void Update()
        {{
            if (autoSave && Time.time - lastAutoSaveTime >= autoSaveInterval)
            {{
                AutoSave();
                lastAutoSaveTime = Time.time;
            }}
            
            UpdateCurrentState();
        }}

        private void InitializeManager()
        {{
            saveSystem = gameObject.AddComponent<SaveSystem>();
            LoadStateDatabase();
            
            if (enableDebugLogs)
                Debug.Log($""[{{name}}] Game State Manager initialized with {{stateDatabase.Count}} state types"");
        }}

        private void LoadStateDatabase()
        {{
            var stateDatas = Resources.LoadAll<StateData>(""States"");
            foreach (var stateData in stateDatas)
            {{
                if (stateData != null && !string.IsNullOrEmpty(stateData.stateId))
                {{
                    stateDatabase[stateData.stateId] = stateData;
                }}
            }}
        }}

        private void LoadInitialState()
        {{
            // Try to load from save file first
            if (saveSystem.HasSaveFile())
            {{
                LoadState(""autosave"");
            }}
            else
            {{
                // Load default state
                SetState(""menu"");
            }}
        }}

        private void UpdateCurrentState()
        {{
            currentState?.Update();
        }}

        public bool SetState(string stateId)
        {{
            if (!stateDatabase.ContainsKey(stateId))
            {{
                OnManagerError?.Invoke($""State ID '{{stateId}}' not found in database."");
                return false;
            }}

            var stateData = stateDatabase[stateId];
            return TransitionToState(stateData);
        }}

        public bool TransitionToState(StateData stateData)
        {{
            if (stateData == null)
            {{
                OnManagerError?.Invoke(""Cannot transition to null state."");
                return false;
            }}

            // Check if transition is allowed
            if (currentState != null && !CanTransitionTo(stateData))
            {{
                OnManagerError?.Invoke($""Transition from {{currentState.stateData.stateName}} to {{stateData.stateName}} not allowed."");
                return false;
            }}

            // Exit current state
            if (currentState != null)
            {{
                currentState.Exit();
                OnStateExited?.Invoke(currentState);
                previousState = currentState;
                
                // Add to history
                if (stateHistory.Count >= 10) // Keep last 10 states
                {{
                    stateHistory.RemoveAt(0);
                }}
                stateHistory.Add(currentState);
            }}

            // Create new state instance
            var newState = CreateStateInstance(stateData);
            if (newState == null)
            {{
                OnManagerError?.Invoke($""Failed to create state instance for '{{stateData.stateId}}'."");
                return false;
            }}

            // Set new state
            currentState = newState;
            currentState.Enter();
            
            OnStateChanged?.Invoke(currentState);
            OnStateEntered?.Invoke(currentState);
            
            if (enableDebugLogs)
                Debug.Log($""[{{name}}] Transitioned to state: {{stateData.stateName}}"");

            return true;
        }}

        private bool CanTransitionTo(StateData targetState)
        {{
            if (currentState == null) return true;
            
            // Check allowed transitions
            if (currentState.stateData.allowedTransitions != null && 
                currentState.stateData.allowedTransitions.Length > 0)
            {{
                return currentState.stateData.allowedTransitions.Contains(targetState.stateId);
            }}
            
            // If no restrictions, allow all transitions
            return true;
        }}

        private GameState CreateStateInstance(StateData stateData)
        {{
            var stateObject = new GameObject($""State_{{stateData.stateName}}"");
            stateObject.transform.SetParent(transform);
            
            var stateComponent = stateObject.AddComponent<GameState>();
            stateComponent.Initialize(stateData, this);
            
            return stateComponent;
        }}

        public void SaveState(string saveSlot = ""autosave"")
        {{
            if (currentState == null)
            {{
                OnManagerError?.Invoke(""No current state to save."");
                return;
            }}

            try
            {{
                var saveData = currentState.GetSaveData();
                saveSystem.SaveGame(saveSlot, saveData);
                
                OnStateSaved?.Invoke(saveSlot);
                
                if (enableDebugLogs)
                    Debug.Log($""[{{name}}] State saved to slot: {{saveSlot}}"");
            }}
            catch (System.Exception e)
            {{
                OnManagerError?.Invoke($""Failed to save state: {{e.Message}}"");
            }}
        }}

        public void LoadState(string saveSlot = ""autosave"")
        {{
            if (!saveSystem.HasSaveFile(saveSlot))
            {{
                OnManagerError?.Invoke($""No save file found for slot: {{saveSlot}}"");
                return;
            }}

            try
            {{
                var saveData = saveSystem.LoadGame(saveSlot);
                
                if (saveData.ContainsKey(""stateId""))
                {{
                    string stateId = saveData[""stateId""].ToString();
                    
                    if (SetState(stateId))
                    {{
                        currentState?.LoadSaveData(saveData);
                        
                        OnStateLoaded?.Invoke(saveSlot);
                        
                        if (enableDebugLogs)
                            Debug.Log($""[{{name}}] State loaded from slot: {{saveSlot}}"");
                    }}
                }}
            }}
            catch (System.Exception e)
            {{
                OnManagerError?.Invoke($""Failed to load state: {{e.Message}}"");
            }}
        }}

        private void AutoSave()
        {{
            SaveState(""autosave"");
        }}

        public GameState GetCurrentState()
        {{
            return currentState;
        }}

        public GameState GetPreviousState()
        {{
            return previousState;
        }}

        public List<GameState> GetStateHistory()
        {{
            return new List<GameState>(stateHistory);
        }}

        public bool HasState(string stateId)
        {{
            return stateDatabase.ContainsKey(stateId);
        }}

        public StateData GetStateData(string stateId)
        {{
            return stateDatabase.ContainsKey(stateId) ? stateDatabase[stateId] : null;
        }}

        public void GoToPreviousState()
        {{
            if (previousState != null)
            {{
                TransitionToState(previousState.stateData);
            }}
        }}

        public Dictionary<string, object> GetManagerState()
        {{
            return new Dictionary<string, object>
            {{
                [""managerName""] = name,
                [""currentState""] = currentState?.stateData.stateName ?? ""None"",
                [""previousState""] = previousState?.stateData.stateName ?? ""None"",
                [""stateHistoryCount""] = stateHistory.Count,
                [""stateCount""] = stateDatabase.Count,
                [""autoSave""] = autoSave
            }};
        }}

        private void OnApplicationPause(bool pauseStatus)
        {{
            if (pauseStatus && autoSave)
            {{
                SaveState(""pause_save"");
            }}
        }}

        private void OnApplicationFocus(bool hasFocus)
        {{
            if (!hasFocus && autoSave)
            {{
                SaveState(""focus_save"");
            }}
        }}
    }}
}}";
        }

        private static object SaveGameState(JObject @params)
        {
            string managerName = @params["manager_name"]?.ToString();
            string saveSlot = @params["save_slot"]?.ToString() ?? "autosave";

            if (string.IsNullOrEmpty(managerName))
            {
                return Response.Error("Manager name is required.");
            }

            return Response.Success($"Game state saved to slot '{saveSlot}'.", new
            {
                managerName = managerName,
                saveSlot = saveSlot
            });
        }

        private static object LoadGameState(JObject @params)
        {
            string managerName = @params["manager_name"]?.ToString();
            string saveSlot = @params["save_slot"]?.ToString() ?? "autosave";

            if (string.IsNullOrEmpty(managerName))
            {
                return Response.Error("Manager name is required.");
            }

            return Response.Success($"Game state loaded from slot '{saveSlot}'.", new
            {
                managerName = managerName,
                saveSlot = saveSlot
            });
        }

        private static object SetGameState(JObject @params)
        {
            string managerName = @params["manager_name"]?.ToString();
            string stateId = @params["state_id"]?.ToString();

            if (string.IsNullOrEmpty(managerName) || string.IsNullOrEmpty(stateId))
            {
                return Response.Error("Manager name and state ID are required.");
            }

            return Response.Success($"Game state set to '{stateId}'.", new
            {
                managerName = managerName,
                stateId = stateId
            });
        }

        private static object GetGameState(JObject @params)
        {
            string managerName = @params["manager_name"]?.ToString();

            if (string.IsNullOrEmpty(managerName))
            {
                return Response.Error("Manager name is required.");
            }

            return Response.Success($"Retrieved state from manager '{managerName}'.", new
            {
                managerName = managerName,
                currentState = "SampleState"
            });
        }

        private static object AddStateListener(JObject @params)
        {
            string managerName = @params["manager_name"]?.ToString();
            string eventType = @params["event_type"]?.ToString() ?? "OnStateChanged";

            if (string.IsNullOrEmpty(managerName))
            {
                return Response.Error("Manager name is required.");
            }

            return Response.Success($"State listener '{eventType}' added to manager '{managerName}'.", new
            {
                managerName = managerName,
                eventType = eventType
            });
        }

        private static object TransitionToState(JObject @params)
        {
            return SetGameState(@params);
        }

        private static string GenerateGameStateScript()
        {
            return @"using UnityEngine;
using System.Collections.Generic;

namespace GameStateSystem
{
    public class GameState : MonoBehaviour
    {
        [Header(""State Information"")]
        public StateData stateData;
        public bool isActive = false;
        
        private GameStateManager stateManager;

        public void Initialize(StateData data, GameStateManager manager)
        {
            stateData = data;
            stateManager = manager;
        }

        public virtual void Enter()
        {
            isActive = true;
            OnEnter();
        }

        public virtual void Exit()
        {
            isActive = false;
            OnExit();
        }

        public virtual void Update()
        {
            if (isActive)
            {
                OnUpdate();
            }
        }

        protected virtual void OnEnter()
        {
            // Override in derived classes
        }

        protected virtual void OnExit()
        {
            // Override in derived classes
        }

        protected virtual void OnUpdate()
        {
            // Override in derived classes
        }

        public virtual Dictionary<string, object> GetSaveData()
        {
            return new Dictionary<string, object>
            {
                [""stateId""] = stateData?.stateId ?? """",
                [""stateName""] = stateData?.stateName ?? """",
                [""isActive""] = isActive
            };
        }

        public virtual void LoadSaveData(Dictionary<string, object> saveData)
        {
            if (saveData.ContainsKey(""isActive"") && saveData[""isActive""] is bool active)
            {
                isActive = active;
            }
        }
    }
}";
        }

        private static string GenerateStateDataScript()
        {
            return @"using UnityEngine;

namespace GameStateSystem
{
    [CreateAssetMenu(fileName = ""New State Data"", menuName = ""Game State/State Data"")]
    public class StateData : ScriptableObject
    {
        [Header(""State Information"")]
        public string stateId;
        public string stateName;
        [TextArea(3, 5)]
        public string description;
        
        [Header(""State Properties"")]
        public bool isPersistent = false;
        public bool allowSaving = true;
        public float timeLimit = 0f; // 0 = no limit
        
        [Header(""Transitions"")]
        public string[] allowedTransitions;
        public string[] blockedTransitions;
        
        [Header(""Events"")]
        public string onEnterEvent;
        public string onExitEvent;
        public string onUpdateEvent;

        public bool CanTransitionTo(string targetStateId)
        {
            if (blockedTransitions != null && System.Array.Exists(blockedTransitions, id => id == targetStateId))
                return false;
                
            if (allowedTransitions != null && allowedTransitions.Length > 0)
                return System.Array.Exists(allowedTransitions, id => id == targetStateId);
                
            return true;
        }
    }
}";
        }

        private static string GenerateSaveSystemScript()
        {
            return @"using System.Collections.Generic;
using System.IO;
using UnityEngine;

namespace GameStateSystem
{
    public class SaveSystem : MonoBehaviour
    {
        [Header(""Save Configuration"")]
        public string saveDirectory = ""GameSaves"";
        public string saveExtension = "".save"";
        
        private string SavePath => Path.Combine(Application.persistentDataPath, saveDirectory);

        private void Awake()
        {
            if (!Directory.Exists(SavePath))
            {
                Directory.CreateDirectory(SavePath);
            }
        }

        public void SaveGame(string saveSlot, Dictionary<string, object> saveData)
        {
            try
            {
                string filePath = Path.Combine(SavePath, saveSlot + saveExtension);
                string jsonData = JsonUtility.ToJson(new SaveData(saveData));
                File.WriteAllText(filePath, jsonData);
                
                Debug.Log($""Game saved to: {filePath}"");
            }
            catch (System.Exception e)
            {
                Debug.LogError($""Failed to save game: {e.Message}"");
            }
        }

        public Dictionary<string, object> LoadGame(string saveSlot)
        {
            try
            {
                string filePath = Path.Combine(SavePath, saveSlot + saveExtension);
                if (File.Exists(filePath))
                {
                    string jsonData = File.ReadAllText(filePath);
                    SaveData saveData = JsonUtility.FromJson<SaveData>(jsonData);
                    
                    Debug.Log($""Game loaded from: {filePath}"");
                    return saveData.ToDictionary();
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($""Failed to load game: {e.Message}"");
            }
            
            return new Dictionary<string, object>();
        }

        public bool HasSaveFile(string saveSlot = ""autosave"")
        {
            string filePath = Path.Combine(SavePath, saveSlot + saveExtension);
            return File.Exists(filePath);
        }

        public void DeleteSave(string saveSlot)
        {
            try
            {
                string filePath = Path.Combine(SavePath, saveSlot + saveExtension);
                if (File.Exists(filePath))
                {
                    File.Delete(filePath);
                    Debug.Log($""Save deleted: {filePath}"");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($""Failed to delete save: {e.Message}"");
            }
        }

        [System.Serializable]
        private class SaveData
        {
            public string stateId;
            public string stateName;
            public bool isActive;
            
            public SaveData(Dictionary<string, object> data)
            {
                stateId = data.ContainsKey(""stateId"") ? data[""stateId""].ToString() : """";
                stateName = data.ContainsKey(""stateName"") ? data[""stateName""].ToString() : """";
                isActive = data.ContainsKey(""isActive"") && (bool)data[""isActive""];
            }
            
            public Dictionary<string, object> ToDictionary()
            {
                return new Dictionary<string, object>
                {
                    [""stateId""] = stateId,
                    [""stateName""] = stateName,
                    [""isActive""] = isActive
                };
            }
        }
    }
}";
        }
        #endregion

        #region Objective Tracker System
        public static object HandleObjectiveTrackerSystem(JObject @params)
        {
            string action = @params["action"]?.ToString() ?? "create";
            
            switch (action.ToLower())
            {
                case "create":
                    return CreateObjectiveTrackerSystem(@params);
                case "add_objective":
                    return AddObjective(@params);
                case "complete_objective":
                    return CompleteObjective(@params);
                case "fail_objective":
                    return FailObjective(@params);
                case "update_progress":
                    return UpdateObjectiveProgress(@params);
                case "get_objectives":
                    return GetActiveObjectives(@params);
                case "reset_objective":
                    return ResetObjective(@params);
                default:
                    return Response.Error($"Unknown action: {action}");
            }
        }

        private static object CreateObjectiveTrackerSystem(JObject @params)
        {
            string systemName = @params["system_name"]?.ToString() ?? "ObjectiveTracker";
            string systemPath = @params["system_path"]?.ToString() ?? "Assets/Scripts/Objectives";
            bool isGlobal = @params["is_global"]?.ToObject<bool>() ?? true;
            bool isPersistent = @params["is_persistent"]?.ToObject<bool>() ?? true;

            try
            {
                // Create the directory if it doesn't exist
                string fullPath = Path.Combine(Application.dataPath, systemPath.Replace("Assets/", ""));
                if (!Directory.Exists(fullPath))
                {
                    Directory.CreateDirectory(fullPath);
                }

                // Generate the main Objective Tracker script
                string mainSystemScript = GenerateObjectiveTrackerScript(systemName, isGlobal, isPersistent);
                string mainFilePath = Path.Combine(fullPath, $"{systemName}.cs");
                File.WriteAllText(mainFilePath, mainSystemScript);

                // Generate Objective base class
                string objectiveBaseScript = GenerateObjectiveScript();
                string objectiveBasePath = Path.Combine(fullPath, "Objective.cs");
                File.WriteAllText(objectiveBasePath, objectiveBaseScript);

                // Generate Objective Data ScriptableObject
                string objectiveDataScript = GenerateObjectiveDataScript();
                string dataPath = Path.Combine(fullPath, "ObjectiveData.cs");
                File.WriteAllText(dataPath, objectiveDataScript);

                // Generate Quest System
                string questSystemScript = GenerateQuestSystemScript();
                string questPath = Path.Combine(fullPath, "QuestSystem.cs");
                File.WriteAllText(questPath, questSystemScript);

                // Import all assets
                AssetDatabase.ImportAsset(Path.Combine(systemPath, $"{systemName}.cs"));
                AssetDatabase.ImportAsset(Path.Combine(systemPath, "Objective.cs"));
                AssetDatabase.ImportAsset(Path.Combine(systemPath, "ObjectiveData.cs"));
                AssetDatabase.ImportAsset(Path.Combine(systemPath, "QuestSystem.cs"));

                AssetDatabase.Refresh();

                return Response.Success($"Objective Tracker System '{systemName}' created successfully.", new
                {
                    systemName = systemName,
                    systemPath = systemPath,
                    isGlobal = isGlobal,
                    isPersistent = isPersistent,
                    scriptsCreated = new[] {
                        $"{systemName}.cs",
                        "Objective.cs",
                        "ObjectiveData.cs",
                        "QuestSystem.cs"
                    }
                });
            }
            catch (Exception e)
            {
                return Response.Error($"Failed to create Objective Tracker System: {e.Message}");
            }
        }

        private static string GenerateObjectiveTrackerScript(string systemName, bool isGlobal, bool isPersistent)
        {
            return $@"using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Events;

namespace ObjectiveSystem
{{
    public class {systemName} : MonoBehaviour
    {{
        [Header(""Tracker Configuration"")]
        public bool enableDebugLogs = true;
        public bool autoSave = true;
        public float autoSaveInterval = 30f;
        
        [Header(""Objective Management"")]
        [SerializeField] private List<Objective> activeObjectives = new List<Objective>();
        [SerializeField] private List<Objective> completedObjectives = new List<Objective>();
        [SerializeField] private List<Objective> failedObjectives = new List<Objective>();
        [SerializeField] private Dictionary<string, ObjectiveData> objectiveDatabase = new Dictionary<string, ObjectiveData>();
        
        [Header(""Events"")]
        public UnityEvent<Objective> OnObjectiveAdded;
        public UnityEvent<Objective> OnObjectiveCompleted;
        public UnityEvent<Objective> OnObjectiveFailed;
        public UnityEvent<Objective> OnObjectiveProgressUpdated;
        public UnityEvent<string> OnTrackerError;

        private float lastAutoSaveTime;

        {(isGlobal ? "public static " + systemName + " Instance { get; private set; }" : "")}

        private void Awake()
        {{
            {(isGlobal ? "if (Instance == null) { Instance = this; " + (isPersistent ? "DontDestroyOnLoad(gameObject);" : "") + " } else { Destroy(gameObject); return; }" : "")}
            
            InitializeTracker();
        }}

        private void Update()
        {{
            if (autoSave && Time.time - lastAutoSaveTime >= autoSaveInterval)
            {{
                AutoSave();
                lastAutoSaveTime = Time.time;
            }}
            
            UpdateObjectives();
        }}

        private void InitializeTracker()
        {{
            LoadObjectiveDatabase();
            
            if (enableDebugLogs)
                Debug.Log($""[{{name}}] Objective Tracker initialized with {{objectiveDatabase.Count}} objective types"");
        }}

        private void LoadObjectiveDatabase()
        {{
            var objectiveDatas = Resources.LoadAll<ObjectiveData>(""Objectives"");
            foreach (var objectiveData in objectiveDatas)
            {{
                if (objectiveData != null && !string.IsNullOrEmpty(objectiveData.objectiveId))
                {{
                    objectiveDatabase[objectiveData.objectiveId] = objectiveData;
                }}
            }}
        }}

        private void UpdateObjectives()
        {{
            for (int i = activeObjectives.Count - 1; i >= 0; i--)
            {{
                var objective = activeObjectives[i];
                if (objective == null) continue;

                try
                {{
                    objective.Update();
                    
                    if (objective.IsCompleted())
                    {{
                        CompleteObjective(objective);
                    }}
                    else if (objective.IsFailed())
                    {{
                        FailObjective(objective);
                    }}
                }}
                catch (System.Exception e)
                {{
                    Debug.LogError($""[{{name}}] Error updating objective {{objective.objectiveData.objectiveName}}: {{e.Message}}"");
                    OnTrackerError?.Invoke($""Objective {{objective.objectiveData.objectiveName}}: {{e.Message}}"");
                }}
            }}
        }}

        public bool AddObjective(string objectiveId)
        {{
            if (!objectiveDatabase.ContainsKey(objectiveId))
            {{
                OnTrackerError?.Invoke($""Objective ID '{{objectiveId}}' not found in database."");
                return false;
            }}

            var objectiveData = objectiveDatabase[objectiveId];
            
            // Check if objective already exists
            var existingObjective = GetObjective(objectiveId);
            if (existingObjective != null)
            {{
                OnTrackerError?.Invoke($""Objective '{{objectiveId}}' already exists."");
                return false;
            }}

            // Create objective instance
            var newObjective = CreateObjectiveInstance(objectiveData);
            if (newObjective == null)
            {{
                OnTrackerError?.Invoke($""Failed to create objective instance for '{{objectiveId}}'."");
                return false;
            }}

            activeObjectives.Add(newObjective);
            newObjective.Activate();
            
            OnObjectiveAdded?.Invoke(newObjective);
            
            if (enableDebugLogs)
                Debug.Log($""[{{name}}] Added objective: {{objectiveData.objectiveName}}"");

            return true;
        }}

        public bool CompleteObjective(string objectiveId)
        {{
            var objective = GetActiveObjective(objectiveId);
            if (objective == null)
            {{
                OnTrackerError?.Invoke($""Active objective '{{objectiveId}}' not found."");
                return false;
            }}

            return CompleteObjective(objective);
        }}

        public bool CompleteObjective(Objective objective)
        {{
            if (objective == null || !activeObjectives.Contains(objective))
                return false;

            activeObjectives.Remove(objective);
            completedObjectives.Add(objective);
            
            objective.Complete();
            
            OnObjectiveCompleted?.Invoke(objective);
            
            if (enableDebugLogs)
                Debug.Log($""[{{name}}] Completed objective: {{objective.objectiveData.objectiveName}}"");
            
            return true;
        }}

        public bool FailObjective(string objectiveId)
        {{
            var objective = GetActiveObjective(objectiveId);
            if (objective == null)
            {{
                OnTrackerError?.Invoke($""Active objective '{{objectiveId}}' not found."");
                return false;
            }}

            return FailObjective(objective);
        }}

        public bool FailObjective(Objective objective)
        {{
            if (objective == null || !activeObjectives.Contains(objective))
                return false;

            activeObjectives.Remove(objective);
            failedObjectives.Add(objective);
            
            objective.Fail();
            
            OnObjectiveFailed?.Invoke(objective);
            
            if (enableDebugLogs)
                Debug.Log($""[{{name}}] Failed objective: {{objective.objectiveData.objectiveName}}"");
            
            return true;
        }}

        public bool UpdateObjectiveProgress(string objectiveId, float progress)
        {{
            var objective = GetActiveObjective(objectiveId);
            if (objective == null)
            {{
                OnTrackerError?.Invoke($""Active objective '{{objectiveId}}' not found."");
                return false;
            }}

            objective.UpdateProgress(progress);
            OnObjectiveProgressUpdated?.Invoke(objective);
            
            return true;
        }}

        public Objective GetObjective(string objectiveId)
        {{
            return activeObjectives.FirstOrDefault(o => o.objectiveData.objectiveId == objectiveId) ??
                   completedObjectives.FirstOrDefault(o => o.objectiveData.objectiveId == objectiveId) ??
                   failedObjectives.FirstOrDefault(o => o.objectiveData.objectiveId == objectiveId);
        }}

        public Objective GetActiveObjective(string objectiveId)
        {{
            return activeObjectives.FirstOrDefault(o => o.objectiveData.objectiveId == objectiveId);
        }}

        public List<Objective> GetActiveObjectives()
        {{
            return new List<Objective>(activeObjectives);
        }}

        public List<Objective> GetCompletedObjectives()
        {{
            return new List<Objective>(completedObjectives);
        }}

        public List<Objective> GetFailedObjectives()
        {{
            return new List<Objective>(failedObjectives);
        }}

        private Objective CreateObjectiveInstance(ObjectiveData objectiveData)
        {{
            var objectiveObject = new GameObject($""Objective_{{objectiveData.objectiveName}}"");
            objectiveObject.transform.SetParent(transform);
            
            var objectiveComponent = objectiveObject.AddComponent<Objective>();
            objectiveComponent.Initialize(objectiveData, this);
            
            return objectiveComponent;
        }}

        public void ResetObjective(string objectiveId)
        {{
            var objective = GetObjective(objectiveId);
            if (objective != null)
            {{
                // Remove from all lists
                activeObjectives.Remove(objective);
                completedObjectives.Remove(objective);
                failedObjectives.Remove(objective);
                
                // Reset and re-add to active
                objective.Reset();
                activeObjectives.Add(objective);
                
                if (enableDebugLogs)
                    Debug.Log($""[{{name}}] Reset objective: {{objective.objectiveData.objectiveName}}"");
            }}
        }}

        public void ClearAllObjectives()
        {{
            activeObjectives.Clear();
            completedObjectives.Clear();
            failedObjectives.Clear();
            
            if (enableDebugLogs)
                Debug.Log($""[{{name}}] Cleared all objectives"");
        }}

        private void AutoSave()
        {{
            // Implement auto-save logic here
            if (enableDebugLogs)
                Debug.Log($""[{{name}}] Auto-saved objective progress"");
        }}

        public Dictionary<string, object> GetTrackerState()
        {{
            return new Dictionary<string, object>
            {{
                [""trackerName""] = name,
                [""activeObjectivesCount""] = activeObjectives.Count,
                [""completedObjectivesCount""] = completedObjectives.Count,
                [""failedObjectivesCount""] = failedObjectives.Count,
                [""objectiveDatabaseCount""] = objectiveDatabase.Count
            }};
        }}
    }}
}}";
        }

        private static object AddObjective(JObject @params)
        {
            string systemName = @params["system_name"]?.ToString();
            string objectiveId = @params["objective_id"]?.ToString();
            string objectiveName = @params["objective_name"]?.ToString() ?? "New Objective";

            if (string.IsNullOrEmpty(systemName) || string.IsNullOrEmpty(objectiveId))
            {
                return Response.Error("System name and objective ID are required.");
            }

            // Find the system in the scene
            var systemObject = GameObject.Find(systemName);
            if (systemObject == null)
            {
                return Response.Error($"Objective Tracker System '{systemName}' not found in scene.");
            }

            return Response.Success($"Objective '{objectiveName}' added to tracker '{systemName}'.", new
            {
                systemName = systemName,
                objectiveId = objectiveId,
                objectiveName = objectiveName
            });
        }

        private static object CompleteObjective(JObject @params)
        {
            string systemName = @params["system_name"]?.ToString();
            string objectiveId = @params["objective_id"]?.ToString();

            if (string.IsNullOrEmpty(systemName) || string.IsNullOrEmpty(objectiveId))
            {
                return Response.Error("System name and objective ID are required.");
            }

            return Response.Success($"Objective '{objectiveId}' completed in tracker '{systemName}'.", new
            {
                systemName = systemName,
                objectiveId = objectiveId,
                status = "Completed"
            });
        }

        private static object FailObjective(JObject @params)
        {
            string systemName = @params["system_name"]?.ToString();
            string objectiveId = @params["objective_id"]?.ToString();

            if (string.IsNullOrEmpty(systemName) || string.IsNullOrEmpty(objectiveId))
            {
                return Response.Error("System name and objective ID are required.");
            }

            return Response.Success($"Objective '{objectiveId}' failed in tracker '{systemName}'.", new
            {
                systemName = systemName,
                objectiveId = objectiveId,
                status = "Failed"
            });
        }

        private static object UpdateObjectiveProgress(JObject @params)
        {
            string systemName = @params["system_name"]?.ToString();
            string objectiveId = @params["objective_id"]?.ToString();
            float progress = @params["progress"]?.ToObject<float>() ?? 0f;

            if (string.IsNullOrEmpty(systemName) || string.IsNullOrEmpty(objectiveId))
            {
                return Response.Error("System name and objective ID are required.");
            }

            return Response.Success($"Objective '{objectiveId}' progress updated to {progress:P0}.", new
            {
                systemName = systemName,
                objectiveId = objectiveId,
                progress = progress
            });
        }

        private static object GetActiveObjectives(JObject @params)
        {
            string systemName = @params["system_name"]?.ToString();

            if (string.IsNullOrEmpty(systemName))
            {
                return Response.Error("System name is required.");
            }

            return Response.Success($"Retrieved active objectives from tracker '{systemName}'.", new
            {
                systemName = systemName,
                activeObjectives = new[] {
                    new { id = "obj_001", name = "Sample Objective", progress = 0.5f, status = "Active" }
                }
            });
        }

        private static object ResetObjective(JObject @params)
        {
            string systemName = @params["system_name"]?.ToString();
            string objectiveId = @params["objective_id"]?.ToString();

            if (string.IsNullOrEmpty(systemName) || string.IsNullOrEmpty(objectiveId))
            {
                return Response.Error("System name and objective ID are required.");
            }

            return Response.Success($"Objective '{objectiveId}' reset in tracker '{systemName}'.", new
            {
                systemName = systemName,
                objectiveId = objectiveId,
                status = "Reset"
            });
        }

        private static string GenerateObjectiveScript()
        {
            return @"using UnityEngine;
using System.Collections.Generic;

namespace ObjectiveSystem
{
    [System.Serializable]
    public class Objective
    {
        [Header(""Objective Information"")]
        public string objectiveId;
        public string title;
        [TextArea(3, 5)]
        public string description;
        
        [Header(""Progress"")]
        public int currentProgress = 0;
        public int targetProgress = 1;
        public bool isCompleted = false;
        public bool isFailed = false;
        
        [Header(""Properties"")]
        public bool isOptional = false;
        public bool isHidden = false;
        public float timeLimit = 0f; // 0 = no limit
        
        [Header(""Rewards"")]
        public int experienceReward = 0;
        public string[] itemRewards;
        
        public float Progress => targetProgress > 0 ? (float)currentProgress / targetProgress : 0f;
        public bool IsActive => !isCompleted && !isFailed;

        public void UpdateProgress(int amount)
        {
            if (!IsActive) return;
            
            currentProgress = Mathf.Clamp(currentProgress + amount, 0, targetProgress);
            
            if (currentProgress >= targetProgress)
            {
                Complete();
            }
        }

        public void Complete()
        {
            if (isCompleted || isFailed) return;
            
            isCompleted = true;
            currentProgress = targetProgress;
        }

        public void Fail()
        {
            if (isCompleted || isFailed) return;
            
            isFailed = true;
        }

        public void Reset()
        {
            currentProgress = 0;
            isCompleted = false;
            isFailed = false;
        }
    }
}";
        }

        private static string GenerateObjectiveDataScript()
        {
            return @"using UnityEngine;

namespace ObjectiveSystem
{
    [CreateAssetMenu(fileName = ""New Objective Data"", menuName = ""Objective System/Objective Data"")]
    public class ObjectiveData : ScriptableObject
    {
        [Header(""Objective Template"")]
        public string objectiveId;
        public string title;
        [TextArea(3, 5)]
        public string description;
        
        [Header(""Requirements"")]
        public int targetProgress = 1;
        public bool isOptional = false;
        public bool isHidden = false;
        public float timeLimit = 0f;
        
        [Header(""Rewards"")]
        public int experienceReward = 0;
        public string[] itemRewards;
        
        [Header(""Dependencies"")]
        public string[] prerequisiteObjectives;
        public string[] blockedByObjectives;

        public Objective CreateObjective()
        {
            var objective = new Objective
            {
                objectiveId = objectiveId,
                title = title,
                description = description,
                targetProgress = targetProgress,
                isOptional = isOptional,
                isHidden = isHidden,
                timeLimit = timeLimit,
                experienceReward = experienceReward,
                itemRewards = itemRewards
            };
            
            return objective;
        }
    }
}";
        }

        private static string GenerateQuestSystemScript()
        {
            return @"using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using UnityEngine.Events;

namespace ObjectiveSystem
{
    public class QuestSystem : MonoBehaviour
    {
        [Header(""Quest Configuration"")]
        public bool enableDebugLogs = true;
        
        [Header(""Active Quests"")]
        [SerializeField] private List<Quest> activeQuests = new List<Quest>();
        [SerializeField] private List<Quest> completedQuests = new List<Quest>();
        [SerializeField] private List<Quest> failedQuests = new List<Quest>();
        
        [Header(""Events"")]
        public UnityEvent<Quest> OnQuestStarted;
        public UnityEvent<Quest> OnQuestCompleted;
        public UnityEvent<Quest> OnQuestFailed;
        public UnityEvent<Quest, Objective> OnObjectiveCompleted;

        public void StartQuest(string questId)
        {
            var questData = Resources.Load<QuestData>(questId);
            if (questData == null)
            {
                Debug.LogError($""Quest data not found: {questId}"");
                return;
            }

            var quest = questData.CreateQuest();
            activeQuests.Add(quest);
            
            OnQuestStarted?.Invoke(quest);
            
            if (enableDebugLogs)
                Debug.Log($""Quest started: {quest.title}"");
        }

        public void CompleteQuest(string questId)
        {
            var quest = activeQuests.FirstOrDefault(q => q.questId == questId);
            if (quest == null) return;

            quest.Complete();
            activeQuests.Remove(quest);
            completedQuests.Add(quest);
            
            OnQuestCompleted?.Invoke(quest);
            
            if (enableDebugLogs)
                Debug.Log($""Quest completed: {quest.title}"");
        }

        public void FailQuest(string questId)
        {
            var quest = activeQuests.FirstOrDefault(q => q.questId == questId);
            if (quest == null) return;

            quest.Fail();
            activeQuests.Remove(quest);
            failedQuests.Add(quest);
            
            OnQuestFailed?.Invoke(quest);
            
            if (enableDebugLogs)
                Debug.Log($""Quest failed: {quest.title}"");
        }

        public Quest GetQuest(string questId)
        {
            return activeQuests.FirstOrDefault(q => q.questId == questId) ??
                   completedQuests.FirstOrDefault(q => q.questId == questId) ??
                   failedQuests.FirstOrDefault(q => q.questId == questId);
        }

        public List<Quest> GetActiveQuests()
        {
            return new List<Quest>(activeQuests);
        }
    }

    [System.Serializable]
    public class Quest
    {
        public string questId;
        public string title;
        public string description;
        public List<Objective> objectives = new List<Objective>();
        public bool isCompleted = false;
        public bool isFailed = false;

        public void Complete()
        {
            isCompleted = true;
            foreach (var objective in objectives)
            {
                if (!objective.isCompleted)
                    objective.Complete();
            }
        }

        public void Fail()
        {
            isFailed = true;
        }
    }

    [CreateAssetMenu(fileName = ""New Quest Data"", menuName = ""Objective System/Quest Data"")]
    public class QuestData : ScriptableObject
    {
        public string questId;
        public string title;
        [TextArea(3, 5)]
        public string description;
        public ObjectiveData[] objectiveTemplates;

        public Quest CreateQuest()
        {
            var quest = new Quest
            {
                questId = questId,
                title = title,
                description = description
            };

            foreach (var template in objectiveTemplates)
            {
                quest.objectives.Add(template.CreateObjective());
            }

            return quest;
        }
    }
}";
        }
        #endregion
    }
}