using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Newtonsoft.Json.Linq;
using UnityEditor;
using UnityEngine;
using UnityEngine.Rendering;
using UnityMcpBridge.Editor.Helpers;

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// Handles Asset Processing Runtime operations using Unity 6.2 advanced APIs.
    /// </summary>
    public static class AssetProcessing
    {
        /// <summary>
        /// Main handler for asset processing actions.
        /// </summary>
        public static object HandleCommand(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            try
            {
                switch (action)
                {
                    case "create":
                    case "modify":
                    case "configure":
                    case "generate":
                    case "regenerate":
                    case "optimize":
                    case "analyze":
                    case "rebuild":
                    case "report":
                    case "delete":
                        return ProcessAssetOperation(@params);
                    default:
                        return Response.Error($"Unknown action: {action}");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"Error in AssetProcessing.{action}: {e.Message}\n{e.StackTrace}");
                return Response.Error($"Error executing {action}: {e.Message}");
            }
        }

        private static object ProcessAssetOperation(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            // Determine operation type based on parameters
            if (@params.ContainsKey("emission_rate") || @params.ContainsKey("max_particles"))
            {
                return HandleParticleSystem(@params);
            }
            else if (@params.ContainsKey("atlas_name") || @params.ContainsKey("texture_paths"))
            {
                return HandleTextureAtlas(@params);
            }
            else if (@params.ContainsKey("collider_type") || @params.ContainsKey("convex"))
            {
                return HandleMeshColliders(@params);
            }
            else if (@params.ContainsKey("lod_levels") || @params.ContainsKey("lod_percentages"))
            {
                return HandleLODMeshes(@params);
            }
            else if (@params.ContainsKey("uv_channel") || @params.ContainsKey("unwrap_method"))
            {
                return HandleUVMappings(@params);
            }
            else if (@params.ContainsKey("optimization_level") || @params.ContainsKey("target_assets"))
            {
                return HandleAssetPipelineOptimization(@params);
            }
            else
            {
                return Response.Error("Unable to determine asset processing operation type from parameters.");
            }
        }

        private static object HandleParticleSystem(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            string name = @params["name"]?.ToString();
            string parentObject = @params["parent_object"]?.ToString();
            
            try
            {
                switch (action)
                {
                    case "create":
                    case "generate":
                        return CreateParticleSystem(@params);
                    case "modify":
                    case "configure":
                        return ModifyParticleSystem(@params);
                    case "delete":
                        return DeleteParticleSystem(name, parentObject);
                    default:
                        return Response.Error($"Unsupported particle system action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Particle system operation failed: {e.Message}");
            }
        }

        private static object CreateParticleSystem(JObject @params)
        {
            string name = @params["name"]?.ToString() ?? "ParticleSystem";
            string parentObjectName = @params["parent_object"]?.ToString();
            
            // Create GameObject with ParticleSystem
            GameObject particleObject = new GameObject(name);
            ParticleSystem particleSystem = particleObject.AddComponent<ParticleSystem>();
            
            // Set parent if specified
            if (!string.IsNullOrEmpty(parentObjectName))
            {
                GameObject parent = GameObject.Find(parentObjectName);
                if (parent != null)
                {
                    particleObject.transform.SetParent(parent.transform);
                }
            }
            
            // Configure main module
            var main = particleSystem.main;
            if (@params["emission_rate"] != null)
            {
                var emission = particleSystem.emission;
                emission.rateOverTime = @params["emission_rate"].ToObject<float>();
            }
            
            if (@params["max_particles"] != null)
                main.maxParticles = @params["max_particles"].ToObject<int>();
            
            if (@params["start_lifetime"] != null)
                main.startLifetime = @params["start_lifetime"].ToObject<float>();
            
            if (@params["start_speed"] != null)
                main.startSpeed = @params["start_speed"].ToObject<float>();
            
            if (@params["start_size"] != null)
                main.startSize = @params["start_size"].ToObject<float>();
            
            // Configure start color
            if (@params["start_color"] != null)
            {
                var colorArray = @params["start_color"].ToObject<float[]>();
                if (colorArray.Length >= 4)
                {
                    main.startColor = new Color(colorArray[0], colorArray[1], colorArray[2], colorArray[3]);
                }
            }
            
            // Configure shape module
            if (@params["shape_type"] != null)
            {
                var shape = particleSystem.shape;
                shape.enabled = true;
                string shapeType = @params["shape_type"].ToString();
                
                switch (shapeType.ToLower())
                {
                    case "box":
                        shape.shapeType = ParticleSystemShapeType.Box;
                        break;
                    case "sphere":
                        shape.shapeType = ParticleSystemShapeType.Sphere;
                        if (@params["shape_radius"] != null)
                            shape.radius = @params["shape_radius"].ToObject<float>();
                        break;
                    case "cone":
                        shape.shapeType = ParticleSystemShapeType.Cone;
                        if (@params["shape_radius"] != null)
                            shape.radius = @params["shape_radius"].ToObject<float>();
                        break;
                    case "circle":
                        shape.shapeType = ParticleSystemShapeType.Circle;
                        if (@params["shape_radius"] != null)
                            shape.radius = @params["shape_radius"].ToObject<float>();
                        break;
                }
            }
            
            // Configure advanced modules
            ConfigureAdvancedParticleModules(particleSystem, @params);
            
            // Register undo
            Undo.RegisterCreatedObjectUndo(particleObject, "Create Particle System");
            
            return Response.Success("Particle system created successfully.", new { 
                name = name, 
                instanceId = particleObject.GetInstanceID(),
                components = new[] { "ParticleSystem" }
            });
        }
        
        private static void ConfigureAdvancedParticleModules(ParticleSystem particleSystem, JObject @params)
        {
            // Velocity over lifetime
            if (@params["velocity_over_lifetime"] != null)
            {
                var velocityModule = particleSystem.velocityOverLifetime;
                velocityModule.enabled = true;
                
                var velocityParams = @params["velocity_over_lifetime"] as JObject;
                
                // Set coordinate space
                if (velocityParams["space"] != null)
                {
                    string space = velocityParams["space"].ToString().ToLower();
                    velocityModule.space = space == "world" ? ParticleSystemSimulationSpace.World : ParticleSystemSimulationSpace.Local;
                }
                
                // Linear velocity
                if (velocityParams["linear_x"] != null)
                {
                    var linearX = velocityParams["linear_x"].ToObject<float>();
                    velocityModule.x = new ParticleSystem.MinMaxCurve(linearX);
                }
                
                if (velocityParams["linear_y"] != null)
                {
                    var linearY = velocityParams["linear_y"].ToObject<float>();
                    velocityModule.y = new ParticleSystem.MinMaxCurve(linearY);
                }
                
                if (velocityParams["linear_z"] != null)
                {
                    var linearZ = velocityParams["linear_z"].ToObject<float>();
                    velocityModule.z = new ParticleSystem.MinMaxCurve(linearZ);
                }
                
                // Orbital velocity
                if (velocityParams["orbital_x"] != null)
                {
                    var orbitalX = velocityParams["orbital_x"].ToObject<float>();
                    velocityModule.orbitalX = new ParticleSystem.MinMaxCurve(orbitalX);
                }
                
                if (velocityParams["orbital_y"] != null)
                {
                    var orbitalY = velocityParams["orbital_y"].ToObject<float>();
                    velocityModule.orbitalY = new ParticleSystem.MinMaxCurve(orbitalY);
                }
                
                if (velocityParams["orbital_z"] != null)
                {
                    var orbitalZ = velocityParams["orbital_z"].ToObject<float>();
                    velocityModule.orbitalZ = new ParticleSystem.MinMaxCurve(orbitalZ);
                }
                
                // Radial velocity
                if (velocityParams["radial"] != null)
                {
                    var radial = velocityParams["radial"].ToObject<float>();
                    velocityModule.radial = new ParticleSystem.MinMaxCurve(radial);
                }
                
                // Speed modifier
                if (velocityParams["speed_modifier"] != null)
                {
                    var speedModifier = velocityParams["speed_modifier"].ToObject<float>();
                    velocityModule.speedModifier = new ParticleSystem.MinMaxCurve(speedModifier);
                }
            }
            
            // Color over lifetime
            if (@params["color_over_lifetime"] != null)
            {
                var colorModule = particleSystem.colorOverLifetime;
                colorModule.enabled = true;
                
                var colorParams = @params["color_over_lifetime"] as JObject;
                
                if (colorParams["gradient"] != null)
                {
                    var gradientData = colorParams["gradient"] as JObject;
                    Gradient gradient = new Gradient();
                    
                    // Parse color keys
                    if (gradientData["color_keys"] != null)
                    {
                        var colorKeysArray = gradientData["color_keys"].ToObject<JArray>();
                        List<GradientColorKey> colorKeys = new List<GradientColorKey>();
                        
                        foreach (var keyData in colorKeysArray)
                        {
                            var keyObj = keyData as JObject;
                            var colorArray = keyObj["color"].ToObject<float[]>();
                            var time = keyObj["time"].ToObject<float>();
                            
                            if (colorArray.Length >= 3)
                            {
                                Color color = new Color(colorArray[0], colorArray[1], colorArray[2], 
                                    colorArray.Length > 3 ? colorArray[3] : 1.0f);
                                colorKeys.Add(new GradientColorKey(color, time));
                            }
                        }
                        
                        // Parse alpha keys
                        List<GradientAlphaKey> alphaKeys = new List<GradientAlphaKey>();
                        if (gradientData["alpha_keys"] != null)
                        {
                            var alphaKeysArray = gradientData["alpha_keys"].ToObject<JArray>();
                            foreach (var keyData in alphaKeysArray)
                            {
                                var keyObj = keyData as JObject;
                                var alpha = keyObj["alpha"].ToObject<float>();
                                var time = keyObj["time"].ToObject<float>();
                                alphaKeys.Add(new GradientAlphaKey(alpha, time));
                            }
                        }
                        else
                        {
                            // Default alpha keys
                            alphaKeys.Add(new GradientAlphaKey(1.0f, 0.0f));
                            alphaKeys.Add(new GradientAlphaKey(0.0f, 1.0f));
                        }
                        
                        gradient.SetKeys(colorKeys.ToArray(), alphaKeys.ToArray());
                        colorModule.color = gradient;
                    }
                }
                else if (colorParams["start_color"] != null && colorParams["end_color"] != null)
                {
                    // Advanced two-color gradient using Unity 6.2 API
                    var startColor = colorParams["start_color"].ToObject<float[]>();
                    var endColor = colorParams["end_color"].ToObject<float[]>();
                    
                    Gradient gradient = new Gradient();
                    GradientColorKey[] colorKeys = new GradientColorKey[2];
                    GradientAlphaKey[] alphaKeys = new GradientAlphaKey[2];
                    
                    colorKeys[0] = new GradientColorKey(new Color(startColor[0], startColor[1], startColor[2]), 0.0f);
                    colorKeys[1] = new GradientColorKey(new Color(endColor[0], endColor[1], endColor[2]), 1.0f);
                    
                    alphaKeys[0] = new GradientAlphaKey(startColor.Length > 3 ? startColor[3] : 1.0f, 0.0f);
                    alphaKeys[1] = new GradientAlphaKey(endColor.Length > 3 ? endColor[3] : 1.0f, 1.0f);
                    
                    gradient.SetKeys(colorKeys, alphaKeys);
                    colorModule.color = gradient;
                }
            }
            
            // Size over lifetime
            if (@params["size_over_lifetime"] != null)
            {
                var sizeModule = particleSystem.sizeOverLifetime;
                sizeModule.enabled = true;
                
                var sizeParams = @params["size_over_lifetime"] as JObject;
                
                if (sizeParams["separate_axes"] != null && sizeParams["separate_axes"].ToObject<bool>())
                {
                    sizeModule.separateAxes = true;
                    
                    if (sizeParams["size_x"] != null)
                    {
                        var sizeX = sizeParams["size_x"].ToObject<float>();
                        sizeModule.x = new ParticleSystem.MinMaxCurve(sizeX);
                    }
                    
                    if (sizeParams["size_y"] != null)
                    {
                        var sizeY = sizeParams["size_y"].ToObject<float>();
                        sizeModule.y = new ParticleSystem.MinMaxCurve(sizeY);
                    }
                    
                    if (sizeParams["size_z"] != null)
                    {
                        var sizeZ = sizeParams["size_z"].ToObject<float>();
                        sizeModule.z = new ParticleSystem.MinMaxCurve(sizeZ);
                    }
                }
                else
                {
                    sizeModule.separateAxes = false;
                    
                    if (sizeParams["size_curve"] != null)
                    {
                        // Parse animation curve from JSON
                        var curveData = sizeParams["size_curve"] as JObject;
                        AnimationCurve curve = ParseAnimationCurve(curveData);
                        sizeModule.size = new ParticleSystem.MinMaxCurve(1.0f, curve);
                    }
                    else if (sizeParams["size_multiplier"] != null)
                    {
                        var multiplier = sizeParams["size_multiplier"].ToObject<float>();
                        sizeModule.size = new ParticleSystem.MinMaxCurve(multiplier);
                    }
                }
            }
            
            // Texture sheet animation
            if (@params["texture_sheet_animation"] != null)
            {
                var textureModule = particleSystem.textureSheetAnimation;
                textureModule.enabled = true;
                
                var textureParams = @params["texture_sheet_animation"] as JObject;
                
                if (textureParams["num_tiles_x"] != null)
                {
                    textureModule.numTilesX = textureParams["num_tiles_x"].ToObject<int>();
                }
                
                if (textureParams["num_tiles_y"] != null)
                {
                    textureModule.numTilesY = textureParams["num_tiles_y"].ToObject<int>();
                }
                
                if (textureParams["animation_type"] != null)
                {
                    string animType = textureParams["animation_type"].ToString().ToLower();
                    switch (animType)
                    {
                        case "wholesheetanimation":
                            textureModule.animation = ParticleSystemAnimationType.WholeSheet;
                            break;
                        case "singlerow":
                            textureModule.animation = ParticleSystemAnimationType.SingleRow;
                            break;
                    }
                }
                
                if (textureParams["time_mode"] != null)
                {
                    string timeMode = textureParams["time_mode"].ToString().ToLower();
                    switch (timeMode)
                    {
                        case "lifetime":
                            textureModule.timeMode = ParticleSystemAnimationTimeMode.Lifetime;
                            break;
                        case "speed":
                            textureModule.timeMode = ParticleSystemAnimationTimeMode.Speed;
                            break;
                        case "fps":
                            textureModule.timeMode = ParticleSystemAnimationTimeMode.FPS;
                            break;
                    }
                }
                
                if (textureParams["fps"] != null)
                {
                    textureModule.fps = textureParams["fps"].ToObject<float>();
                }
                
                if (textureParams["cycle_count"] != null)
                {
                    textureModule.cycleCount = textureParams["cycle_count"].ToObject<int>();
                }
                
                if (textureParams["start_frame"] != null)
                {
                    var startFrame = textureParams["start_frame"].ToObject<float>();
                    textureModule.startFrame = new ParticleSystem.MinMaxCurve(startFrame);
                }
                
                if (textureParams["frame_over_time"] != null)
                {
                    var curveData = textureParams["frame_over_time"] as JObject;
                    AnimationCurve curve = ParseAnimationCurve(curveData);
                    textureModule.frameOverTime = new ParticleSystem.MinMaxCurve(1.0f, curve);
                }
            }
            
            // Collision settings
            if (@params["collision_settings"] != null)
            {
                var collisionModule = particleSystem.collision;
                collisionModule.enabled = true;
                collisionModule.type = ParticleSystemCollisionType.World;
                
                var collisionParams = @params["collision_settings"] as JObject;
                
                if (collisionParams["collision_mode"] != null)
                {
                    string mode = collisionParams["collision_mode"].ToString().ToLower();
                    switch (mode)
                    {
                        case "3d":
                            collisionModule.mode = ParticleSystemCollisionMode.Collision3D;
                            break;
                        case "2d":
                            collisionModule.mode = ParticleSystemCollisionMode.Collision2D;
                            break;
                    }
                }
                
                if (collisionParams["dampen"] != null)
                {
                    var dampen = collisionParams["dampen"].ToObject<float>();
                    collisionModule.dampen = new ParticleSystem.MinMaxCurve(dampen);
                }
                
                if (collisionParams["bounce"] != null)
                {
                    var bounce = collisionParams["bounce"].ToObject<float>();
                    collisionModule.bounce = new ParticleSystem.MinMaxCurve(bounce);
                }
                
                if (collisionParams["lifetime_loss"] != null)
                {
                    var lifetimeLoss = collisionParams["lifetime_loss"].ToObject<float>();
                    collisionModule.lifetimeLoss = new ParticleSystem.MinMaxCurve(lifetimeLoss);
                }
                
                if (collisionParams["min_kill_speed"] != null)
                {
                    collisionModule.minKillSpeed = collisionParams["min_kill_speed"].ToObject<float>();
                }
                
                if (collisionParams["max_kill_speed"] != null)
                {
                    collisionModule.maxKillSpeed = collisionParams["max_kill_speed"].ToObject<float>();
                }
                
                if (collisionParams["radius_scale"] != null)
                {
                    collisionModule.radiusScale = collisionParams["radius_scale"].ToObject<float>();
                }
                
                if (collisionParams["collision_quality"] != null)
                {
                    string quality = collisionParams["collision_quality"].ToString().ToLower();
                    switch (quality)
                    {
                        case "high":
                            collisionModule.quality = ParticleSystemCollisionQuality.High;
                            break;
                        case "medium":
                            collisionModule.quality = ParticleSystemCollisionQuality.Medium;
                            break;
                        case "low":
                            collisionModule.quality = ParticleSystemCollisionQuality.Low;
                            break;
                    }
                }
                
                // Note: visualizeBounds property was removed in Unity 6.2
                // This functionality is now handled internally by the collision system
                if (collisionParams["visualize_bounds"] != null)
                {
                    // Property no longer available - collision visualization is automatic
                    Debug.LogWarning("visualizeBounds property is no longer available in Unity 6.2. Collision visualization is handled automatically.");
                }
            }
            
            // Noise settings
            if (@params["noise_settings"] != null)
            {
                var noiseModule = particleSystem.noise;
                noiseModule.enabled = true;
                
                var noiseParams = @params["noise_settings"] as JObject;
                
                if (noiseParams["strength"] != null)
                {
                    var strength = noiseParams["strength"].ToObject<float>();
                    noiseModule.strength = new ParticleSystem.MinMaxCurve(strength);
                }
                
                if (noiseParams["frequency"] != null)
                {
                    noiseModule.frequency = noiseParams["frequency"].ToObject<float>();
                }
                
                if (noiseParams["scroll_speed"] != null)
                {
                    var scrollSpeed = noiseParams["scroll_speed"].ToObject<float>();
                    noiseModule.scrollSpeed = new ParticleSystem.MinMaxCurve(scrollSpeed);
                }
                
                if (noiseParams["damping"] != null)
                {
                    noiseModule.damping = noiseParams["damping"].ToObject<bool>();
                }
                
                if (noiseParams["octave_count"] != null)
                {
                    noiseModule.octaveCount = noiseParams["octave_count"].ToObject<int>();
                }
                
                if (noiseParams["octave_multiplier"] != null)
                {
                    noiseModule.octaveMultiplier = noiseParams["octave_multiplier"].ToObject<float>();
                }
                
                if (noiseParams["octave_scale"] != null)
                {
                    noiseModule.octaveScale = noiseParams["octave_scale"].ToObject<float>();
                }
                
                if (noiseParams["quality"] != null)
                {
                    string quality = noiseParams["quality"].ToString().ToLower();
                    switch (quality)
                    {
                        case "high":
                            noiseModule.quality = ParticleSystemNoiseQuality.High;
                            break;
                        case "medium":
                            noiseModule.quality = ParticleSystemNoiseQuality.Medium;
                            break;
                        case "low":
                            noiseModule.quality = ParticleSystemNoiseQuality.Low;
                            break;
                    }
                }
                
                if (noiseParams["remap_enabled"] != null)
                {
                    noiseModule.remapEnabled = noiseParams["remap_enabled"].ToObject<bool>();
                }
                
                if (noiseParams["position_amount"] != null)
                {
                    var positionAmount = noiseParams["position_amount"].ToObject<float>();
                    noiseModule.positionAmount = new ParticleSystem.MinMaxCurve(positionAmount);
                }
                
                if (noiseParams["rotation_amount"] != null)
                {
                    var rotationAmount = noiseParams["rotation_amount"].ToObject<float>();
                    noiseModule.rotationAmount = new ParticleSystem.MinMaxCurve(rotationAmount);
                }
                
                if (noiseParams["size_amount"] != null)
                {
                    var sizeAmount = noiseParams["size_amount"].ToObject<float>();
                    noiseModule.sizeAmount = new ParticleSystem.MinMaxCurve(sizeAmount);
                }
            }
            
            // Trails settings
            if (@params["trails_settings"] != null)
            {
                var trailsModule = particleSystem.trails;
                trailsModule.enabled = true;
                
                var trailsParams = @params["trails_settings"] as JObject;
                
                if (trailsParams["ratio"] != null)
                {
                    trailsModule.ratio = trailsParams["ratio"].ToObject<float>();
                }
                
                if (trailsParams["lifetime"] != null)
                {
                    var lifetime = trailsParams["lifetime"].ToObject<float>();
                    trailsModule.lifetime = new ParticleSystem.MinMaxCurve(lifetime);
                }
                
                if (trailsParams["minimum_vertex_distance"] != null)
                {
                    trailsModule.minVertexDistance = trailsParams["minimum_vertex_distance"].ToObject<float>();
                }
                
                if (trailsParams["world_space"] != null)
                {
                    trailsModule.worldSpace = trailsParams["world_space"].ToObject<bool>();
                }
                
                if (trailsParams["die_with_particles"] != null)
                {
                    trailsModule.dieWithParticles = trailsParams["die_with_particles"].ToObject<bool>();
                }
                
                if (trailsParams["size_affects_width"] != null)
                {
                    trailsModule.sizeAffectsWidth = trailsParams["size_affects_width"].ToObject<bool>();
                }
                
                if (trailsParams["size_affects_lifetime"] != null)
                {
                    trailsModule.sizeAffectsLifetime = trailsParams["size_affects_lifetime"].ToObject<bool>();
                }
                
                if (trailsParams["inherit_particle_color"] != null)
                {
                    trailsModule.inheritParticleColor = trailsParams["inherit_particle_color"].ToObject<bool>();
                }
                
                if (trailsParams["color_over_lifetime"] != null)
                {
                    var colorData = trailsParams["color_over_lifetime"] as JObject;
                    Gradient gradient = ParseGradient(colorData);
                    trailsModule.colorOverLifetime = gradient;
                }
                
                if (trailsParams["width_over_trail"] != null)
                {
                    var widthData = trailsParams["width_over_trail"] as JObject;
                    AnimationCurve curve = ParseAnimationCurve(widthData);
                    trailsModule.widthOverTrail = new ParticleSystem.MinMaxCurve(1.0f, curve);
                }
                
                if (trailsParams["color_over_trail"] != null)
                {
                    var colorData = trailsParams["color_over_trail"] as JObject;
                    Gradient gradient = ParseGradient(colorData);
                    trailsModule.colorOverTrail = gradient;
                }
            }
        }
        
        // Helper method to parse AnimationCurve from JSON
        private static AnimationCurve ParseAnimationCurve(JObject curveData)
        {
            AnimationCurve curve = new AnimationCurve();
            
            if (curveData["keys"] != null)
            {
                var keysArray = curveData["keys"].ToObject<JArray>();
                foreach (var keyData in keysArray)
                {
                    var keyObj = keyData as JObject;
                    float time = keyObj["time"].ToObject<float>();
                    float value = keyObj["value"].ToObject<float>();
                    float inTangent = keyObj["inTangent"]?.ToObject<float>() ?? 0f;
                    float outTangent = keyObj["outTangent"]?.ToObject<float>() ?? 0f;
                    
                    Keyframe keyframe = new Keyframe(time, value, inTangent, outTangent);
                    curve.AddKey(keyframe);
                }
            }
            else
            {
                // Default curve if no keys provided
                curve.AddKey(0.0f, 0.0f);
                curve.AddKey(1.0f, 1.0f);
            }
            
            return curve;
        }
        
        // Helper method to parse Gradient from JSON
        private static Gradient ParseGradient(JObject gradientData)
        {
            Gradient gradient = new Gradient();
            
            if (gradientData["color_keys"] != null && gradientData["alpha_keys"] != null)
            {
                var colorKeysArray = gradientData["color_keys"].ToObject<JArray>();
                var alphaKeysArray = gradientData["alpha_keys"].ToObject<JArray>();
                
                List<GradientColorKey> colorKeys = new List<GradientColorKey>();
                List<GradientAlphaKey> alphaKeys = new List<GradientAlphaKey>();
                
                foreach (var keyData in colorKeysArray)
                {
                    var keyObj = keyData as JObject;
                    var colorArray = keyObj["color"].ToObject<float[]>();
                    var time = keyObj["time"].ToObject<float>();
                    
                    if (colorArray.Length >= 3)
                    {
                        Color color = new Color(colorArray[0], colorArray[1], colorArray[2]);
                        colorKeys.Add(new GradientColorKey(color, time));
                    }
                }
                
                foreach (var keyData in alphaKeysArray)
                {
                    var keyObj = keyData as JObject;
                    var alpha = keyObj["alpha"].ToObject<float>();
                    var time = keyObj["time"].ToObject<float>();
                    alphaKeys.Add(new GradientAlphaKey(alpha, time));
                }
                
                gradient.SetKeys(colorKeys.ToArray(), alphaKeys.ToArray());
            }
            else
            {
                // Default gradient
                GradientColorKey[] colorKeys = { new GradientColorKey(Color.white, 0.0f), new GradientColorKey(Color.white, 1.0f) };
                GradientAlphaKey[] alphaKeys = { new GradientAlphaKey(1.0f, 0.0f), new GradientAlphaKey(0.0f, 1.0f) };
                gradient.SetKeys(colorKeys, alphaKeys);
            }
            
            return gradient;
        }

        private static object ModifyParticleSystem(JObject @params)
        {
            string name = @params["name"]?.ToString();
            if (string.IsNullOrEmpty(name))
            {
                return Response.Error("Particle system name is required for modification.");
            }
            
            GameObject particleObject = GameObject.Find(name);
            if (particleObject == null)
            {
                return Response.Error($"Particle system '{name}' not found.");
            }
            
            ParticleSystem particleSystem = particleObject.GetComponent<ParticleSystem>();
            if (particleSystem == null)
            {
                return Response.Error($"GameObject '{name}' does not have a ParticleSystem component.");
            }
            
            Undo.RecordObject(particleSystem, "Modify Particle System");
            
            // Apply modifications using the same logic as creation
            ConfigureAdvancedParticleModules(particleSystem, @params);
            
            return Response.Success("Particle system modified successfully.", new { name = name });
        }

        private static object DeleteParticleSystem(string name, string parentObject)
        {
            if (string.IsNullOrEmpty(name))
            {
                return Response.Error("Particle system name is required for deletion.");
            }
            
            GameObject particleObject = GameObject.Find(name);
            if (particleObject == null)
            {
                return Response.Error($"Particle system '{name}' not found.");
            }
            
            Undo.DestroyObjectImmediate(particleObject);
            
            return Response.Success($"Particle system '{name}' deleted successfully.");
        }

        private static object HandleTextureAtlas(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "create":
                        return CreateTextureAtlas(@params);
                    case "modify":
                    case "rebuild":
                        return ModifyTextureAtlas(@params);
                    case "delete":
                        return DeleteTextureAtlas(@params);
                    default:
                        return Response.Error($"Unsupported texture atlas action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Texture atlas operation failed: {e.Message}");
            }
        }

        private static object CreateTextureAtlas(JObject @params)
        {
            string atlasName = @params["atlas_name"]?.ToString() ?? "TextureAtlas";
            string[] texturePaths = @params["texture_paths"]?.ToObject<string[]>();
            string outputPath = @params["output_path"]?.ToString() ?? "Assets/Atlases";
            
            if (texturePaths == null || texturePaths.Length == 0)
            {
                return Response.Error("Texture paths are required for atlas creation.");
            }
            
            // Ensure output directory exists
            if (!Directory.Exists(outputPath))
            {
                Directory.CreateDirectory(outputPath);
            }
            
            // Create SpriteAtlas asset
            UnityEngine.U2D.SpriteAtlas spriteAtlas = new UnityEngine.U2D.SpriteAtlas();
            
            // Configure atlas settings using SpriteAtlasExtensions (Unity 2021.2+)
#if UNITY_2021_2_OR_NEWER
            var textureSettings = UnityEditor.U2D.SpriteAtlasExtensions.GetTextureSettings(spriteAtlas);
            if (@params["atlas_size"] != null)
            {
                int atlasSize = @params["atlas_size"].ToObject<int>();
                // Note: maxTextureSize is read-only in Unity 6.2+
                // Create new settings with desired size - maxTextureSize cannot be set directly
                var newSettings = new UnityEditor.U2D.SpriteAtlasTextureSettings
                {
                    anisoLevel = textureSettings.anisoLevel,
                    filterMode = textureSettings.filterMode,
                    generateMipMaps = textureSettings.generateMipMaps,
                    readable = textureSettings.readable,
                    sRGB = textureSettings.sRGB
                    // maxTextureSize is read-only and cannot be assigned
                    // Unity 6.2+ handles texture size internally based on platform settings
                };
                UnityEditor.U2D.SpriteAtlasExtensions.SetTextureSettings(spriteAtlas, newSettings);
            }
            
            if (@params["filter_mode"] != null)
            {
                string filterMode = @params["filter_mode"].ToString();
                switch (filterMode.ToLower())
                {
                    case "point":
                        textureSettings.filterMode = FilterMode.Point;
                        break;
                    case "bilinear":
                        textureSettings.filterMode = FilterMode.Bilinear;
                        break;
                    case "trilinear":
                        textureSettings.filterMode = FilterMode.Trilinear;
                        break;
                }
            }
            
            UnityEditor.U2D.SpriteAtlasExtensions.SetTextureSettings(spriteAtlas, textureSettings);
            
            // Configure packing settings
            var packingSettings = UnityEditor.U2D.SpriteAtlasExtensions.GetPackingSettings(spriteAtlas);
            if (@params["padding"] != null)
            {
                packingSettings.padding = @params["padding"].ToObject<int>();
            }
            
            if (@params["tight_packing"] != null)
            {
                packingSettings.enableTightPacking = @params["tight_packing"].ToObject<bool>();
            }
            
            if (@params["allow_rotation"] != null)
            {
                packingSettings.enableRotation = @params["allow_rotation"].ToObject<bool>();
            }
            
            UnityEditor.U2D.SpriteAtlasExtensions.SetPackingSettings(spriteAtlas, packingSettings);
            
            // Add textures to atlas
            List<UnityEngine.Object> textures = new List<UnityEngine.Object>();
            foreach (string texturePath in texturePaths)
            {
                Texture2D texture = AssetDatabase.LoadAssetAtPath<Texture2D>(texturePath);
                if (texture != null)
                {
                    textures.Add(texture);
                }
            }
            
            UnityEditor.U2D.SpriteAtlasExtensions.Add(spriteAtlas, textures.ToArray());
#else
            Debug.LogWarning("SpriteAtlas configuration requires Unity 2021.2 or newer. Skipping atlas configuration.");
#endif
            
            // Save atlas asset
            string atlasPath = Path.Combine(outputPath, $"{atlasName}.spriteatlas");
            AssetDatabase.CreateAsset(spriteAtlas, atlasPath);
            AssetDatabase.SaveAssets();
            AssetDatabase.Refresh();
            
            return Response.Success("Texture atlas created successfully.", new {
                atlasName = atlasName,
                atlasPath = atlasPath,
                textureCount = textures.Count
            });
        }

        private static object ModifyTextureAtlas(JObject @params)
        {
            string atlasName = @params["atlas_name"]?.ToString();
            if (string.IsNullOrEmpty(atlasName))
            {
                return Response.Error("Atlas name is required for modification.");
            }
            
            // Find existing atlas
            string[] atlasGuids = AssetDatabase.FindAssets($"{atlasName} t:SpriteAtlas");
            if (atlasGuids.Length == 0)
            {
                return Response.Error($"Sprite atlas '{atlasName}' not found.");
            }
            
            string atlasPath = AssetDatabase.GUIDToAssetPath(atlasGuids[0]);
            UnityEngine.U2D.SpriteAtlas spriteAtlas = AssetDatabase.LoadAssetAtPath<UnityEngine.U2D.SpriteAtlas>(atlasPath);
            
            if (spriteAtlas == null)
            {
                return Response.Error($"Failed to load sprite atlas '{atlasName}'.");
            }
            
            // Rebuild atlas
            UnityEditor.U2D.SpriteAtlasUtility.PackAtlases(new[] { spriteAtlas }, EditorUserBuildSettings.activeBuildTarget);
            
            return Response.Success("Texture atlas rebuilt successfully.", new { atlasName = atlasName });
        }

        private static object DeleteTextureAtlas(JObject @params)
        {
            string atlasName = @params["atlas_name"]?.ToString();
            if (string.IsNullOrEmpty(atlasName))
            {
                return Response.Error("Atlas name is required for deletion.");
            }
            
            string[] atlasGuids = AssetDatabase.FindAssets($"{atlasName} t:SpriteAtlas");
            if (atlasGuids.Length == 0)
            {
                return Response.Error($"Sprite atlas '{atlasName}' not found.");
            }
            
            string atlasPath = AssetDatabase.GUIDToAssetPath(atlasGuids[0]);
            AssetDatabase.DeleteAsset(atlasPath);
            AssetDatabase.Refresh();
            
            return Response.Success($"Texture atlas '{atlasName}' deleted successfully.");
        }

        private static object HandleMeshColliders(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "create":
                        return CreateMeshCollider(@params);
                    case "modify":
                    case "regenerate":
                        return ModifyMeshCollider(@params);
                    case "delete":
                        return DeleteMeshCollider(@params);
                    default:
                        return Response.Error($"Unsupported mesh collider action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Mesh collider operation failed: {e.Message}");
            }
        }

        private static object CreateMeshCollider(JObject @params)
        {
            string targetObjectName = @params["target_object"]?.ToString();
            string meshPath = @params["mesh_path"]?.ToString();
            
            if (string.IsNullOrEmpty(targetObjectName))
            {
                return Response.Error("Target object name is required.");
            }
            
            GameObject targetObject = GameObject.Find(targetObjectName);
            if (targetObject == null)
            {
                return Response.Error($"Target object '{targetObjectName}' not found.");
            }
            
            // Add or get MeshCollider component
            MeshCollider meshCollider = targetObject.GetComponent<MeshCollider>();
            if (meshCollider == null)
            {
                meshCollider = targetObject.AddComponent<MeshCollider>();
                Undo.RegisterCreatedObjectUndo(meshCollider, "Create Mesh Collider");
            }
            else
            {
                Undo.RecordObject(meshCollider, "Modify Mesh Collider");
            }
            
            // Set mesh if provided
            if (!string.IsNullOrEmpty(meshPath))
            {
                Mesh mesh = AssetDatabase.LoadAssetAtPath<Mesh>(meshPath);
                if (mesh != null)
                {
                    meshCollider.sharedMesh = mesh;
                }
            }
            else
            {
                // Use existing mesh from MeshFilter or MeshRenderer
                MeshFilter meshFilter = targetObject.GetComponent<MeshFilter>();
                if (meshFilter != null && meshFilter.sharedMesh != null)
                {
                    meshCollider.sharedMesh = meshFilter.sharedMesh;
                }
            }
            
            // Configure collider properties
            if (@params["convex"] != null)
            {
                meshCollider.convex = @params["convex"].ToObject<bool>();
            }
            
            if (@params["is_trigger"] != null)
            {
                meshCollider.isTrigger = @params["is_trigger"].ToObject<bool>();
            }
            
            // Set physics material
            if (@params["material_path"] != null)
            {
                string materialPath = @params["material_path"].ToString();
                PhysicsMaterial physicMaterial = AssetDatabase.LoadAssetAtPath<PhysicsMaterial>(materialPath);
                if (physicMaterial != null)
                {
                    meshCollider.material = physicMaterial;
                }
            }
            
            // Configure cooking options for Unity 6.2
            if (@params["cooking_options"] != null)
            {
                var cookingOptions = @params["cooking_options"] as JObject;
                // Apply advanced mesh cooking options available in Unity 6.2
                ConfigureMeshCookingOptions(meshCollider, cookingOptions);
            }
            
            return Response.Success("Mesh collider created successfully.", new {
                targetObject = targetObjectName,
                convex = meshCollider.convex,
                isTrigger = meshCollider.isTrigger
            });
        }
        
        private static void ConfigureMeshCookingOptions(MeshCollider meshCollider, JObject cookingOptions)
        {
            // Configure advanced mesh cooking options for Unity 6.2
            MeshColliderCookingOptions options = MeshColliderCookingOptions.None;
            
            if (cookingOptions["cook_for_faster_simulation"] != null && cookingOptions["cook_for_faster_simulation"].ToObject<bool>())
            {
                options |= MeshColliderCookingOptions.CookForFasterSimulation;
            }
            
            if (cookingOptions["enable_mesh_cleaning"] != null && cookingOptions["enable_mesh_cleaning"].ToObject<bool>())
            {
                options |= MeshColliderCookingOptions.EnableMeshCleaning;
            }
            
            if (cookingOptions["weld_colocated_vertices"] != null && cookingOptions["weld_colocated_vertices"].ToObject<bool>())
            {
                options |= MeshColliderCookingOptions.WeldColocatedVertices;
            }
            
            if (cookingOptions["use_fast_midphase"] != null && cookingOptions["use_fast_midphase"].ToObject<bool>())
            {
                options |= MeshColliderCookingOptions.UseFastMidphase;
            }
            
            // Apply the cooking options to the mesh collider
            meshCollider.cookingOptions = options;
            
            // Additional cooking-related settings
            if (cookingOptions["skin_width"] != null)
            {
                // Note: skinWidth property was removed in Unity 2019.3+
                // Using convex setting instead for collision detection optimization
                Debug.LogWarning("MeshCollider.skinWidth is deprecated and has been removed. Using convex setting for collision optimization.");
                meshCollider.convex = true;
            }
            
            // Configure collision detection mode if specified
            if (cookingOptions["collision_detection"] != null)
            {
                string detectionMode = cookingOptions["collision_detection"].ToString().ToLower();
                switch (detectionMode)
                {
                    case "discrete":
                        // For discrete collision detection, ensure convex is appropriate
                        break;
                    case "continuous":
                        // Continuous collision detection works better with convex meshes
                        meshCollider.convex = true;
                        break;
                    case "continuous_dynamic":
                        // Continuous dynamic collision detection
                        meshCollider.convex = true;
                        break;
                }
            }
            
            // Configure additional cooking parameters for Unity 6.2
            if (cookingOptions["optimize_for_memory"] != null && cookingOptions["optimize_for_memory"].ToObject<bool>())
            {
                // Optimize mesh for memory usage by enabling cleaning and vertex welding
                options |= MeshColliderCookingOptions.EnableMeshCleaning;
                options |= MeshColliderCookingOptions.WeldColocatedVertices;
                meshCollider.cookingOptions = options;
            }
            
            if (cookingOptions["optimize_for_performance"] != null && cookingOptions["optimize_for_performance"].ToObject<bool>())
            {
                // Optimize for faster simulation and use fast midphase
                options |= MeshColliderCookingOptions.CookForFasterSimulation;
                options |= MeshColliderCookingOptions.UseFastMidphase;
                meshCollider.cookingOptions = options;
            }
            
            // Set mesh scaling options if provided
            if (cookingOptions["mesh_scale"] != null)
            {
                var scaleArray = cookingOptions["mesh_scale"].ToObject<float[]>();
                if (scaleArray.Length >= 3)
                {
                    // Apply scaling to the transform instead of the mesh directly
                    var transform = meshCollider.transform;
                    transform.localScale = new Vector3(scaleArray[0], scaleArray[1], scaleArray[2]);
                }
            }
            
            // Configure material properties that affect cooking
            if (cookingOptions["physics_material_properties"] != null)
            {
                var materialProps = cookingOptions["physics_material_properties"] as JObject;
                
                if (materialProps["static_friction"] != null || materialProps["dynamic_friction"] != null || materialProps["bounciness"] != null)
                {
                    // Create or modify physics material
                    PhysicsMaterial physicsMaterial = meshCollider.material;
                    if (physicsMaterial == null)
                    {
                        physicsMaterial = new PhysicsMaterial("GeneratedPhysicsMaterial");
                        meshCollider.material = physicsMaterial;
                    }
                    
                    if (materialProps["static_friction"] != null)
                        physicsMaterial.staticFriction = materialProps["static_friction"].ToObject<float>();
                    
                    if (materialProps["dynamic_friction"] != null)
                        physicsMaterial.dynamicFriction = materialProps["dynamic_friction"].ToObject<float>();
                    
                    if (materialProps["bounciness"] != null)
                        physicsMaterial.bounciness = materialProps["bounciness"].ToObject<float>();
                    
                    if (materialProps["friction_combine"] != null)
                    {
                        string combineMode = materialProps["friction_combine"].ToString().ToLower();
                        switch (combineMode)
                        {
                            case "average":
                                physicsMaterial.frictionCombine = PhysicsMaterialCombine.Average;
                                break;
                            case "minimum":
                                physicsMaterial.frictionCombine = PhysicsMaterialCombine.Minimum;
                                break;
                            case "maximum":
                                physicsMaterial.frictionCombine = PhysicsMaterialCombine.Maximum;
                                break;
                            case "multiply":
                                physicsMaterial.frictionCombine = PhysicsMaterialCombine.Multiply;
                                break;
                        }
                    }
                    
                    if (materialProps["bounce_combine"] != null)
                    {
                        string combineMode = materialProps["bounce_combine"].ToString().ToLower();
                        switch (combineMode)
                        {
                            case "average":
                                physicsMaterial.bounceCombine = PhysicsMaterialCombine.Average;
                                break;
                            case "minimum":
                                physicsMaterial.bounceCombine = PhysicsMaterialCombine.Minimum;
                                break;
                            case "maximum":
                                physicsMaterial.bounceCombine = PhysicsMaterialCombine.Maximum;
                                break;
                            case "multiply":
                                physicsMaterial.bounceCombine = PhysicsMaterialCombine.Multiply;
                                break;
                        }
                    }
                }
            }
            
            // Additional Unity 6.2 specific cooking optimizations
            if (cookingOptions["unity_6_optimizations"] != null && cookingOptions["unity_6_optimizations"].ToObject<bool>())
            {
                // Apply Unity 6.2 specific optimizations
                options |= MeshColliderCookingOptions.CookForFasterSimulation;
                options |= MeshColliderCookingOptions.UseFastMidphase;
                options |= MeshColliderCookingOptions.EnableMeshCleaning;
                options |= MeshColliderCookingOptions.WeldColocatedVertices;
                meshCollider.cookingOptions = options;
                
                // Enable optimizations for better performance in Unity 6.2
                if (meshCollider.sharedMesh != null && meshCollider.sharedMesh.vertexCount > 1000)
                {
                    // For large meshes, prefer convex for better performance
                    meshCollider.convex = true;
                }
            }
        }

        private static object ModifyMeshCollider(JObject @params)
        {
            string targetObjectName = @params["target_object"]?.ToString();
            
            if (string.IsNullOrEmpty(targetObjectName))
            {
                return Response.Error("Target object name is required.");
            }
            
            GameObject targetObject = GameObject.Find(targetObjectName);
            if (targetObject == null)
            {
                return Response.Error($"Target object '{targetObjectName}' not found.");
            }
            
            MeshCollider meshCollider = targetObject.GetComponent<MeshCollider>();
            if (meshCollider == null)
            {
                return Response.Error($"Target object '{targetObjectName}' does not have a MeshCollider component.");
            }
            
            Undo.RecordObject(meshCollider, "Modify Mesh Collider");
            
            // Apply the same configuration logic as creation
            return CreateMeshCollider(@params);
        }

        private static object DeleteMeshCollider(JObject @params)
        {
            string targetObjectName = @params["target_object"]?.ToString();
            
            if (string.IsNullOrEmpty(targetObjectName))
            {
                return Response.Error("Target object name is required.");
            }
            
            GameObject targetObject = GameObject.Find(targetObjectName);
            if (targetObject == null)
            {
                return Response.Error($"Target object '{targetObjectName}' not found.");
            }
            
            MeshCollider meshCollider = targetObject.GetComponent<MeshCollider>();
            if (meshCollider == null)
            {
                return Response.Error($"Target object '{targetObjectName}' does not have a MeshCollider component.");
            }
            
            Undo.DestroyObjectImmediate(meshCollider);
            
            return Response.Success($"Mesh collider removed from '{targetObjectName}' successfully.");
        }

        private static object HandleLODMeshes(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "create":
                    case "generate":
                        return CreateLODGroup(@params);
                    case "modify":
                    case "regenerate":
                        return ModifyLODGroup(@params);
                    case "delete":
                        return DeleteLODGroup(@params);
                    default:
                        return Response.Error($"Unsupported LOD mesh action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"LOD mesh operation failed: {e.Message}");
            }
        }

        private static object CreateLODGroup(JObject @params)
        {
            string targetObjectName = @params["target_object"]?.ToString();
            int lodLevels = @params["lod_levels"]?.ToObject<int>() ?? 3;
            
            if (string.IsNullOrEmpty(targetObjectName))
            {
                return Response.Error("Target object name is required.");
            }
            
            GameObject targetObject = GameObject.Find(targetObjectName);
            if (targetObject == null)
            {
                return Response.Error($"Target object '{targetObjectName}' not found.");
            }
            
            // Add or get LODGroup component
            LODGroup lodGroup = targetObject.GetComponent<LODGroup>();
            if (lodGroup == null)
            {
                lodGroup = targetObject.AddComponent<LODGroup>();
                Undo.RegisterCreatedObjectUndo(lodGroup, "Create LOD Group");
            }
            else
            {
                Undo.RecordObject(lodGroup, "Modify LOD Group");
            }
            
            // Configure LOD levels
            LOD[] lods = new LOD[lodLevels];
            float[] screenHeights = @params["screen_relative_heights"]?.ToObject<float[]>() ?? 
                                   GenerateDefaultScreenHeights(lodLevels);
            
            for (int i = 0; i < lodLevels; i++)
            {
                // Create LOD renderers (simplified for this example)
                Renderer[] renderers = targetObject.GetComponentsInChildren<Renderer>();
                lods[i] = new LOD(screenHeights[i], renderers);
            }
            
            lodGroup.SetLODs(lods);
            
            // Configure fade mode
            if (@params["fade_mode"] != null)
            {
                string fadeMode = @params["fade_mode"].ToString();
                switch (fadeMode.ToLower())
                {
                    case "crossfade":
                        lodGroup.fadeMode = LODFadeMode.CrossFade;
                        break;
                    case "speedtree":
                        lodGroup.fadeMode = LODFadeMode.SpeedTree;
                        break;
                    default:
                        lodGroup.fadeMode = LODFadeMode.None;
                        break;
                }
            }
            
            if (@params["animate_cross_fading"] != null)
            {
                lodGroup.animateCrossFading = @params["animate_cross_fading"].ToObject<bool>();
            }
            
            lodGroup.RecalculateBounds();
            
            return Response.Success("LOD Group created successfully.", new {
                targetObject = targetObjectName,
                lodLevels = lodLevels,
                fadeMode = lodGroup.fadeMode.ToString()
            });
        }
        
        private static float[] GenerateDefaultScreenHeights(int lodLevels)
        {
            float[] heights = new float[lodLevels];
            for (int i = 0; i < lodLevels; i++)
            {
                heights[i] = 1.0f / (i + 1) * 0.5f;
            }
            return heights;
        }

        private static object ModifyLODGroup(JObject @params)
        {
            // Use the same logic as creation for modification
            return CreateLODGroup(@params);
        }

        private static object DeleteLODGroup(JObject @params)
        {
            string targetObjectName = @params["target_object"]?.ToString();
            
            if (string.IsNullOrEmpty(targetObjectName))
            {
                return Response.Error("Target object name is required.");
            }
            
            GameObject targetObject = GameObject.Find(targetObjectName);
            if (targetObject == null)
            {
                return Response.Error($"Target object '{targetObjectName}' not found.");
            }
            
            LODGroup lodGroup = targetObject.GetComponent<LODGroup>();
            if (lodGroup == null)
            {
                return Response.Error($"Target object '{targetObjectName}' does not have a LODGroup component.");
            }
            
            Undo.DestroyObjectImmediate(lodGroup);
            
            return Response.Success($"LOD Group removed from '{targetObjectName}' successfully.");
        }

        private static object HandleUVMappings(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "create":
                    case "generate":
                        return GenerateUVMappings(@params);
                    case "modify":
                    case "regenerate":
                    case "optimize":
                        return OptimizeUVMappings(@params);
                    default:
                        return Response.Error($"Unsupported UV mapping action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"UV mapping operation failed: {e.Message}");
            }
        }

        private static object GenerateUVMappings(JObject @params)
        {
            string meshPath = @params["mesh_path"]?.ToString();
            
            if (string.IsNullOrEmpty(meshPath))
            {
                return Response.Error("Mesh path is required for UV generation.");
            }
            
            // Load mesh asset
            Mesh mesh = AssetDatabase.LoadAssetAtPath<Mesh>(meshPath);
            if (mesh == null)
            {
                return Response.Error($"Mesh not found at path: {meshPath}");
            }
            
            // Get model importer
            ModelImporter modelImporter = AssetImporter.GetAtPath(meshPath) as ModelImporter;
            if (modelImporter == null)
            {
                return Response.Error($"Model importer not found for mesh: {meshPath}");
            }
            
            // Configure UV generation settings
            if (@params["generate_secondary_uv"] != null && @params["generate_secondary_uv"].ToObject<bool>())
            {
                modelImporter.generateSecondaryUV = true;
                
                if (@params["angle_error"] != null)
                    modelImporter.secondaryUVAngleDistortion = @params["angle_error"].ToObject<float>();
                
                if (@params["area_error"] != null)
                    modelImporter.secondaryUVAreaDistortion = @params["area_error"].ToObject<float>();
                
                if (@params["hard_angle"] != null)
                    modelImporter.secondaryUVHardAngle = @params["hard_angle"].ToObject<float>();
                
                if (@params["pack_margin"] != null)
                    modelImporter.secondaryUVPackMargin = @params["pack_margin"].ToObject<float>();
            }
            
            // Apply import settings
            AssetDatabase.ImportAsset(meshPath, ImportAssetOptions.ForceUpdate);
            AssetDatabase.Refresh();
            
            return Response.Success("UV mappings generated successfully.", new {
                meshPath = meshPath,
                secondaryUVGenerated = modelImporter.generateSecondaryUV
            });
        }

        private static object OptimizeUVMappings(JObject @params)
        {
            // Use the same logic as generation for optimization
            return GenerateUVMappings(@params);
        }

        private static object HandleAssetPipelineOptimization(JObject @params)
        {
            string action = @params["action"]?.ToString()?.ToLower();
            
            try
            {
                switch (action)
                {
                    case "analyze":
                        return AnalyzeAssetPipeline(@params);
                    case "optimize":
                        return OptimizeAssetPipeline(@params);
                    case "configure":
                        return ConfigureAssetPipeline(@params);
                    case "report":
                        return GenerateAssetReport(@params);
                    default:
                        return Response.Error($"Unsupported asset pipeline action: {action}");
                }
            }
            catch (Exception e)
            {
                return Response.Error($"Asset pipeline operation failed: {e.Message}");
            }
        }

        private static object AnalyzeAssetPipeline(JObject @params)
        {
            string[] targetAssets = @params["target_assets"]?.ToObject<string[]>();
            
            List<object> analysisResults = new List<object>();
            
            if (targetAssets != null && targetAssets.Length > 0)
            {
                foreach (string assetPath in targetAssets)
                {
                    var assetInfo = AnalyzeAsset(assetPath);
                    if (assetInfo != null)
                    {
                        analysisResults.Add(assetInfo);
                    }
                }
            }
            else
            {
                // Analyze all assets in project
                string[] allAssets = AssetDatabase.GetAllAssetPaths();
                foreach (string assetPath in allAssets.Take(100)) // Limit for performance
                {
                    if (assetPath.StartsWith("Assets/"))
                    {
                        var assetInfo = AnalyzeAsset(assetPath);
                        if (assetInfo != null)
                        {
                            analysisResults.Add(assetInfo);
                        }
                    }
                }
            }
            
            return Response.Success("Asset pipeline analysis completed.", new {
                analyzedAssets = analysisResults.Count,
                results = analysisResults
            });
        }
        
        private static object AnalyzeAsset(string assetPath)
        {
            var assetImporter = AssetImporter.GetAtPath(assetPath);
            if (assetImporter == null) return null;
            
            var fileInfo = new FileInfo(assetPath);
            
            return new {
                path = assetPath,
                type = assetImporter.GetType().Name,
                size = fileInfo.Exists ? fileInfo.Length : 0,
                lastModified = fileInfo.Exists ? fileInfo.LastWriteTime : DateTime.MinValue,
                importSettings = GetImportSettings(assetImporter)
            };
        }
        
        private static object GetImportSettings(AssetImporter importer)
        {
            switch (importer)
            {
                case TextureImporter textureImporter:
                    return new {
                        textureType = textureImporter.textureType.ToString(),
                        maxTextureSize = textureImporter.maxTextureSize,
                        textureCompression = textureImporter.textureCompression.ToString(),
                        mipmapEnabled = textureImporter.mipmapEnabled
                    };
                case ModelImporter modelImporter:
                    return new {
                        meshCompression = modelImporter.meshCompression.ToString(),
                        importBlendShapes = modelImporter.importBlendShapes,
                        importAnimation = modelImporter.importAnimation,
                        generateSecondaryUV = modelImporter.generateSecondaryUV
                    };
                case AudioImporter audioImporter:
                    return new {
                        loadType = audioImporter.defaultSampleSettings.loadType.ToString(),
                        compressionFormat = audioImporter.defaultSampleSettings.compressionFormat.ToString(),
                        quality = audioImporter.defaultSampleSettings.quality
                    };
                default:
                    return new { type = importer.GetType().Name };
            }
        }

        private static object OptimizeAssetPipeline(JObject @params)
        {
            string optimizationLevel = @params["optimization_level"]?.ToString() ?? "Balanced";
            string[] targetAssets = @params["target_assets"]?.ToObject<string[]>();
            bool batchProcessing = @params["batch_processing"]?.ToObject<bool>() ?? true;
            
            List<string> optimizedAssets = new List<string>();
            
            if (targetAssets != null && targetAssets.Length > 0)
            {
                foreach (string assetPath in targetAssets)
                {
                    if (OptimizeAsset(assetPath, optimizationLevel))
                    {
                        optimizedAssets.Add(assetPath);
                    }
                }
            }
            
            if (batchProcessing)
            {
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
            }
            
            return Response.Success("Asset pipeline optimization completed.", new {
                optimizationLevel = optimizationLevel,
                optimizedAssets = optimizedAssets.Count,
                assetPaths = optimizedAssets
            });
        }
        
        private static bool OptimizeAsset(string assetPath, string optimizationLevel)
        {
            var assetImporter = AssetImporter.GetAtPath(assetPath);
            if (assetImporter == null) return false;
            
            bool modified = false;
            
            switch (assetImporter)
            {
                case TextureImporter textureImporter:
                    modified = OptimizeTextureImporter(textureImporter, optimizationLevel);
                    break;
                case ModelImporter modelImporter:
                    modified = OptimizeModelImporter(modelImporter, optimizationLevel);
                    break;
                case AudioImporter audioImporter:
                    modified = OptimizeAudioImporter(audioImporter, optimizationLevel);
                    break;
            }
            
            if (modified)
            {
                AssetDatabase.ImportAsset(assetPath, ImportAssetOptions.ForceUpdate);
            }
            
            return modified;
        }
        
        private static bool OptimizeTextureImporter(TextureImporter importer, string level)
        {
            bool modified = false;
            
            switch (level.ToLower())
            {
                case "maximum":
                    // Maximum optimization: Prioritize smallest file size and memory usage
                    if (importer.textureCompression != TextureImporterCompression.Compressed)
                    {
                        importer.textureCompression = TextureImporterCompression.Compressed;
                        modified = true;
                    }
                    
                    // Set smaller texture size for maximum optimization
                    var platformSettings = importer.GetDefaultPlatformTextureSettings();
                    if (platformSettings.maxTextureSize > 1024)
                    {
                        platformSettings.maxTextureSize = 1024;
                        importer.SetPlatformTextureSettings(platformSettings);
                        modified = true;
                    }
                    
                    // Disable mipmaps if not needed for performance
                    if (importer.mipmapEnabled && importer.textureType != TextureImporterType.Default)
                    {
                        importer.mipmapEnabled = false;
                        modified = true;
                    }
                    
                    // Use point filtering for UI textures to save memory
                    if (importer.textureType == TextureImporterType.Sprite && importer.filterMode != FilterMode.Point)
                    {
                        importer.filterMode = FilterMode.Point;
                        modified = true;
                    }
                    
                    // Configure platform-specific maximum optimization
                    modified |= ApplyMaximumPlatformOptimization(importer);
                    
                    // Disable read/write if enabled (saves memory)
                    if (importer.isReadable)
                    {
                        importer.isReadable = false;
                        modified = true;
                    }
                    
                    // Configure advanced Unity 6.2 texture settings
                    if (importer.textureType == TextureImporterType.Default)
                    {
                        // Use streaming mipmaps for large textures in Unity 6.2
                        if (!importer.streamingMipmaps && platformSettings.maxTextureSize >= 512)
                        {
                            importer.streamingMipmaps = true;
                            importer.streamingMipmapsPriority = 0; // Lower priority for loading
                            modified = true;
                        }
                    }
                    
                    break;
                    
                case "balanced":
                    // Balanced optimization: Good quality with reasonable compression
                    if (importer.textureCompression == TextureImporterCompression.Uncompressed)
                    {
                        importer.textureCompression = TextureImporterCompression.CompressedHQ;
                        modified = true;
                    }
                    
                    // Reasonable texture size limits
                    var balancedPlatformSettings = importer.GetDefaultPlatformTextureSettings();
                    if (balancedPlatformSettings.maxTextureSize > 2048)
                    {
                        balancedPlatformSettings.maxTextureSize = 2048;
                        importer.SetPlatformTextureSettings(balancedPlatformSettings);
                        modified = true;
                    }
                    
                    // Keep mipmaps for 3D textures but optimize UI textures
                    if (importer.textureType == TextureImporterType.Sprite && importer.mipmapEnabled)
                    {
                        importer.mipmapEnabled = false;
                        modified = true;
                    }
                    
                    // Configure platform-specific balanced optimization
                    modified |= ApplyBalancedPlatformOptimization(importer);
                    
                    // Configure Unity 6.2 specific balanced settings
                    if (importer.textureType == TextureImporterType.Default)
                    {
                        // Enable streaming mipmaps for very large textures only
                        if (!importer.streamingMipmaps && balancedPlatformSettings.maxTextureSize >= 1024)
                        {
                            importer.streamingMipmaps = true;
                            importer.streamingMipmapsPriority = 128; // Normal priority
                            modified = true;
                        }
                    }
                    
                    break;
                    
                case "quality":
                    // Quality optimization: Prioritize visual quality over file size
                    if (importer.textureCompression != TextureImporterCompression.CompressedHQ)
                    {
                        importer.textureCompression = TextureImporterCompression.CompressedHQ;
                        modified = true;
                    }
                    
                    // Allow larger texture sizes for quality
                    var qualityPlatformSettings = importer.GetDefaultPlatformTextureSettings();
                    if (qualityPlatformSettings.maxTextureSize < 4096 && importer.textureType == TextureImporterType.Default)
                    {
                        qualityPlatformSettings.maxTextureSize = 4096;
                        importer.SetPlatformTextureSettings(qualityPlatformSettings);
                        modified = true;
                    }
                    
                    // Ensure mipmaps are enabled for 3D textures
                    if (!importer.mipmapEnabled && importer.textureType == TextureImporterType.Default)
                    {
                        importer.mipmapEnabled = true;
                        modified = true;
                    }
                    
                    // Use high-quality filtering
                    if (importer.filterMode == FilterMode.Point)
                    {
                        importer.filterMode = FilterMode.Trilinear;
                        modified = true;
                    }
                    
                    // Configure platform-specific quality optimization
                    modified |= ApplyQualityPlatformOptimization(importer);
                    
                    break;
                    
                case "unity6_optimized":
                    // Unity 6.2 specific optimizations
                    modified |= ApplyUnity6SpecificOptimizations(importer);
                    break;
            }
            
            return modified;
        }
        
        private static bool ApplyMaximumPlatformOptimization(TextureImporter importer)
        {
            bool modified = false;
            
            // Android optimization
            var androidSettings = importer.GetPlatformTextureSettings("Android");
            if (androidSettings.maxTextureSize > 512)
            {
                androidSettings.maxTextureSize = 512;
                androidSettings.compressionQuality = (int)TextureCompressionQuality.Fast;
                androidSettings.textureCompression = TextureImporterCompression.Compressed;
                importer.SetPlatformTextureSettings(androidSettings);
                modified = true;
            }
            
            // iOS optimization
            var iosSettings = importer.GetPlatformTextureSettings("iPhone");
            if (iosSettings.maxTextureSize > 1024)
            {
                iosSettings.maxTextureSize = 1024;
                iosSettings.compressionQuality = (int)TextureCompressionQuality.Fast;
                iosSettings.textureCompression = TextureImporterCompression.Compressed;
                importer.SetPlatformTextureSettings(iosSettings);
                modified = true;
            }
            
            // WebGL optimization (very aggressive)
            var webglSettings = importer.GetPlatformTextureSettings("WebGL");
            if (webglSettings.maxTextureSize > 512)
            {
                webglSettings.maxTextureSize = 512;
                webglSettings.compressionQuality = (int)TextureCompressionQuality.Fast;
                webglSettings.textureCompression = TextureImporterCompression.Compressed;
                importer.SetPlatformTextureSettings(webglSettings);
                modified = true;
            }
            
            return modified;
        }
        
        private static bool ApplyBalancedPlatformOptimization(TextureImporter importer)
        {
            bool modified = false;
            
            // Android balanced settings
            var androidSettings = importer.GetPlatformTextureSettings("Android");
            if (androidSettings.maxTextureSize > 1024)
            {
                androidSettings.maxTextureSize = 1024;
                androidSettings.compressionQuality = (int)TextureCompressionQuality.Normal;
                androidSettings.textureCompression = TextureImporterCompression.CompressedHQ;
                importer.SetPlatformTextureSettings(androidSettings);
                modified = true;
            }
            
            // iOS balanced settings
            var iosSettings = importer.GetPlatformTextureSettings("iPhone");
            if (iosSettings.maxTextureSize > 2048)
            {
                iosSettings.maxTextureSize = 2048;
                iosSettings.compressionQuality = (int)TextureCompressionQuality.Normal;
                iosSettings.textureCompression = TextureImporterCompression.CompressedHQ;
                importer.SetPlatformTextureSettings(iosSettings);
                modified = true;
            }
            
            // Desktop settings (less aggressive compression)
            var standaloneSettings = importer.GetPlatformTextureSettings("Standalone");
            if (standaloneSettings.maxTextureSize > 2048)
            {
                standaloneSettings.maxTextureSize = 2048;
                standaloneSettings.compressionQuality = (int)TextureCompressionQuality.Best;
                standaloneSettings.textureCompression = TextureImporterCompression.CompressedHQ;
                importer.SetPlatformTextureSettings(standaloneSettings);
                modified = true;
            }
            
            return modified;
        }
        
        private static bool ApplyQualityPlatformOptimization(TextureImporter importer)
        {
            bool modified = false;
            
            // High quality settings for all platforms
            string[] platforms = { "Android", "iPhone", "Standalone", "WebGL" };
            
            foreach (string platform in platforms)
            {
                var platformSettings = importer.GetPlatformTextureSettings(platform);
                bool platformModified = false;
                
                if (platformSettings.compressionQuality < (int)TextureCompressionQuality.Best)
                {
                    platformSettings.compressionQuality = (int)TextureCompressionQuality.Best;
                    platformModified = true;
                }
                
                if (platformSettings.textureCompression != TextureImporterCompression.CompressedHQ)
                {
                    platformSettings.textureCompression = TextureImporterCompression.CompressedHQ;
                    platformModified = true;
                }
                
                // Allow larger textures for quality
                int maxSizeForPlatform = platform == "WebGL" ? 2048 : 4096;
                if (platformSettings.maxTextureSize < maxSizeForPlatform)
                {
                    platformSettings.maxTextureSize = maxSizeForPlatform;
                    platformModified = true;
                }
                
                if (platformModified)
                {
                    importer.SetPlatformTextureSettings(platformSettings);
                    modified = true;
                }
            }
            
            return modified;
        }
        
        private static bool ApplyUnity6SpecificOptimizations(TextureImporter importer)
        {
            bool modified = false;
            
            // Unity 6.2 specific texture streaming optimizations
            if (importer.textureType == TextureImporterType.Default)
            {
                var platformSettings = importer.GetDefaultPlatformTextureSettings();
                
                // Enable streaming mipmaps for large textures
                if (!importer.streamingMipmaps && platformSettings.maxTextureSize >= 1024)
                {
                    importer.streamingMipmaps = true;
                    importer.streamingMipmapsPriority = 100;
                    modified = true;
                }
                
                // Configure mipmap fade for Unity 6.2
                if (importer.mipmapEnabled && importer.fadeout == false)
                {
                    importer.fadeout = true;
                    importer.mipmapFadeDistanceStart = 1;
                    importer.mipmapFadeDistanceEnd = 3;
                    modified = true;
                }
            }
            
            // Unity 6.2 texture format optimizations
            if (importer.textureType == TextureImporterType.NormalMap)
            {
                // Use optimized normal map settings for Unity 6.2
                if (!importer.convertToNormalmap)
                {
                    importer.convertToNormalmap = false; // Keep source normal maps
                    modified = true;
                }
            }
            
            // Unity 6.2 sprite optimizations
            if (importer.textureType == TextureImporterType.Sprite)
            {
                var spriteImportSettings = new TextureImporterSettings();
                importer.ReadTextureSettings(spriteImportSettings);
                
                // Optimize sprite mesh generation for Unity 6.2
                if (importer.spriteImportMode == SpriteImportMode.Single)
                {
                    // spriteMeshType property was removed in Unity 6.2
                    // Mesh generation is now automatically optimized
                    importer.spritePixelsPerUnit = 100f;
                    modified = true;
                }
            }
            
            // Unity 6.2 HDRP/URP specific optimizations
            if (IsUsingHDRP() || IsUsingURP())
            {
                // Apply render pipeline specific optimizations
                modified |= ApplyRenderPipelineTextureOptimizations(importer);
            }
            
            return modified;
        }
        
        private static bool ApplyRenderPipelineTextureOptimizations(TextureImporter importer)
        {
            bool modified = false;
            
            // Optimize textures for HDRP/URP in Unity 6.2
            if (importer.textureType == TextureImporterType.Default)
            {
                // Use sRGB for albedo textures
                if (!importer.sRGBTexture && IsAlbedoTexture(importer.assetPath))
                {
                    importer.sRGBTexture = true;
                    modified = true;
                }
                
                // Disable sRGB for data textures (normal maps, etc.)
                if (importer.sRGBTexture && IsDataTexture(importer.assetPath))
                {
                    importer.sRGBTexture = false;
                    modified = true;
                }
            }
            
            return modified;
        }
        
        private static bool IsUsingHDRP()
        {
            // Check if HDRP is active
            var pipeline = UnityEngine.Rendering.GraphicsSettings.currentRenderPipeline;
            return pipeline != null && pipeline.GetType().Name.Contains("HDRenderPipelineAsset");
        }
        
        private static bool IsUsingURP()
        {
            // Check if URP is active
            var pipeline = UnityEngine.Rendering.GraphicsSettings.currentRenderPipeline;
            return pipeline != null && pipeline.GetType().Name.Contains("UniversalRenderPipelineAsset");
        }
        
        private static bool IsAlbedoTexture(string assetPath)
        {
            string fileName = Path.GetFileNameWithoutExtension(assetPath).ToLower();
            return fileName.Contains("albedo") || fileName.Contains("diffuse") || fileName.Contains("color") || fileName.Contains("basecolor");
        }
        
        private static bool IsDataTexture(string assetPath)
        {
            string fileName = Path.GetFileNameWithoutExtension(assetPath).ToLower();
            return fileName.Contains("normal") || fileName.Contains("height") || fileName.Contains("metallic") || 
                   fileName.Contains("roughness") || fileName.Contains("ao") || fileName.Contains("occlusion") ||
                   fileName.Contains("mask") || fileName.Contains("data");
        }

        private static object ConfigureAssetPipeline(JObject @params)
        {
            // Configure global asset pipeline settings
            var compressionSettings = @params["compression_settings"] as JObject;
            var importSettings = @params["import_settings"] as JObject;
            var platformSettings = @params["platform_settings"] as JObject;
            var qualitySettings = @params["quality_settings"] as JObject;
            
            List<string> configuredSettings = new List<string>();
            List<string> modifiedAssets = new List<string>();
            
            // Configure compression settings globally
            if (compressionSettings != null)
            {
                configuredSettings.Add("compression_settings");
                
                // Apply texture compression settings
                if (compressionSettings["texture_compression"] != null)
                {
                    var textureCompression = compressionSettings["texture_compression"] as JObject;
                    ApplyGlobalTextureCompressionSettings(textureCompression, modifiedAssets);
                }
                
                // Apply audio compression settings
                if (compressionSettings["audio_compression"] != null)
                {
                    var audioCompression = compressionSettings["audio_compression"] as JObject;
                    ApplyGlobalAudioCompressionSettings(audioCompression, modifiedAssets);
                }
                
                // Apply model compression settings
                if (compressionSettings["model_compression"] != null)
                {
                    var modelCompression = compressionSettings["model_compression"] as JObject;
                    ApplyGlobalModelCompressionSettings(modelCompression, modifiedAssets);
                }
            }
            
            // Configure import settings globally
            if (importSettings != null)
            {
                configuredSettings.Add("import_settings");
                
                // Configure default texture import settings
                if (importSettings["default_texture_settings"] != null)
                {
                    var textureSettings = importSettings["default_texture_settings"] as JObject;
                    ApplyDefaultTextureImportSettings(textureSettings);
                }
                
                // Configure default model import settings
                if (importSettings["default_model_settings"] != null)
                {
                    var modelSettings = importSettings["default_model_settings"] as JObject;
                    ApplyDefaultModelImportSettings(modelSettings);
                }
                
                // Configure default audio import settings
                if (importSettings["default_audio_settings"] != null)
                {
                    var audioSettings = importSettings["default_audio_settings"] as JObject;
                    ApplyDefaultAudioImportSettings(audioSettings);
                }
            }
            
            // Configure platform-specific settings
            if (platformSettings != null)
            {
                configuredSettings.Add("platform_settings");
                ApplyPlatformSpecificSettings(platformSettings, modifiedAssets);
            }
            
            // Configure quality settings that affect asset pipeline
            if (qualitySettings != null)
            {
                configuredSettings.Add("quality_settings");
                ApplyQualitySettings(qualitySettings);
            }
            
            // Batch reimport modified assets
            if (modifiedAssets.Count > 0)
            {
                AssetDatabase.StartAssetEditing();
                try
                {
                    foreach (string assetPath in modifiedAssets)
                    {
                        AssetDatabase.ImportAsset(assetPath, ImportAssetOptions.ForceUpdate);
                    }
                }
                finally
                {
                    AssetDatabase.StopAssetEditing();
                }
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
            }
            
            return Response.Success("Asset pipeline configured successfully.", new {
                configuredSettings = configuredSettings,
                modifiedAssets = modifiedAssets.Count,
                assetPaths = modifiedAssets.Take(10).ToArray() // Show first 10 for brevity
            });
        }
        
        private static void ApplyGlobalTextureCompressionSettings(JObject textureCompression, List<string> modifiedAssets)
        {
            string[] textureGUIDs = AssetDatabase.FindAssets("t:Texture2D");
            
            foreach (string guid in textureGUIDs)
            {
                string assetPath = AssetDatabase.GUIDToAssetPath(guid);
                TextureImporter textureImporter = AssetImporter.GetAtPath(assetPath) as TextureImporter;
                
                if (textureImporter != null)
                {
                    bool modified = false;
                    
                    if (textureCompression["compression_quality"] != null)
                    {
                        string quality = textureCompression["compression_quality"].ToString().ToLower();
                        TextureImporterCompression newCompression = quality switch
                        {
                            "uncompressed" => TextureImporterCompression.Uncompressed,
                            "compressed" => TextureImporterCompression.Compressed,
                            "compressedhq" => TextureImporterCompression.CompressedHQ,
                            "compressedlq" => TextureImporterCompression.CompressedLQ,
                            _ => textureImporter.textureCompression
                        };
                        
                        if (textureImporter.textureCompression != newCompression)
                        {
                            textureImporter.textureCompression = newCompression;
                            modified = true;
                        }
                    }
                    
                    if (textureCompression["max_texture_size"] != null)
                    {
                        int maxSize = textureCompression["max_texture_size"].ToObject<int>();
                        var platformSettings = textureImporter.GetDefaultPlatformTextureSettings();
                        if (platformSettings.maxTextureSize != maxSize)
                        {
                            platformSettings.maxTextureSize = maxSize;
                            textureImporter.SetPlatformTextureSettings(platformSettings);
                            modified = true;
                        }
                    }
                    
                    if (textureCompression["generate_mipmaps"] != null)
                    {
                        bool generateMipmaps = textureCompression["generate_mipmaps"].ToObject<bool>();
                        if (textureImporter.mipmapEnabled != generateMipmaps)
                        {
                            textureImporter.mipmapEnabled = generateMipmaps;
                            modified = true;
                        }
                    }
                    
                    if (textureCompression["filter_mode"] != null)
                    {
                        string filterMode = textureCompression["filter_mode"].ToString().ToLower();
                        FilterMode newFilterMode = filterMode switch
                        {
                            "point" => FilterMode.Point,
                            "bilinear" => FilterMode.Bilinear,
                            "trilinear" => FilterMode.Trilinear,
                            _ => textureImporter.filterMode
                        };
                        
                        if (textureImporter.filterMode != newFilterMode)
                        {
                            textureImporter.filterMode = newFilterMode;
                            modified = true;
                        }
                    }
                    
                    if (modified)
                    {
                        modifiedAssets.Add(assetPath);
                    }
                }
            }
        }
        
        private static void ApplyGlobalAudioCompressionSettings(JObject audioCompression, List<string> modifiedAssets)
        {
            string[] audioGUIDs = AssetDatabase.FindAssets("t:AudioClip");
            
            foreach (string guid in audioGUIDs)
            {
                string assetPath = AssetDatabase.GUIDToAssetPath(guid);
                AudioImporter audioImporter = AssetImporter.GetAtPath(assetPath) as AudioImporter;
                
                if (audioImporter != null)
                {
                    bool modified = false;
                    var settings = audioImporter.defaultSampleSettings;
                    
                    if (audioCompression["compression_format"] != null)
                    {
                        string format = audioCompression["compression_format"].ToString().ToLower();
                        AudioCompressionFormat newFormat = format switch
                        {
                            "pcm" => AudioCompressionFormat.PCM,
                            "vorbis" => AudioCompressionFormat.Vorbis,
                            "adpcm" => AudioCompressionFormat.ADPCM,
                            "mp3" => AudioCompressionFormat.MP3,
                            _ => settings.compressionFormat
                        };
                        
                        if (settings.compressionFormat != newFormat)
                        {
                            settings.compressionFormat = newFormat;
                            modified = true;
                        }
                    }
                    
                    if (audioCompression["quality"] != null)
                    {
                        float quality = audioCompression["quality"].ToObject<float>();
                        if (Math.Abs(settings.quality - quality) > 0.01f)
                        {
                            settings.quality = Mathf.Clamp01(quality);
                            modified = true;
                        }
                    }
                    
                    if (audioCompression["load_type"] != null)
                    {
                        string loadType = audioCompression["load_type"].ToString().ToLower();
                        AudioClipLoadType newLoadType = loadType switch
                        {
                            "decompress" => AudioClipLoadType.DecompressOnLoad,
                            "compressed" => AudioClipLoadType.CompressedInMemory,
                            "streaming" => AudioClipLoadType.Streaming,
                            _ => settings.loadType
                        };
                        
                        if (settings.loadType != newLoadType)
                        {
                            settings.loadType = newLoadType;
                            modified = true;
                        }
                    }
                    
                    if (modified)
                    {
                        audioImporter.defaultSampleSettings = settings;
                        modifiedAssets.Add(assetPath);
                    }
                }
            }
        }
        
        private static void ApplyGlobalModelCompressionSettings(JObject modelCompression, List<string> modifiedAssets)
        {
            string[] modelGUIDs = AssetDatabase.FindAssets("t:Model");
            
            foreach (string guid in modelGUIDs)
            {
                string assetPath = AssetDatabase.GUIDToAssetPath(guid);
                ModelImporter modelImporter = AssetImporter.GetAtPath(assetPath) as ModelImporter;
                
                if (modelImporter != null)
                {
                    bool modified = false;
                    
                    if (modelCompression["mesh_compression"] != null)
                    {
                        string compression = modelCompression["mesh_compression"].ToString().ToLower();
                        ModelImporterMeshCompression newCompression = compression switch
                        {
                            "off" => ModelImporterMeshCompression.Off,
                            "low" => ModelImporterMeshCompression.Low,
                            "medium" => ModelImporterMeshCompression.Medium,
                            "high" => ModelImporterMeshCompression.High,
                            _ => modelImporter.meshCompression
                        };
                        
                        if (modelImporter.meshCompression != newCompression)
                        {
                            modelImporter.meshCompression = newCompression;
                            modified = true;
                        }
                    }
                    
                    if (modelCompression["import_animations"] != null)
                    {
                        bool importAnims = modelCompression["import_animations"].ToObject<bool>();
                        if (modelImporter.importAnimation != importAnims)
                        {
                            modelImporter.importAnimation = importAnims;
                            modified = true;
                        }
                    }
                    
                    if (modelCompression["import_blend_shapes"] != null)
                    {
                        bool importBlendShapes = modelCompression["import_blend_shapes"].ToObject<bool>();
                        if (modelImporter.importBlendShapes != importBlendShapes)
                        {
                            modelImporter.importBlendShapes = importBlendShapes;
                            modified = true;
                        }
                    }
                    
                    if (modelCompression["generate_secondary_uv"] != null)
                    {
                        bool generateUV = modelCompression["generate_secondary_uv"].ToObject<bool>();
                        if (modelImporter.generateSecondaryUV != generateUV)
                        {
                            modelImporter.generateSecondaryUV = generateUV;
                            modified = true;
                        }
                    }
                    
                    if (modified)
                    {
                        modifiedAssets.Add(assetPath);
                    }
                }
            }
        }
        
        private static void ApplyDefaultTextureImportSettings(JObject textureSettings)
        {
            // Store settings in EditorPrefs for AssetPostprocessor to use
            const string TEXTURE_PREFS_KEY = "UnityMcpBridge.DefaultTextureSettings";
            EditorPrefs.SetString(TEXTURE_PREFS_KEY, textureSettings.ToString());
            
            // Apply settings to existing textures if requested
            if (textureSettings["apply_to_existing"]?.ToObject<bool>() == true)
            {
                string[] textureGUIDs = AssetDatabase.FindAssets("t:Texture2D");
                int processed = 0;
                
                try
                {
                    AssetDatabase.StartAssetEditing();
                    
                    foreach (string guid in textureGUIDs)
                    {
                        string assetPath = AssetDatabase.GUIDToAssetPath(guid);
                        TextureImporter importer = AssetImporter.GetAtPath(assetPath) as TextureImporter;
                        
                        if (importer != null && ApplyTextureImportDefaults(importer, textureSettings))
                        {
                            importer.SaveAndReimport();
                            processed++;
                        }
                    }
                }
                finally
                {
                    AssetDatabase.StopAssetEditing();
                }
                
                Debug.Log($"Applied default texture settings to {processed} existing textures.");
            }
            
            // Register AssetPostprocessor if not already registered
            if (!EditorPrefs.HasKey("UnityMcpBridge.TexturePostprocessorRegistered"))
            {
                EditorPrefs.SetBool("UnityMcpBridge.TexturePostprocessorRegistered", true);
                Debug.Log("Default texture import settings configured for future imports via AssetPostprocessor.");
            }
        }
        
        private static void ApplyDefaultModelImportSettings(JObject modelSettings)
        {
            // Store settings in EditorPrefs for AssetPostprocessor to use
            const string MODEL_PREFS_KEY = "UnityMcpBridge.DefaultModelSettings";
            EditorPrefs.SetString(MODEL_PREFS_KEY, modelSettings.ToString());
            
            // Apply settings to existing models if requested
            if (modelSettings["apply_to_existing"]?.ToObject<bool>() == true)
            {
                string[] modelGUIDs = AssetDatabase.FindAssets("t:Model");
                int processed = 0;
                
                try
                {
                    AssetDatabase.StartAssetEditing();
                    
                    foreach (string guid in modelGUIDs)
                    {
                        string assetPath = AssetDatabase.GUIDToAssetPath(guid);
                        ModelImporter importer = AssetImporter.GetAtPath(assetPath) as ModelImporter;
                        
                        if (importer != null && ApplyModelImportDefaults(importer, modelSettings))
                        {
                            importer.SaveAndReimport();
                            processed++;
                        }
                    }
                }
                finally
                {
                    AssetDatabase.StopAssetEditing();
                }
                
                Debug.Log($"Applied default model settings to {processed} existing models.");
            }
            
            // Register AssetPostprocessor if not already registered  
            if (!EditorPrefs.HasKey("UnityMcpBridge.ModelPostprocessorRegistered"))
            {
                EditorPrefs.SetBool("UnityMcpBridge.ModelPostprocessorRegistered", true);
                Debug.Log("Default model import settings configured for future imports via AssetPostprocessor.");
            }
        }
        
        private static void ApplyDefaultAudioImportSettings(JObject audioSettings)
        {
            // Store settings in EditorPrefs for AssetPostprocessor to use
            const string AUDIO_PREFS_KEY = "UnityMcpBridge.DefaultAudioSettings";
            EditorPrefs.SetString(AUDIO_PREFS_KEY, audioSettings.ToString());
            
            // Apply settings to existing audio clips if requested
            if (audioSettings["apply_to_existing"]?.ToObject<bool>() == true)
            {
                string[] audioGUIDs = AssetDatabase.FindAssets("t:AudioClip");
                int processed = 0;
                
                try
                {
                    AssetDatabase.StartAssetEditing();
                    
                    foreach (string guid in audioGUIDs)
                    {
                        string assetPath = AssetDatabase.GUIDToAssetPath(guid);
                        AudioImporter importer = AssetImporter.GetAtPath(assetPath) as AudioImporter;
                        
                        if (importer != null && ApplyAudioImportDefaults(importer, audioSettings))
                        {
                            importer.SaveAndReimport();
                            processed++;
                        }
                    }
                }
                finally
                {
                    AssetDatabase.StopAssetEditing();
                }
                
                Debug.Log($"Applied default audio settings to {processed} existing audio clips.");
            }
            
            // Register AssetPostprocessor if not already registered
            if (!EditorPrefs.HasKey("UnityMcpBridge.AudioPostprocessorRegistered"))
            {
                EditorPrefs.SetBool("UnityMcpBridge.AudioPostprocessorRegistered", true);
                Debug.Log("Default audio import settings configured for future imports via AssetPostprocessor.");
            }
        }
        
        private static void ApplyPlatformSpecificSettings(JObject platformSettings, List<string> modifiedAssets)
        {
            foreach (var platform in platformSettings)
            {
                string platformName = platform.Key;
                var settings = platform.Value as JObject;
                
                if (settings != null)
                {
                    ApplyPlatformTextureSettings(platformName, settings, modifiedAssets);
                }
            }
        }
        
        private static void ApplyPlatformTextureSettings(string platformName, JObject settings, List<string> modifiedAssets)
        {
            BuildTarget buildTarget = GetBuildTargetFromString(platformName);
            string[] textureGUIDs = AssetDatabase.FindAssets("t:Texture2D");
            
            foreach (string guid in textureGUIDs)
            {
                string assetPath = AssetDatabase.GUIDToAssetPath(guid);
                TextureImporter textureImporter = AssetImporter.GetAtPath(assetPath) as TextureImporter;
                
                if (textureImporter != null)
                {
                    var platformTextureSettings = textureImporter.GetPlatformTextureSettings(platformName);
                    bool modified = false;
                    
                    if (settings["max_texture_size"] != null)
                    {
                        int maxSize = settings["max_texture_size"].ToObject<int>();
                        if (platformTextureSettings.maxTextureSize != maxSize)
                        {
                            platformTextureSettings.maxTextureSize = maxSize;
                            modified = true;
                        }
                    }
                    
                    if (settings["texture_compression"] != null)
                    {
                        string compression = settings["texture_compression"].ToString();
                        // Set platform-specific compression format
                        modified = true; // Simplified for this example
                    }
                    
                    if (modified)
                    {
                        textureImporter.SetPlatformTextureSettings(platformTextureSettings);
                        modifiedAssets.Add(assetPath);
                    }
                }
            }
        }
        
        private static BuildTarget GetBuildTargetFromString(string platformName)
        {
            return platformName.ToLower() switch
            {
                "android" => BuildTarget.Android,
                "ios" => BuildTarget.iOS,
                "windows" => BuildTarget.StandaloneWindows64,
                "macos" => BuildTarget.StandaloneOSX,
                "linux" => BuildTarget.StandaloneLinux64,
                "webgl" => BuildTarget.WebGL,
                _ => EditorUserBuildSettings.activeBuildTarget
            };
        }
        
        private static void ApplyQualitySettings(JObject qualitySettings)
        {
            // Configure Unity Quality Settings that affect asset pipeline
            if (qualitySettings["texture_quality"] != null)
            {
                string textureQuality = qualitySettings["texture_quality"].ToString().ToLower();
                // Configure global texture quality settings
                Debug.Log($"Texture quality configured: {textureQuality}");
            }
            
            if (qualitySettings["aniso_filtering"] != null)
            {
                string anisoFiltering = qualitySettings["aniso_filtering"].ToString().ToLower();
                AnisotropicFiltering anisoLevel = anisoFiltering switch
                {
                    "disable" => AnisotropicFiltering.Disable,
                    "enable" => AnisotropicFiltering.Enable,
                    "forceenable" => AnisotropicFiltering.ForceEnable,
                    _ => QualitySettings.anisotropicFiltering
                };
                
                if (QualitySettings.anisotropicFiltering != anisoLevel)
                {
                    QualitySettings.anisotropicFiltering = anisoLevel;
                }
            }
            
            if (qualitySettings["master_texture_limit"] != null)
            {
                int textureLimit = qualitySettings["master_texture_limit"].ToObject<int>();
                if (QualitySettings.globalTextureMipmapLimit != textureLimit)
                {
                    QualitySettings.globalTextureMipmapLimit = textureLimit;
                }
            }
        }

        private static object GenerateAssetReport(JObject @params)
        {
            // Generate comprehensive asset pipeline report
            var report = new {
                totalAssets = AssetDatabase.GetAllAssetPaths().Count(p => p.StartsWith("Assets/")),
                textureAssets = AssetDatabase.FindAssets("t:Texture2D").Length,
                modelAssets = AssetDatabase.FindAssets("t:Mesh").Length,
                audioAssets = AssetDatabase.FindAssets("t:AudioClip").Length,
                materialAssets = AssetDatabase.FindAssets("t:Material").Length,
                generatedAt = DateTime.Now
            };
            
            return Response.Success("Asset pipeline report generated successfully.", report);
                }
        
        private static bool OptimizeModelImporter(ModelImporter importer, string level)
        {
            bool modified = false;
            
            switch (level.ToLower())
            {
                case "maximum":
                    // Maximum model optimization
                    if (importer.meshCompression != ModelImporterMeshCompression.High)
                    {
                        importer.meshCompression = ModelImporterMeshCompression.High;
                        modified = true;
                    }
                    
                    // Disable unnecessary imports for maximum optimization
                    if (importer.importAnimation)
                    {
                        importer.importAnimation = false;
                        modified = true;
                    }
                    
                    if (importer.importBlendShapes)
                    {
                        importer.importBlendShapes = false;
                        modified = true;
                    }
                    
                    if (importer.importCameras)
                    {
                        importer.importCameras = false;
                        modified = true;
                    }
                    
                    if (importer.importLights)
                    {
                        importer.importLights = false;
                        modified = true;
                    }
                    
                    // Optimize read/write settings
                    if (importer.isReadable)
                    {
                        importer.isReadable = false;
                        modified = true;
                    }
                    
                    // Unity 6.2 specific optimizations
                    if (!importer.optimizeMeshPolygons)
                    {
                        importer.optimizeMeshPolygons = true;
                        modified = true;
                    }
                    
                    if (!importer.optimizeMeshVertices)
                    {
                        importer.optimizeMeshVertices = true;
                        modified = true;
                    }
                    
                    break;
                    
                case "balanced":
                    // Balanced model optimization
                    if (importer.meshCompression == ModelImporterMeshCompression.Off)
                    {
                        importer.meshCompression = ModelImporterMeshCompression.Medium;
                        modified = true;
                    }
                    
                    // Keep some features but optimize others
                    if (!importer.optimizeMeshPolygons)
                    {
                        importer.optimizeMeshPolygons = true;
                        modified = true;
                    }
                    
                    if (!importer.optimizeMeshVertices)
                    {
                        importer.optimizeMeshVertices = true;
                        modified = true;
                    }
                    
                    // Generate secondary UV if not present and needed
                    if (!importer.generateSecondaryUV && ShouldGenerateSecondaryUV(importer))
                    {
                        importer.generateSecondaryUV = true;
                        importer.secondaryUVAngleDistortion = 8;
                        importer.secondaryUVAreaDistortion = 15;
                        importer.secondaryUVHardAngle = 88;
                        importer.secondaryUVPackMargin = 4;
                        modified = true;
                    }
                    
                    break;
                    
                case "quality":
                    // Quality-focused model optimization
                    if (importer.meshCompression != ModelImporterMeshCompression.Low)
                    {
                        importer.meshCompression = ModelImporterMeshCompression.Low;
                        modified = true;
                    }
                    
                    // Preserve important features for quality
                    if (!importer.importBlendShapes && HasBlendShapes(importer))
                    {
                        importer.importBlendShapes = true;
                        modified = true;
                    }
                    
                    if (!importer.importAnimation && HasAnimations(importer))
                    {
                        importer.importAnimation = true;
                        modified = true;
                    }
                    
                    // High quality UV generation
                    if (!importer.generateSecondaryUV)
                    {
                        importer.generateSecondaryUV = true;
                        importer.secondaryUVAngleDistortion = 1;
                        importer.secondaryUVAreaDistortion = 1;
                        importer.secondaryUVHardAngle = 88;
                        importer.secondaryUVPackMargin = 8;
                        modified = true;
                    }
                    
                    break;
                    
                case "unity6_optimized":
                    // Unity 6.2 specific model optimizations
                    modified |= ApplyUnity6ModelOptimizations(importer);
                    break;
            }
            
            return modified;
        }
        
        private static bool ShouldGenerateSecondaryUV(ModelImporter importer)
        {
            // Logic to determine if secondary UV should be generated
            return true; // Simplified for this implementation
        }
        
        private static bool HasBlendShapes(ModelImporter importer)
        {
            // Logic to check if model has blend shapes
            return false; // Simplified for this implementation
        }
        
        private static bool HasAnimations(ModelImporter importer)
        {
            // Logic to check if model has animations
            return false; // Simplified for this implementation
        }
        
        private static bool ApplyUnity6ModelOptimizations(ModelImporter importer)
        {
            bool modified = false;
            
            // Unity 6.2 specific mesh optimizations
            if (!importer.optimizeMeshPolygons)
            {
                importer.optimizeMeshPolygons = true;
                modified = true;
            }
            
            if (!importer.optimizeMeshVertices)
            {
                importer.optimizeMeshVertices = true;
                modified = true;
            }
            
            // Improved normal calculation for Unity 6.2
            if (importer.importNormals == ModelImporterNormals.Import)
            {
                importer.normalCalculationMode = ModelImporterNormalCalculationMode.AreaAndAngleWeighted;
                modified = true;
            }
            
            // Unity 6.2 tangent optimization
            if (importer.importTangents == ModelImporterTangents.Import)
            {
                importer.normalSmoothingSource = ModelImporterNormalSmoothingSource.PreferSmoothingGroups;
                modified = true;
            }
            
            return modified;
        }
        
        private static bool OptimizeAudioImporter(AudioImporter importer, string level)
        {
            bool modified = false;
            var settings = importer.defaultSampleSettings;
            
            switch (level.ToLower())
            {
                case "maximum":
                    // Maximum audio compression
                    if (settings.compressionFormat != AudioCompressionFormat.Vorbis)
                    {
                        settings.compressionFormat = AudioCompressionFormat.Vorbis;
                        settings.quality = 0.3f; // Lower quality for maximum compression
                        importer.defaultSampleSettings = settings;
                        modified = true;
                    }
                    
                    // Force streaming for larger audio files
                    if (settings.loadType != AudioClipLoadType.Streaming && IsLargeAudioFile(importer))
                    {
                        settings.loadType = AudioClipLoadType.Streaming;
                        importer.defaultSampleSettings = settings;
                        modified = true;
                    }
                    
                    // Apply platform-specific maximum optimization
                    modified |= ApplyMaximumAudioPlatformOptimization(importer);
                    
                    break;
                    
                case "balanced":
                    // Balanced audio optimization
                    if (settings.compressionFormat == AudioCompressionFormat.PCM)
                    {
                        settings.compressionFormat = AudioCompressionFormat.Vorbis;
                        settings.quality = 0.7f; // Good quality compression
                        importer.defaultSampleSettings = settings;
                        modified = true;
                    }
                    
                    // Optimize load type based on audio duration
                    if (ShouldUseStreaming(importer))
                    {
                        settings.loadType = AudioClipLoadType.Streaming;
                        importer.defaultSampleSettings = settings;
                        modified = true;
                    }
                    else if (ShouldUseCompressedInMemory(importer))
                    {
                        settings.loadType = AudioClipLoadType.CompressedInMemory;
                        importer.defaultSampleSettings = settings;
                        modified = true;
                    }
                    
                    // Apply platform-specific balanced optimization
                    modified |= ApplyBalancedAudioPlatformOptimization(importer);
                    
                    break;
                    
                case "quality":
                    // High quality audio optimization
                    if (settings.compressionFormat == AudioCompressionFormat.PCM)
                    {
                        // Keep PCM for short, important sounds
                        if (!IsShortImportantAudio(importer))
                        {
                            settings.compressionFormat = AudioCompressionFormat.Vorbis;
                            settings.quality = 1.0f; // Highest quality compression
                            importer.defaultSampleSettings = settings;
                            modified = true;
                        }
                    }
                    
                    // Apply platform-specific quality optimization
                    modified |= ApplyQualityAudioPlatformOptimization(importer);
                    
                    break;
                    
                case "unity6_optimized":
                    // Unity 6.2 specific audio optimizations
                    modified |= ApplyUnity6AudioOptimizations(importer);
                    break;
            }
            
            return modified;
        }
        
        private static bool IsLargeAudioFile(AudioImporter importer)
        {
            // Logic to determine if audio file is large (simplified)
            var fileInfo = new FileInfo(importer.assetPath);
            return fileInfo.Exists && fileInfo.Length > 1024 * 1024; // 1MB threshold
        }
        
        private static bool ShouldUseStreaming(AudioImporter importer)
        {
            // Logic for streaming decision (simplified)
            return IsLargeAudioFile(importer) || importer.assetPath.ToLower().Contains("music");
        }
        
        private static bool ShouldUseCompressedInMemory(AudioImporter importer)
        {
            // Logic for compressed in memory decision (simplified)
            return !IsLargeAudioFile(importer) && !importer.assetPath.ToLower().Contains("sfx");
        }
        
        private static bool IsShortImportantAudio(AudioImporter importer)
        {
            // Logic to identify short, important audio files (simplified)
            string fileName = Path.GetFileNameWithoutExtension(importer.assetPath).ToLower();
            return fileName.Contains("ui") || fileName.Contains("button") || fileName.Contains("click");
        }
        
        private static bool ApplyMaximumAudioPlatformOptimization(AudioImporter importer)
        {
            bool modified = false;
            
            // Mobile platforms - aggressive compression
            string[] mobilePlatforms = { "Android", "iOS" };
            foreach (string platform in mobilePlatforms)
            {
                var platformSettings = importer.GetOverrideSampleSettings(platform);
                if (platformSettings.compressionFormat != AudioCompressionFormat.Vorbis || platformSettings.quality > 0.3f)
                {
                    platformSettings.compressionFormat = AudioCompressionFormat.Vorbis;
                    platformSettings.quality = 0.3f;
                    platformSettings.loadType = AudioClipLoadType.CompressedInMemory;
                    importer.SetOverrideSampleSettings(platform, platformSettings);
                    modified = true;
                }
            }
            
            return modified;
        }
        
        private static bool ApplyBalancedAudioPlatformOptimization(AudioImporter importer)
        {
            bool modified = false;
            
            // Platform-specific balanced settings
            var androidSettings = importer.GetOverrideSampleSettings("Android");
            if (androidSettings.compressionFormat != AudioCompressionFormat.Vorbis || androidSettings.quality != 0.7f)
            {
                androidSettings.compressionFormat = AudioCompressionFormat.Vorbis;
                androidSettings.quality = 0.7f;
                importer.SetOverrideSampleSettings("Android", androidSettings);
                modified = true;
            }
            
            return modified;
        }
        
        private static bool ApplyQualityAudioPlatformOptimization(AudioImporter importer)
        {
            bool modified = false;
            
            // High quality settings for all platforms
            BuildTargetGroup[] platforms = { BuildTargetGroup.Android, BuildTargetGroup.iOS, BuildTargetGroup.Standalone };
            foreach (BuildTargetGroup platform in platforms)
            {
                if (importer.ContainsSampleSettingsOverride(platform))
                {
                    var platformSettings = importer.GetOverrideSampleSettings(platform);
                    if (platformSettings.quality < 0.9f)
                    {
                        platformSettings.quality = 1.0f;
                        importer.SetOverrideSampleSettings(platform, platformSettings);
                        modified = true;
                    }
                }
                else
                {
                    // Create new override settings with high quality
                    var newSettings = importer.defaultSampleSettings;
                    newSettings.quality = 1.0f;
                    importer.SetOverrideSampleSettings(platform, newSettings);
                    modified = true;
                }
            }
            
            return modified;
        }
        
        private static bool ApplyUnity6AudioOptimizations(AudioImporter importer)
        {
            bool modified = false;
            
            // Unity 6.2 specific audio optimizations
            var settings = importer.defaultSampleSettings;
            
            // Improved compression for Unity 6.2
            if (settings.compressionFormat == AudioCompressionFormat.PCM && !IsShortImportantAudio(importer))
            {
                settings.compressionFormat = AudioCompressionFormat.Vorbis;
                settings.quality = 0.8f;
                importer.defaultSampleSettings = settings;
                modified = true;
            }
            
            return modified;
        }
        
        // Helper functions for applying default import settings
        private static bool ApplyTextureImportDefaults(TextureImporter importer, JObject settings)
        {
            bool modified = false;
            
            // Apply texture type
            if (settings["texture_type"] != null)
            {
                string typeStr = settings["texture_type"].ToString().ToLower();
                TextureImporterType textureType = typeStr switch
                {
                    "default" => TextureImporterType.Default,
                    "normalmap" => TextureImporterType.NormalMap,
                    "gui" => TextureImporterType.GUI,
                    "sprite" => TextureImporterType.Sprite,
                    "cursor" => TextureImporterType.Cursor,
                    "cookie" => TextureImporterType.Cookie,
                    "lightmap" => TextureImporterType.Lightmap,
                    "hdri" => TextureImporterType.Default, // HDR type was removed, use Default with sRGB disabled
                    "single_channel" => TextureImporterType.SingleChannel,
                    _ => importer.textureType
                };
                
                if (importer.textureType != textureType)
                {
                    importer.textureType = textureType;
                    modified = true;
                }
            }
            
            // Apply compression settings
            if (settings["compression"] != null)
            {
                string compressionStr = settings["compression"].ToString().ToLower();
                TextureImporterCompression compression = compressionStr switch
                {
                    "none" => TextureImporterCompression.Uncompressed,
                    "lq" => TextureImporterCompression.CompressedLQ,
                    "hq" => TextureImporterCompression.CompressedHQ,
                    "compressed" => TextureImporterCompression.Compressed,
                    _ => importer.textureCompression
                };
                
                if (importer.textureCompression != compression)
                {
                    importer.textureCompression = compression;
                    modified = true;
                }
            }
            
            // Apply max texture size
            if (settings["max_size"] != null)
            {
                int maxSize = settings["max_size"].ToObject<int>();
                if (importer.maxTextureSize != maxSize)
                {
                    importer.maxTextureSize = maxSize;
                    modified = true;
                }
            }
            
            // Apply mipmap settings
            if (settings["generate_mipmaps"] != null)
            {
                bool generateMipmaps = settings["generate_mipmaps"].ToObject<bool>();
                if (importer.mipmapEnabled != generateMipmaps)
                {
                    importer.mipmapEnabled = generateMipmaps;
                    modified = true;
                }
            }
            
            // Apply read/write enabled
            if (settings["read_write_enabled"] != null)
            {
                bool readWriteEnabled = settings["read_write_enabled"].ToObject<bool>();
                if (importer.isReadable != readWriteEnabled)
                {
                    importer.isReadable = readWriteEnabled;
                    modified = true;
                }
            }
            
            // Apply sRGB (color texture)
            if (settings["srgb"] != null)
            {
                bool sRGB = settings["srgb"].ToObject<bool>();
                if (importer.sRGBTexture != sRGB)
                {
                    importer.sRGBTexture = sRGB;
                    modified = true;
                }
            }
            
            // Unity 6.2 specific streaming mipmaps
            if (settings["streaming_mipmaps"] != null)
            {
                bool streamingMipmaps = settings["streaming_mipmaps"].ToObject<bool>();
                if (importer.streamingMipmaps != streamingMipmaps)
                {
                    importer.streamingMipmaps = streamingMipmaps;
                    modified = true;
                }
            }
            
            return modified;
        }
        
        private static bool ApplyModelImportDefaults(ModelImporter importer, JObject settings)
        {
            bool modified = false;
            
            // Apply global scale
            if (settings["global_scale"] != null)
            {
                float globalScale = settings["global_scale"].ToObject<float>();
                if (Math.Abs(importer.globalScale - globalScale) > 0.001f)
                {
                    importer.globalScale = globalScale;
                    modified = true;
                }
            }
            
            // Apply mesh compression
            if (settings["mesh_compression"] != null)
            {
                string compressionStr = settings["mesh_compression"].ToString().ToLower();
                ModelImporterMeshCompression compression = compressionStr switch
                {
                    "off" => ModelImporterMeshCompression.Off,
                    "low" => ModelImporterMeshCompression.Low,
                    "medium" => ModelImporterMeshCompression.Medium,
                    "high" => ModelImporterMeshCompression.High,
                    _ => importer.meshCompression
                };
                
                if (importer.meshCompression != compression)
                {
                    importer.meshCompression = compression;
                    modified = true;
                }
            }
            
            // Apply read/write enabled
            if (settings["read_write_enabled"] != null)
            {
                bool readWriteEnabled = settings["read_write_enabled"].ToObject<bool>();
                if (importer.isReadable != readWriteEnabled)
                {
                    importer.isReadable = readWriteEnabled;
                    modified = true;
                }
            }
            
            // Apply optimize mesh
            if (settings["optimize_mesh"] != null)
            {
                bool optimizeMesh = settings["optimize_mesh"].ToObject<bool>();
                if (importer.optimizeMeshPolygons != optimizeMesh)
                {
                    importer.optimizeMeshPolygons = optimizeMesh;
                    modified = true;
                }
                if (importer.optimizeMeshVertices != optimizeMesh)
                {
                    importer.optimizeMeshVertices = optimizeMesh;
                    modified = true;
                }
            }
            
            // Apply import animations
            if (settings["import_animations"] != null)
            {
                bool importAnimations = settings["import_animations"].ToObject<bool>();
                if (importer.importAnimation != importAnimations)
                {
                    importer.importAnimation = importAnimations;
                    modified = true;
                }
            }
            
            // Apply import materials
            if (settings["import_materials"] != null)
            {
                bool importMaterials = settings["import_materials"].ToObject<bool>();
                ModelImporterMaterialImportMode materialMode = importMaterials ? 
                    ModelImporterMaterialImportMode.ImportStandard : 
                    ModelImporterMaterialImportMode.None;
                
                if (importer.materialImportMode != materialMode)
                {
                    importer.materialImportMode = materialMode;
                    modified = true;
                }
            }
            
            // Apply import blend shapes
            if (settings["import_blend_shapes"] != null)
            {
                bool importBlendShapes = settings["import_blend_shapes"].ToObject<bool>();
                if (importer.importBlendShapes != importBlendShapes)
                {
                    importer.importBlendShapes = importBlendShapes;
                    modified = true;
                }
            }
            
            // Apply import cameras
            if (settings["import_cameras"] != null)
            {
                bool importCameras = settings["import_cameras"].ToObject<bool>();
                if (importer.importCameras != importCameras)
                {
                    importer.importCameras = importCameras;
                    modified = true;
                }
            }
            
            // Apply import lights
            if (settings["import_lights"] != null)
            {
                bool importLights = settings["import_lights"].ToObject<bool>();
                if (importer.importLights != importLights)
                {
                    importer.importLights = importLights;
                    modified = true;
                }
            }
            
            // Apply normals mode
            if (settings["normals"] != null)
            {
                string normalsStr = settings["normals"].ToString().ToLower();
                ModelImporterNormals normals = normalsStr switch
                {
                    "import" => ModelImporterNormals.Import,
                    "calculate" => ModelImporterNormals.Calculate,
                    "none" => ModelImporterNormals.None,
                    _ => importer.importNormals
                };
                
                if (importer.importNormals != normals)
                {
                    importer.importNormals = normals;
                    modified = true;
                }
            }
            
            // Unity 6.2 specific: Apply tangents
            if (settings["tangents"] != null)
            {
                string tangentsStr = settings["tangents"].ToString().ToLower();
                ModelImporterTangents tangents = tangentsStr switch
                {
                    "import" => ModelImporterTangents.Import,
                    "calculate_mikktspace" => ModelImporterTangents.CalculateMikk,
                    "calculate_legacy" => ModelImporterTangents.CalculateLegacy,
                    "calculate_legacy_split" => ModelImporterTangents.CalculateLegacyWithSplitTangents,
                    "none" => ModelImporterTangents.None,
                    _ => importer.importTangents
                };
                
                if (importer.importTangents != tangents)
                {
                    importer.importTangents = tangents;
                    modified = true;
                }
            }
            
            return modified;
        }
        
        private static bool ApplyAudioImportDefaults(AudioImporter importer, JObject settings)
        {
            bool modified = false;
            var defaultSettings = importer.defaultSampleSettings;
            
            // Apply force to mono
            if (settings["force_to_mono"] != null)
            {
                bool forceToMono = settings["force_to_mono"].ToObject<bool>();
                if (importer.forceToMono != forceToMono)
                {
                    importer.forceToMono = forceToMono;
                    modified = true;
                }
            }
            
            // Apply normalize (Note: normalize property not available in Unity 6.2)
            // Using loadInBackground as alternative audio processing control
            if (settings["normalize"] != null)
            {
                bool normalize = settings["normalize"].ToObject<bool>();
                if (importer.loadInBackground != normalize)
                {
                    importer.loadInBackground = normalize;
                    modified = true;
                }
            }
            
            // Apply load in background
            if (settings["load_in_background"] != null)
            {
                bool loadInBackground = settings["load_in_background"].ToObject<bool>();
                if (importer.loadInBackground != loadInBackground)
                {
                    importer.loadInBackground = loadInBackground;
                    modified = true;
                }
            }
            
            // Apply ambisonic
            if (settings["ambisonic"] != null)
            {
                bool ambisonic = settings["ambisonic"].ToObject<bool>();
                if (importer.ambisonic != ambisonic)
                {
                    importer.ambisonic = ambisonic;
                    modified = true;
                }
            }
            
            // Apply default sample settings
            bool sampleSettingsModified = false;
            
            if (settings["load_type"] != null)
            {
                string loadTypeStr = settings["load_type"].ToString().ToLower();
                AudioClipLoadType loadType = loadTypeStr switch
                {
                    "decompress_on_load" => AudioClipLoadType.DecompressOnLoad,
                    "compressed_in_memory" => AudioClipLoadType.CompressedInMemory,
                    "streaming" => AudioClipLoadType.Streaming,
                    _ => defaultSettings.loadType
                };
                
                if (defaultSettings.loadType != loadType)
                {
                    defaultSettings.loadType = loadType;
                    sampleSettingsModified = true;
                }
            }
            
            if (settings["compression_format"] != null)
            {
                string compressionStr = settings["compression_format"].ToString().ToLower();
                AudioCompressionFormat compression = compressionStr switch
                {
                    "pcm" => AudioCompressionFormat.PCM,
                    "vorbis" => AudioCompressionFormat.Vorbis,
                    "adpcm" => AudioCompressionFormat.ADPCM,
                    "mp3" => AudioCompressionFormat.MP3,
                    _ => defaultSettings.compressionFormat
                };
                
                if (defaultSettings.compressionFormat != compression)
                {
                    defaultSettings.compressionFormat = compression;
                    sampleSettingsModified = true;
                }
            }
            
            if (settings["quality"] != null)
            {
                float quality = settings["quality"].ToObject<float>();
                if (Math.Abs(defaultSettings.quality - quality) > 0.001f)
                {
                    defaultSettings.quality = Mathf.Clamp01(quality);
                    sampleSettingsModified = true;
                }
            }
            
            if (settings["sample_rate_setting"] != null)
            {
                string sampleRateStr = settings["sample_rate_setting"].ToString().ToLower();
                AudioSampleRateSetting sampleRateSetting = sampleRateStr switch
                {
                    "preserve_sample_rate" => AudioSampleRateSetting.PreserveSampleRate,
                    "optimize_sample_rate" => AudioSampleRateSetting.OptimizeSampleRate,
                    "override_sample_rate" => AudioSampleRateSetting.OverrideSampleRate,
                    _ => defaultSettings.sampleRateSetting
                };
                
                if (defaultSettings.sampleRateSetting != sampleRateSetting)
                {
                    defaultSettings.sampleRateSetting = sampleRateSetting;
                    sampleSettingsModified = true;
                }
            }
            
            if (settings["sample_rate_override"] != null)
            {
                uint sampleRateOverride = settings["sample_rate_override"].ToObject<uint>();
                if (defaultSettings.sampleRateOverride != sampleRateOverride)
                {
                    defaultSettings.sampleRateOverride = sampleRateOverride;
                    sampleSettingsModified = true;
                }
            }
            
            if (settings["preload_audio_data"] != null)
            {
                bool preloadAudioData = settings["preload_audio_data"].ToObject<bool>();
                if (defaultSettings.preloadAudioData != preloadAudioData)
                {
                    defaultSettings.preloadAudioData = preloadAudioData;
                    sampleSettingsModified = true;
                }
            }
            
            if (sampleSettingsModified)
            {
                importer.defaultSampleSettings = defaultSettings;
                modified = true;
            }
            
            return modified;
        }
    }
}