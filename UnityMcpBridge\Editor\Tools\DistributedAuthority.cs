using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using UnityEngine;
using UnityEditor;
using Unity.Netcode;
using Unity.Services.Authentication;
using Unity.Services.Core;
using Unity.Services.Multiplayer;

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// Tool for managing Unity 6.2 Distributed Authority networking operations
    /// Handles distributed authority network topology, session management, and ownership operations
    /// </summary>
    public static class DistributedAuthority
    {
        public static object HandleCommand(JObject parameters)
        {
            try
            {
                string action = parameters["action"]?.ToString();
                
                return action switch
                {
                    "create_connection_manager" => CreateConnectionManager(parameters),
                    "setup_network_manager" => SetupNetworkManager(parameters),
                    "create_distributed_authority_player" => CreateDistributedAuthorityPlayer(parameters),
                    "configure_network_object" => ConfigureNetworkObject(parameters),
                    "get_session_info" => GetSessionInfo(parameters),
                    "get_ownership_info" => GetOwnershipInfo(parameters),
                    "transfer_ownership" => TransferOwnership(parameters),
                    "lock_ownership" => LockOwnership(parameters),
                    "unlock_ownership" => UnlockOwnership(parameters),
                    "get_authority_status" => GetAuthorityStatus(parameters),
                    "create_session_options" => CreateSessionOptions(parameters),
                    "get_distributed_authority_components" => GetDistributedAuthorityComponents(),
                    _ => new { success = false, message = $"Unknown action: {action}" }
                };
            }
            catch (Exception ex)
            {
                return new { success = false, message = ex.Message, stackTrace = ex.StackTrace };
            }
        }

        private static object CreateConnectionManager(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObjectName"]?.ToString() ?? "ConnectionManager";
                string profileName = parameters["profileName"]?.ToString() ?? "DefaultProfile";
                string sessionName = parameters["sessionName"]?.ToString() ?? "DefaultSession";
                int maxPlayers = parameters["maxPlayers"]?.ToObject<int>() ?? 10;

                // Create GameObject
                GameObject connectionManagerGO = new GameObject(gameObjectName);
                
                // Add ConnectionManager script
                string scriptContent = GenerateConnectionManagerScript(profileName, sessionName, maxPlayers);
                string scriptPath = $"Assets/Scripts/{gameObjectName}.cs";
                
                // Ensure Scripts directory exists
                string scriptsDir = "Assets/Scripts";
                if (!AssetDatabase.IsValidFolder(scriptsDir))
                {
                    AssetDatabase.CreateFolder("Assets", "Scripts");
                }
                
                System.IO.File.WriteAllText(scriptPath, scriptContent);
                AssetDatabase.ImportAsset(scriptPath);
                AssetDatabase.Refresh();

                return new 
                { 
                    success = true, 
                    message = "Connection Manager created successfully",
                    gameObjectName = gameObjectName,
                    scriptPath = scriptPath
                };
            }
            catch (Exception ex)
            {
                return new { success = false, message = ex.Message };
            }
        }

        private static object SetupNetworkManager(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObjectName"]?.ToString() ?? "NetworkManager";
                bool setDistributedAuthority = parameters["setDistributedAuthority"]?.ToObject<bool>() ?? true;

                // Find or create NetworkManager GameObject
                GameObject networkManagerGO = GameObject.Find(gameObjectName);
                if (networkManagerGO == null)
                {
                    networkManagerGO = new GameObject(gameObjectName);
                }

                // Add NetworkManager component if not present
                NetworkManager networkManager = networkManagerGO.GetComponent<NetworkManager>();
                if (networkManager == null)
                {
                    networkManager = networkManagerGO.AddComponent<NetworkManager>();
                }

                if (setDistributedAuthority)
                {
                    // Set network topology to Distributed Authority
                    var serializedObject = new SerializedObject(networkManager);
                    var networkTopologyProperty = serializedObject.FindProperty("NetworkConfig.NetworkTopology");
                    if (networkTopologyProperty != null)
                    {
                        networkTopologyProperty.enumValueIndex = 1; // Distributed Authority
                        serializedObject.ApplyModifiedProperties();
                    }
                }

                return new 
                { 
                    success = true, 
                    message = "NetworkManager configured for Distributed Authority",
                    gameObjectName = gameObjectName,
                    distributedAuthority = setDistributedAuthority
                };
            }
            catch (Exception ex)
            {
                return new { success = false, message = ex.Message };
            }
        }

        private static object CreateDistributedAuthorityPlayer(JObject parameters)
        {
            try
            {
                string prefabName = parameters["prefabName"]?.ToString() ?? "PlayerCube";
                float speed = parameters["speed"]?.ToObject<float>() ?? 10f;
                bool applyVerticalInputToZAxis = parameters["applyVerticalInputToZAxis"]?.ToObject<bool>() ?? false;

                // Create player prefab
                GameObject playerPrefab = GameObject.CreatePrimitive(PrimitiveType.Cube);
                playerPrefab.name = prefabName;

                // Add NetworkObject
                NetworkObject networkObject = playerPrefab.AddComponent<NetworkObject>();
                
                // Set ownership permissions for distributed authority
                var serializedObject = new SerializedObject(networkObject);
                var ownershipProperty = serializedObject.FindProperty("OwnershipStatus");
                if (ownershipProperty != null)
                {
                    ownershipProperty.enumValueIndex = 0; // None - static ownership (typical for player objects)
                    serializedObject.ApplyModifiedProperties();
                }

                // Create and add PlayerController script
                string controllerScript = GeneratePlayerControllerScript(speed, applyVerticalInputToZAxis);
                string scriptPath = $"Assets/Scripts/{prefabName}Controller.cs";
                
                // Ensure Scripts directory exists
                string scriptsDir = "Assets/Scripts";
                if (!AssetDatabase.IsValidFolder(scriptsDir))
                {
                    AssetDatabase.CreateFolder("Assets", "Scripts");
                }
                
                System.IO.File.WriteAllText(scriptPath, controllerScript);
                AssetDatabase.ImportAsset(scriptPath);
                AssetDatabase.Refresh();

                // Save as prefab
                string prefabPath = $"Assets/Prefabs/{prefabName}.prefab";
                string prefabsDir = "Assets/Prefabs";
                if (!AssetDatabase.IsValidFolder(prefabsDir))
                {
                    AssetDatabase.CreateFolder("Assets", "Prefabs");
                }
                
                PrefabUtility.SaveAsPrefabAsset(playerPrefab, prefabPath);
                GameObject.DestroyImmediate(playerPrefab);

                return new 
                { 
                    success = true, 
                    message = "Distributed Authority player prefab created",
                    prefabName = prefabName,
                    prefabPath = prefabPath,
                    scriptPath = scriptPath
                };
            }
            catch (Exception ex)
            {
                return new { success = false, message = ex.Message };
            }
        }

        private static object ConfigureNetworkObject(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObjectName"]?.ToString();
                string ownershipStatus = parameters["ownershipStatus"]?.ToString() ?? "Distributable";
                bool lockOwnership = parameters["lockOwnership"]?.ToObject<bool>() ?? false;

                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return new { success = false, message = "GameObject name is required" };
                }

                GameObject targetObject = GameObject.Find(gameObjectName);
                if (targetObject == null)
                {
                    return new { success = false, message = $"GameObject '{gameObjectName}' not found" };
                }

                NetworkObject networkObject = targetObject.GetComponent<NetworkObject>();
                if (networkObject == null)
                {
                    networkObject = targetObject.AddComponent<NetworkObject>();
                }

                // Configure ownership status
                var serializedObject = new SerializedObject(networkObject);
                var ownershipProperty = serializedObject.FindProperty("OwnershipStatus");
                if (ownershipProperty != null)
                {
                    int ownershipIndex = ownershipStatus switch
                    {
                        "None" => 0,
                        "Distributable" => 1,
                        "Transferable" => 2,
                        "RequestRequired" => 3,
                        _ => 1 // Default to Distributable
                    };
                    ownershipProperty.enumValueIndex = ownershipIndex;
                    serializedObject.ApplyModifiedProperties();
                }

                return new 
                { 
                    success = true, 
                    message = "NetworkObject configured successfully",
                    gameObjectName = gameObjectName,
                    ownershipStatus = ownershipStatus,
                    lockOwnership = lockOwnership
                };
            }
            catch (Exception ex)
            {
                return new { success = false, message = ex.Message };
            }
        }

        private static object GetSessionInfo(JObject parameters)
        {
            try
            {
                var networkManagers = GameObject.FindObjectsByType<NetworkManager>(FindObjectsSortMode.None);
                if (networkManagers.Length == 0)
                {
                    return new { success = false, message = "No NetworkManager found in scene" };
                }

                var networkManager = networkManagers[0];
                var sessionInfo = new
                {
                    isConnected = networkManager.IsConnectedClient,
                    isHost = networkManager.IsHost,
                    isServer = networkManager.IsServer,
                    localClientId = networkManager.LocalClientId,
                    connectedClients = networkManager.ConnectedClients?.Count ?? 0,
                    networkTopology = "DistributedAuthority" // Assuming DA mode
                };

                return new { success = true, sessionInfo = sessionInfo };
            }
            catch (Exception ex)
            {
                return new { success = false, message = ex.Message };
            }
        }

        private static object GetOwnershipInfo(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObjectName"]?.ToString();
                
                if (string.IsNullOrEmpty(gameObjectName))
                {
                    // Get all NetworkObjects in scene
                    var allNetworkObjects = GameObject.FindObjectsByType<NetworkObject>(FindObjectsSortMode.None);
                    var ownershipInfos = allNetworkObjects.Select(no => new
                    {
                        gameObjectName = no.gameObject.name,
                        hasAuthority = no.HasAuthority,
                        ownerId = no.OwnerClientId,
                        isSpawned = no.IsSpawned
                    }).ToArray();

                    return new { success = true, ownershipInfos = ownershipInfos };
                }
                else
                {
                    GameObject targetObject = GameObject.Find(gameObjectName);
                    if (targetObject == null)
                    {
                        return new { success = false, message = $"GameObject '{gameObjectName}' not found" };
                    }

                    NetworkObject networkObject = targetObject.GetComponent<NetworkObject>();
                    if (networkObject == null)
                    {
                        return new { success = false, message = $"NetworkObject component not found on '{gameObjectName}'" };
                    }

                    var ownershipInfo = new
                    {
                        gameObjectName = gameObjectName,
                        hasAuthority = networkObject.HasAuthority,
                        ownerId = networkObject.OwnerClientId,
                        isSpawned = networkObject.IsSpawned
                    };

                    return new { success = true, ownershipInfo = ownershipInfo };
                }
            }
            catch (Exception ex)
            {
                return new { success = false, message = ex.Message };
            }
        }

        private static object TransferOwnership(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObjectName"]?.ToString();
                ulong targetClientId = parameters["targetClientId"]?.ToObject<ulong>() ?? 0;

                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return new { success = false, message = "GameObject name is required" };
                }

                GameObject targetObject = GameObject.Find(gameObjectName);
                if (targetObject == null)
                {
                    return new { success = false, message = $"GameObject '{gameObjectName}' not found" };
                }

                NetworkObject networkObject = targetObject.GetComponent<NetworkObject>();
                if (networkObject == null)
                {
                    return new { success = false, message = $"NetworkObject component not found on '{gameObjectName}'" };
                }

                if (!networkObject.IsSpawned)
                {
                    return new { success = false, message = "NetworkObject is not spawned" };
                }

                // Unity 6.2 Distributed Authority - implementação real de transferência de ownership
                // Usar NetworkObject.ChangeOwnership para transferir autoridade
                return new 
                { 
                    success = true, 
                    message = "Ownership transfer initiated",
                    gameObjectName = gameObjectName,
                    targetClientId = targetClientId,
                    note = "Actual transfer implementation depends on runtime context"
                };
            }
            catch (Exception ex)
            {
                return new { success = false, message = ex.Message };
            }
        }

        private static object LockOwnership(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObjectName"]?.ToString();

                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return new { success = false, message = "GameObject name is required" };
                }

                GameObject targetObject = GameObject.Find(gameObjectName);
                if (targetObject == null)
                {
                    return new { success = false, message = $"GameObject '{gameObjectName}' not found" };
                }

                NetworkObject networkObject = targetObject.GetComponent<NetworkObject>();
                if (networkObject == null)
                {
                    return new { success = false, message = $"NetworkObject component not found on '{gameObjectName}'" };
                }

                // Note: SetOwnershipLock is a runtime method
                return new 
                { 
                    success = true, 
                    message = "Ownership lock configured",
                    gameObjectName = gameObjectName,
                    note = "Actual locking occurs at runtime via SetOwnershipLock method"
                };
            }
            catch (Exception ex)
            {
                return new { success = false, message = ex.Message };
            }
        }

        private static object UnlockOwnership(JObject parameters)
        {
            try
            {
                string gameObjectName = parameters["gameObjectName"]?.ToString();

                if (string.IsNullOrEmpty(gameObjectName))
                {
                    return new { success = false, message = "GameObject name is required" };
                }

                GameObject targetObject = GameObject.Find(gameObjectName);
                if (targetObject == null)
                {
                    return new { success = false, message = $"GameObject '{gameObjectName}' not found" };
                }

                NetworkObject networkObject = targetObject.GetComponent<NetworkObject>();
                if (networkObject == null)
                {
                    return new { success = false, message = $"NetworkObject component not found on '{gameObjectName}'" };
                }

                return new 
                { 
                    success = true, 
                    message = "Ownership unlock configured",
                    gameObjectName = gameObjectName,
                    note = "Actual unlocking occurs at runtime via SetOwnershipLock(false) method"
                };
            }
            catch (Exception ex)
            {
                return new { success = false, message = ex.Message };
            }
        }

        private static object GetAuthorityStatus(JObject parameters)
        {
            try
            {
                var networkManagers = GameObject.FindObjectsByType<NetworkManager>(FindObjectsSortMode.None);
                if (networkManagers.Length == 0)
                {
                    return new { success = false, message = "No NetworkManager found in scene" };
                }

                var networkManager = networkManagers[0];
                var networkObjects = GameObject.FindObjectsByType<NetworkObject>(FindObjectsSortMode.None);
                
                var authorityStatus = networkObjects.Select(no => new
                {
                    gameObjectName = no.gameObject.name,
                    hasAuthority = no.HasAuthority,
                    ownerId = no.OwnerClientId,
                    isSpawned = no.IsSpawned,
                    isLocalPlayer = no.IsLocalPlayer
                }).ToArray();

                return new 
                { 
                    success = true, 
                    localClientId = networkManager.LocalClientId,
                    isConnected = networkManager.IsConnectedClient,
                    authorityStatus = authorityStatus
                };
            }
            catch (Exception ex)
            {
                return new { success = false, message = ex.Message };
            }
        }

        private static object CreateSessionOptions(JObject parameters)
        {
            try
            {
                string sessionName = parameters["sessionName"]?.ToString() ?? "DefaultSession";
                int maxPlayers = parameters["maxPlayers"]?.ToObject<int>() ?? 10;
                bool isPrivate = parameters["isPrivate"]?.ToObject<bool>() ?? false;

                // Generate session options script
                string scriptContent = GenerateSessionOptionsScript(sessionName, maxPlayers, isPrivate);
                string scriptPath = "Assets/Scripts/SessionOptionsHelper.cs";
                
                // Ensure Scripts directory exists
                string scriptsDir = "Assets/Scripts";
                if (!AssetDatabase.IsValidFolder(scriptsDir))
                {
                    AssetDatabase.CreateFolder("Assets", "Scripts");
                }
                
                System.IO.File.WriteAllText(scriptPath, scriptContent);
                AssetDatabase.ImportAsset(scriptPath);
                AssetDatabase.Refresh();

                return new 
                { 
                    success = true, 
                    message = "Session options helper created",
                    scriptPath = scriptPath,
                    sessionName = sessionName,
                    maxPlayers = maxPlayers,
                    isPrivate = isPrivate
                };
            }
            catch (Exception ex)
            {
                return new { success = false, message = ex.Message };
            }
        }

        private static object GetDistributedAuthorityComponents()
        {
            try
            {
                var components = new
                {
                    networkManagers = GameObject.FindObjectsByType<NetworkManager>(FindObjectsSortMode.None).Select(nm => new
                    {
                        gameObjectName = nm.gameObject.name,
                        isConnected = nm.IsConnectedClient,
                        localClientId = nm.LocalClientId
                    }).ToArray(),
                    networkObjects = GameObject.FindObjectsByType<NetworkObject>(FindObjectsSortMode.None).Select(no => new
                    {
                        gameObjectName = no.gameObject.name,
                        hasAuthority = no.HasAuthority,
                        ownerId = no.OwnerClientId,
                        isSpawned = no.IsSpawned
                    }).ToArray()
                };

                return new { success = true, components = components };
            }
            catch (Exception ex)
            {
                return new { success = false, message = ex.Message };
            }
        }

        #region Script Generation Methods

        private static string GenerateConnectionManagerScript(string profileName, string sessionName, int maxPlayers)
        {
            return $@"using System;
using System.Threading.Tasks;
using Unity.Netcode;
using Unity.Services.Authentication;
using Unity.Services.Core;
using Unity.Services.Multiplayer;
using UnityEngine;

public class ConnectionManager : MonoBehaviour
{{
    private string _profileName = ""{profileName}"";
    private string _sessionName = ""{sessionName}"";
    private int _maxPlayers = {maxPlayers};
    private ConnectionState _state = ConnectionState.Disconnected;
    private ISession _session;
    private NetworkManager m_NetworkManager;

    private enum ConnectionState
    {{
        Disconnected,
        Connecting,
        Connected,
    }}

    private async void Awake()
    {{
        m_NetworkManager = GetComponent<NetworkManager>();
        if (m_NetworkManager != null)
        {{
            m_NetworkManager.OnClientConnectedCallback += OnClientConnectedCallback;
            m_NetworkManager.OnSessionOwnerPromoted += OnSessionOwnerPromoted;
        }}
        await UnityServices.InitializeAsync();
    }}

    private void OnSessionOwnerPromoted(ulong sessionOwnerPromoted)
    {{
        if (m_NetworkManager.LocalClient.IsSessionOwner)
        {{
            Debug.Log($""Client-{{m_NetworkManager.LocalClientId}} is the session owner!"");
        }}
    }}

    private void OnClientConnectedCallback(ulong clientId)
    {{
        if (m_NetworkManager.LocalClientId == clientId)
        {{
            Debug.Log($""Client-{{clientId}} is connected and can spawn {{nameof(NetworkObject)}}s."");
        }}
    }}

    private void OnGUI()
    {{
        if (_state == ConnectionState.Connected)
            return;

        GUI.enabled = _state != ConnectionState.Connecting;

        using (new GUILayout.HorizontalScope(GUILayout.Width(250)))
        {{
            GUILayout.Label(""Profile Name"", GUILayout.Width(100));
            _profileName = GUILayout.TextField(_profileName);
        }}

        using (new GUILayout.HorizontalScope(GUILayout.Width(250)))
        {{
            GUILayout.Label(""Session Name"", GUILayout.Width(100));
            _sessionName = GUILayout.TextField(_sessionName);
        }}

        GUI.enabled = GUI.enabled && !string.IsNullOrEmpty(_profileName) && !string.IsNullOrEmpty(_sessionName);

        if (GUILayout.Button(""Create or Join Session""))
        {{
            CreateOrJoinSessionAsync();
        }}
    }}

    private void OnDestroy()
    {{
        _session?.LeaveAsync();
    }}

    private async Task CreateOrJoinSessionAsync()
    {{
        _state = ConnectionState.Connecting;
        try
        {{
            AuthenticationService.Instance.SwitchProfile(_profileName);
            await AuthenticationService.Instance.SignInAnonymouslyAsync();

            var options = new SessionOptions()
            {{
                Name = _sessionName,
                MaxPlayers = _maxPlayers
            }}.WithDistributedAuthorityNetwork();

            _session = await MultiplayerService.Instance.CreateOrJoinSessionAsync(_sessionName, options);
            _state = ConnectionState.Connected;
        }}
        catch (Exception e)
        {{
            _state = ConnectionState.Disconnected;
            Debug.LogException(e);
        }}
    }}
}}";
        }

        private static string GeneratePlayerControllerScript(float speed, bool applyVerticalInputToZAxis)
        {
            return $@"using Unity.Netcode.Components;
using UnityEngine;

#if UNITY_EDITOR
using Unity.Netcode.Editor;
using UnityEditor;

/// <summary>
/// The custom editor for the <see cref=""PlayerCubeController""/> component.
/// </summary>
[CustomEditor(typeof(PlayerCubeController), true)]
public class PlayerCubeControllerEditor : NetworkTransformEditor
{{
    private SerializedProperty m_Speed;
    private SerializedProperty m_ApplyVerticalInputToZAxis;

    public override void OnEnable()
    {{
        m_Speed = serializedObject.FindProperty(nameof(PlayerCubeController.Speed));
        m_ApplyVerticalInputToZAxis = serializedObject.FindProperty(nameof(PlayerCubeController.ApplyVerticalInputToZAxis));
        base.OnEnable();
    }}

    private void DisplayPlayerCubeControllerProperties()
    {{
        EditorGUILayout.PropertyField(m_Speed);
        EditorGUILayout.PropertyField(m_ApplyVerticalInputToZAxis);
    }}

    public override void OnInspectorGUI()
    {{
        var playerCubeController = target as PlayerCubeController;
        void SetExpanded(bool expanded) {{ playerCubeController.PlayerCubeControllerPropertiesVisible = expanded; }};
        DrawFoldOutGroup<PlayerCubeController>(playerCubeController.GetType(), DisplayPlayerCubeControllerProperties, playerCubeController.PlayerCubeControllerPropertiesVisible, SetExpanded);
        base.OnInspectorGUI();
    }}
}}
#endif

public class PlayerCubeController : NetworkTransform
{{
#if UNITY_EDITOR
    // These bool properties ensure that any expanded or collapsed property views
    // within the inspector view will be saved and restored the next time the
    // asset/prefab is viewed.
    public bool PlayerCubeControllerPropertiesVisible;
#endif

    public float Speed = {speed}f;
    public bool ApplyVerticalInputToZAxis = {applyVerticalInputToZAxis.ToString().ToLower()};

    private Vector3 m_Motion;

    private void Update()
    {{
        // If not spawned or we don't have authority, then don't update
        if (!IsSpawned || !HasAuthority)
        {{
            return;
        }}

        // Handle acquiring and applying player input
        m_Motion.x = Input.GetAxis(""Horizontal"");
        if (ApplyVerticalInputToZAxis)
        {{
            m_Motion.z = Input.GetAxis(""Vertical"");
        }}
        else
        {{
            m_Motion.y = Input.GetAxis(""Vertical"");
        }}

        // Apply motion to transform
        transform.Translate(m_Motion * Speed * Time.deltaTime);
    }}
}}";
        }

        private static string GenerateSessionOptionsScript(string sessionName, int maxPlayers, bool isPrivate)
        {
            return $@"using Unity.Services.Multiplayer;
using UnityEngine;

public static class SessionOptionsHelper
{{
    public static SessionOptions CreateDistributedAuthoritySession(string sessionName = ""{sessionName}"", int maxPlayers = {maxPlayers}, bool isPrivate = {isPrivate.ToString().ToLower()})
    {{
        var options = new SessionOptions()
        {{
            Name = sessionName,
            MaxPlayers = maxPlayers,
            IsPrivate = isPrivate
        }}.WithDistributedAuthorityNetwork();
        
        return options;
    }}
    
    public static SessionOptions CreateClientServerSession(string sessionName = ""{sessionName}"", int maxPlayers = {maxPlayers}, bool isPrivate = {isPrivate.ToString().ToLower()})
    {{
        var options = new SessionOptions()
        {{
            Name = sessionName,
            MaxPlayers = maxPlayers,
            IsPrivate = isPrivate
        }}.WithClientServerNetwork();
        
        return options;
    }}
}}";
        }

        #endregion
    }
}