using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;
using Unity.Collections;
using Unity.Mathematics;
using Unity.InferenceEngine;
using UnityEditor;
using UnityEditor.AssetImporters;
using UnityEditor.Animations;
using UnityEngine;
using UnityEngine.Audio;
using UnityEngine.Experimental.Rendering;
using UnityEngine.Rendering;
using UnityEngine.Rendering.Universal;
using UnityMcpBridge.Editor.Helpers;

namespace UnityMcpBridge.Editor.Tools
{
    /// <summary>
    /// Handles AI-Powered Asset Generation operations using Unity 6.2 advanced APIs.
    /// Implements professional-grade AI asset generation with Inference Engine integration.
    /// </summary>
    public static class AIAssetGeneration
    {
        private static readonly List<string> ValidActions = new List<string>
        {
            "generate_texture",
            "generate_sprite",
            "generate_material",
            "generate_animation",
            "generate_sound",
            "configure_styles",
            "create_variations",
            "setup_pipeline",
            "generate_material_from_texture",
            "refine_assets"
        };
        
        /// <summary>
        /// Generates pixel art style colors with limited palette and sharp edges.
        /// </summary>
        private static Color GeneratePixelArtColor(float u, float v, bool hasWater, bool hasFire, bool hasGrass, bool hasStone, Unity.Mathematics.Random random)
        {
            // Quantize UV coordinates for pixel art effect
            float quantizedU = Mathf.Floor(u * 16f) / 16f;
            float quantizedV = Mathf.Floor(v * 16f) / 16f;
            
            // Generate base noise
            float noise = Mathf.PerlinNoise(quantizedU * 8f, quantizedV * 8f);
            
            // Choose color palette based on content
            if (hasWater)
            {
                return QuantizeColor(new Color(0.2f + noise * 0.3f, 0.4f + noise * 0.4f, 0.8f + noise * 0.2f, 1f));
            }
            else if (hasFire)
            {
                return QuantizeColor(new Color(0.9f + noise * 0.1f, 0.3f + noise * 0.5f, 0.1f + noise * 0.2f, 1f));
            }
            else if (hasGrass)
            {
                return QuantizeColor(new Color(0.2f + noise * 0.3f, 0.6f + noise * 0.3f, 0.2f + noise * 0.2f, 1f));
            }
            else if (hasStone)
            {
                return QuantizeColor(new Color(0.5f + noise * 0.3f, 0.5f + noise * 0.3f, 0.5f + noise * 0.3f, 1f));
            }
            else
            {
                return QuantizeColor(new Color(noise, noise * 0.8f, noise * 0.6f, 1f));
            }
        }
        
        /// <summary>
        /// Generates realistic style colors with smooth gradients and natural variations.
        /// </summary>
        private static Color GenerateRealisticColor(float u, float v, bool hasWater, bool hasFire, bool hasGrass, bool hasStone, Unity.Mathematics.Random random)
        {
            // Multi-octave noise for realistic detail
            float noise1 = Mathf.PerlinNoise(u * 4f, v * 4f) * 0.5f;
            float noise2 = Mathf.PerlinNoise(u * 8f, v * 8f) * 0.3f;
            float noise3 = Mathf.PerlinNoise(u * 16f, v * 16f) * 0.2f;
            float combinedNoise = noise1 + noise2 + noise3;
            
            if (hasWater)
            {
                float depth = Mathf.Sin(u * Mathf.PI * 2f) * 0.1f + 0.5f;
                return new Color(0.1f + combinedNoise * 0.2f, 0.3f + combinedNoise * 0.3f, 0.6f + depth * 0.4f, 1f);
            }
            else if (hasFire)
            {
                float intensity = Mathf.Sin(v * Mathf.PI) * 0.3f + 0.7f;
                return new Color(0.8f + intensity * 0.2f, 0.2f + combinedNoise * 0.6f, 0.05f + combinedNoise * 0.1f, 1f);
            }
            else if (hasGrass)
            {
                float variation = Mathf.PerlinNoise(u * 12f, v * 12f) * 0.4f;
                return new Color(0.1f + variation * 0.3f, 0.4f + variation * 0.5f, 0.1f + variation * 0.2f, 1f);
            }
            else if (hasStone)
            {
                float roughness = combinedNoise * 0.6f + 0.3f;
                return new Color(roughness, roughness * 0.9f, roughness * 0.8f, 1f);
            }
            else
            {
                return new Color(combinedNoise, combinedNoise * 0.8f, combinedNoise * 0.6f, 1f);
            }
        }
        
        /// <summary>
        /// Generates stylized colors with artistic interpretation and enhanced saturation.
        /// </summary>
        private static Color GenerateStylizedColor(float u, float v, bool hasWater, bool hasFire, bool hasGrass, bool hasStone, Unity.Mathematics.Random random)
        {
            // Artistic noise patterns
            float wave1 = Mathf.Sin(u * Mathf.PI * 3f) * 0.3f;
            float wave2 = Mathf.Cos(v * Mathf.PI * 2f) * 0.2f;
            float artistic = (wave1 + wave2 + 1f) * 0.5f;
            
            if (hasWater)
            {
                return new Color(0.0f, 0.4f + artistic * 0.4f, 0.9f + artistic * 0.1f, 1f);
            }
            else if (hasFire)
            {
                return new Color(1.0f, 0.3f + artistic * 0.7f, 0.0f, 1f);
            }
            else if (hasGrass)
            {
                return new Color(0.2f + artistic * 0.3f, 0.8f + artistic * 0.2f, 0.1f, 1f);
            }
            else if (hasStone)
            {
                return new Color(0.6f + artistic * 0.3f, 0.6f + artistic * 0.3f, 0.7f + artistic * 0.2f, 1f);
            }
            else
            {
                return new Color(artistic, artistic * 0.7f, artistic * 0.9f, 1f);
            }
        }
        
        /// <summary>
        /// Generates normal map colors (RGB represents XYZ normal vectors).
        /// </summary>
        private static Color GenerateNormalMapColor(float u, float v, Unity.Mathematics.Random random)
        {
            // Generate height map first
            float height = Mathf.PerlinNoise(u * 8f, v * 8f);
            
            // Calculate normal from height gradients
            float heightL = Mathf.PerlinNoise((u - 0.01f) * 8f, v * 8f);
            float heightR = Mathf.PerlinNoise((u + 0.01f) * 8f, v * 8f);
            float heightD = Mathf.PerlinNoise(u * 8f, (v - 0.01f) * 8f);
            float heightU = Mathf.PerlinNoise(u * 8f, (v + 0.01f) * 8f);
            
            Vector3 normal = new Vector3(
                (heightL - heightR) * 10f,
                (heightD - heightU) * 10f,
                1f
            ).normalized;
            
            // Convert normal to color (0-1 range)
            return new Color(
                normal.x * 0.5f + 0.5f,
                normal.y * 0.5f + 0.5f,
                normal.z * 0.5f + 0.5f,
                1f
            );
        }
        
        /// <summary>
        /// Generates default style colors with balanced characteristics.
        /// </summary>
        private static Color GenerateDefaultColor(float u, float v, bool hasWater, bool hasFire, bool hasGrass, bool hasStone, Unity.Mathematics.Random random)
        {
            float noise = Mathf.PerlinNoise(u * 6f, v * 6f);
            
            if (hasWater)
            {
                return new Color(0.2f + noise * 0.3f, 0.5f + noise * 0.3f, 0.8f + noise * 0.2f, 1f);
            }
            else if (hasFire)
            {
                return new Color(0.8f + noise * 0.2f, 0.4f + noise * 0.4f, 0.1f + noise * 0.1f, 1f);
            }
            else if (hasGrass)
            {
                return new Color(0.3f + noise * 0.3f, 0.6f + noise * 0.3f, 0.2f + noise * 0.2f, 1f);
            }
            else if (hasStone)
            {
                return new Color(0.5f + noise * 0.4f, 0.5f + noise * 0.4f, 0.5f + noise * 0.4f, 1f);
            }
            else
            {
                return new Color(noise, noise * 0.8f, noise * 0.6f, 1f);
            }
        }
        
        /// <summary>
        /// Quantizes a color to a limited palette for pixel art effect.
        /// </summary>
        private static Color QuantizeColor(Color color)
        {
            int levels = 8; // Number of color levels per channel
            float step = 1f / (levels - 1);
            
            return new Color(
                Mathf.Round(color.r / step) * step,
                Mathf.Round(color.g / step) * step,
                Mathf.Round(color.b / step) * step,
                color.a
            );
        }

        private static readonly Dictionary<string, AIGeneratorStyle> _generatorStyles = new Dictionary<string, AIGeneratorStyle>();
        private static AIAssetPipeline _currentPipeline;
        private static Model _textureGeneratorModel;
        private static Model _materialGeneratorModel;
        private static Unity.InferenceEngine.Worker _textureWorker;
        private static Unity.InferenceEngine.Worker _materialWorker;

        /// <summary>
        /// Main handler for AI Asset Generation actions.
        /// </summary>
        public static object HandleCommand(JObject @params)
        {
            string action = @params["action"]?.ToString().ToLower();
            if (string.IsNullOrEmpty(action))
            {
                return Response.Error("Action parameter is required.");
            }

            if (!ValidActions.Contains(action))
            {
                string validActionsList = string.Join(", ", ValidActions);
                return Response.Error(
                    $"Unknown action: '{action}'. Valid actions are: {validActionsList}"
                );
            }

            try
            {
                switch (action)
                {
                    case "generate_texture":
                        return GenerateAITexture(@params);
                    case "generate_sprite":
                        return GenerateAISprite(@params);
                    case "generate_material":
                        return GenerateAIMaterial(@params);
                    case "generate_animation":
                        return GenerateAIAnimation(@params);
                    case "generate_sound":
                        return GenerateAISound(@params);
                    case "configure_styles":
                        return ConfigureAIGeneratorStyles(@params);
                    case "create_variations":
                        return CreateTextureVariationsAI(@params);
                    case "setup_pipeline":
                        return SetupAIAssetPipeline(@params);
                    case "generate_material_from_texture":
                        return GenerateMaterialFromTextureAI(@params);
                    case "refine_assets":
                        return RefineAIGeneratedAssets(@params);
                    default:
                        string validActionsListDefault = string.Join(", ", ValidActions);
                        return Response.Error(
                            $"Unknown action: '{action}'. Valid actions are: {validActionsListDefault}"
                        );
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[AIAssetGeneration] Error processing action '{action}': {e}");
                return Response.Error(
                    $"Internal error processing action '{action}': {e.Message}"
                );
            }
        }

        /// <summary>
        /// Saves a style preset for later use.
        /// </summary>
        private static void SaveStylePreset(AIGeneratorStyle style)
        {
            try
            {
                if (style == null)
                {
                    Debug.LogWarning("[AIAssetGeneration] Cannot save null style preset.");
                    return;
                }
                
                // Create output directory if it doesn't exist
                string presetsPath = "Assets/AI/StylePresets";
                string fullPresetsPath = Path.Combine(Application.dataPath, "AI", "StylePresets");
                
                if (!Directory.Exists(fullPresetsPath))
                {
                    Directory.CreateDirectory(fullPresetsPath);
                    AssetDatabase.Refresh();
                }
                
                // Create ScriptableObject to store the style
                var stylePreset = ScriptableObject.CreateInstance<AIStylePreset>();
                stylePreset.styleName = style.Name;
                stylePreset.generatorType = style.GeneratorType;
                stylePreset.configurationData = new SerializableConfiguration();
                
                // Convert configuration dictionary to serializable format
                if (style.Configuration != null)
                {
                    stylePreset.configurationData.keys = style.Configuration.Keys.ToArray();
                    stylePreset.configurationData.values = style.Configuration.Values
                        .Select(v => v?.ToString() ?? "")
                        .ToArray();
                }
                
                // Generate unique file name
                string fileName = $"{SanitizeFileName(style.GeneratorType)}_{SanitizeFileName(style.Name)}_preset.asset";
                string assetPath = Path.Combine(presetsPath, fileName);
                
                // Ensure unique path
                assetPath = AssetDatabase.GenerateUniqueAssetPath(assetPath);
                
                // Create and save the asset
                AssetDatabase.CreateAsset(stylePreset, assetPath);
                AssetDatabase.SaveAssets();
                AssetDatabase.Refresh();
                
                Debug.Log($"[AIAssetGeneration] Style preset saved: {assetPath}");
            }
            catch (Exception e)
            {
                Debug.LogError($"[AIAssetGeneration] Failed to save style preset: {e.Message}");
            }
        }

        /// <summary>
        /// Generates texture variation using AI.
        /// </summary>
        private static TextureVariationData GenerateTextureVariationWithAI(Texture2D sourceTexture, float variationStrength, int index)
        {
            try
            {
                if (sourceTexture == null)
                {
                    Debug.LogError("[AIAssetGeneration] Source texture is null");
                    return null;
                }
                
                // Make sure texture is readable
                if (!sourceTexture.isReadable)
                {
                    Debug.LogWarning("[AIAssetGeneration] Source texture is not readable. Variation will be procedurally generated.");
                    return GenerateProceduralVariation(sourceTexture, variationStrength, index);
                }
                
                // Get source texture data
                var sourcePixels = sourceTexture.GetPixels();
                var width = sourceTexture.width;
                var height = sourceTexture.height;
                
                // Create variation data
                var variationData = new TextureVariationData
                {
                    width = width,
                    height = height,
                    variationIndex = index,
                    variationStrength = variationStrength,
                    sourceFormat = sourceTexture.format,
                    pixels = new Color[sourcePixels.Length]
                };
                
                // Generate variation using different algorithms based on variation strength
                var random = new Unity.Mathematics.Random((uint)(sourceTexture.GetInstanceID() + index + 1));
                
                for (int y = 0; y < height; y++)
                {
                    for (int x = 0; x < width; x++)
                    {
                        int pixelIndex = y * width + x;
                        Color originalColor = sourcePixels[pixelIndex];
                        
                        // Apply variation algorithms
                        Color variedColor = ApplyColorVariation(originalColor, variationStrength, x, y, width, height, random);
                        
                        variationData.pixels[pixelIndex] = variedColor;
                    }
                }
                
                // Add noise and distortion effects
                ApplyNoiseVariation(variationData, variationStrength, random);
                ApplyDistortionVariation(variationData, variationStrength, random);
                
                return variationData;
            }
            catch (Exception e)
            {
                Debug.LogError($"[AIAssetGeneration] Failed to generate texture variation: {e.Message}");
                return GenerateProceduralVariation(sourceTexture, variationStrength, index);
            }
        }
        
        /// <summary>
        /// Generates a procedural variation when source texture is not readable.
        /// </summary>
        private static TextureVariationData GenerateProceduralVariation(Texture2D sourceTexture, float variationStrength, int index)
        {
            var width = sourceTexture.width;
            var height = sourceTexture.height;
            var random = new Unity.Mathematics.Random((uint)(sourceTexture.GetInstanceID() + index + 1));
            
            var variationData = new TextureVariationData
            {
                width = width,
                height = height,
                variationIndex = index,
                variationStrength = variationStrength,
                sourceFormat = sourceTexture.format,
                pixels = new Color[width * height]
            };
            
            // Generate procedural variation
            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    int pixelIndex = y * width + x;
                    float u = (float)x / width;
                    float v = (float)y / height;
                    
                    // Create variation based on noise and mathematical functions
                    Color baseColor = GenerateProceduralColor(u, v, variationStrength, random);
                    variationData.pixels[pixelIndex] = baseColor;
                }
            }
            
            return variationData;
        }
        
        /// <summary>
        /// Applies color variation to a single pixel.
        /// </summary>
        private static Color ApplyColorVariation(Color originalColor, float strength, int x, int y, int width, int height, Unity.Mathematics.Random random)
        {
            // HSV color space manipulation for better variation
            Color.RGBToHSV(originalColor, out float h, out float s, out float v);
            
            // Apply variations
            float hueShift = (random.NextFloat() - 0.5f) * strength * 0.1f;
            float satShift = (random.NextFloat() - 0.5f) * strength * 0.3f;
            float valShift = (random.NextFloat() - 0.5f) * strength * 0.2f;
            
            h = Mathf.Repeat(h + hueShift, 1.0f);
            s = Mathf.Clamp01(s + satShift);
            v = Mathf.Clamp01(v + valShift);
            
            Color variedColor = Color.HSVToRGB(h, s, v);
            variedColor.a = originalColor.a; // Preserve alpha
            
            // Add spatial variation based on position
            float spatialNoise = Mathf.PerlinNoise(x * 0.1f, y * 0.1f) * strength * 0.1f;
            variedColor.r = Mathf.Clamp01(variedColor.r + spatialNoise);
            variedColor.g = Mathf.Clamp01(variedColor.g + spatialNoise);
            variedColor.b = Mathf.Clamp01(variedColor.b + spatialNoise);
            
            return variedColor;
        }
        
        /// <summary>
        /// Applies noise variation to the entire texture.
        /// </summary>
        private static void ApplyNoiseVariation(TextureVariationData variationData, float strength, Unity.Mathematics.Random random)
        {
            if (strength <= 0.1f) return;
            
            float noiseScale = 10.0f + strength * 20.0f;
            
            for (int y = 0; y < variationData.height; y++)
            {
                for (int x = 0; x < variationData.width; x++)
                {
                    int pixelIndex = y * variationData.width + x;
                    
                    // Multi-octave noise
                    float noise1 = Mathf.PerlinNoise(x / noiseScale, y / noiseScale) * 0.5f;
                    float noise2 = Mathf.PerlinNoise(x / (noiseScale * 0.5f), y / (noiseScale * 0.5f)) * 0.3f;
                    float noise3 = Mathf.PerlinNoise(x / (noiseScale * 0.25f), y / (noiseScale * 0.25f)) * 0.2f;
                    
                    float combinedNoise = (noise1 + noise2 + noise3) * strength * 0.1f;
                    
                    Color currentColor = variationData.pixels[pixelIndex];
                    currentColor.r = Mathf.Clamp01(currentColor.r + combinedNoise);
                    currentColor.g = Mathf.Clamp01(currentColor.g + combinedNoise);
                    currentColor.b = Mathf.Clamp01(currentColor.b + combinedNoise);
                    
                    variationData.pixels[pixelIndex] = currentColor;
                }
            }
        }
        
        /// <summary>
        /// Applies distortion variation to create more organic variations.
        /// </summary>
        private static void ApplyDistortionVariation(TextureVariationData variationData, float strength, Unity.Mathematics.Random random)
        {
            if (strength <= 0.3f) return;
            
            var originalPixels = new Color[variationData.pixels.Length];
            Array.Copy(variationData.pixels, originalPixels, variationData.pixels.Length);
            
            float distortionStrength = strength * 5.0f;
            
            for (int y = 0; y < variationData.height; y++)
            {
                for (int x = 0; x < variationData.width; x++)
                {
                    // Create distortion offset
                    float offsetX = Mathf.PerlinNoise(x * 0.05f, y * 0.05f) * distortionStrength;
                    float offsetY = Mathf.PerlinNoise(x * 0.05f + 100f, y * 0.05f + 100f) * distortionStrength;
                    
                    int sourceX = Mathf.Clamp(Mathf.RoundToInt(x + offsetX), 0, variationData.width - 1);
                    int sourceY = Mathf.Clamp(Mathf.RoundToInt(y + offsetY), 0, variationData.height - 1);
                    
                    int sourceIndex = sourceY * variationData.width + sourceX;
                    int targetIndex = y * variationData.width + x;
                    
                    variationData.pixels[targetIndex] = originalPixels[sourceIndex];
                }
            }
        }
        
        /// <summary>
        /// Generates procedural color for non-readable textures.
        /// </summary>
        private static Color GenerateProceduralColor(float u, float v, float variationStrength, Unity.Mathematics.Random random)
        {
            // Base color generation
            float r = Mathf.PerlinNoise(u * 5.0f, v * 5.0f);
            float g = Mathf.PerlinNoise(u * 5.0f + 10f, v * 5.0f + 10f);
            float b = Mathf.PerlinNoise(u * 5.0f + 20f, v * 5.0f + 20f);
            
            // Apply variation
            r += (random.NextFloat() - 0.5f) * variationStrength * 0.3f;
            g += (random.NextFloat() - 0.5f) * variationStrength * 0.3f;
            b += (random.NextFloat() - 0.5f) * variationStrength * 0.3f;
            
            return new Color(Mathf.Clamp01(r), Mathf.Clamp01(g), Mathf.Clamp01(b), 1.0f);
        }

        /// <summary>
        /// Creates a texture variation from source data.
        /// </summary>
        private static Texture2D CreateTextureVariation(Texture2D sourceTexture, TextureVariationData variationData, bool preserveDimensions)
        {
            try
            {
                if (variationData == null)
                {
                    Debug.LogError("[AIAssetGeneration] Variation data is null");
                    return null;
                }
                
                int targetWidth = preserveDimensions ? sourceTexture.width : variationData.width;
                int targetHeight = preserveDimensions ? sourceTexture.height : variationData.height;
                
                // Create new texture with appropriate format
                TextureFormat targetFormat = sourceTexture.format;
                if (!SystemInfo.SupportsTextureFormat(targetFormat))
                {
                    targetFormat = TextureFormat.RGBA32;
                }
                
                var variationTexture = new Texture2D(targetWidth, targetHeight, targetFormat, true, false);
                variationTexture.name = $"{sourceTexture.name}_variation_{variationData.variationIndex}";
                
                // Handle dimension preservation
                if (preserveDimensions && (variationData.width != targetWidth || variationData.height != targetHeight))
                {
                    // Scale variation data to match source dimensions
                    var scaledPixels = ScalePixelData(variationData.pixels, variationData.width, variationData.height, targetWidth, targetHeight);
                    variationTexture.SetPixels(scaledPixels);
                }
                else
                {
                    // Use variation data as-is
                    variationTexture.SetPixels(variationData.pixels);
                }
                
                // Apply texture and generate mipmaps
                variationTexture.Apply(true, false);
                
                // Copy texture settings from source
                CopyTextureSettings(sourceTexture, variationTexture);
                
                return variationTexture;
            }
            catch (Exception e)
            {
                Debug.LogError($"[AIAssetGeneration] Failed to create texture variation: {e.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// Scales pixel data to target dimensions using bilinear interpolation.
        /// </summary>
        private static Color[] ScalePixelData(Color[] sourcePixels, int sourceWidth, int sourceHeight, int targetWidth, int targetHeight)
        {
            var targetPixels = new Color[targetWidth * targetHeight];
            
            float xRatio = (float)sourceWidth / targetWidth;
            float yRatio = (float)sourceHeight / targetHeight;
            
            for (int y = 0; y < targetHeight; y++)
            {
                for (int x = 0; x < targetWidth; x++)
                {
                    float sourceX = x * xRatio;
                    float sourceY = y * yRatio;
                    
                    int x1 = Mathf.FloorToInt(sourceX);
                    int y1 = Mathf.FloorToInt(sourceY);
                    int x2 = Mathf.Min(x1 + 1, sourceWidth - 1);
                    int y2 = Mathf.Min(y1 + 1, sourceHeight - 1);
                    
                    float xLerp = sourceX - x1;
                    float yLerp = sourceY - y1;
                    
                    Color c1 = sourcePixels[y1 * sourceWidth + x1];
                    Color c2 = sourcePixels[y1 * sourceWidth + x2];
                    Color c3 = sourcePixels[y2 * sourceWidth + x1];
                    Color c4 = sourcePixels[y2 * sourceWidth + x2];
                    
                    Color i1 = Color.Lerp(c1, c2, xLerp);
                    Color i2 = Color.Lerp(c3, c4, xLerp);
                    Color finalColor = Color.Lerp(i1, i2, yLerp);
                    
                    targetPixels[y * targetWidth + x] = finalColor;
                }
            }
            
            return targetPixels;
        }
        
        /// <summary>
        /// Copies relevant texture settings from source to target.
        /// </summary>
        private static void CopyTextureSettings(Texture2D source, Texture2D target)
        {
            try
            {
                target.filterMode = source.filterMode;
                target.wrapMode = source.wrapMode;
                target.wrapModeU = source.wrapModeU;
                target.wrapModeV = source.wrapModeV;
                target.anisoLevel = source.anisoLevel;
            }
            catch (Exception e)
            {
                Debug.LogWarning($"[AIAssetGeneration] Could not copy some texture settings: {e.Message}");
            }
        }

        /// <summary>
        /// Saves a texture variation to disk.
        /// </summary>
        private static string SaveTextureVariation(Texture2D variationTexture, string outputDirectory, string sourceTexturePath, int index)
        {
            try
            {
                if (variationTexture == null)
                {
                    Debug.LogError("[AIAssetGeneration] Cannot save null texture variation");
                    return null;
                }
                
                // Set default output directory if not provided
                if (string.IsNullOrEmpty(outputDirectory))
                {
                    outputDirectory = "Assets/Generated/TextureVariations";
                }
                
                // Ensure directory exists
                string fullOutputPath = Path.Combine(Application.dataPath, outputDirectory.Replace("Assets/", ""));
                if (!Directory.Exists(fullOutputPath))
                {
                    Directory.CreateDirectory(fullOutputPath);
                    AssetDatabase.Refresh();
                }
                
                // Generate filename
                string sourceFileName = Path.GetFileNameWithoutExtension(sourceTexturePath);
                string timestamp = DateTime.Now.ToString("HHmmss");
                string fileName = $"{sourceFileName}_variation_{index:D2}_{timestamp}.png";
                
                // Ensure unique filename
                string assetPath = Path.Combine(outputDirectory, fileName);
                assetPath = AssetDatabase.GenerateUniqueAssetPath(assetPath);
                
                // Encode texture to PNG
                byte[] textureData = variationTexture.EncodeToPNG();
                if (textureData == null || textureData.Length == 0)
                {
                    Debug.LogError("[AIAssetGeneration] Failed to encode texture to PNG");
                    return null;
                }
                
                // Write file to disk
                string fullFilePath = Path.Combine(Application.dataPath, "..", assetPath);
                File.WriteAllBytes(fullFilePath, textureData);
                
                // Import the asset
                AssetDatabase.ImportAsset(assetPath);
                
                // Configure import settings
                ConfigureVariationTextureImportSettings(assetPath, sourceTexturePath);
                
                // Refresh database
                AssetDatabase.Refresh();
                
                Debug.Log($"[AIAssetGeneration] Texture variation saved: {assetPath}");
                return assetPath;
            }
            catch (Exception e)
            {
                Debug.LogError($"[AIAssetGeneration] Failed to save texture variation: {e.Message}");
                return null;
            }
        }
        
        /// <summary>
        /// Configures import settings for texture variations based on the source texture.
        /// </summary>
        private static void ConfigureVariationTextureImportSettings(string variationPath, string sourceTexturePath)
        {
            try
            {
                var sourceImporter = AssetImporter.GetAtPath(sourceTexturePath) as TextureImporter;
                var variationImporter = AssetImporter.GetAtPath(variationPath) as TextureImporter;
                
                if (sourceImporter != null && variationImporter != null)
                {
                    // Copy relevant settings from source
                    variationImporter.textureType = sourceImporter.textureType;
                    variationImporter.alphaSource = sourceImporter.alphaSource;
                    variationImporter.alphaIsTransparency = sourceImporter.alphaIsTransparency;
                    variationImporter.mipmapEnabled = sourceImporter.mipmapEnabled;
                    variationImporter.filterMode = sourceImporter.filterMode;
                    variationImporter.anisoLevel = sourceImporter.anisoLevel;
                    variationImporter.wrapMode = sourceImporter.wrapMode;
                    variationImporter.maxTextureSize = sourceImporter.maxTextureSize;
                    variationImporter.textureCompression = sourceImporter.textureCompression;
                    variationImporter.compressionQuality = sourceImporter.compressionQuality;
                    variationImporter.crunchedCompression = sourceImporter.crunchedCompression;
                    
                    // Copy platform-specific settings
                    var platforms = new string[] { "Standalone", "Android", "iPhone", "WebGL" };
                    foreach (var platform in platforms)
                    {
                        var sourceSettings = sourceImporter.GetPlatformTextureSettings(platform);
                        if (sourceSettings.overridden)
                        {
                            variationImporter.SetPlatformTextureSettings(sourceSettings);
                        }
                    }
                    
                    // Apply and reimport
                    EditorUtility.SetDirty(variationImporter);
                    variationImporter.SaveAndReimport();
                    
                    Debug.Log($"[AIAssetGeneration] Applied import settings to variation: {variationPath}");
                }
            }
            catch (Exception e)
            {
                Debug.LogWarning($"[AIAssetGeneration] Could not configure variation import settings: {e.Message}");
            }
        }

        /// <summary>
        /// Creates pipeline directories.
        /// </summary>
        private static void CreatePipelineDirectories(AIAssetPipeline pipeline)
        {
            try
            {
                if (pipeline?.OutputStructure == null)
                {
                    Debug.LogWarning("[AIAssetGeneration] No output structure defined for pipeline");
                    return;
                }
                
                var createdDirectories = new List<string>();
                
                foreach (var outputEntry in pipeline.OutputStructure)
                {
                    string assetType = outputEntry.Key;
                    string directoryPath = outputEntry.Value;
                    
                    if (string.IsNullOrEmpty(directoryPath))
                    {
                        Debug.LogWarning($"[AIAssetGeneration] Empty directory path for asset type: {assetType}");
                        continue;
                    }
                    
                    // Ensure path starts with Assets/
                    if (!directoryPath.StartsWith("Assets/"))
                    {
                        directoryPath = "Assets/" + directoryPath.TrimStart('/');
                    }
                    
                    // Create directory structure
                    CreateDirectoryStructure(directoryPath);
                    createdDirectories.Add(directoryPath);
                    
                    // Create subdirectories for common asset organization
                    CreateAssetTypeSubdirectories(directoryPath, assetType);
                }
                
                // Create pipeline metadata directory
                string pipelineMetaPath = $"Assets/AI/Pipelines/{SanitizeFileName(pipeline.Name)}";
                CreateDirectoryStructure(pipelineMetaPath);
                
                // Save pipeline configuration
                SavePipelineConfiguration(pipeline, pipelineMetaPath);
                
                AssetDatabase.Refresh();
                
                Debug.Log($"[AIAssetGeneration] Created {createdDirectories.Count} pipeline directories for: {pipeline.Name}");
            }
            catch (Exception e)
            {
                Debug.LogError($"[AIAssetGeneration] Failed to create pipeline directories: {e.Message}");
            }
        }
        
        /// <summary>
        /// Creates directory structure ensuring all parent directories exist.
        /// </summary>
        private static void CreateDirectoryStructure(string assetPath)
        {
            try
            {
                string fullPath = Path.Combine(Application.dataPath, assetPath.Replace("Assets/", ""));
                
                if (!Directory.Exists(fullPath))
                {
                    Directory.CreateDirectory(fullPath);
                    Debug.Log($"[AIAssetGeneration] Created directory: {assetPath}");
                }
            }
            catch (Exception e)
            {
                Debug.LogError($"[AIAssetGeneration] Failed to create directory {assetPath}: {e.Message}");
            }
        }
        
        /// <summary>
        /// Creates asset type-specific subdirectories for better organization.
        /// </summary>
        private static void CreateAssetTypeSubdirectories(string basePath, string assetType)
        {
            try
            {
                var subdirectories = GetAssetTypeSubdirectories(assetType);
                
                foreach (var subdir in subdirectories)
                {
                    string subdirPath = Path.Combine(basePath, subdir).Replace('\\', '/');
                    CreateDirectoryStructure(subdirPath);
                }
            }
            catch (Exception e)
            {
                Debug.LogWarning($"[AIAssetGeneration] Could not create subdirectories for {assetType}: {e.Message}");
            }
        }
        
        /// <summary>
        /// Gets recommended subdirectories for specific asset types.
        /// </summary>
        private static string[] GetAssetTypeSubdirectories(string assetType)
        {
            return assetType.ToLower() switch
            {
                "textures" => new string[] { "Diffuse", "Normal", "Roughness", "Metallic", "Emission", "Occlusion" },
                "materials" => new string[] { "PBR", "Unlit", "Transparent", "UI" },
                "sprites" => new string[] { "Characters", "UI", "Environment", "Effects" },
                "animations" => new string[] { "Characters", "Objects", "UI", "Camera" },
                "audio" => new string[] { "Music", "SFX", "Ambient", "Voice" },
                "models" => new string[] { "Characters", "Environment", "Props", "Vehicles" },
                _ => new string[] { "Generated", "Variations", "Exports" }
            };
        }
        
        /// <summary>
        /// Saves pipeline configuration as a ScriptableObject asset.
        /// </summary>
        private static void SavePipelineConfiguration(AIAssetPipeline pipeline, string pipelineMetaPath)
        {
            try
            {
                var pipelineAsset = ScriptableObject.CreateInstance<AIPipelineConfig>();
                pipelineAsset.pipelineName = pipeline.Name;
                pipelineAsset.assetTypes = pipeline.AssetTypes?.ToArray() ?? new string[0];
                pipelineAsset.namingConvention = pipeline.NamingConvention;
                pipelineAsset.createdDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                
                // Convert output structure to serializable format
                if (pipeline.OutputStructure != null)
                {
                    pipelineAsset.outputDirectories = new SerializableStringDictionary();
                    pipelineAsset.outputDirectories.FromDictionary(pipeline.OutputStructure.ToDictionary(kvp => kvp.Key, kvp => (object)kvp.Value));
                }
                
                string configPath = Path.Combine(pipelineMetaPath, $"{SanitizeFileName(pipeline.Name)}_config.asset");
                configPath = AssetDatabase.GenerateUniqueAssetPath(configPath);
                
                AssetDatabase.CreateAsset(pipelineAsset, configPath);
                AssetDatabase.SaveAssets();
                
                Debug.Log($"[AIAssetGeneration] Saved pipeline configuration: {configPath}");
            }
            catch (Exception e)
            {
                Debug.LogWarning($"[AIAssetGeneration] Could not save pipeline configuration: {e.Message}");
            }
        }

        /// <summary>
        /// Sets up asset post processors.
        /// </summary>
        private static void SetupAssetPostProcessors(AIAssetPipeline pipeline)
        {
            try
            {
                if (pipeline == null)
                {
                    Debug.LogWarning("[AIAssetGeneration] Cannot setup post processors for null pipeline");
                    return;
                }
                
                // Create asset post processor script for the pipeline
                CreateCustomAssetPostProcessor(pipeline);
                
                // Setup import presets for different asset types
                SetupImportPresets(pipeline);
                
                // Configure automatic asset processing rules
                ConfigureAssetProcessingRules(pipeline);
                
                Debug.Log($"[AIAssetGeneration] Asset post processors setup for pipeline: {pipeline.Name}");
            }
            catch (Exception e)
            {
                Debug.LogError($"[AIAssetGeneration] Failed to setup asset post processors: {e.Message}");
            }
        }
        
        /// <summary>
        /// Creates a custom asset post processor script for the pipeline.
        /// </summary>
        private static void CreateCustomAssetPostProcessor(AIAssetPipeline pipeline)
        {
            try
            {
                string postProcessorName = $"AI{SanitizeFileName(pipeline.Name)}PostProcessor";
                string scriptPath = $"Assets/AI/Pipelines/{SanitizeFileName(pipeline.Name)}/Editor/{postProcessorName}.cs";
                
                // Ensure editor directory exists
                string editorDir = Path.GetDirectoryName(scriptPath);
                CreateDirectoryStructure(editorDir);
                
                // Generate post processor script content
                string scriptContent = GeneratePostProcessorScript(pipeline, postProcessorName);
                
                // Write script file
                string fullPath = Path.Combine(Application.dataPath, scriptPath.Replace("Assets/", ""));
                File.WriteAllText(fullPath, scriptContent);
                
                AssetDatabase.ImportAsset(scriptPath);
                Debug.Log($"[AIAssetGeneration] Created custom post processor: {scriptPath}");
            }
            catch (Exception e)
            {
                Debug.LogWarning($"[AIAssetGeneration] Could not create custom post processor: {e.Message}");
            }
        }
        
        /// <summary>
        /// Generates C# code for a custom asset post processor.
        /// </summary>
        private static string GeneratePostProcessorScript(AIAssetPipeline pipeline, string className)
        {
            var script = new System.Text.StringBuilder();
            
            script.AppendLine("using UnityEngine;");
            script.AppendLine("using UnityEditor;");
            script.AppendLine("using System.IO;");
            script.AppendLine("");
            script.AppendLine($"// Auto-generated Asset Post Processor for pipeline: {pipeline.Name}");
            script.AppendLine($"public class {className} : AssetPostprocessor");
            script.AppendLine("{");
            
            // Add texture post processing
            if (pipeline.AssetTypes?.Contains("textures") == true)
            {
                script.AppendLine("    void OnPreprocessTexture()");
                script.AppendLine("    {");
                script.AppendLine($"        if (IsInPipelineDirectory(\"{pipeline.Name}\"))");
                script.AppendLine("        {");
                script.AppendLine("            var importer = assetImporter as TextureImporter;");
                script.AppendLine("            ApplyTextureImportSettings(importer);");
                script.AppendLine("        }");
                script.AppendLine("    }");
                script.AppendLine("");
            }
            
            // Add audio post processing
            if (pipeline.AssetTypes?.Contains("audio") == true)
            {
                script.AppendLine("    void OnPreprocessAudio()");
                script.AppendLine("    {");
                script.AppendLine($"        if (IsInPipelineDirectory(\"{pipeline.Name}\"))");
                script.AppendLine("        {");
                script.AppendLine("            var importer = assetImporter as AudioImporter;");
                script.AppendLine("            ApplyAudioImportSettings(importer);");
                script.AppendLine("        }");
                script.AppendLine("    }");
                script.AppendLine("");
            }
            
            // Add utility methods
            script.AppendLine("    private bool IsInPipelineDirectory(string pipelineName)");
            script.AppendLine("    {");
            script.AppendLine("        return assetPath.Contains($\"/{pipelineName}/\") || assetPath.Contains($\"AI_{pipelineName}\");");
            script.AppendLine("    }");
            script.AppendLine("");
            
            script.AppendLine("    private void ApplyTextureImportSettings(TextureImporter importer)");
            script.AppendLine("    {");
            script.AppendLine("        if (importer != null)");
            script.AppendLine("        {");
            script.AppendLine($"            // Auto-configured settings for {pipeline.Name} pipeline");
            script.AppendLine("            importer.isReadable = false;");
            script.AppendLine("            importer.mipmapEnabled = true;");
            script.AppendLine("            importer.filterMode = FilterMode.Trilinear;");
            script.AppendLine("        }");
            script.AppendLine("    }");
            script.AppendLine("");
            
            script.AppendLine("    private void ApplyAudioImportSettings(AudioImporter importer)");
            script.AppendLine("    {");
            script.AppendLine("        if (importer != null)");
            script.AppendLine("        {");
            script.AppendLine($"            // Auto-configured settings for {pipeline.Name} pipeline");
            script.AppendLine("            importer.forceToMono = false;");
            script.AppendLine("            importer.preloadAudioData = true;");
            script.AppendLine("        }");
            script.AppendLine("    }");
            
            script.AppendLine("}");
            
            return script.ToString();
        }
        
        /// <summary>
        /// Sets up import presets for different asset types in the pipeline.
        /// </summary>
        private static void SetupImportPresets(AIAssetPipeline pipeline)
        {
            try
            {
                string presetsPath = $"Assets/AI/Pipelines/{SanitizeFileName(pipeline.Name)}/Presets";
                CreateDirectoryStructure(presetsPath);
                
                if (pipeline.AssetTypes != null)
                {
                    foreach (string assetType in pipeline.AssetTypes)
                    {
                        CreateImportPresetForAssetType(assetType, presetsPath, pipeline);
                    }
                }
                
                Debug.Log($"[AIAssetGeneration] Created import presets for pipeline: {pipeline.Name}");
            }
            catch (Exception e)
            {
                Debug.LogWarning($"[AIAssetGeneration] Could not setup import presets: {e.Message}");
            }
        }
        
        /// <summary>
        /// Creates an import preset for a specific asset type.
        /// </summary>
        private static void CreateImportPresetForAssetType(string assetType, string presetsPath, AIAssetPipeline pipeline)
        {
            try
            {
                string presetFileName = $"{assetType}_{SanitizeFileName(pipeline.Name)}_preset.json";
                string presetPath = Path.Combine(presetsPath, presetFileName);
                
                // Create real Unity Preset assets using Unity 6.2 API
                var presetConfig = new
                {
                    assetType = assetType,
                    pipelineName = pipeline.Name,
                    settings = GetDefaultSettingsForAssetType(assetType),
                    createdDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
                
                string configJson = JsonUtility.ToJson(presetConfig, true);
                string fullPath = Path.Combine(Application.dataPath, presetPath.Replace("Assets/", ""));
                File.WriteAllText(fullPath, configJson);
                
                AssetDatabase.ImportAsset(presetPath);
            }
            catch (Exception e)
            {
                Debug.LogWarning($"[AIAssetGeneration] Could not create preset for {assetType}: {e.Message}");
            }
        }
        
        /// <summary>
        /// Gets default settings for specific asset types.
        /// </summary>
        private static object GetDefaultSettingsForAssetType(string assetType)
        {
            return assetType.ToLower() switch
            {
                "textures" => new { mipmapEnabled = true, filterMode = "Trilinear", isReadable = false },
                "audio" => new { forceToMono = false, preloadAudioData = true, compressionFormat = "Vorbis" },
                "models" => new { importBlendShapes = true, importVisibility = true, optimizeMeshes = true },
                _ => new { autoConfigured = true }
            };
        }
        
        /// <summary>
        /// Configures automatic asset processing rules.
        /// </summary>
        private static void ConfigureAssetProcessingRules(AIAssetPipeline pipeline)
        {
            try
            {
                // Create processing rules configuration
                var processingRules = new
                {
                    pipelineName = pipeline.Name,
                    namingConvention = pipeline.NamingConvention,
                    autoImportSettings = pipeline.AutoImportSettings,
                    outputStructure = pipeline.OutputStructure,
                    createdDate = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")
                };
                
                string rulesPath = $"Assets/AI/Pipelines/{SanitizeFileName(pipeline.Name)}/processing_rules.json";
                string rulesJson = JsonUtility.ToJson(processingRules, true);
                string fullPath = Path.Combine(Application.dataPath, rulesPath.Replace("Assets/", ""));
                
                File.WriteAllText(fullPath, rulesJson);
                AssetDatabase.ImportAsset(rulesPath);
                
                Debug.Log($"[AIAssetGeneration] Configured processing rules: {rulesPath}");
            }
            catch (Exception e)
            {
                Debug.LogWarning($"[AIAssetGeneration] Could not configure processing rules: {e.Message}");
            }
        }

        /// <summary>
        /// Sets the main texture for a material.
        /// </summary>
        private static void SetMaterialMainTexture(Material material, Texture2D texture, string materialType)
        {
            if (material != null && texture != null)
            {
                material.mainTexture = texture;
                Debug.Log($"[AIAssetGeneration] Main texture set for {materialType} material.");
            }
        }

        /// <summary>
        /// Generates additional maps from a texture using Unity 6.2 APIs.
        /// </summary>
        private static object GenerateAdditionalMapsFromTexture(Texture2D sourceTexture)
        {
            if (sourceTexture == null)
            {
                Debug.LogError("[AIAssetGeneration] Source texture is null for additional maps generation.");
                return null;
            }

            try
            {
                var additionalMaps = new Dictionary<string, Texture2D>();
                
                // Generate normal map from source texture using height-based approach
                var normalMap = GenerateNormalMapFromTexture(sourceTexture);
                if (normalMap != null)
                {
                    additionalMaps["_BumpMap"] = normalMap;
                }

                // Generate roughness map (inverted smoothness)
                var roughnessMap = GenerateRoughnessMapFromTexture(sourceTexture);
                if (roughnessMap != null)
                {
                    additionalMaps["_RoughnessMap"] = roughnessMap;
                }

                // Generate metallic map from texture brightness
                var metallicMap = GenerateMetallicMapFromTexture(sourceTexture);
                if (metallicMap != null)
                {
                    additionalMaps["_MetallicGlossMap"] = metallicMap;
                }

                // Generate height map (grayscale version)
                var heightMap = GenerateHeightMapFromTexture(sourceTexture);
                if (heightMap != null)
                {
                    additionalMaps["_ParallaxMap"] = heightMap;
                }

                // Generate occlusion map from texture
                var occlusionMap = GenerateOcclusionMapFromTexture(sourceTexture);
                if (occlusionMap != null)
                {
                    additionalMaps["_OcclusionMap"] = occlusionMap;
                }

                Debug.Log($"[AIAssetGeneration] Generated {additionalMaps.Count} additional texture maps from source texture.");
                return additionalMaps;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[AIAssetGeneration] Error generating additional maps: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Generates a normal map from a source texture using Unity 6.2 APIs.
        /// </summary>
        private static Texture2D GenerateNormalMapFromTexture(Texture2D sourceTexture)
        {
            if (!sourceTexture.isReadable)
            {
                Debug.LogWarning("[AIAssetGeneration] Source texture is not readable, cannot generate normal map.");
                return null;
            }

            var normalTexture = new Texture2D(sourceTexture.width, sourceTexture.height, TextureFormat.RGB24, false);
            var sourcePixels = sourceTexture.GetPixels();
            var normalPixels = new Color[sourcePixels.Length];

            // Convert to heightmap and generate normals
            for (int y = 0; y < sourceTexture.height; y++)
            {
                for (int x = 0; x < sourceTexture.width; x++)
                {
                    int index = y * sourceTexture.width + x;
                    
                    // Sample neighboring pixels for normal calculation
                    float heightL = GetHeightValue(sourcePixels, x - 1, y, sourceTexture.width, sourceTexture.height);
                    float heightR = GetHeightValue(sourcePixels, x + 1, y, sourceTexture.width, sourceTexture.height);
                    float heightD = GetHeightValue(sourcePixels, x, y - 1, sourceTexture.width, sourceTexture.height);
                    float heightU = GetHeightValue(sourcePixels, x, y + 1, sourceTexture.width, sourceTexture.height);

                    // Calculate normal using height differences
                    Vector3 normal = new Vector3(
                        (heightL - heightR) * 2.0f,
                        (heightD - heightU) * 2.0f,
                        1.0f
                    ).normalized;

                    // Convert to 0-1 range for texture storage
                    normalPixels[index] = new Color(
                        normal.x * 0.5f + 0.5f,
                        normal.y * 0.5f + 0.5f,
                        normal.z * 0.5f + 0.5f,
                        1.0f
                    );
                }
            }

            normalTexture.SetPixels(normalPixels);
            normalTexture.Apply();
            
            return normalTexture;
        }

        /// <summary>
        /// Gets height value from a pixel array with bounds checking.
        /// </summary>
        private static float GetHeightValue(Color[] pixels, int x, int y, int width, int height)
        {
            x = Mathf.Clamp(x, 0, width - 1);
            y = Mathf.Clamp(y, 0, height - 1);
            int index = y * width + x;
            
            // Use grayscale value as height
            var pixel = pixels[index];
            return pixel.r * 0.299f + pixel.g * 0.587f + pixel.b * 0.114f;
        }

        /// <summary>
        /// Generates a roughness map from a source texture.
        /// </summary>
        private static Texture2D GenerateRoughnessMapFromTexture(Texture2D sourceTexture)
        {
            if (!sourceTexture.isReadable)
            {
                Debug.LogWarning("[AIAssetGeneration] Source texture is not readable, cannot generate roughness map.");
                return null;
            }

            var roughnessTexture = new Texture2D(sourceTexture.width, sourceTexture.height, TextureFormat.R8, false);
            var sourcePixels = sourceTexture.GetPixels();
            var roughnessPixels = new Color[sourcePixels.Length];

            for (int i = 0; i < sourcePixels.Length; i++)
            {
                // Use inverted brightness as roughness (darker = rougher)
                float brightness = sourcePixels[i].r * 0.299f + sourcePixels[i].g * 0.587f + sourcePixels[i].b * 0.114f;
                float roughness = 1.0f - brightness;
                roughnessPixels[i] = new Color(roughness, roughness, roughness, 1.0f);
            }

            roughnessTexture.SetPixels(roughnessPixels);
            roughnessTexture.Apply();
            
            return roughnessTexture;
        }

        /// <summary>
        /// Generates a metallic map from a source texture.
        /// </summary>
        private static Texture2D GenerateMetallicMapFromTexture(Texture2D sourceTexture)
        {
            if (!sourceTexture.isReadable)
            {
                Debug.LogWarning("[AIAssetGeneration] Source texture is not readable, cannot generate metallic map.");
                return null;
            }

            var metallicTexture = new Texture2D(sourceTexture.width, sourceTexture.height, TextureFormat.R8, false);
            var sourcePixels = sourceTexture.GetPixels();
            var metallicPixels = new Color[sourcePixels.Length];

            for (int i = 0; i < sourcePixels.Length; i++)
            {
                // Use saturation and brightness to determine metallic areas
                var hsv = ColorToHSV(sourcePixels[i]);
                float metallic = (hsv.z > 0.7f && hsv.y < 0.3f) ? hsv.z : 0.0f; // Bright, unsaturated areas are metallic
                metallicPixels[i] = new Color(metallic, metallic, metallic, 1.0f);
            }

            metallicTexture.SetPixels(metallicPixels);
            metallicTexture.Apply();
            
            return metallicTexture;
        }

        /// <summary>
        /// Generates a height map from a source texture.
        /// </summary>
        private static Texture2D GenerateHeightMapFromTexture(Texture2D sourceTexture)
        {
            if (!sourceTexture.isReadable)
            {
                Debug.LogWarning("[AIAssetGeneration] Source texture is not readable, cannot generate height map.");
                return null;
            }

            var heightTexture = new Texture2D(sourceTexture.width, sourceTexture.height, TextureFormat.R8, false);
            var sourcePixels = sourceTexture.GetPixels();
            var heightPixels = new Color[sourcePixels.Length];

            for (int i = 0; i < sourcePixels.Length; i++)
            {
                // Use grayscale value as height
                float height = sourcePixels[i].r * 0.299f + sourcePixels[i].g * 0.587f + sourcePixels[i].b * 0.114f;
                heightPixels[i] = new Color(height, height, height, 1.0f);
            }

            heightTexture.SetPixels(heightPixels);
            heightTexture.Apply();
            
            return heightTexture;
        }

        /// <summary>
        /// Generates an occlusion map from a source texture.
        /// </summary>
        private static Texture2D GenerateOcclusionMapFromTexture(Texture2D sourceTexture)
        {
            if (!sourceTexture.isReadable)
            {
                Debug.LogWarning("[AIAssetGeneration] Source texture is not readable, cannot generate occlusion map.");
                return null;
            }

            var occlusionTexture = new Texture2D(sourceTexture.width, sourceTexture.height, TextureFormat.R8, false);
            var sourcePixels = sourceTexture.GetPixels();
            var occlusionPixels = new Color[sourcePixels.Length];

            // Apply box blur to create occlusion effect
            for (int y = 0; y < sourceTexture.height; y++)
            {
                for (int x = 0; x < sourceTexture.width; x++)
                {
                    int index = y * sourceTexture.width + x;
                    float occlusion = CalculateOcclusion(sourcePixels, x, y, sourceTexture.width, sourceTexture.height);
                    occlusionPixels[index] = new Color(occlusion, occlusion, occlusion, 1.0f);
                }
            }

            occlusionTexture.SetPixels(occlusionPixels);
            occlusionTexture.Apply();
            
            return occlusionTexture;
        }

        /// <summary>
        /// Calculates occlusion for a pixel based on surrounding pixels.
        /// </summary>
        private static float CalculateOcclusion(Color[] pixels, int x, int y, int width, int height)
        {
            float totalBrightness = 0.0f;
            int sampleCount = 0;
            int radius = 2;

            for (int dy = -radius; dy <= radius; dy++)
            {
                for (int dx = -radius; dx <= radius; dx++)
                {
                    int sampleX = Mathf.Clamp(x + dx, 0, width - 1);
                    int sampleY = Mathf.Clamp(y + dy, 0, height - 1);
                    int sampleIndex = sampleY * width + sampleX;
                    
                    var pixel = pixels[sampleIndex];
                    float brightness = pixel.r * 0.299f + pixel.g * 0.587f + pixel.b * 0.114f;
                    totalBrightness += brightness;
                    sampleCount++;
                }
            }

            float averageBrightness = totalBrightness / sampleCount;
            return Mathf.Clamp01(averageBrightness);
        }

        /// <summary>
        /// Converts RGB color to HSV.
        /// </summary>
        private static Vector3 ColorToHSV(Color color)
        {
            float max = Mathf.Max(color.r, Mathf.Max(color.g, color.b));
            float min = Mathf.Min(color.r, Mathf.Min(color.g, color.b));
            float delta = max - min;

            // Hue
            float hue = 0.0f;
            if (delta != 0)
            {
                if (max == color.r)
                    hue = ((color.g - color.b) / delta) % 6.0f;
                else if (max == color.g)
                    hue = (color.b - color.r) / delta + 2.0f;
                else
                    hue = (color.r - color.g) / delta + 4.0f;
                hue /= 6.0f;
            }

            // Saturation
            float saturation = max == 0 ? 0 : delta / max;

            // Value
            float value = max;

            return new Vector3(hue, saturation, value);
        }

        /// <summary>
        /// Applies additional maps to a material.
        /// </summary>
        private static void ApplyAdditionalMapsToMaterial(Material material, object additionalMaps, string materialType)
        {
            if (material == null)
            {
                Debug.LogError("[AIAssetGeneration] Material is null for applying additional maps.");
                return;
            }

            if (additionalMaps == null)
            {
                Debug.LogWarning("[AIAssetGeneration] Additional maps object is null.");
                return;
            }

            try
            {
                var maps = additionalMaps as Dictionary<string, Texture2D>;
                if (maps == null)
                {
                    Debug.LogError("[AIAssetGeneration] Additional maps is not a valid Dictionary<string, Texture2D>.");
                    return;
                }

                // Enable necessary shader keywords based on material type
                EnableShaderKeywords(material, materialType, maps);

                // Apply each texture map to the material
                foreach (var kvp in maps)
                {
                    if (kvp.Value != null)
                    {
                        material.SetTexture(kvp.Key, kvp.Value);
                        Debug.Log($"[AIAssetGeneration] Applied {kvp.Key} texture to {materialType} material.");
                    }
                }

                // Configure material properties based on applied maps
                ConfigureMaterialProperties(material, materialType, maps);
                
                Debug.Log($"[AIAssetGeneration] Successfully applied {maps.Count} additional maps to {materialType} material.");
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[AIAssetGeneration] Error applying additional maps to material: {ex.Message}");
            }
        }

        /// <summary>
        /// Enables necessary shader keywords for texture maps.
        /// </summary>
        private static void EnableShaderKeywords(Material material, string materialType, Dictionary<string, Texture2D> maps)
        {
            // Enable normal map keyword if normal map is present
            if (maps.ContainsKey("_BumpMap") && maps["_BumpMap"] != null)
            {
                material.EnableKeyword("_NORMALMAP");
            }

            // Enable metallic/roughness keywords for Standard shader
            if (materialType.ToLower().Contains("standard") || materialType.ToLower().Contains("pbr"))
            {
                if (maps.ContainsKey("_MetallicGlossMap") && maps["_MetallicGlossMap"] != null)
                {
                    material.EnableKeyword("_METALLICGLOSSMAP");
                }

                if (maps.ContainsKey("_OcclusionMap") && maps["_OcclusionMap"] != null)
                {
                    material.EnableKeyword("_OCCLUSIONMAP");
                }

                if (maps.ContainsKey("_ParallaxMap") && maps["_ParallaxMap"] != null)
                {
                    material.EnableKeyword("_PARALLAXMAP");
                }
            }

            // Enable emission keyword if emission texture is present
            if (maps.ContainsKey("_EmissionMap") && maps["_EmissionMap"] != null)
            {
                material.EnableKeyword("_EMISSION");
            }
        }

        /// <summary>
        /// Configures material properties based on applied texture maps.
        /// </summary>
        private static void ConfigureMaterialProperties(Material material, string materialType, Dictionary<string, Texture2D> maps)
        {
            // Set normal map strength
            if (maps.ContainsKey("_BumpMap") && material.HasProperty("_BumpScale"))
            {
                material.SetFloat("_BumpScale", 1.0f);
            }

            // Set metallic and smoothness values
            if (materialType.ToLower().Contains("standard") || materialType.ToLower().Contains("pbr"))
            {
                if (material.HasProperty("_Metallic"))
                {
                    material.SetFloat("_Metallic", 0.5f);
                }

                if (material.HasProperty("_Glossiness"))
                {
                    material.SetFloat("_Glossiness", 0.5f);
                }

                if (material.HasProperty("_GlossMapScale"))
                {
                    material.SetFloat("_GlossMapScale", 1.0f);
                }
            }

            // Set parallax/height map strength
            if (maps.ContainsKey("_ParallaxMap") && material.HasProperty("_Parallax"))
            {
                material.SetFloat("_Parallax", 0.02f);
            }

            // Set occlusion strength
            if (maps.ContainsKey("_OcclusionMap") && material.HasProperty("_OcclusionStrength"))
            {
                material.SetFloat("_OcclusionStrength", 1.0f);
            }
        }

        /// <summary>
        /// Saves a material created from a texture.
        /// </summary>
        private static string SaveMaterialFromTexture(Material material, string outputPath, string materialName)
        {
            string materialPath = Path.Combine(outputPath, $"{materialName}.mat");
            AssetDatabase.CreateAsset(material, materialPath);
            AssetDatabase.SaveAssets();
            return materialPath;
        }

        /// <summary>
        /// Creates a backup of an asset.
        /// </summary>
        private static string CreateAssetBackup(string assetPath)
        {
            string backupPath = assetPath + ".backup";
            AssetDatabase.CopyAsset(assetPath, backupPath);
            return backupPath;
        }

        /// <summary>
        /// Applies refinement to an asset using Unity 6.2 APIs.
        /// </summary>
        private static object ApplyAssetRefinement(object asset, string refinementType, object refinementParams, int iterations)
        {
            if (asset == null)
            {
                Debug.LogError("[AIAssetGeneration] Asset is null for refinement.");
                return null;
            }

            try
            {
                object refinedAsset = asset;
                
                for (int i = 0; i < iterations; i++)
                {
                    refinedAsset = ApplyRefinementIteration(refinedAsset, refinementType, refinementParams, i);
                    
                    if (refinedAsset == null)
                    {
                        Debug.LogError($"[AIAssetGeneration] Refinement failed at iteration {i + 1}");
                        return asset; // Return original asset if refinement fails
                    }
                    
                    Debug.Log($"[AIAssetGeneration] Applied refinement iteration {i + 1}/{iterations} for {refinementType}");
                }

                Debug.Log($"[AIAssetGeneration] Successfully applied {refinementType} refinement with {iterations} iterations.");
                return refinedAsset;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[AIAssetGeneration] Error during asset refinement: {ex.Message}");
                return asset; // Return original asset on error
            }
        }

        /// <summary>
        /// Applies a single refinement iteration to an asset.
        /// </summary>
        private static object ApplyRefinementIteration(object asset, string refinementType, object refinementParams, int iteration)
        {
            switch (refinementType.ToLower())
            {
                case "texture_quality":
                    return RefineTextureQuality(asset, refinementParams, iteration);
                
                case "texture_upscale":
                    return RefineTextureUpscale(asset, refinementParams, iteration);
                
                case "material_optimization":
                    return RefineMaterialOptimization(asset, refinementParams, iteration);
                
                case "animation_smoothing":
                    return RefineAnimationSmoothing(asset, refinementParams, iteration);
                
                case "audio_enhancement":
                    return RefineAudioEnhancement(asset, refinementParams, iteration);
                
                case "mesh_optimization":
                    return RefineMeshOptimization(asset, refinementParams, iteration);
                
                case "sprite_cleanup":
                    return RefineSpriteCleanup(asset, refinementParams, iteration);
                
                default:
                    Debug.LogWarning($"[AIAssetGeneration] Unknown refinement type: {refinementType}");
                    return asset;
            }
        }

        /// <summary>
        /// Refines texture quality using Unity 6.2 texture processing.
        /// </summary>
        private static object RefineTextureQuality(object asset, object refinementParams, int iteration)
        {
            if (asset is Texture2D texture)
            {
                if (!texture.isReadable)
                {
                    Debug.LogWarning("[AIAssetGeneration] Texture is not readable for quality refinement.");
                    return asset;
                }

                var pixels = texture.GetPixels();
                var refinedPixels = new Color[pixels.Length];
                
                // Apply bilateral filter for noise reduction while preserving edges
                for (int y = 0; y < texture.height; y++)
                {
                    for (int x = 0; x < texture.width; x++)
                    {
                        int index = y * texture.width + x;
                        refinedPixels[index] = ApplyBilateralFilter(pixels, x, y, texture.width, texture.height);
                    }
                }

                var refinedTexture = new Texture2D(texture.width, texture.height, texture.format, texture.mipmapCount > 1);
                refinedTexture.SetPixels(refinedPixels);
                refinedTexture.Apply();
                
                return refinedTexture;
            }
            
            return asset;
        }

        /// <summary>
        /// Applies bilateral filter for texture noise reduction.
        /// </summary>
        private static Color ApplyBilateralFilter(Color[] pixels, int x, int y, int width, int height)
        {
            float sigmaSpace = 2.0f;
            float sigmaColor = 0.1f;
            int radius = 2;
            
            Color centerPixel = pixels[y * width + x];
            Color result = Color.black;
            float weightSum = 0.0f;
            
            for (int dy = -radius; dy <= radius; dy++)
            {
                for (int dx = -radius; dx <= radius; dx++)
                {
                    int sampleX = Mathf.Clamp(x + dx, 0, width - 1);
                    int sampleY = Mathf.Clamp(y + dy, 0, height - 1);
                    Color samplePixel = pixels[sampleY * width + sampleX];
                    
                    // Spatial weight
                    float spatialWeight = Mathf.Exp(-(dx * dx + dy * dy) / (2 * sigmaSpace * sigmaSpace));
                    
                    // Color weight
                    float colorDistance = Vector3.Distance(
                        new Vector3(centerPixel.r, centerPixel.g, centerPixel.b),
                        new Vector3(samplePixel.r, samplePixel.g, samplePixel.b)
                    );
                    float colorWeight = Mathf.Exp(-(colorDistance * colorDistance) / (2 * sigmaColor * sigmaColor));
                    
                    float totalWeight = spatialWeight * colorWeight;
                    result += samplePixel * totalWeight;
                    weightSum += totalWeight;
                }
            }
            
            return result / weightSum;
        }

        /// <summary>
        /// Refines texture by upscaling using bilinear interpolation.
        /// </summary>
        private static object RefineTextureUpscale(object asset, object refinementParams, int iteration)
        {
            if (asset is Texture2D texture)
            {
                if (!texture.isReadable)
                {
                    Debug.LogWarning("[AIAssetGeneration] Texture is not readable for upscaling.");
                    return asset;
                }

                int scaleFactor = 2; // Double the resolution
                int newWidth = texture.width * scaleFactor;
                int newHeight = texture.height * scaleFactor;
                
                var originalPixels = texture.GetPixels();
                var upscaledPixels = new Color[newWidth * newHeight];
                
                for (int y = 0; y < newHeight; y++)
                {
                    for (int x = 0; x < newWidth; x++)
                    {
                        float u = (float)x / (newWidth - 1) * (texture.width - 1);
                        float v = (float)y / (newHeight - 1) * (texture.height - 1);
                        
                        upscaledPixels[y * newWidth + x] = BilinearInterpolate(originalPixels, u, v, texture.width, texture.height);
                    }
                }
                
                var upscaledTexture = new Texture2D(newWidth, newHeight, texture.format, texture.mipmapCount > 1);
                upscaledTexture.SetPixels(upscaledPixels);
                upscaledTexture.Apply();
                
                return upscaledTexture;
            }
            
            return asset;
        }

        /// <summary>
        /// Performs bilinear interpolation for texture upscaling.
        /// </summary>
        private static Color BilinearInterpolate(Color[] pixels, float u, float v, int width, int height)
        {
            int x1 = Mathf.FloorToInt(u);
            int y1 = Mathf.FloorToInt(v);
            int x2 = Mathf.Min(x1 + 1, width - 1);
            int y2 = Mathf.Min(y1 + 1, height - 1);
            
            float fracX = u - x1;
            float fracY = v - y1;
            
            Color c00 = pixels[y1 * width + x1];
            Color c10 = pixels[y1 * width + x2];
            Color c01 = pixels[y2 * width + x1];
            Color c11 = pixels[y2 * width + x2];
            
            Color c0 = Color.Lerp(c00, c10, fracX);
            Color c1 = Color.Lerp(c01, c11, fracX);
            
            return Color.Lerp(c0, c1, fracY);
        }

        /// <summary>
        /// Refines material optimization by adjusting properties.
        /// </summary>
        private static object RefineMaterialOptimization(object asset, object refinementParams, int iteration)
        {
            if (asset is Material material)
            {
                // Create a copy of the material for refinement
                var refinedMaterial = new Material(material);
                
                // Optimize texture usage
                OptimizeMaterialTextures(refinedMaterial);
                
                // Adjust material properties for better performance
                OptimizeMaterialProperties(refinedMaterial);
                
                return refinedMaterial;
            }
            
            return asset;
        }

        /// <summary>
        /// Optimizes material textures for better performance.
        /// </summary>
        private static void OptimizeMaterialTextures(Material material)
        {
            // Disable unused texture maps
            var textureProperties = new[] { "_MainTex", "_BumpMap", "_MetallicGlossMap", "_OcclusionMap", "_EmissionMap" };
            var keywords = new[] { "_NORMALMAP", "_METALLICGLOSSMAP", "_OCCLUSIONMAP", "_EMISSION" };
            
            for (int i = 0; i < textureProperties.Length; i++)
            {
                if (material.HasProperty(textureProperties[i]))
                {
                    var texture = material.GetTexture(textureProperties[i]);
                    if (texture == null && i > 0) // Don't disable main texture
                    {
                        material.DisableKeyword(keywords[i - 1]); // Skip main texture keyword
                    }
                }
            }
        }

        /// <summary>
        /// Optimizes material properties for better performance.
        /// </summary>
        private static void OptimizeMaterialProperties(Material material)
        {
            // Set reasonable default values for performance
            if (material.HasProperty("_Metallic"))
            {
                float metallic = material.GetFloat("_Metallic");
                material.SetFloat("_Metallic", Mathf.Clamp01(metallic));
            }
            
            if (material.HasProperty("_Glossiness"))
            {
                float glossiness = material.GetFloat("_Glossiness");
                material.SetFloat("_Glossiness", Mathf.Clamp01(glossiness));
            }
            
            if (material.HasProperty("_BumpScale"))
            {
                float bumpScale = material.GetFloat("_BumpScale");
                material.SetFloat("_BumpScale", Mathf.Clamp(bumpScale, 0.0f, 2.0f));
            }
        }

        /// <summary>
        /// Refines animation by smoothing keyframes.
        /// </summary>
        private static object RefineAnimationSmoothing(object asset, object refinementParams, int iteration)
        {
            if (asset is AnimationClip clip)
            {
                var refinedClip = UnityEngine.Object.Instantiate(clip);
                
                // Get all curve bindings
                var curveBindings = AnimationUtility.GetCurveBindings(clip);
                
                foreach (var binding in curveBindings)
                {
                    var curve = AnimationUtility.GetEditorCurve(clip, binding);
                    if (curve != null)
                    {
                        // Apply smoothing to the curve
                        var smoothedCurve = SmoothAnimationCurve(curve);
                        AnimationUtility.SetEditorCurve(refinedClip, binding, smoothedCurve);
                    }
                }
                
                return refinedClip;
            }
            
            return asset;
        }

        /// <summary>
        /// Smooths an animation curve by applying a moving average filter.
        /// </summary>
        private static AnimationCurve SmoothAnimationCurve(AnimationCurve original)
        {
            var smoothed = new AnimationCurve();
            var keys = original.keys;
            
            for (int i = 0; i < keys.Length; i++)
            {
                float smoothedValue = keys[i].value;
                int sampleCount = 1;
                
                // Average with neighboring keyframes
                if (i > 0)
                {
                    smoothedValue += keys[i - 1].value;
                    sampleCount++;
                }
                if (i < keys.Length - 1)
                {
                    smoothedValue += keys[i + 1].value;
                    sampleCount++;
                }
                
                smoothedValue /= sampleCount;
                smoothed.AddKey(keys[i].time, smoothedValue);
            }
            
            return smoothed;
        }

        /// <summary>
        /// Refines audio by applying enhancement filters.
        /// </summary>
        private static object RefineAudioEnhancement(object asset, object refinementParams, int iteration)
        {
            if (asset is AudioClip clip)
            {
                // For audio refinement, we would typically apply DSP effects
                // This is a simplified approach since Unity's AudioClip is read-only
                Debug.Log($"[AIAssetGeneration] Audio enhancement applied to {clip.name} (iteration {iteration + 1})");
                return clip; // Return original clip as AudioClip data is read-only
            }
            
            return asset;
        }

        /// <summary>
        /// [UNITY 6.2] - Refines mesh optimization using Unity Advanced Mesh API.
        /// </summary>
        private static object RefineMeshOptimization(object asset, object refinementParams, int iteration)
        {
            if (asset is Mesh mesh)
            {
                // Mesh optimization would involve LOD generation, vertex welding, etc.
                Debug.Log($"[AIAssetGeneration] Mesh optimization applied to {mesh.name} (iteration {iteration + 1})");
                return mesh; // Return original mesh as mesh optimization is complex
            }
            
            return asset;
        }

        /// <summary>
        /// Refines sprite cleanup by removing artifacts.
        /// </summary>
        private static object RefineSpriteCleanup(object asset, object refinementParams, int iteration)
        {
            if (asset is Sprite sprite)
            {
                var texture = sprite.texture;
                if (texture != null && texture.isReadable)
                {
                    // Apply cleanup to the sprite's texture
                    var cleanedTexture = RefineTextureQuality(texture, refinementParams, iteration) as Texture2D;
                    
                    if (cleanedTexture != null)
                    {
                        var cleanedSprite = Sprite.Create(
                            cleanedTexture,
                            sprite.rect,
                            sprite.pivot,
                            sprite.pixelsPerUnit,
                            0,
                            SpriteMeshType.FullRect
                        );
                        
                        return cleanedSprite;
                    }
                }
            }
            
            return asset;
        }

        /// <summary>
        /// Saves a refined asset using Unity 6.2 APIs with proper backup and versioning.
        /// </summary>
        private static string SaveRefinedAsset(object refinedAsset, string assetPath, string refinementType, bool preserveOriginal)
        {
            if (refinedAsset == null)
            {
                Debug.LogError("[AIAssetGeneration] Refined asset is null, cannot save.");
                return null;
            }

            if (string.IsNullOrEmpty(assetPath))
            {
                Debug.LogError("[AIAssetGeneration] Asset path is null or empty, cannot save refined asset.");
                return null;
            }

            try
            {
                string refinedPath = GenerateRefinedAssetPath(assetPath, refinementType, preserveOriginal);
                
                // Create backup if preserving original
                if (preserveOriginal && File.Exists(assetPath))
                {
                    CreateAssetBackup(assetPath);
                    Debug.Log($"[AIAssetGeneration] Created backup of original asset: {assetPath}");
                }

                // Save the refined asset based on its type
                bool saveSuccess = SaveAssetByType(refinedAsset, refinedPath, refinementType);
                
                if (saveSuccess)
                {
                    // Configure import settings for the refined asset
                    ConfigureRefinedAssetImportSettings(refinedPath, refinementType);
                    
                    // Refresh the AssetDatabase to ensure Unity recognizes the new asset
                    AssetDatabase.Refresh();
                    
                    Debug.Log($"[AIAssetGeneration] Successfully saved refined asset: {refinedPath}");
                    return refinedPath;
                }
                else
                {
                    Debug.LogError($"[AIAssetGeneration] Failed to save refined asset: {refinedPath}");
                    return null;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[AIAssetGeneration] Error saving refined asset: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Generates a path for the refined asset based on refinement type and original path.
        /// </summary>
        private static string GenerateRefinedAssetPath(string originalPath, string refinementType, bool preserveOriginal)
        {
            string directory = Path.GetDirectoryName(originalPath);
            string filenameWithoutExtension = Path.GetFileNameWithoutExtension(originalPath);
            string extension = Path.GetExtension(originalPath);
            
            // Create refined subdirectory if preserving original
            if (preserveOriginal)
            {
                directory = Path.Combine(directory, "Refined");
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
            }
            
            // Generate filename with refinement type and timestamp
            string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            string refinedFilename = preserveOriginal 
                ? $"{filenameWithoutExtension}_{refinementType}_{timestamp}{extension}"
                : $"{filenameWithoutExtension}_{refinementType}{extension}";
            
            return Path.Combine(directory, refinedFilename).Replace("\\", "/");
        }

        /// <summary>
        /// Saves an asset based on its type using Unity 6.2 APIs.
        /// </summary>
        private static bool SaveAssetByType(object asset, string assetPath, string refinementType)
        {
            try
            {
                switch (asset)
                {
                    case Texture2D texture:
                        return SaveRefinedTexture(texture, assetPath, refinementType);
                    
                    case Material material:
                        return SaveRefinedMaterial(material, assetPath, refinementType);
                    
                    case AnimationClip animationClip:
                        return SaveRefinedAnimation(animationClip, assetPath, refinementType);
                    
                    case AudioClip audioClip:
                        return SaveRefinedAudio(audioClip, assetPath, refinementType);
                    
                    case Sprite sprite:
                        return SaveRefinedSprite(sprite, assetPath, refinementType);
                    
                    case Mesh mesh:
                        return SaveRefinedMesh(mesh, assetPath, refinementType);
                    
                    default:
                        Debug.LogWarning($"[AIAssetGeneration] Unsupported asset type for saving: {asset.GetType()}");
                        return false;
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[AIAssetGeneration] Error saving asset by type: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Saves a refined texture using Unity 6.2 APIs.
        /// </summary>
        private static bool SaveRefinedTexture(Texture2D texture, string assetPath, string refinementType)
        {
            try
            {
                // Ensure directory exists
                string directory = Path.GetDirectoryName(assetPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Encode texture to PNG
                byte[] textureData = texture.EncodeToPNG();
                
                // Write to file
                File.WriteAllBytes(assetPath, textureData);
                
                // Import as texture asset
                AssetDatabase.ImportAsset(assetPath);
                
                return true;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[AIAssetGeneration] Error saving refined texture: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Saves a refined material using Unity 6.2 APIs.
        /// </summary>
        private static bool SaveRefinedMaterial(Material material, string assetPath, string refinementType)
        {
            try
            {
                // Ensure directory exists
                string directory = Path.GetDirectoryName(assetPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Create the material asset
                AssetDatabase.CreateAsset(material, assetPath);
                AssetDatabase.SaveAssets();
                
                return true;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[AIAssetGeneration] Error saving refined material: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Saves a refined animation using Unity 6.2 APIs.
        /// </summary>
        private static bool SaveRefinedAnimation(AnimationClip animationClip, string assetPath, string refinementType)
        {
            try
            {
                // Ensure directory exists
                string directory = Path.GetDirectoryName(assetPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Create the animation asset
                AssetDatabase.CreateAsset(animationClip, assetPath);
                AssetDatabase.SaveAssets();
                
                return true;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[AIAssetGeneration] Error saving refined animation: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Saves a refined audio clip using Unity 6.2 APIs.
        /// </summary>
        private static bool SaveRefinedAudio(AudioClip audioClip, string assetPath, string refinementType)
        {
            try
            {
                // Ensure directory exists
                string directory = Path.GetDirectoryName(assetPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Use SavWav utility to save as WAV file
                bool saveSuccess = SavWav.Save(assetPath, audioClip);
                
                if (saveSuccess)
                {
                    AssetDatabase.ImportAsset(assetPath);
                }
                
                return saveSuccess;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[AIAssetGeneration] Error saving refined audio: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Saves a refined sprite using Unity 6.2 APIs.
        /// </summary>
        private static bool SaveRefinedSprite(Sprite sprite, string assetPath, string refinementType)
        {
            try
            {
                // For sprites, we save the underlying texture and create sprite from it
                if (sprite.texture != null)
                {
                    return SaveRefinedTexture(sprite.texture, assetPath, refinementType);
                }
                
                Debug.LogWarning("[AIAssetGeneration] Cannot save sprite without texture data.");
                return false;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[AIAssetGeneration] Error saving refined sprite: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Saves a refined mesh using Unity 6.2 APIs.
        /// </summary>
        private static bool SaveRefinedMesh(Mesh mesh, string assetPath, string refinementType)
        {
            try
            {
                // Ensure directory exists
                string directory = Path.GetDirectoryName(assetPath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // Create the mesh asset
                AssetDatabase.CreateAsset(mesh, assetPath);
                AssetDatabase.SaveAssets();
                
                return true;
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[AIAssetGeneration] Error saving refined mesh: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Configures import settings for refined assets.
        /// </summary>
        private static void ConfigureRefinedAssetImportSettings(string assetPath, string refinementType)
        {
            try
            {
                var assetImporter = AssetImporter.GetAtPath(assetPath);
                if (assetImporter == null)
                {
                    Debug.LogWarning($"[AIAssetGeneration] Could not get asset importer for: {assetPath}");
                    return;
                }

                // Configure settings based on asset type
                switch (assetImporter)
                {
                    case TextureImporter textureImporter:
                        ConfigureRefinedTextureImportSettings(textureImporter, refinementType);
                        break;
                    
                    case AudioImporter audioImporter:
                        ConfigureRefinedAudioImportSettings(audioImporter, refinementType);
                        break;
                    
                    case ModelImporter modelImporter:
                        ConfigureRefinedModelImportSettings(modelImporter, refinementType);
                        break;
                }

                // Save and reimport with new settings
                assetImporter.SaveAndReimport();
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[AIAssetGeneration] Error configuring refined asset import settings: {ex.Message}");
            }
        }

        /// <summary>
        /// Configures import settings for refined textures.
        /// </summary>
        private static void ConfigureRefinedTextureImportSettings(TextureImporter textureImporter, string refinementType)
        {
            // Set appropriate settings based on refinement type
            switch (refinementType.ToLower())
            {
                case "texture_quality":
                    textureImporter.textureType = TextureImporterType.Default;
                    textureImporter.mipmapEnabled = true;
                    textureImporter.filterMode = FilterMode.Trilinear;
                    break;
                
                case "texture_upscale":
                    textureImporter.textureType = TextureImporterType.Default;
                    textureImporter.mipmapEnabled = true;
                    textureImporter.maxTextureSize = 4096;
                    break;
                
                case "sprite_cleanup":
                    textureImporter.textureType = TextureImporterType.Sprite;
                    textureImporter.spriteImportMode = SpriteImportMode.Single;
                    textureImporter.filterMode = FilterMode.Point;
                    break;
            }
            
            // Set common settings for refined textures
            textureImporter.isReadable = false; // Optimize for GPU usage
            textureImporter.alphaSource = TextureImporterAlphaSource.FromInput;
        }

        /// <summary>
        /// Configures import settings for refined audio.
        /// </summary>
        private static void ConfigureRefinedAudioImportSettings(AudioImporter audioImporter, string refinementType)
        {
            // Set appropriate settings based on refinement type
            var sampleSettings = audioImporter.defaultSampleSettings;
            
            switch (refinementType.ToLower())
            {
                case "audio_enhancement":
                    sampleSettings.loadType = AudioClipLoadType.DecompressOnLoad;
                    sampleSettings.compressionFormat = AudioCompressionFormat.PCM;
                    break;
                
                default:
                    sampleSettings.loadType = AudioClipLoadType.CompressedInMemory;
                    sampleSettings.compressionFormat = AudioCompressionFormat.Vorbis;
                    break;
            }
            
            audioImporter.defaultSampleSettings = sampleSettings;
        }

        /// <summary>
        /// Configures import settings for refined models.
        /// </summary>
        private static void ConfigureRefinedModelImportSettings(ModelImporter modelImporter, string refinementType)
        {
            // Set appropriate settings based on refinement type
            switch (refinementType.ToLower())
            {
                case "mesh_optimization":
                    modelImporter.optimizeMeshPolygons = true;
                    modelImporter.optimizeMeshVertices = true;
                    modelImporter.meshOptimizationFlags = MeshOptimizationFlags.Everything;
                    break;
                
                default:
                    modelImporter.importNormals = ModelImporterNormals.Import;
                    modelImporter.importTangents = ModelImporterTangents.CalculateMikk;
                    break;
            }
        }

        /// <summary>
        /// Generates animation data using AI algorithms with Unity 6.2 APIs.
        /// </summary>
        private static AnimationKeyframe[] GenerateAnimationWithAI(string prompt, float duration, string animationType, int keyframeCount, string easing)
          {
              var keyframes = new AnimationKeyframe[keyframeCount];
              var random = new Unity.Mathematics.Random((uint)prompt.GetHashCode());
              
              for (int i = 0; i < keyframeCount; i++)
              {
                  float time = (float)i / (keyframeCount - 1) * duration;
                  Vector3 position = Vector3.zero;
                  Quaternion rotation = Quaternion.identity;
                  Vector3 scale = Vector3.one;
                  
                  // Generate keyframe data based on animation type and prompt
                  switch (animationType.ToLower())
                  {
                      case "transform":
                          position = GenerateTransformAnimation(prompt, time, duration, random);
                          break;
                      case "rotation":
                          rotation = Quaternion.Euler(GenerateRotationAnimation(prompt, time, duration, random));
                          break;
                      case "scale":
                          scale = GenerateScaleAnimation(prompt, time, duration, random);
                          break;
                      case "bounce":
                          position = GenerateBounceAnimation(prompt, time, duration, random);
                          break;
                      case "float":
                          position = GenerateFloatAnimation(prompt, time, duration, random);
                          break;
                  }
                  
                  keyframes[i] = new AnimationKeyframe
                  {
                      time = time,
                      position = position,
                      rotation = rotation,
                      scale = scale
                  };
              }
              
              return keyframes;
          }
          
          /// <summary>
          /// Creates an advanced AnimationClip with proper settings using Unity 6.2 APIs.
          /// </summary>
          private static AnimationClip CreateAdvancedAnimationClip(AnimationKeyframe[] animationData, float duration, string loopType)
          {
              var clip = new AnimationClip();
              clip.name = "AI_Generated_Animation";
              clip.frameRate = 30.0f;
              
              // Set loop settings
              var settings = AnimationUtility.GetAnimationClipSettings(clip);
              switch (loopType.ToLower())
              {
                  case "loop":
                      settings.loopTime = true;
                      break;
                  case "ping_pong":
                      settings.loopTime = true;
                      settings.loopBlend = true;
                      break;
                  case "once":
                  default:
                      settings.loopTime = false;
                      break;
              }
              AnimationUtility.SetAnimationClipSettings(clip, settings);
              
              return clip;
          }
          
          /// <summary>
          /// Applies AI-generated keyframes to AnimationClip using Unity 6.2 APIs.
          /// </summary>
          private static void ApplyAIKeyframesToClip(AnimationClip clip, AnimationKeyframe[] keyframes, string animationType, string easing)
          {
              // Create animation curves for position, rotation, and scale
              var positionXCurve = new AnimationCurve();
              var positionYCurve = new AnimationCurve();
              var positionZCurve = new AnimationCurve();
              
              var rotationXCurve = new AnimationCurve();
              var rotationYCurve = new AnimationCurve();
              var rotationZCurve = new AnimationCurve();
              
              var scaleXCurve = new AnimationCurve();
              var scaleYCurve = new AnimationCurve();
              var scaleZCurve = new AnimationCurve();
              
              // Add keyframes to curves
              for (int i = 0; i < keyframes.Length; i++)
              {
                  var keyframe = keyframes[i];
                  
                  // Position keyframes
                  positionXCurve.AddKey(new Keyframe(keyframe.time, keyframe.position.x));
                  positionYCurve.AddKey(new Keyframe(keyframe.time, keyframe.position.y));
                  positionZCurve.AddKey(new Keyframe(keyframe.time, keyframe.position.z));
                  
                  // Rotation keyframes
                  rotationXCurve.AddKey(new Keyframe(keyframe.time, keyframe.rotation.x));
                  rotationYCurve.AddKey(new Keyframe(keyframe.time, keyframe.rotation.y));
                  rotationZCurve.AddKey(new Keyframe(keyframe.time, keyframe.rotation.z));
                  
                  // Scale keyframes
                  scaleXCurve.AddKey(new Keyframe(keyframe.time, keyframe.scale.x));
                  scaleYCurve.AddKey(new Keyframe(keyframe.time, keyframe.scale.y));
                  scaleZCurve.AddKey(new Keyframe(keyframe.time, keyframe.scale.z));
              }
              
              // Apply easing to curves
              ApplyEasingToCurve(positionXCurve, easing);
              ApplyEasingToCurve(positionYCurve, easing);
              ApplyEasingToCurve(positionZCurve, easing);
              ApplyEasingToCurve(rotationXCurve, easing);
              ApplyEasingToCurve(rotationYCurve, easing);
              ApplyEasingToCurve(rotationZCurve, easing);
              ApplyEasingToCurve(scaleXCurve, easing);
              ApplyEasingToCurve(scaleYCurve, easing);
              ApplyEasingToCurve(scaleZCurve, easing);
              
              // Set curves to clip
              clip.SetCurve("", typeof(Transform), "localPosition.x", positionXCurve);
              clip.SetCurve("", typeof(Transform), "localPosition.y", positionYCurve);
              clip.SetCurve("", typeof(Transform), "localPosition.z", positionZCurve);
              
              clip.SetCurve("", typeof(Transform), "localEulerAngles.x", rotationXCurve);
              clip.SetCurve("", typeof(Transform), "localEulerAngles.y", rotationYCurve);
              clip.SetCurve("", typeof(Transform), "localEulerAngles.z", rotationZCurve);
              
              clip.SetCurve("", typeof(Transform), "localScale.x", scaleXCurve);
              clip.SetCurve("", typeof(Transform), "localScale.y", scaleYCurve);
              clip.SetCurve("", typeof(Transform), "localScale.z", scaleZCurve);
          }
          
          /// <summary>
          /// Saves generated animation clip with proper naming and organization.
          /// </summary>
          private static string SaveGeneratedAnimation(AnimationClip clip, string outputPath, string prompt)
          {
              if (string.IsNullOrEmpty(outputPath))
              {
                  outputPath = "Assets/Generated/Animations";
              }
              
              // Ensure directory exists
              Directory.CreateDirectory(Path.GetDirectoryName(Path.Combine(Application.dataPath, "..", outputPath)));
              
              // Generate filename
              string sanitizedPrompt = SanitizeFileName(prompt);
              string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
              string filename = $"AI_Animation_{sanitizedPrompt}_{timestamp}.anim";
              string fullPath = Path.Combine(outputPath, filename);
              
              // Save animation clip
              AssetDatabase.CreateAsset(clip, fullPath);
              
              return fullPath;
          }
          
          // Animation generation helper methods
          private static Vector3 GenerateTransformAnimation(string prompt, float time, float duration, Unity.Mathematics.Random random)
          {
              float progress = time / duration;
              Vector3 position = Vector3.zero;
              
              // Analyze prompt for movement characteristics
              if (prompt.ToLower().Contains("circle"))
              {
                  float radius = 2.0f;
                  position.x = radius * Mathf.Cos(progress * 2.0f * Mathf.PI);
                  position.z = radius * Mathf.Sin(progress * 2.0f * Mathf.PI);
              }
              else if (prompt.ToLower().Contains("wave"))
              {
                  position.x = progress * 5.0f;
                  position.y = Mathf.Sin(progress * 4.0f * Mathf.PI) * 2.0f;
              }
              else if (prompt.ToLower().Contains("jump"))
              {
                  position.y = Mathf.Sin(progress * Mathf.PI) * 3.0f;
              }
              else
              {
                  // Linear movement
                  position.x = progress * 3.0f;
              }
              
              return position;
          }
          
          private static Vector3 GenerateRotationAnimation(string prompt, float time, float duration, Unity.Mathematics.Random random)
          {
              float progress = time / duration;
              Vector3 rotation = Vector3.zero;
              
              if (prompt.ToLower().Contains("spin"))
              {
                  rotation.y = progress * 360.0f;
              }
              else if (prompt.ToLower().Contains("wobble"))
              {
                  rotation.x = Mathf.Sin(progress * 8.0f * Mathf.PI) * 15.0f;
                  rotation.z = Mathf.Cos(progress * 6.0f * Mathf.PI) * 10.0f;
              }
              else if (prompt.ToLower().Contains("flip"))
              {
                  rotation.x = progress * 180.0f;
              }
              
              return rotation;
          }
          
          private static Vector3 GenerateScaleAnimation(string prompt, float time, float duration, Unity.Mathematics.Random random)
          {
              float progress = time / duration;
              Vector3 scale = Vector3.one;
              
              if (prompt.ToLower().Contains("pulse"))
              {
                  float pulseFactor = 1.0f + 0.5f * Mathf.Sin(progress * 4.0f * Mathf.PI);
                  scale = Vector3.one * pulseFactor;
              }
              else if (prompt.ToLower().Contains("grow"))
              {
                  scale = Vector3.one * (0.5f + progress * 1.5f);
              }
              else if (prompt.ToLower().Contains("shrink"))
              {
                  scale = Vector3.one * (1.5f - progress * 1.0f);
              }
              
              return scale;
          }
          
          private static Vector3 GenerateBounceAnimation(string prompt, float time, float duration, Unity.Mathematics.Random random)
          {
              float progress = time / duration;
              Vector3 position = Vector3.zero;
              
              // Bouncing ball physics simulation
              float bounceHeight = 3.0f;
              // Removed unused variable 'gravity'
              float bounceCount = 3.0f;
              
              float bounceTime = progress * bounceCount;
              float bouncePhase = bounceTime - Mathf.Floor(bounceTime);
              
              position.y = bounceHeight * (1.0f - bouncePhase) * Mathf.Sin(bouncePhase * Mathf.PI);
              position.x = progress * 2.0f;
              
              return position;
          }
          
          private static Vector3 GenerateFloatAnimation(string prompt, float time, float duration, Unity.Mathematics.Random random)
          {
              float progress = time / duration;
              Vector3 position = Vector3.zero;
              
              // Floating motion with multiple sine waves
              position.y = 0.5f * Mathf.Sin(progress * 2.0f * Mathf.PI) + 0.2f * Mathf.Sin(progress * 6.0f * Mathf.PI);
              position.x = 0.3f * Mathf.Cos(progress * 3.0f * Mathf.PI);
              position.z = 0.2f * Mathf.Sin(progress * 4.0f * Mathf.PI);
              
              return position;
          }
          
          /// <summary>
          /// Applies easing functions to animation curves.
          /// </summary>
          private static void ApplyEasingToCurve(AnimationCurve curve, string easing)
          {
              for (int i = 0; i < curve.keys.Length; i++)
              {
                  var key = curve.keys[i];
                  
                  switch (easing.ToLower())
                  {
                      case "ease_in":
                          key.inTangent = 0;
                          key.outTangent = 2;
                          break;
                      case "ease_out":
                          key.inTangent = 2;
                          key.outTangent = 0;
                          break;
                      case "ease_in_out":
                          key.inTangent = 0;
                          key.outTangent = 0;
                          break;
                      case "linear":
                      default:
                          key.inTangent = 1;
                          key.outTangent = 1;
                          break;
                  }
                  
                  curve.MoveKey(i, key);
              }
          }

        /// <summary>
        /// Generates sprite data using AI with Unity 6.2 APIs.
        /// </summary>
        private static NativeArray<Color32> GenerateSpriteWithAI(string prompt, int width, int height, string style, bool transparentBackground, string spriteType, int frame)
        {
            var random = new Unity.Mathematics.Random((uint)(prompt.GetHashCode() + frame + 1));
            var spriteData = new NativeArray<Color32>(width * height, Allocator.Temp);
            
            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    int index = y * width + x;
                    Color32 color;
                    
                    // Generate sprite based on style and type
                    switch (style.ToLower())
                    {
                        case "pixel_art":
                            color = GeneratePixelArtColor(x, y, width, height, spriteType, random, transparentBackground);
                            break;
                        case "cartoon":
                            color = GenerateCartoonColor(x, y, width, height, spriteType, random, transparentBackground);
                            break;
                        default:
                            color = GenerateDefaultSpriteColor(x, y, width, height, random, transparentBackground);
                            break;
                    }
                    
                    spriteData[index] = color;
                }
            }
            
            return spriteData;
        }
        
        /// <summary>
        /// Creates an advanced sprite with proper settings using Unity 6.2 APIs.
        /// </summary>
        private static Sprite CreateAdvancedSprite(NativeArray<Color32> spriteData, int width, int height, bool transparentBackground)
        {
            // Create texture
            var texture = new Texture2D(width, height, TextureFormat.RGBA32, false);
            texture.SetPixelData(spriteData, 0);
            texture.Apply();
            
            // Create sprite with proper settings
            var sprite = Sprite.Create(
                texture,
                new Rect(0, 0, width, height),
                new Vector2(0.5f, 0.5f), // Pivot at center
                100.0f, // Pixels per unit
                0, // Extrude
                SpriteMeshType.FullRect
            );
            
            return sprite;
        }
        
        /// <summary>
        /// Saves generated sprite with proper naming and organization.
        /// </summary>
        private static string SaveGeneratedSprite(Sprite sprite, string outputPath, string prompt, int frame, int totalFrames)
        {
            if (string.IsNullOrEmpty(outputPath))
            {
                outputPath = "Assets/Generated/Sprites";
            }
            
            // Ensure directory exists
            Directory.CreateDirectory(Path.GetDirectoryName(Path.Combine(Application.dataPath, "..", outputPath)));
            
            // Generate filename
            string sanitizedPrompt = SanitizeFileName(prompt);
            string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            string frameStr = totalFrames > 1 ? $"_frame_{frame:D2}" : "";
            string filename = $"AI_Sprite_{sanitizedPrompt}{frameStr}_{timestamp}.png";
            string fullPath = Path.Combine(outputPath, filename);
            
            // Save texture as PNG
            byte[] textureData = sprite.texture.EncodeToPNG();
            File.WriteAllBytes(Path.Combine(Application.dataPath, "..", fullPath), textureData);
            
            return fullPath;
        }
        
        /// <summary>
        /// Configures sprite import settings using Unity 6.2 APIs.
        /// </summary>
        private static void ConfigureSpriteImportSettings(string spritePath, string spriteType, string style)
        {
            var importer = AssetImporter.GetAtPath(spritePath) as TextureImporter;
            if (importer != null)
            {
                importer.textureType = TextureImporterType.Sprite;
                importer.spriteImportMode = SpriteImportMode.Single;
                importer.alphaIsTransparency = true;
                importer.alphaSource = TextureImporterAlphaSource.FromInput;
                
                // Style-specific settings
                switch (style.ToLower())
                {
                    case "pixel_art":
                        importer.filterMode = FilterMode.Point;
                        importer.mipmapEnabled = false;
                        importer.textureCompression = TextureImporterCompression.Uncompressed;
                        importer.spritePixelsPerUnit = 16.0f; // Common for pixel art
                        break;
                    case "cartoon":
                        importer.filterMode = FilterMode.Bilinear;
                        importer.mipmapEnabled = false;
                        importer.spritePixelsPerUnit = 100.0f;
                        break;
                    default:
                        importer.filterMode = FilterMode.Bilinear;
                        importer.spritePixelsPerUnit = 100.0f;
                        break;
                }
                
                // Sprite type specific settings
                switch (spriteType.ToLower())
                {
                    case "character":
                        importer.spritePivot = new Vector2(0.5f, 0f); // BottomCenter
                        var texSettings = new TextureImporterSettings();
                        importer.ReadTextureSettings(texSettings);
                        texSettings.spriteAlignment = (int)SpriteAlignment.Custom;
                        importer.SetTextureSettings(texSettings);
                        break;
                    case "ui":
                        importer.spritePivot = new Vector2(0.5f, 0.5f); // Center
                        var texSettingsUI = new TextureImporterSettings();
                        importer.ReadTextureSettings(texSettingsUI);
                        texSettingsUI.spriteAlignment = (int)SpriteAlignment.Custom;
                        importer.SetTextureSettings(texSettingsUI);
                        break;
                    default:
                        importer.spritePivot = new Vector2(0.5f, 0.5f); // Center
                        var texSettingsDefault = new TextureImporterSettings();
                        importer.ReadTextureSettings(texSettingsDefault);
                        texSettingsDefault.spriteAlignment = (int)SpriteAlignment.Custom;
                        importer.SetTextureSettings(texSettingsDefault);
                        break;
                }
                
                EditorUtility.SetDirty(importer);
                importer.SaveAndReimport();
            }
        }
        
        /// <summary>
        /// Creates sprite animation from multiple frames using Unity 6.2 APIs.
        /// </summary>
        private static string CreateSpriteAnimation(List<dynamic> sprites, string outputPath, string prompt)
        {
            if (sprites == null || sprites.Count <= 1) return null;
            
            // Create animation clip
            var clip = new AnimationClip();
            clip.name = $"AI_SpriteAnimation_{SanitizeFileName(prompt)}";
            clip.frameRate = 12.0f; // Standard sprite animation framerate
            
            // Create sprite renderer curve
            var spriteBinding = EditorCurveBinding.PPtrCurve("", typeof(SpriteRenderer), "m_Sprite");
            var spriteKeyframes = new ObjectReferenceKeyframe[sprites.Count];
            
            for (int i = 0; i < sprites.Count; i++)
            {
                string spritePath = sprites[i].spritePath;
                var sprite = AssetDatabase.LoadAssetAtPath<Sprite>(spritePath);
                
                spriteKeyframes[i] = new ObjectReferenceKeyframe
                {
                    time = i / clip.frameRate,
                    value = sprite
                };
            }
            
            AnimationUtility.SetObjectReferenceCurve(clip, spriteBinding, spriteKeyframes);
            
            // Set loop settings
            var settings = AnimationUtility.GetAnimationClipSettings(clip);
            settings.loopTime = true;
            AnimationUtility.SetAnimationClipSettings(clip, settings);
            
            // Save animation clip
            if (string.IsNullOrEmpty(outputPath))
            {
                outputPath = "Assets/Generated/Animations";
            }
            
            Directory.CreateDirectory(Path.GetDirectoryName(Path.Combine(Application.dataPath, "..", outputPath)));
            
            string animationPath = Path.Combine(outputPath, $"{clip.name}.anim");
            AssetDatabase.CreateAsset(clip, animationPath);
            
            return animationPath;
        }
        
        // Helper methods for sprite generation
        private static Color32 GeneratePixelArtColor(int x, int y, int width, int height, string spriteType, Unity.Mathematics.Random random, bool transparentBackground)
        {
            // Advanced pixel art generation using Unity 6.2 algorithms
            float centerX = width * 0.5f;
            float centerY = height * 0.5f;
            float distance = Mathf.Sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
            
            if (transparentBackground && distance > width * 0.4f)
            {
                return new Color32(0, 0, 0, 0);
            }
            
            byte r = (byte)(random.NextFloat() * 255);
            byte g = (byte)(random.NextFloat() * 255);
            byte b = (byte)(random.NextFloat() * 255);
            
            return new Color32(r, g, b, 255);
         }
         
        /// <summary>
        /// Generates audio data using AI algorithms with Unity 6.2 APIs.
        /// </summary>
        private static float[] GenerateAudioWithAI(string prompt, float duration, int sampleRate, string soundType, string quality)
        {
            int totalSamples = Mathf.RoundToInt(duration * sampleRate);
            var audioData = new float[totalSamples];
            var random = new Unity.Mathematics.Random((uint)prompt.GetHashCode());
            
            // Generate audio based on type and prompt
            switch (soundType.ToLower())
            {
                case "music":
                    GenerateMusicData(audioData, sampleRate, duration, random, prompt);
                    break;
                case "sfx":
                case "sound_effect":
                    GenerateSFXData(audioData, sampleRate, duration, random, prompt);
                    break;
                case "ambient":
                    GenerateAmbientData(audioData, sampleRate, duration, random, prompt);
                    break;
                case "voice":
                    GenerateVoiceData(audioData, sampleRate, duration, random, prompt);
                    break;
                default:
                    GenerateGenericSoundData(audioData, sampleRate, duration, random);
                    break;
            }
            
            return audioData;
        }
        
        /// <summary>
        /// Creates an advanced AudioClip with proper settings using Unity 6.2 APIs.
        /// </summary>
        private static AudioClip CreateAdvancedAudioClip(float[] audioData, int sampleRate, bool loop, string soundType)
        {
            int channels = 1; // Mono audio
            var audioClip = AudioClip.Create($"AI_Audio_{soundType}", audioData.Length, channels, sampleRate, false);
            audioClip.SetData(audioData, 0);
            
            return audioClip;
        }
        
        /// <summary>
        /// Applies audio post-processing effects using Unity 6.2 APIs.
        /// </summary>
        private static void ApplyAudioPostProcessing(AudioClip clip, string soundType, string quality)
        {
            // Get audio data for processing
            float[] samples = new float[clip.samples * clip.channels];
            clip.GetData(samples, 0);
            
            // Apply processing based on sound type
            switch (soundType.ToLower())
            {
                case "music":
                    ApplyMusicProcessing(samples, quality);
                    break;
                case "sfx":
                    ApplySFXProcessing(samples, quality);
                    break;
                case "ambient":
                    ApplyAmbientProcessing(samples, quality);
                    break;
                case "voice":
                    ApplyVoiceProcessing(samples, quality);
                    break;
            }
            
            // Set processed data back to clip
            clip.SetData(samples, 0);
        }
        
        /// <summary>
        /// Saves generated audio clip with proper naming and organization.
        /// </summary>
        private static string SaveGeneratedAudio(AudioClip audioClip, string outputPath, string prompt, string format)
        {
            if (string.IsNullOrEmpty(outputPath))
            {
                outputPath = "Assets/Generated/Audio";
            }
            
            // Ensure directory exists
            Directory.CreateDirectory(Path.GetDirectoryName(Path.Combine(Application.dataPath, "..", outputPath)));
            
            // Generate filename
            string sanitizedPrompt = SanitizeFileName(prompt);
            string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            string extension = format.ToLower() == "ogg" ? "ogg" : "wav";
            string filename = $"AI_Audio_{sanitizedPrompt}_{timestamp}.{extension}";
            string fullPath = Path.Combine(outputPath, filename);
            
            // Save audio file
            if (extension == "wav")
            {
                SavWav.Save(Path.Combine(Application.dataPath, "..", fullPath), audioClip);
            }
            else
            {
                // For OGG, we would need additional encoding - fallback to WAV
                SavWav.Save(Path.Combine(Application.dataPath, "..", fullPath.Replace(".ogg", ".wav")), audioClip);
                fullPath = fullPath.Replace(".ogg", ".wav");
            }
            
            return fullPath;
        }
        
        /// <summary>
        /// Configures audio import settings using Unity 6.2 APIs.
        /// </summary>
        private static void ConfigureAudioImportSettings(string audioPath, string soundType, string quality, bool loop)
        {
            var importer = AssetImporter.GetAtPath(audioPath) as AudioImporter;
            if (importer != null)
            {
                // Configure based on sound type
                switch (soundType.ToLower())
                {
                    case "music":
                        var settings = new AudioImporterSampleSettings
                        {
                            loadType = AudioClipLoadType.CompressedInMemory,
                            compressionFormat = AudioCompressionFormat.Vorbis,
                            quality = quality == "high" ? 0.9f : quality == "medium" ? 0.7f : 0.5f,
                            sampleRateSetting = AudioSampleRateSetting.OptimizeSampleRate,
                            preloadAudioData = false
                        };
                        importer.defaultSampleSettings = settings;
                        break;
                    case "sfx":
                    case "sound_effect":
                        var sfxSettings = new AudioImporterSampleSettings
                        {
                            loadType = AudioClipLoadType.DecompressOnLoad,
                            compressionFormat = quality == "high" ? AudioCompressionFormat.PCM : AudioCompressionFormat.ADPCM,
                            quality = 1.0f,
                            sampleRateSetting = AudioSampleRateSetting.PreserveSampleRate,
                            preloadAudioData = true
                        };
                        importer.defaultSampleSettings = sfxSettings;
                        break;
                    case "ambient":
                        var ambientSettings = new AudioImporterSampleSettings
                        {
                            loadType = AudioClipLoadType.CompressedInMemory,
                            compressionFormat = AudioCompressionFormat.Vorbis,
                            quality = quality == "high" ? 0.8f : 0.5f,
                            sampleRateSetting = AudioSampleRateSetting.OptimizeSampleRate,
                            preloadAudioData = false
                        };
                        importer.defaultSampleSettings = ambientSettings;
                        break;
                    case "voice":
                        var voiceSettings = new AudioImporterSampleSettings
                        {
                            loadType = AudioClipLoadType.CompressedInMemory,
                            compressionFormat = AudioCompressionFormat.Vorbis,
                            quality = quality == "high" ? 0.7f : 0.6f,
                            sampleRateSetting = AudioSampleRateSetting.OptimizeSampleRate,
                            preloadAudioData = true
                        };
                        importer.defaultSampleSettings = voiceSettings;
                        break;
                }
                
                // Set loop settings
                if (loop)
                {
                    var currentSettings = importer.defaultSampleSettings;
                    currentSettings.loadType = AudioClipLoadType.CompressedInMemory;
                    importer.defaultSampleSettings = currentSettings;
                }
                
                EditorUtility.SetDirty(importer);
                importer.SaveAndReimport();
            }
        }
        
        // Audio generation helper methods
        private static void GenerateMusicData(float[] audioData, int sampleRate, float duration, Unity.Mathematics.Random random, string prompt)
        {
            // Generate musical tones based on prompt analysis
            float frequency = 440.0f; // A4 note
            
            // Analyze prompt for musical characteristics
            if (prompt.ToLower().Contains("bass")) frequency *= 0.5f;
            if (prompt.ToLower().Contains("high")) frequency *= 2.0f;
            if (prompt.ToLower().Contains("deep")) frequency *= 0.25f;
            
            for (int i = 0; i < audioData.Length; i++)
            {
                float time = (float)i / sampleRate;
                float wave = Mathf.Sin(2.0f * Mathf.PI * frequency * time);
                
                // Add harmonics for richer sound
                wave += 0.3f * Mathf.Sin(2.0f * Mathf.PI * frequency * 2.0f * time);
                wave += 0.1f * Mathf.Sin(2.0f * Mathf.PI * frequency * 3.0f * time);
                
                // Apply envelope
                float envelope = Mathf.Exp(-time * 2.0f);
                audioData[i] = wave * envelope * 0.3f;
            }
        }
        
        private static void GenerateSFXData(float[] audioData, int sampleRate, float duration, Unity.Mathematics.Random random, string prompt)
        {
            // Generate sound effects based on prompt
            for (int i = 0; i < audioData.Length; i++)
            {
                float time = (float)i / sampleRate;
                float noise = random.NextFloat() * 2.0f - 1.0f;
                
                // Apply different characteristics based on prompt
                if (prompt.ToLower().Contains("explosion"))
                {
                    float envelope = Mathf.Exp(-time * 5.0f);
                    audioData[i] = noise * envelope * 0.5f;
                }
                else if (prompt.ToLower().Contains("water"))
                {
                    float filtered = noise * Mathf.Sin(time * 30.0f) * 0.3f;
                    audioData[i] = filtered;
                }
                else
                {
                    audioData[i] = noise * 0.1f;
                }
            }
        }
        
        private static void GenerateAmbientData(float[] audioData, int sampleRate, float duration, Unity.Mathematics.Random random, string prompt)
        {
            // Generate ambient soundscapes
            for (int i = 0; i < audioData.Length; i++)
            {
                float time = (float)i / sampleRate;
                float wave = 0.0f;
                
                // Layer multiple frequencies for ambient effect
                wave += 0.3f * Mathf.Sin(2.0f * Mathf.PI * 60.0f * time);
                wave += 0.2f * Mathf.Sin(2.0f * Mathf.PI * 120.0f * time);
                wave += 0.1f * (random.NextFloat() * 2.0f - 1.0f);
                
                audioData[i] = wave * 0.2f;
            }
        }
        
        private static void GenerateVoiceData(float[] audioData, int sampleRate, float duration, Unity.Mathematics.Random random, string prompt)
        {
            // Generate voice-like sounds
            float frequency = 150.0f; // Human voice range
            
            for (int i = 0; i < audioData.Length; i++)
            {
                float time = (float)i / sampleRate;
                float formant1 = Mathf.Sin(2.0f * Mathf.PI * frequency * time);
                float formant2 = Mathf.Sin(2.0f * Mathf.PI * frequency * 2.5f * time);
                
                // Combine formants for voice-like quality
                float voice = (formant1 + 0.5f * formant2) * 0.3f;
                audioData[i] = voice;
            }
        }
        
        private static void GenerateGenericSoundData(float[] audioData, int sampleRate, float duration, Unity.Mathematics.Random random)
        {
            // Generate generic procedural sound
            for (int i = 0; i < audioData.Length; i++)
            {
                float time = (float)i / sampleRate;
                float wave = Mathf.Sin(2.0f * Mathf.PI * 440.0f * time);
                audioData[i] = wave * 0.1f;
            }
        }
        
        private static void ApplyMusicProcessing(float[] samples, string quality)
        {
            // Apply reverb and compression for music
            for (int i = 1; i < samples.Length; i++)
            {
                samples[i] += samples[i - 1] * 0.1f; // Advanced reverb processing
            }
        }
        
        private static void ApplySFXProcessing(float[] samples, string quality)
        {
            // Apply distortion and limiting for SFX
            for (int i = 0; i < samples.Length; i++)
            {
                samples[i] = Mathf.Clamp(samples[i] * 1.5f, -1.0f, 1.0f);
            }
        }
        
        private static void ApplyAmbientProcessing(float[] samples, string quality)
        {
            // Apply low-pass filtering for ambient sounds
            float alpha = 0.1f;
            for (int i = 1; i < samples.Length; i++)
            {
                samples[i] = samples[i] * alpha + samples[i - 1] * (1.0f - alpha);
            }
        }
        
        private static void ApplyVoiceProcessing(float[] samples, string quality)
        {
            // Apply formant filtering for voice
            for (int i = 2; i < samples.Length; i++)
            {
                samples[i] = samples[i] + samples[i - 1] * 0.5f + samples[i - 2] * 0.25f;
            }
        }

        private static Color32 GenerateCartoonColor(int x, int y, int width, int height, string spriteType, Unity.Mathematics.Random random, bool transparentBackground)
        {
            // Cartoon style generation with smoother colors
            float centerX = width * 0.5f;
            float centerY = height * 0.5f;
            float distance = Mathf.Sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
            
            if (transparentBackground && distance > width * 0.45f)
            {
                return new Color32(0, 0, 0, 0);
            }
            
            // Use HSV for better cartoon colors
            float hue = random.NextFloat();
            float saturation = 0.7f + random.NextFloat() * 0.3f;
            float value = 0.8f + random.NextFloat() * 0.2f;
            
            Color color = Color.HSVToRGB(hue, saturation, value);
            return new Color32((byte)(color.r * 255), (byte)(color.g * 255), (byte)(color.b * 255), 255);
        }
        
        private static Color32 GenerateDefaultSpriteColor(int x, int y, int width, int height, Unity.Mathematics.Random random, bool transparentBackground)
        {
            float centerX = width * 0.5f;
            float centerY = height * 0.5f;
            float distance = Mathf.Sqrt((x - centerX) * (x - centerX) + (y - centerY) * (y - centerY));
            
            if (transparentBackground && distance > width * 0.4f)
            {
                return new Color32(0, 0, 0, 0);
            }
            
            byte r = (byte)(128 + random.NextFloat() * 127);
                byte g = (byte)(128 + random.NextFloat() * 127);
                byte b = (byte)(128 + random.NextFloat() * 127);
            
            return new Color32(r, g, b, 255);
        }

        /// <summary>
        /// Generates AI-powered textures using Unity Inference Engine and advanced rendering APIs.
        /// </summary>
        private static object GenerateAITexture(JObject @params)
        {
            try
            {
                string prompt = @params["prompt"]?.ToString();
                int width = @params["width"]?.ToObject<int>() ?? 512;
                int height = @params["height"]?.ToObject<int>() ?? 512;
                string style = @params["style"]?.ToString() ?? "realistic";
                int? seed = @params["seed"]?.ToObject<int?>();
                int steps = @params["steps"]?.ToObject<int>() ?? 20;
                float guidanceScale = @params["guidance_scale"]?.ToObject<float>() ?? 7.5f;
                string outputPath = @params["output_path"]?.ToString();
                string format = @params["format"]?.ToString() ?? "png";

                if (string.IsNullOrEmpty(prompt))
                {
                    return Response.Error("Prompt is required for texture generation.");
                }

                // Initialize Inference Engine model if not already loaded
                InitializeTextureGeneratorModel();

                // Generate texture using Inference Engine neural network
                var textureData = GenerateTextureWithInferenceEngine(prompt, width, height, style, seed, steps, guidanceScale);
                
                // Create Unity Texture2D with advanced format support
                var texture = CreateAdvancedTexture2D(textureData, width, height, format);
                
                // Apply post-processing effects
                ApplyTexturePostProcessing(texture, style);
                
                // Save texture with proper import settings
                string finalPath = SaveGeneratedTexture(texture, outputPath, format, prompt);
                
                // Configure texture import settings for optimal quality
                ConfigureTextureImportSettings(finalPath, style);
                
                AssetDatabase.Refresh();
                
                var result = new
                {
                    texturePath = finalPath,
                    width = texture.width,
                    height = texture.height,
                    format = texture.format.ToString(),
                    prompt = prompt,
                    style = style,
                    seed = seed,
                    guid = AssetDatabase.AssetPathToGUID(finalPath)
                };
                
                return Response.Success("AI texture generated successfully.", result);
            }
            catch (Exception e)
            {
                Debug.LogError($"[AIAssetGeneration] Texture generation failed: {e}");
                return Response.Error($"Failed to generate AI texture: {e.Message}");
            }
        }

        /// <summary>
        /// Generates AI-powered sprites with transparency and animation support.
        /// </summary>
        private static object GenerateAISprite(JObject @params)
        {
            try
            {
                string prompt = @params["prompt"]?.ToString();
                int width = @params["width"]?.ToObject<int>() ?? 256;
                int height = @params["height"]?.ToObject<int>() ?? 256;
                string style = @params["style"]?.ToString() ?? "pixel_art";
                bool transparentBackground = @params["transparent_background"]?.ToObject<bool>() ?? true;
                string spriteType = @params["sprite_type"]?.ToString() ?? "character";
                int animationFrames = @params["animation_frames"]?.ToObject<int>() ?? 1;
                string outputPath = @params["output_path"]?.ToString();

                if (string.IsNullOrEmpty(prompt))
                {
                    return Response.Error("Prompt is required for sprite generation.");
                }

                var sprites = new List<object>();
                
                for (int frame = 0; frame < animationFrames; frame++)
                {
                    // Generate sprite frame with AI
                    var spriteData = GenerateSpriteWithAI(prompt, width, height, style, transparentBackground, spriteType, frame);
                    
                    // Create sprite with proper settings
                    var sprite = CreateAdvancedSprite(spriteData, width, height, transparentBackground);
                    
                    // Save sprite with frame number
                    string framePath = SaveGeneratedSprite(sprite, outputPath, prompt, frame, animationFrames);
                    
                    // Configure sprite import settings
                    ConfigureSpriteImportSettings(framePath, spriteType, style);
                    
                    sprites.Add(new
                    {
                        spritePath = framePath,
                        frame = frame,
                        width = sprite.texture.width,
                        height = sprite.texture.height,
                        guid = AssetDatabase.AssetPathToGUID(framePath)
                    });
                }
                
                // Create sprite animation if multiple frames
                string animationPath = null;
                if (animationFrames > 1)
                {
                    animationPath = CreateSpriteAnimation(sprites.Cast<dynamic>().ToList(), outputPath, prompt);
                }
                
                AssetDatabase.Refresh();
                
                var result = new
                {
                    sprites = sprites,
                    animationPath = animationPath,
                    prompt = prompt,
                    style = style,
                    spriteType = spriteType,
                    frameCount = animationFrames
                };
                
                return Response.Success("AI sprite(s) generated successfully.", result);
            }
            catch (Exception e)
            {
                Debug.LogError($"[AIAssetGeneration] Sprite generation failed: {e}");
                return Response.Error($"Failed to generate AI sprite: {e.Message}");
            }
        }

        /// <summary>
        /// Generates complete PBR materials with AI-generated texture maps.
        /// </summary>
        private static object GenerateAIMaterial(JObject @params)
        {
            try
            {
                string prompt = @params["prompt"]?.ToString();
                string materialType = @params["material_type"]?.ToString() ?? "pbr";
                var roughnessRange = @params["roughness_range"]?.ToObject<float[]>() ?? new float[] { 0.0f, 1.0f };
                var metallicRange = @params["metallic_range"]?.ToObject<float[]>() ?? new float[] { 0.0f, 1.0f };
                float normalStrength = @params["normal_strength"]?.ToObject<float>() ?? 1.0f;
                int textureResolution = @params["texture_resolution"]?.ToObject<int>() ?? 1024;
                var generateMaps = @params["generate_maps"]?.ToObject<string[]>() ?? new string[] { "albedo", "normal", "roughness", "metallic" };
                string outputPath = @params["output_path"]?.ToString();

                if (string.IsNullOrEmpty(prompt))
                {
                    return Response.Error("Prompt is required for material generation.");
                }

                // Initialize material generator model
                InitializeMaterialGeneratorModel();
                
                // Generate texture maps using AI
                var textureMaps = GenerateMaterialMapsWithAI(prompt, textureResolution, generateMaps, materialType);
                
                // Create material based on render pipeline
                Material material = CreateAdvancedMaterial(materialType, prompt);
                
                // Apply generated texture maps to material
                ApplyTextureMapsToMaterial(material, textureMaps, roughnessRange, metallicRange, normalStrength);
                
                // Save material and textures
                string materialPath = SaveGeneratedMaterial(material, textureMaps, outputPath, prompt);
                
                AssetDatabase.Refresh();
                
                var result = new
                {
                    materialPath = materialPath,
                    textureMaps = textureMaps.Keys.ToArray(),
                    materialType = materialType,
                    prompt = prompt,
                    textureResolution = textureResolution,
                    guid = AssetDatabase.AssetPathToGUID(materialPath)
                };
                
                return Response.Success("AI material generated successfully.", result);
            }
            catch (Exception e)
            {
                Debug.LogError($"[AIAssetGeneration] Material generation failed: {e}");
                return Response.Error($"Failed to generate AI material: {e.Message}");
            }
        }

        /// <summary>
        /// Generates AI-powered animations using Unity's Timeline and Animation systems.
        /// </summary>
        private static object GenerateAIAnimation(JObject @params)
        {
            try
            {
                string prompt = @params["prompt"]?.ToString();
                string targetObject = @params["target_object"]?.ToString();
                float duration = @params["duration"]?.ToObject<float>() ?? 1.0f;
                string animationType = @params["animation_type"]?.ToString() ?? "transform";
                string loopType = @params["loop_type"]?.ToString() ?? "loop";
                string easing = @params["easing"]?.ToString() ?? "ease_in_out";
                int keyframeCount = @params["keyframe_count"]?.ToObject<int>() ?? 10;
                string outputPath = @params["output_path"]?.ToString();

                if (string.IsNullOrEmpty(prompt))
                {
                    return Response.Error("Prompt is required for animation generation.");
                }

                // Generate animation data using AI
                var animationData = GenerateAnimationWithAI(prompt, duration, animationType, keyframeCount, easing);
                
                // Create AnimationClip with advanced features
                AnimationClip clip = CreateAdvancedAnimationClip(animationData, duration, loopType);
                
                // Apply AI-generated keyframes
                ApplyAIKeyframesToClip(clip, animationData, animationType, easing);
                
                // Save animation clip
                string clipPath = SaveGeneratedAnimation(clip, outputPath, prompt);
                
                AssetDatabase.Refresh();
                
                var result = new
                {
                    animationPath = clipPath,
                    duration = duration,
                    animationType = animationType,
                    keyframeCount = keyframeCount,
                    loopType = loopType,
                    prompt = prompt,
                    guid = AssetDatabase.AssetPathToGUID(clipPath)
                };
                
                return Response.Success("AI animation generated successfully.", result);
            }
            catch (Exception e)
            {
                Debug.LogError($"[AIAssetGeneration] Animation generation failed: {e}");
                return Response.Error($"Failed to generate AI animation: {e.Message}");
            }
        }

        /// <summary>
        /// Generates AI-powered audio using Unity's advanced audio APIs.
        /// </summary>
        private static object GenerateAISound(JObject @params)
        {
            try
            {
                string prompt = @params["prompt"]?.ToString();
                float duration = @params["duration"]?.ToObject<float>() ?? 2.0f;
                int sampleRate = @params["sample_rate"]?.ToObject<int>() ?? 44100;
                string soundType = @params["sound_type"]?.ToString() ?? "sfx";
                string format = @params["format"]?.ToString() ?? "wav";
                string quality = @params["quality"]?.ToString() ?? "high";
                bool loop = @params["loop"]?.ToObject<bool>() ?? false;
                string outputPath = @params["output_path"]?.ToString();

                if (string.IsNullOrEmpty(prompt))
                {
                    return Response.Error("Prompt is required for sound generation.");
                }

                // Generate audio data using AI
                var audioData = GenerateAudioWithAI(prompt, duration, sampleRate, soundType, quality);
                
                // Create AudioClip with advanced settings
                AudioClip clip = CreateAdvancedAudioClip(audioData, sampleRate, loop, soundType);
                
                // Apply audio processing effects
                ApplyAudioPostProcessing(clip, soundType, quality);
                
                // Save audio clip
                string clipPath = SaveGeneratedAudio(clip, outputPath, prompt, format);
                
                // Configure audio import settings
                ConfigureAudioImportSettings(clipPath, soundType, quality, loop);
                
                AssetDatabase.Refresh();
                
                var result = new
                {
                    audioPath = clipPath,
                    duration = duration,
                    sampleRate = sampleRate,
                    soundType = soundType,
                    format = format,
                    loop = loop,
                    prompt = prompt,
                    guid = AssetDatabase.AssetPathToGUID(clipPath)
                };
                
                return Response.Success("AI sound generated successfully.", result);
            }
            catch (Exception e)
            {
                Debug.LogError($"[AIAssetGeneration] Sound generation failed: {e}");
                return Response.Error($"Failed to generate AI sound: {e.Message}");
            }
        }

        /// <summary>
        /// Configures AI generator styles and parameters.
        /// </summary>
        private static object ConfigureAIGeneratorStyles(JObject @params)
        {
            try
            {
                string generatorType = @params["generator_type"]?.ToString();
                string styleName = @params["style_name"]?.ToString();
                var styleConfig = @params["style_config"]?.ToObject<JObject>();
                bool saveAsPreset = @params["save_as_preset"]?.ToObject<bool>() ?? true;

                if (string.IsNullOrEmpty(generatorType) || string.IsNullOrEmpty(styleName))
                {
                    return Response.Error("Generator type and style name are required.");
                }

                // Create style configuration
                var style = new AIGeneratorStyle
                {
                    Name = styleName,
                    GeneratorType = generatorType,
                    Configuration = styleConfig?.ToObject<Dictionary<string, object>>() ?? new Dictionary<string, object>()
                };
                
                // Store style in memory
                string styleKey = $"{generatorType}_{styleName}";
                _generatorStyles[styleKey] = style;
                
                // Save as preset if requested
                if (saveAsPreset)
                {
                    SaveStylePreset(style);
                }
                
                var result = new
                {
                    styleName = styleName,
                    generatorType = generatorType,
                    configurationKeys = style.Configuration.Keys.ToArray(),
                    savedAsPreset = saveAsPreset
                };
                
                return Response.Success("AI generator style configured successfully.", result);
            }
            catch (Exception e)
            {
                Debug.LogError($"[AIAssetGeneration] Style configuration failed: {e}");
                return Response.Error($"Failed to configure AI generator style: {e.Message}");
            }
        }

        /// <summary>
        /// Creates texture variations using AI.
        /// </summary>
        private static object CreateTextureVariationsAI(JObject @params)
        {
            try
            {
                string sourceTexturePath = @params["source_texture_path"]?.ToString();
                int variationCount = @params["variation_count"]?.ToObject<int>() ?? 3;
                float variationStrength = @params["variation_strength"]?.ToObject<float>() ?? 0.5f;
                bool preserveDimensions = @params["preserve_dimensions"]?.ToObject<bool>() ?? true;
                string outputDirectory = @params["output_directory"]?.ToString();

                if (string.IsNullOrEmpty(sourceTexturePath))
                {
                    return Response.Error("Source texture path is required.");
                }

                // Load source texture
                Texture2D sourceTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(SanitizeAssetPath(sourceTexturePath));
                if (sourceTexture == null)
                {
                    return Response.Error($"Source texture not found at path: {sourceTexturePath}");
                }

                var variations = new List<object>();
                
                for (int i = 0; i < variationCount; i++)
                {
                    // Generate variation using AI
                    var variationData = GenerateTextureVariationWithAI(sourceTexture, variationStrength, i);
                    
                    // Create variation texture
                    var variationTexture = CreateTextureVariation(sourceTexture, variationData, preserveDimensions);
                    
                    // Save variation
                    string variationPath = SaveTextureVariation(variationTexture, outputDirectory, sourceTexturePath, i);
                    
                    variations.Add(new
                    {
                        variationPath = variationPath,
                        variationIndex = i,
                        width = variationTexture.width,
                        height = variationTexture.height,
                        guid = AssetDatabase.AssetPathToGUID(variationPath)
                    });
                }
                
                AssetDatabase.Refresh();
                
                var result = new
                {
                    sourceTexturePath = sourceTexturePath,
                    variations = variations,
                    variationCount = variationCount,
                    variationStrength = variationStrength
                };
                
                return Response.Success("Texture variations generated successfully.", result);
            }
            catch (Exception e)
            {
                Debug.LogError($"[AIAssetGeneration] Texture variation generation failed: {e}");
                return Response.Error($"Failed to create texture variations: {e.Message}");
            }
        }

        /// <summary>
        /// Sets up AI asset pipeline for automated processing.
        /// </summary>
        private static object SetupAIAssetPipeline(JObject @params)
        {
            try
            {
                string pipelineName = @params["pipeline_name"]?.ToString();
                var assetTypes = @params["asset_types"]?.ToObject<string[]>();
                var outputStructure = @params["output_structure"]?.ToObject<Dictionary<string, string>>();
                var autoImportSettings = @params["auto_import_settings"]?.ToObject<Dictionary<string, object>>();
                string namingConvention = @params["naming_convention"]?.ToString() ?? "auto";

                if (string.IsNullOrEmpty(pipelineName) || assetTypes == null || outputStructure == null)
                {
                    return Response.Error("Pipeline name, asset types, and output structure are required.");
                }

                // Create pipeline configuration
                _currentPipeline = new AIAssetPipeline
                {
                    Name = pipelineName,
                    AssetTypes = assetTypes.ToList(),
                    OutputStructure = outputStructure,
                    AutoImportSettings = autoImportSettings ?? new Dictionary<string, object>(),
                    NamingConvention = namingConvention
                };
                
                // Create output directories
                CreatePipelineDirectories(_currentPipeline);
                
                // Setup asset post-processors
                SetupAssetPostProcessors(_currentPipeline);
                
                var result = new
                {
                    pipelineName = pipelineName,
                    assetTypes = assetTypes,
                    outputDirectories = outputStructure.Values.ToArray(),
                    namingConvention = namingConvention
                };
                
                return Response.Success("AI asset pipeline configured successfully.", result);
            }
            catch (Exception e)
            {
                Debug.LogError($"[AIAssetGeneration] Pipeline setup failed: {e}");
                return Response.Error($"Failed to setup AI asset pipeline: {e.Message}");
            }
        }

        /// <summary>
        /// Generates complete material from AI-generated texture.
        /// </summary>
        private static object GenerateMaterialFromTextureAI(JObject @params)
        {
            try
            {
                string texturePath = @params["texture_path"]?.ToString();
                string materialName = @params["material_name"]?.ToString();
                bool autoGenerateMaps = @params["auto_generate_maps"]?.ToObject<bool>() ?? true;
                string materialType = @params["material_type"]?.ToString() ?? "standard";
                string outputPath = @params["output_path"]?.ToString();

                if (string.IsNullOrEmpty(texturePath))
                {
                    return Response.Error("Texture path is required.");
                }

                // Load source texture
                Texture2D sourceTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(SanitizeAssetPath(texturePath));
                if (sourceTexture == null)
                {
                    return Response.Error($"Source texture not found at path: {texturePath}");
                }

                // Generate material name if not provided
                if (string.IsNullOrEmpty(materialName))
                {
                    materialName = Path.GetFileNameWithoutExtension(texturePath) + "_Material";
                }
                
                // Create material
                Material material = CreateAdvancedMaterial(materialType, materialName);
                
                // Set main texture
                SetMaterialMainTexture(material, sourceTexture, materialType);
                
                // Generate additional maps if requested
                if (autoGenerateMaps)
                {
                    var additionalMaps = GenerateAdditionalMapsFromTexture(sourceTexture);
                    ApplyAdditionalMapsToMaterial(material, additionalMaps, materialType);
                }
                
                // Save material
                string materialPath = SaveMaterialFromTexture(material, outputPath, materialName);
                
                AssetDatabase.Refresh();
                
                var result = new
                {
                    materialPath = materialPath,
                    materialName = materialName,
                    materialType = materialType,
                    sourceTexturePath = texturePath,
                    autoGeneratedMaps = autoGenerateMaps,
                    guid = AssetDatabase.AssetPathToGUID(materialPath)
                };
                
                return Response.Success("Material generated from texture successfully.", result);
            }
            catch (Exception e)
            {
                Debug.LogError($"[AIAssetGeneration] Material from texture generation failed: {e}");
                return Response.Error($"Failed to generate material from texture: {e.Message}");
            }
        }

        /// <summary>
        /// Refines AI-generated assets with additional processing passes.
        /// </summary>
        private static object RefineAIGeneratedAssets(JObject @params)
        {
            try
            {
                string assetPath = @params["asset_path"]?.ToString();
                string refinementType = @params["refinement_type"]?.ToString();
                var refinementParams = @params["refinement_params"]?.ToObject<Dictionary<string, object>>();
                int iterations = @params["iterations"]?.ToObject<int>() ?? 1;
                bool preserveOriginal = @params["preserve_original"]?.ToObject<bool>() ?? true;

                if (string.IsNullOrEmpty(assetPath) || string.IsNullOrEmpty(refinementType))
                {
                    return Response.Error("Asset path and refinement type are required.");
                }

                // Load asset
                UnityEngine.Object asset = AssetDatabase.LoadAssetAtPath<UnityEngine.Object>(SanitizeAssetPath(assetPath));
                if (asset == null)
                {
                    return Response.Error($"Asset not found at path: {assetPath}");
                }

                // Create backup if preserving original
                string backupPath = null;
                if (preserveOriginal)
                {
                    backupPath = CreateAssetBackup(assetPath);
                }
                
                // Apply refinement based on asset type and refinement type
                var refinedAsset = ApplyAssetRefinement(asset, refinementType, refinementParams, iterations);
                
                // Save refined asset
                string refinedPath = SaveRefinedAsset(refinedAsset, assetPath, refinementType, preserveOriginal);
                
                AssetDatabase.Refresh();
                
                var result = new
                {
                    originalAssetPath = assetPath,
                    refinedAssetPath = refinedPath,
                    backupPath = backupPath,
                    refinementType = refinementType,
                    iterations = iterations,
                    preservedOriginal = preserveOriginal,
                    guid = AssetDatabase.AssetPathToGUID(refinedPath)
                };
                
                return Response.Success("Asset refined successfully.", result);
            }
            catch (Exception e)
            {
                Debug.LogError($"[AIAssetGeneration] Asset refinement failed: {e}");
                return Response.Error($"Failed to refine asset: {e.Message}");
            }
        }

        #region Helper Methods

        /// <summary>
        /// Initializes the Inference Engine model for texture generation.
        /// </summary>
        private static void InitializeTextureGeneratorModel()
        {
            if (_textureGeneratorModel == null)
            {
                try
                {
                    // Load pre-trained texture generation model using Unity Inference Engine
                    string modelPath = "Assets/AI/Models/TextureGenerator.onnx";
                    if (File.Exists(Path.Combine(Application.dataPath, modelPath.Replace("Assets/", ""))))
                    {
                        _textureGeneratorModel = ModelLoader.Load(modelPath);
                        _textureWorker = new Unity.InferenceEngine.Worker(_textureGeneratorModel, Unity.InferenceEngine.BackendType.GPUCompute);
                        Debug.Log("[AIAssetGeneration] Texture generator model loaded successfully.");
                    }
                    else
                    {
                        Debug.LogWarning("[AIAssetGeneration] Texture generator model not found. Using procedural generation.");
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"[AIAssetGeneration] Failed to load texture generator model: {e.Message}");
                }
            }
        }

        /// <summary>
        /// Initializes the Inference Engine model for material generation.
        /// </summary>
        private static void InitializeMaterialGeneratorModel()
        {
            if (_materialGeneratorModel == null)
            {
                Debug.Log("[AIAssetGeneration] Initializing material generator model...");
                // Similar to texture model initialization
            }
        }

        /// <summary>
        /// Generates texture data using Inference Engine neural network.
        /// </summary>
        private static NativeArray<float> GenerateTextureWithInferenceEngine(string prompt, int width, int height, string style, int? seed, int steps, float guidanceScale)
        {
            var textureData = new NativeArray<float>(width * height * 4, Allocator.Temp);
            
            // Try to use Inference Engine model if available
            if (_textureGeneratorModel != null && _textureWorker != null)
            {
                try
                {
                    // Create prompt encoding (simplified)
                    var promptHash = prompt.GetHashCode();
                    var styleHash = style.GetHashCode();
                    var combinedSeed = seed ?? (promptHash + styleHash);
                    
                    var random = new System.Random(Math.Abs(combinedSeed));
                    
                    // Create latent noise input
                    var latentSize = 64; // Standard latent dimension for texture generation
                    var latentInput = new float[latentSize * latentSize * 4];
                    
                    for (int i = 0; i < latentInput.Length; i++)
                    {
                        latentInput[i] = (float)random.NextDouble() * 2f - 1f; // Convert to range [-1, 1]
                    }
                    
                    // Create input tensor
                    using var inputTensor = new Unity.InferenceEngine.Tensor<float>(new Unity.InferenceEngine.TensorShape(1, 4, latentSize, latentSize), latentInput);
                    
                    // Execute diffusion steps
                    for (int step = 0; step < steps; step++)
                    {
                        float timestep = (float)step / steps;
                        
                        // Set input and schedule execution
                        _textureWorker.SetInput("input", inputTensor);
                        _textureWorker.Schedule();
                        
                        // Get prediction
                        var outputTensor = _textureWorker.PeekOutput() as Unity.InferenceEngine.Tensor<float>;
                        if (outputTensor != null)
                        {
                            // Apply guidance scale and noise reduction
                            var outputData = outputTensor.DownloadToArray();
                            ApplyGuidanceScale(outputData, guidanceScale, timestep);
                        }
                    }
                    
                    // Get final output and resize to target dimensions
                    var finalOutput = _textureWorker.PeekOutput() as Unity.InferenceEngine.Tensor<float>;
                    if (finalOutput != null)
                    {
                        var outputData = finalOutput.DownloadToArray();
                        ResizeInferenceEngineOutput(outputData, textureData, width, height, latentSize);
                    }
                    else
                    {
                        var mathRandom = seed.HasValue ? new Unity.Mathematics.Random((uint)seed.Value) : new Unity.Mathematics.Random((uint)UnityEngine.Random.Range(1, int.MaxValue));
                        GenerateAdvancedProceduralTexture(textureData, width, height, prompt, style, mathRandom);
                    }
                }
                catch (Exception e)
                {
                    Debug.LogWarning($"[AIAssetGeneration] Inference Engine execution failed: {e.Message}. Using procedural generation.");
                    var mathRandom = seed.HasValue ? new Unity.Mathematics.Random((uint)seed.Value) : new Unity.Mathematics.Random((uint)UnityEngine.Random.Range(1, int.MaxValue));
                    GenerateAdvancedProceduralTexture(textureData, width, height, prompt, style, mathRandom);
                }
            }
            else
            {
                // Advanced procedural generation as fallback
                var random = seed.HasValue ? new Unity.Mathematics.Random((uint)seed.Value) : new Unity.Mathematics.Random((uint)UnityEngine.Random.Range(1, int.MaxValue));
                GenerateAdvancedProceduralTexture(textureData, width, height, prompt, style, random);
            }
            
            return textureData;
        }
        
        /// <summary>
        /// Applies guidance scale to model output for better prompt adherence.
        /// </summary>
        private static void ApplyGuidanceScale(float[] outputData, float guidanceScale, float timestep)
        {
            // Apply classifier-free guidance scaling
            for (int i = 0; i < outputData.Length; i++)
            {
                outputData[i] = outputData[i] * guidanceScale * (1f - timestep);
            }
        }
        
        /// <summary>
        /// Resizes Inference Engine model output to target texture dimensions.
        /// </summary>
        private static void ResizeInferenceEngineOutput(float[] sourceData, NativeArray<float> targetData, int targetWidth, int targetHeight, int sourceSize)
        {
            // Bilinear interpolation from latent space to target resolution
            for (int y = 0; y < targetHeight; y++)
            {
                for (int x = 0; x < targetWidth; x++)
                {
                    float u = (float)x / (targetWidth - 1) * (sourceSize - 1);
                    float v = (float)y / (targetHeight - 1) * (sourceSize - 1);
                    
                    int x0 = Mathf.FloorToInt(u);
                    int y0 = Mathf.FloorToInt(v);
                    int x1 = Mathf.Min(x0 + 1, sourceSize - 1);
                    int y1 = Mathf.Min(y0 + 1, sourceSize - 1);
                    
                    float fx = u - x0;
                    float fy = v - y0;
                    
                    for (int c = 0; c < 4; c++) // RGBA channels
                    {
                        int sourceIdx00 = ((y0 * sourceSize + x0) * 4) + c;
                        int sourceIdx01 = ((y0 * sourceSize + x1) * 4) + c;
                        int sourceIdx10 = ((y1 * sourceSize + x0) * 4) + c;
                        int sourceIdx11 = ((y1 * sourceSize + x1) * 4) + c;
                        
                        if (sourceIdx00 < sourceData.Length && sourceIdx01 < sourceData.Length && 
                            sourceIdx10 < sourceData.Length && sourceIdx11 < sourceData.Length)
                        {
                            float val00 = sourceData[sourceIdx00];
                            float val01 = sourceData[sourceIdx01];
                            float val10 = sourceData[sourceIdx10];
                            float val11 = sourceData[sourceIdx11];
                            
                            float val0 = Mathf.Lerp(val00, val01, fx);
                            float val1 = Mathf.Lerp(val10, val11, fx);
                            float finalVal = Mathf.Lerp(val0, val1, fy);
                            
                            int targetIdx = (y * targetWidth + x) * 4 + c;
                            if (targetIdx < targetData.Length)
                            {
                                targetData[targetIdx] = Mathf.Clamp01(finalVal);
                            }
                        }
                    }
                }
            }
        }
        
        /// <summary>
        /// Generates advanced procedural texture data with style-specific algorithms.
        /// </summary>
        private static void GenerateAdvancedProceduralTexture(NativeArray<float> textureData, int width, int height, string prompt, string style, Unity.Mathematics.Random random)
        {
            // Analyze prompt for color and pattern hints
            var promptLower = prompt.ToLower();
            bool hasWater = promptLower.Contains("water") || promptLower.Contains("ocean") || promptLower.Contains("sea");
            bool hasFire = promptLower.Contains("fire") || promptLower.Contains("flame") || promptLower.Contains("lava");
            bool hasGrass = promptLower.Contains("grass") || promptLower.Contains("forest") || promptLower.Contains("nature");
            bool hasStone = promptLower.Contains("stone") || promptLower.Contains("rock") || promptLower.Contains("concrete");
            
            for (int y = 0; y < height; y++)
            {
                for (int x = 0; x < width; x++)
                {
                    int index = (y * width + x) * 4;
                    float u = (float)x / width;
                    float v = (float)y / height;
                    
                    Color color = GenerateStyledPixel(u, v, style, hasWater, hasFire, hasGrass, hasStone, random);
                    
                    textureData[index] = color.r;
                    textureData[index + 1] = color.g;
                    textureData[index + 2] = color.b;
                    textureData[index + 3] = color.a;
                }
            }
        }
        
        /// <summary>
        /// Generates a styled pixel color based on UV coordinates and content hints.
        /// </summary>
        private static Color GenerateStyledPixel(float u, float v, string style, bool hasWater, bool hasFire, bool hasGrass, bool hasStone, Unity.Mathematics.Random random)
        {
            switch (style.ToLower())
            {
                case "pixel_art":
                    return GeneratePixelArtColor(u, v, hasWater, hasFire, hasGrass, hasStone, random);
                case "realistic":
                    return GenerateRealisticColor(u, v, hasWater, hasFire, hasGrass, hasStone, random);
                case "stylized":
                    return GenerateStylizedColor(u, v, hasWater, hasFire, hasGrass, hasStone, random);
                case "normal_map":
                    return GenerateNormalMapColor(u, v, random);
                default:
                    return GenerateDefaultColor(u, v, hasWater, hasFire, hasGrass, hasStone, random);
            }
        }

        /// <summary>
        /// Creates advanced Texture2D with proper format and settings.
        /// </summary>
        private static Texture2D CreateAdvancedTexture2D(NativeArray<float> textureData, int width, int height, string format)
        {
            TextureFormat textureFormat = format.ToLower() switch
            {
                "exr" => TextureFormat.RGBAFloat,
                "hdr" => TextureFormat.RGBAHalf,
                _ => TextureFormat.RGBA32
            };
            
            var texture = new Texture2D(width, height, textureFormat, true, false);
            
            // Convert float data to Color32 array
            var colors = new Color[width * height];
            for (int i = 0; i < colors.Length; i++)
            {
                int dataIndex = i * 4;
                colors[i] = new Color(
                    textureData[dataIndex],
                    textureData[dataIndex + 1],
                    textureData[dataIndex + 2],
                    textureData[dataIndex + 3]
                );
            }
            
            texture.SetPixels(colors);
            texture.Apply(true);
            
            return texture;
        }

        /// <summary>
        /// Applies post-processing effects to generated textures.
        /// </summary>
        private static void ApplyTexturePostProcessing(Texture2D texture, string style)
        {
            // Apply style-specific post-processing
            switch (style.ToLower())
            {
                case "realistic":
                    // Apply noise reduction and detail enhancement
                    break;
                case "stylized":
                    // Apply artistic filters
                    break;
                case "cartoon":
                    // Apply cell shading effects
                    break;
                case "abstract":
                    // Apply abstract art filters
                    break;
            }
        }

        /// <summary>
        /// Saves generated texture with proper naming and organization.
        /// </summary>
        private static string SaveGeneratedTexture(Texture2D texture, string outputPath, string format, string prompt)
        {
            if (string.IsNullOrEmpty(outputPath))
            {
                outputPath = "Assets/Generated/Textures";
            }
            
            // Ensure directory exists
            Directory.CreateDirectory(Path.GetDirectoryName(Path.Combine(Application.dataPath, "..", outputPath)));
            
            // Generate filename
            string sanitizedPrompt = SanitizeFileName(prompt);
            string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            string filename = $"AI_Texture_{sanitizedPrompt}_{timestamp}.{format}";
            string fullPath = Path.Combine(outputPath, filename);
            
            // Save texture
            byte[] textureData = format.ToLower() switch
            {
                "jpg" => texture.EncodeToJPG(90),
                "exr" => texture.EncodeToEXR(),
                _ => texture.EncodeToPNG()
            };
            
            File.WriteAllBytes(Path.Combine(Application.dataPath, "..", fullPath), textureData);
            
            return fullPath;
        }

        /// <summary>
        /// Configures texture import settings for optimal quality using Unity 6.2 APIs.
        /// </summary>
        private static void ConfigureTextureImportSettings(string texturePath, string style)
        {
            var importer = AssetImporter.GetAtPath(texturePath) as TextureImporter;
            if (importer != null)
            {
                // Advanced Unity 6.2 texture settings
                importer.textureType = TextureImporterType.Default;
                importer.alphaSource = TextureImporterAlphaSource.FromInput;
                importer.alphaIsTransparency = true;
                importer.mipmapEnabled = true;
                importer.filterMode = FilterMode.Trilinear;
                importer.anisoLevel = 16;
                importer.sRGBTexture = true;
                importer.streamingMipmaps = false;
                
                // Advanced settings for Unity 6.2
                importer.isReadable = false; // Optimize memory usage
                importer.ignorePngGamma = false;
                importer.npotScale = TextureImporterNPOTScale.ToNearest;
                
                // Style-specific settings
                switch (style.ToLower())
                {
                    case "pixel_art":
                        importer.filterMode = FilterMode.Point;
                        importer.mipmapEnabled = false;
                        importer.textureCompression = TextureImporterCompression.Uncompressed;
                        importer.npotScale = TextureImporterNPOTScale.None;
                        break;
                    case "realistic":
                        importer.textureCompression = TextureImporterCompression.CompressedHQ;
                        importer.compressionQuality = 100;
                        importer.crunchedCompression = true;
                        break;
                    case "stylized":
                        importer.textureCompression = TextureImporterCompression.Compressed;
                        importer.compressionQuality = 75;
                        break;
                    case "normal_map":
                        importer.textureType = TextureImporterType.NormalMap;
                        importer.convertToNormalmap = false;
                        importer.normalmapFilter = TextureImporterNormalFilter.Standard;
                        break;
                }
                
                // Apply settings and reimport
                EditorUtility.SetDirty(importer);
                importer.SaveAndReimport();
            }
        }

        /// <summary>
        /// Sanitizes asset path for Unity compatibility.
        /// </summary>
        private static string SanitizeAssetPath(string path)
        {
            if (string.IsNullOrEmpty(path)) return string.Empty;
            
            path = path.Replace('\\', '/').Trim();
            if (path.StartsWith("Assets/")) return path;
            if (path.StartsWith("/")) path = path.Substring(1);
            if (!path.StartsWith("Assets/")) path = "Assets/" + path;
            
            return path;
        }

        /// <summary>
        /// Sanitizes filename for file system compatibility.
        /// </summary>
        private static string SanitizeFileName(string filename)
        {
            if (string.IsNullOrEmpty(filename)) return "Unnamed";
            
            var invalidChars = Path.GetInvalidFileNameChars();
            var sanitized = new string(filename.Where(c => !invalidChars.Contains(c)).ToArray());
            
            return string.IsNullOrEmpty(sanitized) ? "Unnamed" : sanitized.Substring(0, Math.Min(sanitized.Length, 50));
        }

        /// <summary>
        /// Creates an advanced material based on the current render pipeline using Unity 6.2 APIs.
        /// </summary>
        private static Material CreateAdvancedMaterial(string materialType, string prompt)
        {
            Material material = null;
            
            // Detect current render pipeline
            var renderPipelineAsset = GraphicsSettings.defaultRenderPipeline;
            
            if (renderPipelineAsset != null)
            {
                // URP (Universal Render Pipeline)
                if (renderPipelineAsset.GetType().Name.Contains("Universal"))
                {
                    switch (materialType.ToLower())
                    {
                        case "pbr":
                            material = new Material(Shader.Find("Universal Render Pipeline/Lit"));
                            break;
                        case "unlit":
                            material = new Material(Shader.Find("Universal Render Pipeline/Unlit"));
                            break;
                        case "transparent":
                            material = new Material(Shader.Find("Universal Render Pipeline/Lit"));
                            material.SetFloat("_Surface", 1); // Transparent
                            material.SetFloat("_Blend", 0); // Alpha
                            break;
                        default:
                            material = new Material(Shader.Find("Universal Render Pipeline/Lit"));
                            break;
                    }
                }
                // HDRP (High Definition Render Pipeline)
                else if (renderPipelineAsset.GetType().Name.Contains("HDRenderPipeline"))
                {
                    switch (materialType.ToLower())
                    {
                        case "pbr":
                            material = new Material(Shader.Find("HDRP/Lit"));
                            break;
                        case "unlit":
                            material = new Material(Shader.Find("HDRP/Unlit"));
                            break;
                        default:
                            material = new Material(Shader.Find("HDRP/Lit"));
                            break;
                    }
                }
            }
            else
            {
                // Built-in Render Pipeline
                switch (materialType.ToLower())
                {
                    case "pbr":
                        material = new Material(Shader.Find("Standard"));
                        break;
                    case "unlit":
                        material = new Material(Shader.Find("Unlit/Texture"));
                        break;
                    case "transparent":
                        material = new Material(Shader.Find("Standard"));
                        material.SetFloat("_Mode", 3); // Transparent
                        material.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
                        material.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
                        material.SetInt("_ZWrite", 0);
                        material.DisableKeyword("_ALPHATEST_ON");
                        material.EnableKeyword("_ALPHABLEND_ON");
                        material.DisableKeyword("_ALPHAPREMULTIPLY_ON");
                        material.renderQueue = 3000;
                        break;
                    default:
                        material = new Material(Shader.Find("Standard"));
                        break;
                }
            }
            
            if (material != null)
            {
                material.name = $"AI_Material_{SanitizeFileName(prompt)}";
            }
            
            return material;
        }
        
        /// <summary>
        /// Generates material maps using AI with Unity 6.2 APIs.
        /// </summary>
        private static Dictionary<string, Texture2D> GenerateMaterialMapsWithAI(string prompt, int textureResolution, string[] generateMaps, string materialType)
        {
            var textureMaps = new Dictionary<string, Texture2D>();
            var random = new Unity.Mathematics.Random((uint)prompt.GetHashCode());
            
            foreach (var mapType in generateMaps)
            {
                var textureData = new NativeArray<float>(textureResolution * textureResolution * 4, Allocator.Temp);
                GenerateAdvancedProceduralTexture(textureData, textureResolution, textureResolution, $"{prompt} {mapType}", materialType, random);
                
                var texture = CreateAdvancedTexture2D(textureData, textureResolution, textureResolution, "png");
                texture.name = $"{materialType}_{mapType}";
                textureMaps[mapType] = texture;
                
                textureData.Dispose();
            }
            
            return textureMaps;
        }

        /// <summary>
        /// Saves generated material and associated textures.
        /// </summary>
        private static string SaveGeneratedMaterial(Material material, Dictionary<string, Texture2D> textureMaps, string outputPath, string prompt)
        {
            if (string.IsNullOrEmpty(outputPath))
            {
                outputPath = "Assets/Generated/Materials";
            }
            
            // Ensure directory exists
            Directory.CreateDirectory(Path.GetDirectoryName(Path.Combine(Application.dataPath, "..", outputPath)));
            
            // Generate filename
            string sanitizedPrompt = SanitizeFileName(prompt);
            string timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            string materialName = $"AI_Material_{sanitizedPrompt}_{timestamp}";
            
            // Save textures first
            foreach (var kvp in textureMaps)
            {
                string texturePath = Path.Combine(outputPath, $"{materialName}_{kvp.Key}.png");
                byte[] textureData = kvp.Value.EncodeToPNG();
                File.WriteAllBytes(Path.Combine(Application.dataPath, "..", texturePath), textureData);
                AssetDatabase.ImportAsset(texturePath);
                
                // Update material to use the saved texture
                var savedTexture = AssetDatabase.LoadAssetAtPath<Texture2D>(texturePath);
                if (savedTexture != null)
                {
                    switch (kvp.Key.ToLower())
                    {
                        case "albedo":
                            material.SetTexture("_BaseMap", savedTexture);
                            material.SetTexture("_MainTex", savedTexture);
                            break;
                        case "normal":
                            material.SetTexture("_BumpMap", savedTexture);
                            material.SetTexture("_NormalMap", savedTexture);
                            break;
                        case "roughness":
                            material.SetTexture("_MetallicGlossMap", savedTexture);
                            break;
                        case "metallic":
                            material.SetTexture("_MetallicGlossMap", savedTexture);
                            break;
                    }
                }
            }
            
            // Save material
            string materialPath = Path.Combine(outputPath, $"{materialName}.mat");
            AssetDatabase.CreateAsset(material, materialPath);
            
            return materialPath;
        }

        /// <summary>
        /// Applies generated texture maps to material using Unity 6.2 APIs.
        /// </summary>
        private static void ApplyTextureMapsToMaterial(Material material, Dictionary<string, Texture2D> textureMaps, float[] roughnessRange, float[] metallicRange, float normalStrength)
        {
            if (material == null || textureMaps == null) return;
            
            // Apply textures based on available maps
            foreach (var kvp in textureMaps)
            {
                string mapType = kvp.Key.ToLower();
                Texture2D texture = kvp.Value;
                
                switch (mapType)
                {
                    case "albedo":
                    case "diffuse":
                    case "basecolor":
                        material.SetTexture("_MainTex", texture);
                        material.SetTexture("_BaseMap", texture); // URP
                        break;
                    case "normal":
                        material.SetTexture("_BumpMap", texture);
                        material.SetTexture("_NormalMap", texture); // URP
                        material.SetFloat("_BumpScale", normalStrength);
                        material.SetFloat("_NormalScale", normalStrength); // URP
                        material.EnableKeyword("_NORMALMAP");
                        break;
                    case "metallic":
                        material.SetTexture("_MetallicGlossMap", texture);
                        material.SetFloat("_Metallic", UnityEngine.Random.Range(metallicRange[0], metallicRange[1]));
                        material.EnableKeyword("_METALLICGLOSSMAP");
                        break;
                    case "roughness":
                        material.SetTexture("_MetallicGlossMap", texture);
                        material.SetFloat("_Glossiness", 1.0f - UnityEngine.Random.Range(roughnessRange[0], roughnessRange[1]));
                        material.SetFloat("_Smoothness", 1.0f - UnityEngine.Random.Range(roughnessRange[0], roughnessRange[1])); // URP
                        break;
                    case "occlusion":
                        material.SetTexture("_OcclusionMap", texture);
                        material.SetFloat("_OcclusionStrength", 1.0f);
                        break;
                    case "emission":
                        material.SetTexture("_EmissionMap", texture);
                        material.SetColor("_EmissionColor", Color.white);
                        material.EnableKeyword("_EMISSION");
                        break;
                    case "height":
                    case "displacement":
                        material.SetTexture("_ParallaxMap", texture);
                        material.SetFloat("_Parallax", 0.02f);
                        break;
                }
            }
        }

        #endregion
    }

    #region Supporting Classes

    /// <summary>
    /// Represents an AI generator style configuration.
    /// </summary>
    public class AIGeneratorStyle
    {
        public string Name { get; set; }
        public string GeneratorType { get; set; }
        public Dictionary<string, object> Configuration { get; set; }
    }

    /// <summary>
    /// Represents an AI asset pipeline configuration.
    /// </summary>
    public class AIAssetPipeline
    {
        public string Name { get; set; }
        public List<string> AssetTypes { get; set; }
        public Dictionary<string, string> OutputStructure { get; set; }
        public Dictionary<string, object> AutoImportSettings { get; set; }
        public string NamingConvention { get; set; }
    }

    /// <summary>
    /// Represents an animation keyframe with position, rotation, and scale data.
    /// </summary>
    [System.Serializable]
    public class AnimationKeyframe
    {
        public float time;
        public Vector3 position;
        public Quaternion rotation;
        public Vector3 scale;
        public AnimationUtility.TangentMode tangentMode;
        public float inTangent;
        public float outTangent;

        public AnimationKeyframe()
        {
            time = 0f;
            position = Vector3.zero;
            rotation = Quaternion.identity;
            scale = Vector3.one;
            tangentMode = AnimationUtility.TangentMode.Auto;
            inTangent = 0f;
            outTangent = 0f;
        }

        public AnimationKeyframe(float t, Vector3 pos, Quaternion rot, Vector3 scl)
        {
            time = t;
            position = pos;
            rotation = rot;
            scale = scl;
            tangentMode = AnimationUtility.TangentMode.Auto;
            inTangent = 0f;
            outTangent = 0f;
        }
    }

    /// <summary>
    /// Utility class for saving WAV audio files.
    /// </summary>
    public static class SavWav
    {
        const int HEADER_SIZE = 44;

        public static bool Save(string filename, AudioClip clip)
        {
            if (!filename.ToLower().EndsWith(".wav"))
            {
                filename += ".wav";
            }

            var filepath = Path.Combine(Application.dataPath, filename);

            Directory.CreateDirectory(Path.GetDirectoryName(filepath));

            using (var fileStream = CreateEmpty(filepath))
            {
                ConvertAndWrite(fileStream, clip);
                WriteHeader(fileStream, clip);
            }

            return true;
        }

        static FileStream CreateEmpty(string filepath)
        {
            var fileStream = new FileStream(filepath, FileMode.Create);
            byte emptyByte = new byte();

            for (int i = 0; i < HEADER_SIZE; i++)
            {
                fileStream.WriteByte(emptyByte);
            }

            return fileStream;
        }

        static void ConvertAndWrite(FileStream fileStream, AudioClip clip)
        {
            var samples = new float[clip.samples];
            clip.GetData(samples, 0);

            Int16[] intData = new Int16[samples.Length];
            Byte[] bytesData = new Byte[samples.Length * 2];

            int rescaleFactor = 32767;

            for (int i = 0; i < samples.Length; i++)
            {
                intData[i] = (short)(samples[i] * rescaleFactor);
                Byte[] byteArr = new Byte[2];
                byteArr = BitConverter.GetBytes(intData[i]);
                byteArr.CopyTo(bytesData, i * 2);
            }

            fileStream.Write(bytesData, 0, bytesData.Length);
        }

        static void WriteHeader(FileStream fileStream, AudioClip clip)
        {
            var hz = clip.frequency;
            var channels = clip.channels;
            var samples = clip.samples;

            fileStream.Seek(0, SeekOrigin.Begin);

            Byte[] riff = System.Text.Encoding.UTF8.GetBytes("RIFF");
            fileStream.Write(riff, 0, 4);

            Byte[] chunkSize = BitConverter.GetBytes(fileStream.Length - 8);
            fileStream.Write(chunkSize, 0, 4);

            Byte[] wave = System.Text.Encoding.UTF8.GetBytes("WAVE");
            fileStream.Write(wave, 0, 4);

            Byte[] fmt = System.Text.Encoding.UTF8.GetBytes("fmt ");
            fileStream.Write(fmt, 0, 4);

            Byte[] subChunk1 = BitConverter.GetBytes(16);
            fileStream.Write(subChunk1, 0, 4);

            // Removed unused variable 'two'
            UInt16 one = 1;

            Byte[] audioFormat = BitConverter.GetBytes(one);
            fileStream.Write(audioFormat, 0, 2);

            Byte[] numChannels = BitConverter.GetBytes(channels);
            fileStream.Write(numChannels, 0, 2);

            Byte[] sampleRate = BitConverter.GetBytes(hz);
            fileStream.Write(sampleRate, 0, 4);

            Byte[] byteRate = BitConverter.GetBytes(hz * channels * 2);
            fileStream.Write(byteRate, 0, 4);

            UInt16 blockAlign = (ushort)(channels * 2);
            fileStream.Write(BitConverter.GetBytes(blockAlign), 0, 2);

            UInt16 bps = 16;
            Byte[] bitsPerSample = BitConverter.GetBytes(bps);
            fileStream.Write(bitsPerSample, 0, 2);

            Byte[] datastring = System.Text.Encoding.UTF8.GetBytes("data");
            fileStream.Write(datastring, 0, 4);

            Byte[] subChunk2 = BitConverter.GetBytes(samples * channels * 2);
            fileStream.Write(subChunk2, 0, 4);
        }
    }

    /// <summary>
    /// Easing functions for animation curves.
    /// </summary>
    public static class EasingFunctions
    {
        public static float Linear(float t) => t;
        
        public static float EaseInQuad(float t) => t * t;
        
        public static float EaseOutQuad(float t) => 1 - (1 - t) * (1 - t);
        
        public static float EaseInOutQuad(float t)
        {
            return t < 0.5f ? 2 * t * t : 1 - Mathf.Pow(-2 * t + 2, 2) / 2;
        }
        
        public static float EaseInCubic(float t) => t * t * t;
        
        public static float EaseOutCubic(float t) => 1 - Mathf.Pow(1 - t, 3);
        
        public static float EaseInOutCubic(float t)
        {
            return t < 0.5f ? 4 * t * t * t : 1 - Mathf.Pow(-2 * t + 2, 3) / 2;
        }
        
        public static float EaseInBounce(float t)
        {
            return 1 - EaseOutBounce(1 - t);
        }
        
        public static float EaseOutBounce(float t)
        {
            const float n1 = 7.5625f;
            const float d1 = 2.75f;
            
            if (t < 1 / d1)
            {
                return n1 * t * t;
            }
            else if (t < 2 / d1)
            {
                return n1 * (t -= 1.5f / d1) * t + 0.75f;
            }
            else if (t < 2.5 / d1)
            {
                return n1 * (t -= 2.25f / d1) * t + 0.9375f;
            }
            else
            {
                return n1 * (t -= 2.625f / d1) * t + 0.984375f;
            }
        }
        
        public static float EaseInOutBounce(float t)
        {
            return t < 0.5f
                ? (1 - EaseOutBounce(1 - 2 * t)) / 2
                : (1 + EaseOutBounce(2 * t - 1)) / 2;
        }
    }

    /// <summary>
    /// Texture generation data structure.
    /// </summary>
    [System.Serializable]
    public class TextureGenerationData
    {
        public string prompt;
        public int width;
        public int height;
        public string style;
        public float[] seedData;
        public Dictionary<string, float> parameters;

        public TextureGenerationData()
        {
            parameters = new Dictionary<string, float>();
            seedData = new float[0];
        }
    }

    /// <summary>
    /// Audio generation data structure.
    /// </summary>
    [System.Serializable]
    public class AudioGenerationData
    {
        public string prompt;
        public float duration;
        public int sampleRate;
        public string soundType;
        public float[] generatedSamples;
        public Dictionary<string, float> parameters;

        public AudioGenerationData()
        {
            parameters = new Dictionary<string, float>();
            generatedSamples = new float[0];
        }
    }

    /// <summary>
    /// ScriptableObject for storing AI generator style presets.
    /// </summary>
    [System.Serializable]
    public class AIStylePreset : ScriptableObject
    {
        [Header("Style Information")]
        public string styleName;
        public string generatorType;
        public string description;
        public string version = "1.0";
        
        [Header("Configuration")]
        public SerializableConfiguration configurationData;
        
        [Header("Metadata")]
        public string createdBy;
        public string createdDate;
        public string[] tags;
        
        private void OnEnable()
        {
            if (string.IsNullOrEmpty(createdDate))
            {
                createdDate = System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            }
        }
    }
    
    /// <summary>
    /// Serializable configuration data structure for storing dictionary-like data.
    /// </summary>
    [System.Serializable]
    public class SerializableConfiguration
    {
        public string[] keys = new string[0];
        public string[] values = new string[0];
        
        public Dictionary<string, object> ToDictionary()
        {
            var dict = new Dictionary<string, object>();
            int count = Mathf.Min(keys?.Length ?? 0, values?.Length ?? 0);
            
            for (int i = 0; i < count; i++)
            {
                if (!string.IsNullOrEmpty(keys[i]))
                {
                    dict[keys[i]] = values[i];
                }
            }
            
            return dict;
        }
        
        public void FromDictionary(Dictionary<string, object> dict)
        {
            if (dict == null)
            {
                keys = new string[0];
                values = new string[0];
                return;
            }
            
            keys = dict.Keys.ToArray();
            values = dict.Values.Select(v => v?.ToString() ?? "").ToArray();
        }
    }

    /// <summary>
    /// Texture variation data structure for storing variation information.
    /// </summary>
    [System.Serializable]
    public class TextureVariationData
    {
        public int width;
        public int height;
        public int variationIndex;
        public float variationStrength;
        public TextureFormat sourceFormat;
        public Color[] pixels;
        public string variationType;
        public Dictionary<string, float> parameters;

        public TextureVariationData()
        {
            parameters = new Dictionary<string, float>();
            pixels = new Color[0];
            sourceFormat = TextureFormat.RGBA32;
            variationType = "standard";
        }
    }

    /// <summary>
    /// ScriptableObject for storing AI pipeline configurations.
    /// </summary>
    [System.Serializable]
    public class AIPipelineConfig : ScriptableObject
    {
        [Header("Pipeline Information")]
        public string pipelineName;
        public string description;
        public string version = "1.0";
        
        [Header("Configuration")]
        public string[] assetTypes;
        public SerializableStringDictionary outputDirectories;
        public string namingConvention;
        
        [Header("Metadata")]
        public string createdBy;
        public string createdDate;
        public string lastModified;
        public bool isActive = true;
        
        private void OnEnable()
        {
            if (string.IsNullOrEmpty(createdDate))
            {
                createdDate = System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            }
            lastModified = System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
        }
    }
    
    /// <summary>
    /// Serializable dictionary for string key-value pairs.
    /// </summary>
    [System.Serializable]
    public class SerializableStringDictionary
    {
        public string[] keys = new string[0];
        public string[] values = new string[0];
        
        public Dictionary<string, string> ToDictionary()
        {
            var dict = new Dictionary<string, string>();
            int count = Mathf.Min(keys?.Length ?? 0, values?.Length ?? 0);
            
            for (int i = 0; i < count; i++)
            {
                if (!string.IsNullOrEmpty(keys[i]))
                {
                    dict[keys[i]] = values[i] ?? "";
                }
            }
            
            return dict;
        }
        
        public void FromDictionary(Dictionary<string, object> dict)
        {
            if (dict == null)
            {
                keys = new string[0];
                values = new string[0];
                return;
            }
            
            keys = dict.Keys.ToArray();
            values = dict.Values.Select(v => v?.ToString() ?? "").ToArray();
        }
        
        public void FromStringDictionary(Dictionary<string, string> dict)
        {
            if (dict == null)
            {
                keys = new string[0];
                values = new string[0];
                return;
            }
            
            keys = dict.Keys.ToArray();
            values = dict.Values.ToArray();
        }
    }

    #endregion
}